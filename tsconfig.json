{"compilerOptions": {"skipLibCheck": true, "types": ["node", "jest"], "module": "commonjs", "target": "es6", "strict": true, "allowJs": true, "declaration": true, "esModuleInterop": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["node_modules/*", "types/*.d.ts"]}, "outDir": "./dist", "rootDir": "./src"}, "include": ["src/**/*", "types/*.d.ts"], "exclude": ["node_modules", "**/*.test.ts", "**/*.spec.ts"]}