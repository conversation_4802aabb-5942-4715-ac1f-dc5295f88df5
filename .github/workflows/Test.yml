name: Tests
on:
  pull_request:
    types: [ edited, synchronize, opened, labeled ]
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint-and-test-code:
    runs-on: ubuntu-latest

    steps:
      - name: Fail if "Run Tests" label is not set
        id: assert_tests_label
        if: ${{ !contains(github.event.pull_request.labels.*.name, 'Run Tests') }}
        run: |
          echo "Set the label 'Run Test', to execute tests."
          exit 1

      - name: Checkout code
        id: checkout_code
        uses: actions/checkout@v2

      - name: Set up Node.js
        id: setup_nodejs
        uses: actions/setup-node@v2
        with:
          node-version: 18

      - name: Install dependencies
        env:
          WEB_AUTOMATE_GITHUB_TOKEN: ${{ secrets.W73_PACKAGE_TOKEN }}
        run: yarn install --frozen-lockfile

      - name: Run Tests
        env:
          WEB_AUTOMATE_GITHUB_TOKEN: ${{ secrets.W73_PACKAGE_TOKEN }}
        run: yarn test
