name: Build and Publish Tagged Release

on:
  pull_request:
    types:
      - closed

jobs:
  build-and-publish:
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'master'
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        id: setup_nodejs
        uses: actions/setup-node@v2
        with:
          node-version: 18

      - name: Configure Git
        run: |
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"
          git config --global push.followTags true

      - name: Get PR labels
        id: get_labels
        run: |
          echo "LABELS=$(jq -r '.pull_request.labels | map(.name) | join(",")' $GITHUB_EVENT_PATH)" >> $GITHUB_ENV

      - name: Determine version type
        id: get_version_type
        run: |
          if [[ "$LABELS" == *"RELEASE/patch"* ]]; then
            echo "VERSION_TYPE=patch" >> $GITHUB_ENV
          elif [[ "$LABELS" == *"RELEASE/minor"* ]]; then
            echo "VERSION_TYPE=minor" >> $GITHUB_ENV
          elif [[ "$LABELS" == *"RELEASE/major"* ]]; then
            echo "VERSION_TYPE=major" >> $GITHUB_ENV
          else
            echo "VERSION_TYPE=unknown" >> $GITHUB_ENV
          fi

      - name: Set App deployment flags
        id: deploy_apps
        run: |
          VERSION_TYPE=${{ env.VERSION_TYPE }}
          ENSURE_VERSION_TYPE=false
          
          if [[ "$ENSURE_VERSION_TYPE" == true && "$VERSION_TYPE" == "unknown" ]]; then
              echo "VERSION_TYPE=patch" >> $GITHUB_ENV
          fi

      - name: Fetch all tags
        if: env.VERSION_TYPE != 'unknown'
        run: git fetch --tags

      - name: Determine latest version from tags
        if: env.VERSION_TYPE != 'unknown'
        id: get_latest_version
        run: |
          latest_tag=$(git describe --tags `git rev-list --tags --max-count=1` 2>/dev/null || echo "no-tags")
          echo "Latest tag: $latest_tag"
          if [[ $latest_tag == "no-tags" ]]; then
            echo "LATEST_VERSION=0.0.0" >> $GITHUB_ENV
          elif [[ $latest_tag =~ ^v[0-9]+\.[0-9]+\.[0-9]+(_(.)*)?$ ]]; then
            # Extract the base version number
            base_version=$(echo $latest_tag | grep -oE '^v[0-9]+\.[0-9]+\.[0-9]+')
            echo "LATEST_VERSION=${base_version#v}" >> $GITHUB_ENV
          else
            echo "LATEST_VERSION=0.0.0" >> $GITHUB_ENV
          fi

      - name: Determine new version
        if: env.VERSION_TYPE != 'unknown'
        id: get_new_version
        run: |
          LATEST_VERSION=${{ env.LATEST_VERSION }}
          VERSION_TYPE=${{ env.VERSION_TYPE }}
          IFS='.' read -r -a version_parts <<< "$LATEST_VERSION"
          major=${version_parts[0]}
          minor=${version_parts[1]}
          patch=${version_parts[2]}
          if [[ "$VERSION_TYPE" == "major" ]]; then
            major=$((major + 1))
            minor=0
            patch=0
          elif [[ "$VERSION_TYPE" == "minor" ]]; then
            minor=$((minor + 1))
            patch=0
          elif [[ "$VERSION_TYPE" == "patch" ]]; then
            patch=$((patch + 1))
          else
            echo "Unknown version type: $VERSION_TYPE"
            exit 1
          fi
          NEW_VERSION="$major.$minor.$patch"
          echo "New version: $NEW_VERSION"
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV

      - name: Create GitHub Release
        if: env.VERSION_TYPE != 'unknown'
        uses: actions/create-release@v1
        with:
          tag_name: "v${{ env.NEW_VERSION }}"
          release_name: Release "v${{ env.NEW_VERSION }}"
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.W73_PACKAGE_TOKEN }}
