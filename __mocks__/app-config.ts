import type {BuildTimeConfig} from 'config';

const testBuildTimeConfig: BuildTimeConfig = {
    AI: {
        SOLUTION_BUILDER_ASSISTANT_ID: 'assistant-12345',
    },
    ACCESS_CODE_KEY: 'access-key',
    ACCESS_CODE_VALUE: 'secret-code',
    ALLOWED_ORIGINS: ['https://myapp.com', 'https://staging.myapp.com'],
    APP_DATA: {
        BUILDER_HEARTBEAT_INTERVAL: 30000,
        BUILDER_HEARTBEAT_TIMEOUT: 120000,
        MAX_LOOPS_ITERATIONS: 100,
        MAX_OPEN_TABS: 10,
        MAX_PROJECT_RUN_RETRIES: 0,
    },
    APP_NAME: 'MyApp',
    APP_NAME_SHORT: 'MyAppShort',
    BUILD_ID: 'build-123',
    SERVER_ID: 'instance-abc',
    COMPANY_CITY: 'MyCity',
    COMPANY_CONTACT: '<EMAIL>',
    COMPANY_COUNTRY: 'MyCountry',
    COMPANY_NAME: 'My Company Inc.',
    COMPANY_SOCIALS: {
        YOUTUBE: 'https://youtube.com/mycompany',
        X: 'https://x.com/mycompany',
        WHATSAPP: 'https://wa.me/**********',
    },
    COMPANY_STATE: 'MyState',
    COMPANY_STREET: '1234 Company St.',
    COMPANY_UID: 'UID123456789',
    DEACTIVATE_ACCESS_CHECK: false,
    EMAILS_ABUSE: '<EMAIL>',
    EMAILS_COMPANY: '<EMAIL>',
    EMAILS_CONTACT: '<EMAIL>',
    EMAILS_SUPPORT: '<EMAIL>',
    ENV: 'dev',
    GCP: {
        credential: {
            type: 'service_account',
            project_id: 'my-gcp-project',
            private_key_id: 'abc123',
            private_key: '-----BEGIN PRIVATE KEY-----\nMIIEvAIBADANBgkqhki...\n-----END PRIVATE KEY-----\n',
            client_email: '<EMAIL>',
            client_id: '********************',
            auth_uri: 'https://accounts.google.com/o/oauth2/auth',
            token_uri: 'https://oauth2.googleapis.com/token',
            auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
            client_x509_cert_url: 'https://www.googleapis.com/robot/v1/metadata/x509/gcp-service-account%40my-gcp-project.iam.gserviceaccount.com',
        },
        storageBucket: 'my-gcp-bucket',
    },
    IS_DEV: true,
    IS_PROD: false,

    // Optional BuildTimeConfigForAPI fields
    IS_STAGE: false,
    OPEN_AI: {
        ORGANIZATION: 'org-xyz123',
        API_KEY: 'sk-abc123',
    },
    PAYMENT: {
        PROVIDER: 'Stripe',
        PROVIDER_LINK: 'https://stripe.com',
    },
    PORT: '8080',
    SENDGRID: {
        API_KEY: 'SG.xxxxxx.yyyyyyy',
        SUPPORT_EMAIL: '<EMAIL>',
        TEST_EMAIL: '<EMAIL>',
    },
    SESSION_COOKIE_NAME: 'myapp-session',
    STRIPE: {
        public_key: 'pk_test_abc123',
        secret_key: 'sk_test_abc123',
        webhook_key: 'whsec_abc123',
    },

    // Optional BuildTimeConfigForBuilder fields
    TIMEZONE: 'Europe/Zurich',
    WEBSITE: 'https://myapp.com',
};


export function getConfig(): BuildTimeConfig {
    return testBuildTimeConfig;
}
