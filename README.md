# Usage

Before using any of the resources in this directory, you must first run import the `/activate` file in the root directory to consume libraries.
Failing to do so may result in errors.

# Adding products + Prices

1. Create product: https://dashboard.stripe.com/test/products
2. Create prices for monthly and yearly recurrent payments in the above product
3. Export products and save in src/config/pricing/productDataList.json
4. Export prices and save in src/config/pricing/priceDataList.json

# Required globals

`webAutomate.appOrigin: string`: The website origin of the app, used for the cookie domain and the redirect url
`webAutomate.i18n: {changeLanguage: Function, t: Function}`: The reference to the translation library

# i18n Troubleshooting

If you are having trouble with the i18n library, make sure that the `webAutomate.i18n` is already set.
The i18n object should contain the following properties:

* `changeLanguage: (language: string) => void` // The function to change the language
* `t: (key: string, options?: any) => string` // The translation function

You can use the default i18n library by importing `<root>/src/i18n/server-setup`.

# Releasing a new version

Add the following labels

`RELEASE/major`: Do a major release after merge
`RELEASE/minor`: Do a minor RELEASE after merge
`RELEASE/patch`: Do a patch RELEASE after merge

# Version history

- `0.0.1`: Initial version

# API versions

- v1 = Link-Checker
- v2 = Dima
- v3 = Webeagle
- v4 = Webautomate
