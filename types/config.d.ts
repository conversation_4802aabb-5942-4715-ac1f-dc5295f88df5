export interface BuildTimeConfigForAPI {
    WEBSITE: string;
    TIMEZONE: string;
    ALLOWED_ORIGINS: string[];
    DEACTIVATE_ACCESS_CHECK: boolean;
    ACCESS_CODE_KEY: string;
    ACCESS_CODE_VALUE: string;
    SESSION_COOKIE_NAME: string;
    PORT: string;
}

export interface BuildTimeConfigForBuilder {
    WEBSITE: string;
    TIMEZONE: string;
    BUILD_ID: string;
    SERVER_ID: string;
    SERVER_PROCESS_ID: string;
}

export type BuildTimeConfig = {
    AI: {
        SOLUTION_BUILDER_ASSISTANT_ID: string;
    },
    APP_DATA: {
        MAX_LOOPS_ITERATIONS: number;
        MAX_OPEN_TABS: number;
        MAX_PROJECT_RUN_RETRIES: number;
        BUILDER_HEARTBEAT_INTERVAL: number;
        BUILDER_HEARTBEAT_TIMEOUT: number;
    },
    APP_NAME: string,
    APP_NAME_SHORT: string;
    COMPANY_CITY: string;
    COMPANY_CONTACT: string;
    COMPANY_COUNTRY: string;
    COMPANY_NAME: string;
    COMPANY_SOCIALS: {
        YOUTUBE: string;
        X: string;
        WHATSAPP: string;
    };
    COMPANY_STATE: string;
    COMPANY_STREET: string;
    COMPANY_UID: string;
    EMAILS_ABUSE: string;
    EMAILS_COMPANY: string;
    EMAILS_CONTACT: string;
    EMAILS_SUPPORT: string;
    ENV: string,
    GCP: {
        credential: {
            type: string;
            project_id: string;
            private_key_id: string;
            private_key: string;
            client_email: string;
            client_id: string;
            auth_uri: string;
            token_uri: string;
            auth_provider_x509_cert_url: string;
            client_x509_cert_url: string;
        };
        storageBucket: string;
    },
    IS_DEV: boolean,
    IS_PROD: boolean,
    IS_STAGE: boolean;
    OPEN_AI: {
        ORGANIZATION: string;
        API_KEY: string;
    };
    PAYMENT: {
        PROVIDER: string;
        PROVIDER_LINK: string;
    };
    SENDGRID: {
        API_KEY: string;
        SUPPORT_EMAIL: string;
        TEST_EMAIL: string;
    };
    STRIPE: {
        public_key: string;
        secret_key: string;
        webhook_key: string;
    },
} & Partial<BuildTimeConfigForAPI> & Partial<BuildTimeConfigForBuilder>;

