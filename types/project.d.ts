import type {
    <PERSON><PERSON><PERSON><PERSON>,
    Devi<PERSON>,
    InfoCode,
    JSCodeItem,
    LinkOptionsSelection,
    RequestOptionsResources,
    RequestOptionsStorage,
    UrlItem,
    UrlMatchItem,
} from 'webautomate/global';
import type {BrowserType} from 'playwright';
import type {
    BuildSummary,
    ConditionResult,
    LighthouseCategory,
    LighthouseOutputFormat, ProjectBuildOverviewData} from 'solutions';
import type {REQUEST_WAIT_UNTIL} from '../src/config/request';
import type {buildModeLicenses} from '../src/config/catalog/subscriptions';
import type {projectStates, vendors} from '../src/config/project';
import type {URL_CHALLENGE_MODES} from '../src/config/urlChallenge';
import type {SubscriptionPackage} from 'administration';

export type BuildMode = keyof typeof buildModeLicenses;

export type ProjectBuildQueueItemVendor = typeof vendors[keyof typeof vendors];

export type ProjectBuildQueueItem<T extends boolean = false> = {
    accountId: string;
    billingCycle?: BillingCycle;
    buildId: T extends true ? number : number | undefined;
    createdOn: number;
    executeAt: number;
    heartbeat: Partial<{
        last: number;
        buildRuntimeMinutes?: number;
        buildSummary?: BuildSummary;
        solutionSummary?: ProjectBuildOverviewData['solutionSummary'];
    }>;
    id: string;
    isAssigned: boolean;
    isCompleted: boolean;
    projectId: string;
    retries: Array<{
        id: string;
        ts: number;
    }>;
    runner: {
        lastAttempt: T extends true ? number : number | undefined;
        attempts: Array<{
            ts: number;
            nodeId: string;
            processId: string;
        }>;
        nodeId: T extends true ? string : undefined;
        processId: T extends true ? string : undefined;
        termination?: {
            ts: number;
            reason: string;
        };
    };
    priority: SubscriptionPackage['priorityLevel']['value'];
    subscription: string;
    triggeredBy: {
        type: 'processGracefulProjectBuild' | 'processTimeoutProjectBuild' | 'adminApi' | 'managementApi' | 'uiRequest' | 'projectUpdate' | 'subscriptionUpdate';
    };
    vendor: ProjectBuildQueueItemVendor;
};

export type ProjectBuildArchiveItem = {
    nodeId: string;
    projectBuildQueueItem: ProjectBuildQueueItem<true>;
    ts: number;
};

export type ProjectBuildStep = {
    abortOnError?: boolean;
    fallbackLabel: string;
    id: string;
    ignoreOnError?: boolean;
    isInactive?: boolean;
    label: string;
    labelScript?: {
        id?: string;
        value: string;
    };
    originalStepIndex: number;
    labelStrategy?: string;
    selector?: string;
    stepType: string;
    type: string;
};

export type ActionStep = ProjectBuildStep & {
    categoryId: string;
    action: string;
    key: string;
    config: Record<string, any>;
    value: Record<string, any>;
};

export type ConditionStep = ProjectBuildStep & {
    value: Record<string, any>;
    stepList: ProjectBuildStep[];
};

export type LoopStep = ProjectBuildStep & {
    value: Record<string, any>;
    indexReference: string;
    stepList: ProjectBuildStep[];
    breakConditionJSCode: JSCodeItem;
};

export type DataExtractionStep = ProjectBuildStep & {
    categoryId: string;
    value: Record<string, any>;
};

export type ScreenshotStep = ProjectBuildStep & {
    categoryId?: string;
    value: Partial<{
        target: string;
        strategy: string;
        selector: string;
        selectorTargets: string;
        customSelectorTargets: string;
        ai: {
            instructions: string;
            confidenceLevel: number;
            reuseSelectors: boolean;
        };
    }>;
};

export type E2EVisualTestStep = ScreenshotStep & {
    strategy?: string;
};

export type URLChallengeStep = ProjectBuildStep & {
    jsCode: {
        id?: string;
        value: string;
    };
    categoryId: string;
};

export type GenericProjectBuildStep = ActionStep | ConditionStep | LoopStep | DataExtractionStep | ScreenshotStep | E2EVisualTestStep;

export type ProjectSolutionStepResultData<T = unknown> = {
    success: boolean;
    result?: T extends 'condition' ? ConditionResult : null | {};
    stepListResults?: ProjectBuildStepResult[];
};

export type ProjectBuildStepLabelling = {
    label: string;
    infoCodes?: InfoCode[];
};

export type ProjectBuildStepResult<T = unknown> = {
    data?: ProjectSolutionStepResultData<T>;
    id: string;
    infoCodes: InfoCode[],
    isAborted: boolean,
    isInactive?: boolean;
    isVoided?: boolean;
    labellingData: ProjectBuildStepLabelling;
    rootInfoCodes?: InfoCode[];
    step: ProjectBuildStep;
    stepType: undefined | string;
    ts: number;
};

export type LighthouseProjectBuildConfig = {
    categoryList: Array<LighthouseCategory>;
    lighthouseOutputFormats: LighthouseOutputFormat[];
};

export type ScreenVideosProjectBuildConfig = {
    stepDelay: number;
    stepList: Array<ProjectBuildStep>;
};

export type E2eVisualTestsProjectBuildConfig = {
    clustersSize: string;
    highlightColor: string;
    stepList: Array<ProjectBuildStep>;
};

export type URLChallengeProjectBuildConfig = {
    stepList: Array<ProjectBuildStep>;
    mode: typeof URL_CHALLENGE_MODES[keyof typeof URL_CHALLENGE_MODES];
};

export type DataExtractionProjectBuildConfig = {
    stepList: Array<ProjectBuildStep>;
};

export type ScreenshotProjectBuildConfig = {
    stepList: Array<ProjectBuildStep>;
};

export type SolutionBuildConfig = ScreenshotProjectBuildConfig | ScreenVideosProjectBuildConfig | E2eVisualTestsProjectBuildConfig | URLChallengeProjectBuildConfig | DataExtractionProjectBuildConfig;

export type ProjectDeleteData = {
    accountId: string,
    projectId: string,
    deletedOn: string
    deletedBy: string
};

export type ProjectState = typeof projectStates[keyof typeof projectStates];

export type ProjectNotificationConfigData = {
    email: {
        main: string;
    };
    webhooks: {
        auth: {
            active: boolean;
            username: string;
            password: string;
        };
        headers: {
            active: boolean;
            items: {
                name: string;
                value: string;
            }[];
        },
        url: string;
    };
};

export type ProjectConfigData = {
    linkOptions: {
        selection: LinkOptionsSelection;
        filtering: {
            includeSubdomains: boolean;
            includeExternalLinks: boolean;
            urlFilter: {
                urlMatch: Array<UrlMatchItem>;
                urlMatchAny: boolean;
            };
            includeInternalLinks: boolean;
            useResolvedURLBase: boolean;
        };
        normalizations: {
            stripWWW: boolean;
            stripHash: boolean;
            stripAuthentication: boolean;
            removeQueryParameters: boolean;
            removeTrailingSlash: boolean;
        };
    };
    projectId: string;
    projectName: string;
    requestOptions: {
        storage: RequestOptionsStorage;
        browser: {
            linkRequestTimeout: number;
            language: string;
            deactivateJS: boolean;
            waitUntil: {
                option: typeof REQUEST_WAIT_UNTIL[keyof typeof REQUEST_WAIT_UNTIL];
                jsCode: JSCodeItem;
            };
        };
        resources: RequestOptionsResources;
    };
    solution: {
        key: string;
        appName: string;
        config: LighthouseProjectBuildConfig | ScreenshotProjectBuildConfig | ScreenVideosProjectBuildConfig | E2eVisualTestsProjectBuildConfig | URLChallengeProjectBuildConfig | DataExtractionProjectBuildConfig;
    };
    subscriptionId: string;
    summary: {
        activateScheduler: null | string;
        requestData: object;
        schedulerEndDate: number;
        schedulerStartDate: number;
        notifications: {
            webhooks: boolean;
            email: boolean;
        };
        notificationsData: ProjectNotificationConfigData;
        schedulerData: {
            config: {
                cron: string;
                cronPrompt: string;
                interval: [number, number];
                frequency: string;
                frequencyQualifier: string;
            };
            type: string;
            schedulerEndDate: number;
            schedulerStartDate: number;
        };
        logLevels: Record<string, any>;
    };
    target: {
        checkType: string;
        urls: Array<UrlItem>;
        browser: {
            key: BrowserType;
        };
        executionStrategy: string;
        device: Device;
        lighthouseDevices: Array<string>;
    };
};
