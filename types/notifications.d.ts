import type {I18nInfo, InfoCode} from 'webautomate/global';
import type {accountNotificationTypes} from '../src/config/account';
import type {packages} from '../src/config/catalog/subscriptions';
import type {PROJECT_DEQUEUE_REASONS} from '../src/config/project';
import type {
    NOTIFICATION_TYPES,
    NOTIFICATION_METHODS,
} from '../src/config/notifications';
import type {ProjectNotificationConfigData} from 'project';

export type AccountNotification = {
    id: string;
    type: typeof accountNotificationTypes[keyof typeof accountNotificationTypes];
    ts: number;
    isNew: boolean;
    infoCodes?: InfoCode[];
    i18nInfos?: I18nInfo[];
};

export type ProjectBuildNotification = {
    type: typeof packages.notifications[keyof typeof packages.notifications];
    projectId: string,
    accountId: string,
    buildId: number,
};

export type ProjectSubscriptionProblemNotificationPayload = {
    id: string;
    reason: typeof PROJECT_DEQUEUE_REASONS[keyof typeof PROJECT_DEQUEUE_REASONS];
    projectId: string,
    accountId: string,
    ts: number;
};

export type ProjectNotificationPayload = {
    projectId: string;
    projectName: string;
    blacklistedDomainListInTarget?: string[];
};

export type EmailNotification<T = undefined> = {
    accountId: string;
    createdAt: number;
    data: {
        email: ProjectNotificationConfigData['email']['main'];
        language: string;
        payload: T;
    };
    method: typeof NOTIFICATION_METHODS[keyof typeof NOTIFICATION_METHODS];
    sendAt: number;
    subType?: string;
    type: typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];
};

export type WebhookNotification<T = undefined> = {
    accountId: string;
    createdAt: number;
    data: {
        config: {
            url: ProjectNotificationConfigData['webhooks']['url'];
            auth: ProjectNotificationConfigData['webhooks']['auth'];
            headers: ProjectNotificationConfigData['webhooks']['headers'];
        };
        payload: T;
    };
    method: typeof NOTIFICATION_METHODS[keyof typeof NOTIFICATION_METHODS];
    sendAt: number;
    subType?: string;
    type: typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];
};
