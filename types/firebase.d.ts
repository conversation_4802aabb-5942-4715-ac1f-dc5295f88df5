import type {conditionOps} from '../src/config/firebase';
import type {FileTransferData} from 'solutions';

export type FilesConfig = {
    id: string;
    hint?: string;
    uploadData: FileTransferData;
    lastEditedAt: number,
    history: Array<{
        uploadedBy: string,
        uploadedAt: number,
        name: string,
        extension: string,
        isImage: boolean,
        size: number,
        mimeType: string,
    }>,
};

export type FirebaseConditionConfig = {
    fieldPath: string;
    opStr: typeof conditionOps[number];
    value: any,
};
