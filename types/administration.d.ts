import type {Stripe} from 'stripe';
import type {BuildMode} from 'project';
import type {categories, plans} from '../src/config/catalog/subscriptions';
import type {
    BILLING_ACTION,
    BILLING_REQUEST_TYPES,
} from '../src/config/administration';
import type {BillingCycle} from 'webautomate/global';

export type StripeCheckoutSessionData = Stripe.Checkout.Session;

export type StripeCheckoutLineItem = Stripe.LineItem;

export type StripeChargeItem = Stripe.Charge;

export type InvoiceLineItem = Stripe.InvoiceItem;

export type Invoice = Stripe.Invoice;

export type SubscriptionItem = Stripe.SubscriptionItem;

export type SubscriptionCategory = keyof typeof categories;

export type SubscriptionPlan = keyof typeof plans;

export type ProjectSubscriptionData = {
    active: boolean;
    description: string;
    id: string;
    isLicenseBuildModeOk: boolean;
    isSolutionLicenceTypeOk: boolean;
    periodEnd: number;
    periodStart: number;
    projectSubscriptionData?: ProjectSubscriptionData;
    subscriptionCategory: SubscriptionCategory;
};

export type ProjectSubscriptionSummary = {
    matchingSubscriptionCategoryList: string[];
    projectBuildMode: string;
    projectNotifications: {
        email: boolean;
        webhooks: boolean;
    };
    projectSolution: string;
    requiredLicenseType: string;
};

export type SubscriptionPackage = {
    solutions: {
        items: {
            dataExtractions: {
                active: boolean;
                count: number;
            },
            e2eVisualTests: {
                active: boolean;
                count: number;
            },
            screenshots: {
                active: boolean;
                count: number;
            },
            screenVideos: {
                active: boolean;
                count: number;
            },
            lighthouse: {
                active: boolean;
                count: number;
            },
            urlChallenge: {
                active: boolean;
                count: number;
            },
        },
    },
    administration: {
        items: {
            httpRequests: {
                active: boolean;
                count: number;
            },
            buildSteps: {
                active: boolean;
                count: number;
            },
            aiRequests: {
                active: boolean;
                count: number;
            },
            builds: {
                active: boolean;
                count: number;
            },
            buildRuntimeMinutes: {
                active: boolean;
                count: number;
            },
        },
    },
    notifications: {
        email: {
            active: boolean,
        },
        webhooks: {
            active: boolean,
        },
    },
    limits: {
        parallelBuilds: number,
        storageInGB: number,
    },
    additional: {
        automationCredits: {
            active: boolean,
            quantity: number,
        },
    },
    priorityLevel: {
        appName: string;
        value: number;
    },
};

export type SubscriptionSummary = {
    hasEnteredOverage: boolean;
    buildModeLicence: BuildMode,
    initialContingent: SubscriptionPackage;
    interval: SubscriptionPlan,
    restContingent: SubscriptionPackage,
    subscriptionCategory: SubscriptionCategory,
};

export type Checkout = {
    billingCycle: BillingCycle;
    created: number;
    currency: string;
    email: string;
    id: string;
    invoiceUrl: string;
    locale: string;
    paymentStatus: string;
    price: number;
    status: string;
    context: typeof BILLING_ACTION[keyof typeof BILLING_ACTION];
};

export type SubscriptionRenewal = {
    count: number;
    isPastDue: boolean;
    billingReference: string;
};

export type CheckoutTransaction<IsSubscriptionItem = false> = {
    active: boolean;
    checkout: Checkout;
    checkoutId: string;
    currency: 'chf';
    data: IsSubscriptionItem extends false ? undefined : {
        cancelAtPeriodEnd: boolean;
        created: number;
        id: string;
        isCancelled: boolean;
        periodEnd: number;
        periodStart: number;
        renewals: SubscriptionRenewal & {
            history: Array<SubscriptionRenewal>;
        };
        subscriptionId: string;
        subscriptionSummary: SubscriptionSummary,
    };
    description: string;
    id: string;
    isSubscriptionItem: IsSubscriptionItem;
    monthlyReset: {
        count: number;
        nextResetTimestamp: number;
        previousResetTimestamp: number;
    };
    priceId: string;
    product: string;
    quantity: number;
    type: 'recurring' | 'one_time';
};

export type Purchase = {
    checkout: Checkout;
    checkoutId: string;
    currency: 'chf';
    description: string;
    id: string;
    priceId: string;
    product: string;
    quantity: number;
    isLifeTimeCollaboratorSeats: boolean;
    isLifeTimeAutomationCredits: boolean;
    type: 'recurring' | 'one_time';
};

export type Catalog<T = unknown> = {
    aiRequests: T;
    buildRuntimeMinutes: T;
    buildSteps: T;
    builds: T;
    dataExtractions: T;
    e2eVisualTests: T;
    httpRequests: T;
    jsEvaluation: T;
    lighthouse: T;
    screenVideos: T;
    screenshots: T;
    urlChallenge: T;
};

export type CartItem = {
    priceId: string;
    quantity: number;
    autoRenew?: boolean;
};

export type BillingRequestType = typeof BILLING_REQUEST_TYPES[keyof typeof BILLING_REQUEST_TYPES];

export type BillingRequestOverageData = {
    subscriptionId: string;
    resetCount: number;
};

export type BillingRequest<T = undefined> = {
    accountId: string;
    billingCycle: BillingCycle;
    created: number;
    data?: T;
    items: Array<{
        billableAction: string;
        quantity: number;
    }>;
    type: BillingRequestType;
};

export type BillingHistory = {
    invoiceId: string;
};
