import type {
    AccountData,
    BillingCycle,
    BlacklistedDomain,
    InfoCode,
} from './webautomate/global';
import type {ProjectBuildQueueItem} from './project';

export type UIState = Partial<{
    accountAudit: {
        next: number;
        active: boolean;
    };
    accountCreated: AccountData['created'];
    accountHolder: AccountData['accountHolder'];
    accountId: string;
    accountState: AccountData['state'];
    automationCredits: AccountData['lifeTimeAutomationCredits']['value'];
    automationCreditsExpiry: AccountData['lifeTimeAutomationCredits']['expiry'];
    billingCycle: BillingCycle;
    blacklistedDomains: AccountData['blacklistedDomains'];
    collaboratorSeats: AccountData['lifeTimeCollaboratorSeats']['value'];
    customerData: AccountData['customerData'];
    demoRequest: AccountData['demoRequest'];
    domainBuilds: AccountData['domainBuilds'];
    domainVerifications: AccountData['domainVerifications'];
    globallyBlacklistedDomainList: BlacklistedDomain[];
    isAlpha: boolean;
    organisation: AccountData['organisation'];
    preferredLanguage: AccountData['preferredLanguage'];
    projectBuildQueue: Array<ProjectBuildQueueItem>;
    timeZone: AccountData['timeZone'];
    timeoutHandling: AccountData['timeoutHandling'];
    ts: number;
    billingHistory: AccountData['administration']['billing']['history'];
}>;

export type SupportRequest = {
    data: {
        company: string;
        email: string;
        interests: string[];
        language: string;
        lastName: string;
        message: string;
        name: string;
        request: string;
        role: string;
        website: string;
    };
    id: string;
    state: string;
    language: string;
    ts: number;
    user: null | {
        accountId: string;
        email: string;
    };
};

export type UILog = {
    serverId: string,
    serverEnv: string,
    ts: number,
    infoCode: InfoCode,
    data: Record<string, unknown>,
};
