declare module 'glob';

declare module 'app-config' {
    import type {BuildTimeConfig, BuildTimeConfigForAPI, BuildTimeConfigForBuilder} from 'config';

    export function getConfig(): BuildTimeConfig;
    export function setWebAutomateGlobals(data: {
        appOrigin: string;
        timeZone: string;
    }): void;
    export function updateConfig(config: BuildTimeConfigForBuilder | BuildTimeConfigForAPI): void;
}

declare module '*.md' {
    const content: string;
    export default content;
}

declare module '*.json' {
    const content: string;
    export default content;
}
