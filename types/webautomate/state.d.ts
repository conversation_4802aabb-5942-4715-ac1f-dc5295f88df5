import type {
    LighthouseResult,
    PageBuild,
    PageBuildConfigProps,
    RequestData,
} from './request';
import type {Page} from '../playwright';
import type {LinkCheckDataReference, URLRequestState} from 'solutions';
import type {Vendor} from 'webautomate/global';
import type {BrowserContext, Frame, Request, Response} from 'playwright';

export type ServiceWorkerSession = {
    id: string;
    index: number;
    url: string;
    networkActivityDataList: RequestData[],
};

export type WebAutomateDocumentRequest = {
    id: string;
    index: number;
    frameId: string;
    parentDocumentRequestId: null | WebAutomateDocumentRequest['id'];
    pageBuildSessionId: null | string;
    networkActivityDataList: Array<RequestData>;
    requestData: null | RequestData,
    serviceWorkers: Array<ServiceWorkerSession>;
    urlRequestState: null | URLRequestState;
};

export type BrowserContextSession = {
    id: string;
    createdAt: number;
};

export type PageBuildSession = {
    id: string;
    index: number;
    browserContextSessionId: BrowserContextSession['id'];
    pageBuild: PageBuild,
    documentRequestList: Array<WebAutomateDocumentRequest>;
};

export type RequestItem = {
    url: string;
    method: string;
    resourceType: string;
};

export type ResponseItem = {
    url: string;
    method: string;
    status: number;
};

export type W103Global = {
    browserContexts: Map<BrowserContext, BrowserContextSession>;
    buildId: number;
    frames: Map<Frame, WebAutomateDocumentRequest['frameId']>;
    lighthouse: Record<string, LighthouseResult>;
    linkCheck: Record<string, LinkCheckDataReference>;
    miscPageData: {
        [pageId: string]: {
            dialogHandling: PageBuildConfigProps['dialogHandling'],
        },
    };
    pageSessions: Map<PageBuildSession['id'], PageBuildSession>;
    browserPages: Map<Page, PageBuildSession['id']>; // Used to map browserPages to their session IDs
    browserPagesReversed: Map<PageBuildSession['id'], Page>, // Used to map session IDs to their browserPages
    projectId: string;
    requests: Map<Request, RequestItem>;
    responses: Map<Response, ResponseItem>;
    sessionStart: number;
    vendor: Vendor;
};

export type AutomationState = {
    browserContextList: Array<BrowserContextSession>;
    buildId: W103Global['buildId'];
    currentUrl: string;
    pageBuildSessionList: Array<PageBuildSession>;
    projectId: W103Global['projectId'];
    sessionStart: W103Global['sessionStart'];
    vendor: W103Global['vendor'];
};
