import type {<PERSON><PERSON>, UrlConfigItem} from 'webautomate/global';
import type {
    BasicLinkCheckData,
    LinkCheckerSolutionDetails,
} from 'solutions';

export type RequestSizes = {
    requestBodySize: number;
    requestHeadersSize: number;
    responseBodySize: number;
    responseHeadersSize: number;
};

export type RequestTiming = {
    startTime: number;
    domainLookupStart: number;
    domainLookupEnd: number;
    connectStart: number;
    secureConnectionStart: number;
    connectEnd: number;
    requestStart: number;
    responseStart: number;
    responseEnd: number;
};

export type SecurityDetails = Partial<{
    issuer: string;
    protocol: string;
    subjectName: string;
    validFrom: string;
    validTo: string;
}>;

export type ServerAddr = {
    ipAddress: string;
    port: number;
};

export type ResponseData = {
    url: string;
    status: number;
    statusText: string;
    contentType: string;
    headers: Record<string, string>;
    body: string;
    json: Record<string, unknown> | null;
    ok: boolean;
    securityDetails: SecurityDetails | null;
    serverAddr: ServerAddr | null;
    redirectedFrom?: string;
};

export type ErrorResponseData = {
    url: string;
    status: number;
    errorText: string;
    redirectedFrom?: string;
};

export type RequestData = {
    reload: number;
    url: string;
    method: string;
    headers: Record<string, string>;
    postData: string;
    redirectedTo?: string;
    resourceType: string;
    sizes: RequestSizes;
    timing: RequestTiming;
    responseData: null | ResponseData | ErrorResponseData;
};

export type ConsoleLog = {
    documentRequestId?: string;
    ts: number;
    type: string;
    message: string;
    args: Array<unknown>;
    url?: string;
    lineNumber: number;
    columnNumber: number;
};

export type Exception = {
    documentRequestId?: string;
    url: string;
    ts: number;
    message: string;
};

export type Crash = {
    documentRequestId?: string;
    url: string;
    ts: number;
};

export type Open = {
    urlConfig: UrlConfigItem;
    actor: string;
    ts: number;
};

export type Close = {
    documentRequestId?: string;
    url: string;
    ts: number;
    actor: string;
};

export type Dialog = {
    documentRequestId?: string;
    url: string;
    ts: number;
    message: string;
    decision: 'accept' | 'dismiss';
    promptText?: string;
    defaultValue?: string;
};

export type PageWebSocket = {
    documentRequestId?: string;
    url: string;
    ts: number;
    events: {
        close: {
            ts: number;
        }[];
        framereceived: {
            ts: number;
        }[];
        framesent: {
            ts: number;
        }[];
        socketerror: {
            ts: number;
        }[];
    };
};

export type PageWorker = {
    documentRequestId?: string;
    url: string;
    ts: number;
    instantiated: {
        ts: number;
    };
    terminated: {
        ts: number;
    };
};

export type PageBuildEventBasedProps = {
    close: null | Close;
    workers: PageWorker[];
    consoleLogs: ConsoleLog[];
    crashes: Crash[];
    dialogs: Dialog[];
    exceptions: Exception[];
    webSockets: PageWebSocket[];
};

export type PageBuildRuntimeProps = {
    localStorage: Record<string, string>;
    sessionStorage: Record<string, string>;
    cookies: Cookie[];
};

export type PageBuildConfigProps = {
    dialogHandling: Pick<Dialog, 'decision' | 'promptText'>;
};

export type LighthouseResult = {
    lighthouseVersion: string;
    requestedUrl: string;
    finalUrl: string;
    fetchTime: string;
    gatherMode: string;
    userAgent: string;
    categories: Record<string, {
        title: string;
        score: number;
        auditRefs: Array<{
            id: string;
            weight: number;
        }>;
    }>;
};

export type PageBuild = {
    open: null | Open;
    responseSummary: null | BasicLinkCheckData | LinkCheckerSolutionDetails;
    events: PageBuildEventBasedProps;
    runtime: PageBuildRuntimeProps;
    config: PageBuildConfigProps;
};
