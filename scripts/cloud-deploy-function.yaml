steps:
    # Step 1: Install Firebase CLI and build the frontend app
    - name: 'node:16'
      entrypoint: 'bash'
      secretEnv: ["WEB_AUTOMATE_GITHUB_TOKEN"]
      args:
          - '-c'
          - |
              npm install --no-save --legacy-peer-deps
              npm run build:stage

    # Step 2: Deploy to Firebase Hosting
    - name: 'node:18'
      entrypoint: 'bash'
      args:
          - '-c'
          - |
              npm install -g firebase-tools
              firebase use stage
              firebase deploy --only hosting:stage

    # Step 3: Deploy Firebase Functions
    - name: 'node:18'
      entrypoint: 'bash'
      args:
          - '-c'
          - |
              npm install -g firebase-tools
              firebase use stage
              firebase deploy --only functions

options:
    logging: "CLOUD_LOGGING_ONLY"

availableSecrets:
    secretManager:
        - versionName: projects/$_INFRA_PROJECT_ID/secrets/webautomate-github-token/versions/latest
          env: "WEB_AUTOMATE_GITHUB_TOKEN"
