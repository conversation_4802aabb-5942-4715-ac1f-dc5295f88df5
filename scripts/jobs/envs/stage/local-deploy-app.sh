#!/bin/bash

TIMEOUT=1800s
TIMEOUT_WARNING=60s

set -e
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
  ulimit -v $MEMORY_LIMIT
fi

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/../../utils/set-params.sh"

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/../../utils/init-helper-functions.sh"

BUILD_FOLDER="${SCRIPT_DIR}/../../../../build/apps/${WEB_AUTOMATE_APP}"

echo "Running ${WEB_AUTOMATE_APP} for ${WEB_AUTOMATE_ENV} in folder ${BUILD_FOLDER}..."

WEB_AUTOMATE_ENV=stage
WEB_AUTOMATE_WEBSITE=https://stage.webautomate.app

required_envs=("WEB_AUTOMATE_APP" "WEB_AUTOMATE_ENV" "WEB_AUTOMATE_WEBSITE" "WEB_AUTOMATE_TIMEZONE" "WEB_AUTOMATE_SERVER_ID");

check_env_vars "${required_envs[@]}"

cd "${BUILD_FOLDER}" && \
  WEB_AUTOMATE_SERVER_PROCESS_ID=$WEB_AUTOMATE_SERVER_ID \
  WEB_AUTOMATE_APP=$WEB_AUTOMATE_APP \
  WEB_AUTOMATE_ENV=$WEB_AUTOMATE_ENV \
  WEB_AUTOMATE_WEBSITE=$WEB_AUTOMATE_WEBSITE \
  WEB_AUTOMATE_TIMEZONE=$WEB_AUTOMATE_TIMEZONE \
  WEB_AUTOMATE_SERVER_ID=$WEB_AUTOMATE_SERVER_ID \
  timeout -k $TIMEOUT_WARNING $TIMEOUT xvfb-run node $WEB_AUTOMATE_ENV.js
