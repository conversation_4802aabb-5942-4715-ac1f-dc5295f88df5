#!/bin/bash

# set -e

# Source nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Source your env variables (if you use a .env file or .bash_profile)
[ -f "$HOME/.w10.3rc" ] && source "$HOME/.w10.3rc"

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/utils/init-helper-functions.sh"

# $WEB_AUTOMATE_RUNTIME_PATH is an environment variable
# $WEB_AUTOMATE_SERVICES_PATH is an environment variable
# $MAX_INSTANCES is passed as an argument to the script
# $WEB_AUTOMATE_VERSION is passed as an argument to the script
# $WEB_AUTOMATE_ENV is passed as an argument to the script
# $WEB_AUTOMATE_APP is passed as an argument to the script

required_common_envs=("WEB_AUTOMATE_RUNTIME_PATH" "WEB_AUTOMATE_SERVICES_PATH" "WEB_AUTOMATE_VERSION" "WEB_AUTOMATE_ENV" "WEB_AUTOMATE_APP" "MAX_INSTANCES");

check_env_vars "${required_common_envs[@]}"

INSTANCE_DIR="${WEB_AUTOMATE_RUNTIME_PATH}/$WEB_AUTOMATE_VERSION/${WEB_AUTOMATE_APP}-${WEB_AUTOMATE_ENV}-instances"
LOG_FILE="${WEB_AUTOMATE_RUNTIME_PATH}/$WEB_AUTOMATE_VERSION/${WEB_AUTOMATE_APP}-${WEB_AUTOMATE_ENV}-limits-$(date +"%Y-%m-%d").log"

echo "LOG_FILE = $LOG_FILE"
echo "INSTANCE_DIR = $INSTANCE_DIR"

nvm use 18
# Ensure the instance directory exists
mkdir -p "$INSTANCE_DIR"

# Check current instance count by counting files in the directory
CURRENT_COUNT=$(ls -1qA "$INSTANCE_DIR" | wc -l)

if [ "$CURRENT_COUNT" -ge "$MAX_INSTANCES" ]; then
    echo "[$(date)] Maximum instance limit ($MAX_INSTANCES) reached. Exiting..." >> "$LOG_FILE"
    exit 1
fi

echo "[$(date)] Current instance count: $CURRENT_COUNT" >> "$LOG_FILE"
for i in $(seq 0 $(($MAX_INSTANCES - 1))); do
    INSTANCE_FILE="$INSTANCE_DIR/$i"

    if [ ! -f "$INSTANCE_FILE" ]; then
        # Create the instance file to mark this process
        touch "$INSTANCE_FILE"
        echo "[$(date)] Starting worker instance $$ (Created file: $INSTANCE_FILE)" >> "$LOG_FILE"
        break
    fi
done

# Script Start
export WEB_AUTOMATE_SERVER_PROCESS_ID=$$
echo "$WEB_AUTOMATE_SERVER_ID" >> "$INSTANCE_FILE"

WEB_AUTOMATE_APP=$WEB_AUTOMATE_APP bash "$WEB_AUTOMATE_SERVICES_PATH/$WEB_AUTOMATE_VERSION/scripts/jobs/envs/$WEB_AUTOMATE_ENV/local-deploy-app.sh"
# Script End

# Cleanup: Delete the instance file after the script is done
if [ -f "$INSTANCE_FILE" ]; then
    rm "$INSTANCE_FILE"
    echo "[$(date)] Completed worker instance $$ (Removed file: $INSTANCE_FILE)" >> "$LOG_FILE"
fi

