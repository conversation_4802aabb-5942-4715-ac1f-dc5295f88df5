#!/bin/bash

set -e
source ~/.bashrc

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/utils/init-helper-functions.sh"

APP_LIST=(
  account-auditor
  account-auditor-manager
  billing-manager
  billings
  notifications
  payments
  runner-manager
  runners
  subscriptions
)

required_common_envs=("WEB_AUTOMATE_SERVICES_PATH" "VERSION_MASTER");

check_env_vars "${required_common_envs[@]}"

if [ -z "$1" ]; then
  read -p "Please enter a tag version: " VERSION
else
  VERSION="$1"
fi

if [ -z "$VERSION" ]; then
  echo "Tag version is required."
  exit 1
fi

if [[ "$VERSION" == "$VERSION_MASTER" ]]; then
  echo "Version cannot be same as master version: $VERSION_MASTER"
  exit 1
fi

echo "WIll provision version: $VERSION"

if [ -z "$2" ]; then
  read -p "Please enter environment (dev, stage or prod): " WEB_AUTOMATE_ENV
else
  WEB_AUTOMATE_ENV="$2"
fi

if [[ "$WEB_AUTOMATE_ENV" != "dev" && "$WEB_AUTOMATE_ENV" != "stage" && "$WEB_AUTOMATE_ENV" != "prod" ]]; then
  echo "Invalid environment: $WEB_AUTOMATE_ENV"
  echo "Please specify one of: dev, stage, prod"
  exit 1
fi
echo "Using environment: $WEB_AUTOMATE_ENV"

if [ -z "$3" ]; then
  read -p "Please specify one of: ${APP_LIST[*]} or leave empty to build all: " APP
else
  APP="$3"
fi

if [ -z "$APP" ]; then
  echo "No app specified, will build all apps."
  APP="all"
else
  if [[ ! " ${APP_LIST[@]} " =~ " $APP " ]]; then
    echo "Invalid app: $APP"
    echo "Please specify one of: ${APP_LIST[*]} or leave empty to build all"
    exit 1
  fi
fi
echo "Building $APP app"

mkdir -p $WEB_AUTOMATE_SERVICES_PATH
TARGET_DIR=$WEB_AUTOMATE_SERVICES_PATH/$VERSION
SOURCE_DIR=$WEB_AUTOMATE_SERVICES_PATH/$VERSION_MASTER

if [ -d "$TARGET_DIR" ]; then
  echo "Destination $TARGET_DIR already exists. Skipping clone and checkout."
else
  if git ls-remote --tags **************:w7-3/webeagle-api.git | grep "refs/tags/$VERSION$"; then
    git clone --branch $VERSION --depth 1 **************:w7-3/webeagle-api.git "$TARGET_DIR"
  else
    echo "Tag $VERSION does not exist!"
    exit 1
  fi
  echo "Tag $VERSION has been checked out to $TARGET_DIR"
fi

TARGET_CONFIG_DIR="$TARGET_DIR/build/apps/config"
SOURCE_CONFIG_DIR="$SOURCE_DIR/build/apps/config"

nvm use 18

if [ ! -d "$TARGET_CONFIG_DIR/$WEB_AUTOMATE_ENV" ]; then # if config folder does not exist, then no environment has been created yet
  if [ ! -d "$SOURCE_CONFIG_DIR/$WEB_AUTOMATE_ENV" ]; then # if master config does not exist, then install and create it
    bash "$SOURCE_DIR/scripts/jobs/local-createConfig.sh"
    # wait 1 second
    sleep 1
  fi

  mkdir -p "$SOURCE_CONFIG_DIR"
  mkdir -p "$TARGET_CONFIG_DIR"
  # copy from master config to target config
  echo "Copying config folder from $SOURCE_CONFIG_DIR to $TARGET_CONFIG_DIR"
  cp -r "$SOURCE_CONFIG_DIR/" "$TARGET_CONFIG_DIR"
  echo "Config folder copied from $SOURCE_CONFIG_DIR to $TARGET_CONFIG_DIR"
else
  echo "Config folder already exists at $TARGET_CONFIG_DIR/$WEB_AUTOMATE_ENV, skipping copy and install."
fi

cd $TARGET_DIR
# if no node_modules folder, then install
if [ ! -d "node_modules" ]; then
  echo "Installing node modules..."
  yarn install
  yarn playwright install
  yarn playwright install-deps
else
  echo "Node modules already installed, skipping."
fi

# Build services

if [[ "$APP" == "all" ]]; then
  for APP in "${APP_LIST[@]}"; do
    echo "$APP getting built..."
    WEB_AUTOMATE_APP=$APP WEB_AUTOMATE_ENV=$WEB_AUTOMATE_ENV bash $TARGET_DIR/scripts/jobs/local-build-app.sh
  done
  echo "Successfully built version $VERSION for: ${APP_LIST[*]} and released to $TARGET_DIR"
else
  echo "$APP getting built..."
  WEB_AUTOMATE_APP=$APP WEB_AUTOMATE_ENV=$WEB_AUTOMATE_ENV bash $TARGET_DIR/scripts/jobs/local-build-app.sh
  echo "Successfully built version $VERSION for: $APP and released to $TARGET_DIR"
fi

# New service has been built and ready for release
