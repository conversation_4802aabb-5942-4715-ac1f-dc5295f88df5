#!/bin/bash

set -e

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/set-params.sh"

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/init-helper-functions.sh"

required_envs=("WEB_AUTOMATE_APP" "WEB_AUTOMATE_ENV");

check_env_vars "${required_envs[@]}"

# Declare parameters
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_FOLDER_ROOT="$SCRIPT_DIR/../../../build/apps"

# Declare parameters
BUILD_FOLDER="${BUILD_FOLDER_ROOT}/${WEB_AUTOMATE_APP}"
CONFIG_FOLDER="config/${WEB_AUTOMATE_ENV}"
COMMON_CONFIG="config/common.json"
ENV_CONFIG="${CONFIG_FOLDER}/env.json"
SERVICE_ACCOUNT_CERT="${CONFIG_FOLDER}/service-account.json"
ENV_FILE=${BUILD_FOLDER}/.env

mkdir -p ${BUILD_FOLDER_ROOT}/${CONFIG_FOLDER}
mkdir -p ${BUILD_FOLDER}

echo "Creating configs for ${WEB_AUTOMATE_APP} in ${WEB_AUTOMATE_ENV}..."
echo "Creating ${BUILD_FOLDER_ROOT}/${CONFIG_FOLDER} ${BUILD_FOLDER}/.env.${WEB_AUTOMATE_ENV}..."

cat <<EOF > ${ENV_FILE}
WEB_AUTOMATE_COMMON_CONFIG_PATH_RELATIVE=../${COMMON_CONFIG}
EOF

cat <<EOF > ${ENV_FILE}.${WEB_AUTOMATE_ENV}
WEB_AUTOMATE_ENV_CONFIG_PATH_RELATIVE=../${ENV_CONFIG}
WEB_AUTOMATE_SERVICE_ACCOUNT_CERT_PATH_RELATIVE=../${SERVICE_ACCOUNT_CERT}
EOF

# Indicate the script has finished
echo "Created configs: ${BUILD_FOLDER_ROOT}/${CONFIG_FOLDER} ${BUILD_FOLDER}/.env.${WEB_AUTOMATE_ENV}"
