#!/bin/bash

set -e

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")

source "$SCRIPT_DIR/set-params.sh"
source "$SCRIPT_DIR/init-helper-functions.sh"
source "$SCRIPT_DIR/create-envs.sh"

required_common_envs=("WEB_AUTOMATE_ENV" "WEB_AUTOMATE_APP" "WEB_AUTOMATE_GITHUB_TOKEN");

check_env_vars "${required_common_envs[@]}"
# Location of the script: /Users/<USER>/work/webeagle-api/scripts/jobs/utils
echo "Location of the script: $SCRIPT_DIR"
echo "Current directory: $(pwd)"
cd "${SCRIPT_DIR}/../../.."
echo "Current directory: $(pwd)"
echo "Building ${WEB_AUTOMATE_APP} for ${WEB_AUTOMATE_ENV}..."
  env \
    WEB_AUTOMATE_APP="${WEB_AUTOMATE_APP}" \
    yarn build:${WEB_AUTOMATE_ENV}

echo "Build completed successfully."
