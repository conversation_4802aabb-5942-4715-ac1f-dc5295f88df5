#!/bin/bash

set -e

# Check if /build folder exists
if [ ! -d "./build/apps" ]; then
  echo "Build folder does not exist"
  exit 1
fi

# Declare parameters
DOCKER_FILE="./Dockerfile"
DOCKER_APP_HOME="/usr/src/app/"

cat <<EOF > ${DOCKER_FILE}
# Use an appropriate base image
FROM --platform=linux/amd64 node:18

# Copy build folder to the container
COPY ./build/apps/. ${DOCKER_APP_HOME}

# Set the working directory
WORKDIR ${DOCKER_APP_HOME}api

# Install dependencies
RUN yarn --no-lockfile

EOF

echo "Successfully created Dockerfile."
