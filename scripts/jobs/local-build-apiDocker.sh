#!/bin/bash

set -e

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/local-build-api.sh"

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/local-createConfig.sh"

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "$SCRIPT_DIR/utils/create-api-Dockerfile.sh"

WEB_AUTOMATE_DOCKER_IMAGE_NAME="webautomate-api:latest"
docker build -t $WEB_AUTOMATE_DOCKER_IMAGE_NAME .

echo "API Docker image created successfully."

rm -rf ./Dockerfile
