#!/bin/bash

wh

# If WEB_AUTOMATE_ENV and WEB_AUTOMATE_APP are not set, throw an error
if [ -z "$WEB_AUTOMATE_ENV" ]; then
  echo "WEB_AUTOMATE_ENV is not set. Please set it to dev, stage or prod."
  exit 1
fi

if [ -z "$WEB_AUTOMATE_APP" ]; then
  echo "WEB_AUTOMATE_APP is not set. Please set it to the app you want to build."
  exit 1
fi

SCRIPT_DIR=$(dirname "${BASH_SOURCE[0]}")
cd "${SCRIPT_DIR}/../.."

cd ./build/apps/${WEB_AUTOMATE_APP}

yarn --no-lockfile

echo "API build completed successfully."

