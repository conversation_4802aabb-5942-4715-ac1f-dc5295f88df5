steps:
  - id: "Deploying to Cloud Run"
    name: "gcr.io/cloud-builders/gcloud"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        gcloud run deploy $_CLOUD_RUN_SERVICE_NAME --image $_ARTIFACT_REGION-docker.pkg.dev/$_INFRA_PROJECT_ID/$_AR_REPOSITORY/$_DOCKER_IMAGE_NAME --no-cpu-boost --region $_CLOUD_RUN_REGION --project $_PROJECT_ID --allow-unauthenticated

options:
  logging: "CLOUD_LOGGING_ONLY"
