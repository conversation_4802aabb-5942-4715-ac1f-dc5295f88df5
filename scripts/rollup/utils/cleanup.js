import {promises as fs} from 'fs';

export default (filePaths = [], folders = []) => {
    return {
        name: 'custom-cleanup-plugin',
        async closeBundle() {
            for (const filePath of filePaths) {
                try {
                    await fs.unlink(filePath);
                } catch (error) {
                    console.error(`Error deleting ${filePath}:`, error);
                }
            }

            for (const folder of folders) {
                try {
                    await fs.rm(folder, {recursive: true});
                } catch (error) {
                    console.error(`Error deleting ${folder}:`, error);
                }
            }
        },
    };
};
