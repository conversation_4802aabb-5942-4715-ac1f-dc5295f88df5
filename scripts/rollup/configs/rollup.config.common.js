/* eslint-disable max-len */

import path from 'path';
import fs from 'fs';
import process from 'process';
import json from '@rollup/plugin-json';
import typescript from '@rollup/plugin-typescript';
import nodeResolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import replace from '@rollup/plugin-replace';
import alias from '@rollup/plugin-alias';
import html from '@rollup/plugin-html';
import copy from '../utils/copy';

const rootPath = process.cwd();
const configDir = __dirname;
const app = process.env.WEB_AUTOMATE_APP;
const input = path.resolve(rootPath, `src/apps/${app}/index.ts`);

if (!fs.existsSync(input)) {
    throw new Error(`WEB_AUTOMATE_APP: ${process.env.WEB_AUTOMATE_APP} is not set or invalid.`);
}

const configFolder = path.resolve(rootPath, 'config');
const appPackageJSONPath = path.resolve(rootPath, `scripts/apps/${app}/package.json`);

if (!fs.existsSync(appPackageJSONPath)) {
    throw new Error(`Artefact package.json is missing for app: ${app}. Expected file: ${appPackageJSONPath}`);
}

const appPackageJSON = JSON.parse(fs.readFileSync(appPackageJSONPath, 'utf8'));

export default {
    input,
    output: {
        format: 'cjs',
    },
    onwarn: function(warning, warn) {
        if ([
            'INVALID_ANNOTATION',
            'EVAL',
            'CIRCULAR_DEPENDENCY',
        ].includes(warning.code)) {
            return;
        }

        if (warning.code === 'UNRESOLVED_IMPORT') {
            throw new Error(`Error: Missing dependency detected: ${JSON.stringify(warning, null, 4)}`);
        }

        warn(warning);
    },
    plugins: [
        json(),
        nodeResolve({
            preferBuiltins: true,
            customResolveOptions: {
                throwOnUnresolved: true,
            },
        }),
        commonjs(),
        html({
            include: '**/*.md',
        }),
        typescript({
            tsconfig: path.resolve(rootPath, 'tsconfig.json'),
        }),
        alias({
            entries: [
                {find: 'app-config', replacement: configFolder},
            ],
        }),
        replace({
            preventAssignment: true,
            values: {
                __dirname: JSON.stringify(configDir),
            },
        }),
        copy(appPackageJSONPath, path.resolve(rootPath, `build/apps/${app}/package.json`)),
    ],
    external: Object.keys(appPackageJSON.dependencies),
};
