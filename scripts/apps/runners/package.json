{"name": "runner.webautomate.app", "description": "This is the webautomate.app runner package", "license": "UNLICENSED", "version": "0.0.1", "private": true, "author": "c.francis", "engines": {"node": "=18"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "1.1.0", "@google-cloud/datastore": "8.7.0", "@google-cloud/logging": "^11.1.0", "@google-cloud/pubsub": "^4.5.0", "@sendgrid/mail": "8.1.3", "axios": "1.6.8", "deepmerge": "^4.3.1", "dotenv-extended": "2.9.0", "firebase": "9.19.1", "firebase-admin": "11.8.0", "fs-extra": "11.2.0", "lighthouse": "9.3.1", "looks-same": "9.0.0", "mime-types": "2.1.35", "openai": "^4.52.7", "playwright": "1.40.0", "playwright-lighthouse": "2.2.1", "playwright-video": "2.4.0"}}