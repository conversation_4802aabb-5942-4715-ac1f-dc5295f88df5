{"name": "account-auditor.webautomate.app", "description": "This is the account auditor for webautomate.app", "license": "UNLICENSED", "version": "0.0.1", "private": true, "author": "c.francis", "engines": {"node": "=18"}, "dependencies": {"@google-cloud/datastore": "8.7.0", "@google-cloud/logging": "^11.1.0", "@google-cloud/pubsub": "^4.5.0", "@sendgrid/mail": "8.1.3", "axios": "1.6.8", "deepmerge": "^4.3.1", "dotenv-extended": "2.9.0", "firebase": "9.19.1", "firebase-admin": "11.8.0", "fs-extra": "11.2.0", "mime-types": "2.1.35", "openai": "^4.52.7", "ramda": "0.30.0"}}