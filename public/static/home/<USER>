<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b)">
<rect x="0.5" width="40" height="40" rx="12" fill="#007ACC" fill-opacity="0.72"/>
<rect x="0.5" width="40" height="40" rx="12" fill="url(#paint0_linear)" fill-opacity="0.16"/>
</g>
<g filter="url(#filter1_d)">
<path d="M24.7705 22.3028H21.8197V32.8262H19.4016V22.3028H16.5V20.1738H24.7705V22.3028Z" fill="white"/>
<path d="M31.0656 29.5067C31.0656 28.9911 30.9399 28.6029 30.6885 28.3422C30.4426 28.0758 29.9918 27.8006 29.3361 27.5167C28.1393 27.0359 27.2787 26.4739 26.7541 25.8309C26.2295 25.182 25.9672 24.4173 25.9672 23.5368C25.9672 22.4708 26.3224 21.6163 27.0328 20.9733C27.7486 20.3244 28.6557 20 29.7541 20C30.4863 20 31.1393 20.1651 31.7131 20.4953C32.2869 20.8197 32.7268 21.2803 33.0328 21.877C33.3443 22.4737 33.5 23.1515 33.5 23.9104H31.0984C31.0984 23.3195 30.9781 22.8705 30.7377 22.5635C30.5027 22.2507 30.1612 22.0943 29.7131 22.0943C29.2923 22.0943 28.9645 22.2275 28.7295 22.494C28.4945 22.7547 28.377 23.1081 28.377 23.5541C28.377 23.9017 28.5082 24.2175 28.7705 24.5013C29.0328 24.7794 29.4973 25.0691 30.1639 25.3703C31.3279 25.8164 32.1721 26.3639 32.6967 27.0127C33.2268 27.6615 33.4918 28.4871 33.4918 29.4893C33.4918 30.59 33.1612 31.4503 32.5 32.0702C31.8388 32.6901 30.9399 33 29.8033 33C29.0328 33 28.3306 32.832 27.6967 32.496C27.0628 32.16 26.5656 31.6791 26.2049 31.0535C25.8497 30.4278 25.6721 29.6892 25.6721 28.8376H28.0902C28.0902 29.5675 28.224 30.0976 28.4918 30.4278C28.7596 30.758 29.1967 30.9231 29.8033 30.9231C30.6448 30.9231 31.0656 30.451 31.0656 29.5067Z" fill="white"/>
</g>
<defs>
<filter id="filter0_b" x="-9.5" y="-10" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur" result="shape"/>
</filter>
<filter id="filter1_d" x="12.5" y="18" width="25" height="21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="0.5" y1="0" x2="0.5" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.5"/>
<stop offset="1" stop-opacity="0.5"/>
</linearGradient>
</defs>
</svg>
