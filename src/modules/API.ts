import axios from 'axios';

type AxiosRequestConfig = any;
type AxiosPromise = any;

class API {
    static request(config: AxiosRequestConfig): AxiosPromise {
        return axios.request({
            ...config,
            withCredentials: true,
        });
    }

    static get(url: string, params: Record<string, unknown> = {}, config: AxiosRequestConfig): AxiosPromise {
        return API.request({
            ...config,
            method: 'get',
            url,
            params,
        });
    }

    static post(url: string, data: Record<string, unknown> = {}, config?: AxiosRequestConfig): AxiosPromise {
        return API.request({
            ...config,
            method: 'post',
            url,
            data,
        });
    }

    static put(url: string, data: Record<string, unknown> = {}, config?: AxiosRequestConfig): AxiosPromise {
        return API.request({
            ...config,
            method: 'put',
            url,
            data,
        });
    }

    static patch(url: string, data: Record<string, unknown> = {}, config?: AxiosRequestConfig): AxiosPromise {
        return API.request({
            ...config,
            method: 'patch',
            url,
            data,
        });
    }

    static delete(url: string, config?: AxiosRequestConfig): AxiosPromise {
        return API.request({
            ...config,
            method: 'delete',
            url,
        });
    }
}

export default API;
