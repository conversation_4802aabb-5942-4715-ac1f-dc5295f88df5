import mime from 'mime-types';
import * as admin from 'firebase-admin';
import {PubSub} from '@google-cloud/pubsub';
import fse from 'fs-extra';
import dissocPath from 'ramda/src/dissocPath';
import {
    isNonEmptyArray,
    isNonEmptyObject,
    isNonEmptyString,
    isPositiveInt,
} from '../libs/validators';
import {conditionOps} from '../config/firebase';
import {EVENTS, INFO_CODE_LEVELS} from '../config/infoCodes';
import {deleteFile} from '../libs/file';
import type {FirebaseConditionConfig} from 'firebase';
import type {FileTransferData} from 'solutions';
import type {InfoCode} from 'webautomate/global';

type Auth = any;
type Firestore = any;
type Storage = any;
type StorageFile = any;
type Query = any;
type PubSub = any;
type AppOptions = any;
type QueryDocumentSnapshot = any;

class GCPClient {
    FieldValue: typeof admin.firestore.FieldValue;
    db: Firestore;
    auth: Auth;
    pubsub: PubSub;
    storage: Storage;
    constructor({
        credential,
        storageBucket,
    }: AppOptions) {
        admin.initializeApp({
            credential: admin.credential.cert(credential),
            storageBucket,
        });

        const firestore = admin.firestore();
        firestore.settings({ignoreUndefinedProperties: true});

        this.FieldValue = admin.firestore.FieldValue;
        this.db = firestore;
        this.auth = admin.auth();
        this.storage = admin.storage();
        this.pubsub = new PubSub({
            projectId: credential.project_id,
            credentials: credential,
        });
    }

    private getQuery({
        query,
        condition,
        limit,
        order,
    }:{
        query: Query,
        condition?: FirebaseConditionConfig;
        limit?: number;
        order?: {
            fieldPath: string;
            directionStr: 'asc' | 'desc';
        };
    }): Query {
        if (isNonEmptyObject(condition)) {
            if (condition?.fieldPath && conditionOps.includes(condition?.opStr)) {
                query = query.where(condition.fieldPath, condition.opStr, condition.value);
            }
        }

        if (isPositiveInt(limit as number, true)) {
            query = query.limit(limit);
        }

        if (
            order?.directionStr
            && ['asc', 'desc'].includes(order?.directionStr)
            && isNonEmptyString(order?.fieldPath)) {
            query = query.orderBy(order!.fieldPath, order!.directionStr);
        }

        return query;
    }

    async getSubCollections({
        documentPath,
    }: {
        documentPath: string;
    }): Promise<[Error | null, string[]]> {
        try {
            const collections = await this.db.doc(documentPath).listCollections() as any[];

            return [null, collections.map((collection) => collection.id)];
        } catch (e) {
            return [e as Error, []];
        }
    }

    async getAllCollectionData<T>({
        collection,
        condition,
        filter,
        limit,
    }: {
        collection: string;
        condition?: FirebaseConditionConfig;
        filter?: (data: T) => boolean;
        limit?: number;
    }): Promise<[Error | null, Record<string, T>]> {
        const result: Record<string, T> = {};

        try {
            const query = this.getQuery({
                query: this.db.collection(collection),
                condition,
                limit,
            });
            const snapshots = await query.get();
            snapshots.forEach((doc: QueryDocumentSnapshot) => {
                const data = doc.data() as T;
                const isMatch = typeof filter === 'function' ? filter(data) : true;

                if (!isMatch) {
                    return;
                }

                result[doc.id] = data;
            });

            return [null, result];
        } catch (e) {
            return [e as Error, result];
        }
    }

    async getAllCollectionDataAsList<T>({
        collection,
        condition,
        filter,
        orders,
        limit,
    }: {
        collection: string;
        condition?: FirebaseConditionConfig;
        filter?: (data: T) => boolean;
        filterLimit?: number;
        orders?: Array<{
            fieldPath: string;
            directionStr: 'asc' | 'desc';
        }>;
        limit?: number;
    }): Promise<[Error | null, T[]]> {
        const [error, data] = await this.getAllCollectionData<T>({
            collection,
            condition,
            filter,
            limit,
        });

        if (error) {
            return [error, []];
        }

        const result = Object.values(data);

        if (isNonEmptyArray(orders)) {
            orders!.forEach((order) => {
                result.sort((a: any, b: any) => {
                    if (order.directionStr === 'asc') {
                        return a[order.fieldPath] - b[order.fieldPath];
                    }

                    return b[order.fieldPath] - a[order.fieldPath];
                });
            });
        }

        return [null, result];
    }

    async getCollectionDocumentData<T>({
        id,
        collection,
    }: {
        id: string | number;
        collection: string;
    }): Promise<[null | Error, null | T]> {
        if (!id) {
            return [new Error('Id is undefined'), null];
        }

        try {
            const snapshot = await this.db.collection(collection).doc(`${id}`).get();

            return [null, snapshot.data() as T];
        } catch (e) {
            return [e as Error, null];
        }
    }

    async getDocumentData<T>({
        path,
    }: {
        path: string;
    }): Promise<[null | Error, null | T]> {
        if (!isNonEmptyString(path)) {
            return [new Error('Path is invalid'), null];
        }

        try {
            const snapshot = await this.db.doc(path).get();

            return [null, snapshot.data() as T];
        } catch (e) {
            return [e as Error, null];
        }
    }

    async getDocumentExists({
        path,
    }: {
        path: string;
    }): Promise<[Error | null, boolean]> {
        if (!isNonEmptyString(path)) {
            return [new Error('Path is invalid'), false];
        }

        try {
            const snapshot = await this.db.doc(path).get();

            return [null, snapshot.exists];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async getCollectionDocumentSize({
        collection,
    }: {
        collection: string;
    }): Promise<[Error | null, number]> {
        if (!isNonEmptyString(collection)) {
            return [new Error('collection is invalid'), 0];
        }

        try {
            const snapshot = await this.db.collection(collection).get();

            return [null, snapshot.size];
        } catch (e) {
            return [e as Error, 0];
        }
    }

    async getStorageData({
        folder,
    }: {
        folder: string;
    }): Promise<[Error | null, {
        numberOfFiles: number;
        sizeInBytes: number;
    }]> {
        try {
            let sizeInBytes = 0;
            const [files] = await this.storage.bucket().getFiles({prefix: folder});
            files.forEach((file: StorageFile) => {
                sizeInBytes += file.metadata.size ? parseInt(file.metadata.size, 10) : 0;
            });

            return [null, {
                numberOfFiles: files.length,
                sizeInBytes,
            }];
        } catch (e) {
            return [e as Error, {
                numberOfFiles: 0,
                sizeInBytes: 0,
            }];
        }
    }

    async setCollectionDocumentData<T = Record<string, unknown>>({
        id,
        collection,
        data,
    }: {
        id: string;
        collection: string;
        data: T;
    }): Promise<[Error | null, boolean]> {
        try {
            await this.db.collection(collection).doc(`${id}`).set(data, {merge: true});

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async setDocumentData<T = Record<string, unknown>>({
        path,
        data,
    }: {
        path: string;
        data: T;
    }): Promise<[Error | null, boolean]> {
        if (!isNonEmptyString(path)) {
            return [new Error('Path is invalid'), false];
        }

        try {
            await this.db.doc(path).set(data, {merge: true});

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async insertCollectionData<T = Record<string, unknown>>({
        collection,
        data,
    }: {
        collection: string;
        data: T;
    }): Promise<[Error | null, string | null]> {
        try {
            const res = await this.db.collection(collection).add(data);

            return [null, res.id];
        } catch (e) {
            return [e as Error, null];
        }
    }

    async deleteCollectionData<T = Record<string, unknown>>({
        id,
        collection,
    }: {
        id: string;
        collection: string;
    }): Promise<[Error | null, boolean, T | null]> {
        if (!isNonEmptyString(id)) {
            return [new Error('Id is undefined'), false, null];
        }

        try {
            const docRef = this.db.collection(collection).doc(id);
            const doc = await docRef.get();
            await docRef.delete();

            return [null, true, doc.data()];
        } catch (e) {
            return [e as Error, false, null];
        }
    }

    async deleteCollectionDataField({
        id,
        collection,
        propPath,
    }: {
        id: string;
        collection: string;
        propPath: string[];
    }): Promise<[Error | null, boolean]> {
        if (!isNonEmptyString(id)) {
            return [new Error('Id is undefined'), false];
        }

        try {
            const docRef = this.db.collection(collection).doc(id);
            const doc = await docRef.get();

            await docRef.update(dissocPath(propPath, doc.data()));

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async recursiveDeleteDocuments({
        path,
    }: {
        path: string;
    }): Promise<[Error | null, boolean]> {
        if (!isNonEmptyString(path)) {
            return [new Error('Path is invalid'), false];
        }

        try {
            await this.db.recursiveDelete(this.db.doc(path));

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async recursiveDeleteStorageDirectory({
        path,
    }: {
        path: string;
    }): Promise<[Error | null, boolean]> {
        if (!isNonEmptyString(path)) {
            return [new Error('Path is invalid'), false];
        }

        try {
            await this.storage.bucket().deleteFiles({
                prefix: path,
            });

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async getLoginData({
        idToken,
    }: {
        idToken: string,
    }): Promise<[Error | null, null | {
        email: string,
        emailVerified: string,
        uid: string,
    }]> {
        if (!idToken) {
            return [new Error('Id token is undefined'), null];
        }

        try {
            const data = await this.auth.verifyIdToken(idToken);

            return [null, {
                email: data?.email,
                emailVerified: data?.email_verified,
                uid: data?.uid,
            }];
        } catch (error) {
            return [error as Error, null];
        }
    }

    async logout({
        loginData,
    }: {
        loginData: {
            uid: string,
        },
    }): Promise<[Error | null, boolean]> {
        if (!isNonEmptyString(loginData?.uid)) {
            return [new Error('Id token is undefined'), false];
        }

        try {
            await this.auth.revokeRefreshTokens(loginData?.uid);

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async getStorageFileExists({
        storageFilePath,
    }: {
        storageFilePath: string,
    }): Promise<[Error | null, boolean]> {
        try {
            const bucket = this.storage.bucket();
            const file = bucket.file(storageFilePath);
            const [exists] = await file.exists();

            return [null, exists];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async downloadFile({
        source,
        destination,
    }: {
        source: string,
        destination: string,
    }): Promise<FileTransferData> {
        try {
            const bucket = this.storage.bucket();
            const file = bucket.file(source);

            await fse.ensureFile(destination);
            await file.download({destination});

            return {
                success: true,
                infoCodes: [],
                data: {
                    source,
                    destination,
                    direction: 'download',
                },
            };
        } catch (e) {
            const error = e as Error;
            return {
                success: false,
                infoCodes: [{
                    code: EVENTS.GS_ERROR_DOWNLOAD,
                    level: INFO_CODE_LEVELS.ERROR as InfoCode['level'],
                    timestamp: Date.now(),
                    data: {
                        source,
                        destination,
                        error: {
                            name: error.name,
                            message: error.message,
                        },
                    },
                }],
            };
        }
    }

    async deleteStorageFile({
        storageFilePath,
    }: {
        storageFilePath: string,
    }): Promise<[Error | null, boolean]> {
        try {
            await this.storage.bucket().file(storageFilePath).delete();

            return [null, true];
        } catch (e) {
            return [e as Error, false];
        }
    }

    async createFile({
        storageFilePath,
        contentType,
        data,
    }: {
        storageFilePath: string,
        contentType: string,
        data: string | Buffer,
    }): Promise<FileTransferData> {
        try {
            await this.storage.bucket().file(storageFilePath).save(data, {
                contentType,
            });

            return {
                success: true,
                infoCodes: [],
                data: {
                    destination: storageFilePath,
                },
            };
        } catch (e) {
            const error = e as Error;

            return {
                success: false,
                infoCodes: [{
                    code: EVENTS.GS_ERROR_UPLOAD,
                    level: INFO_CODE_LEVELS.ERROR as InfoCode['level'],
                    timestamp: Date.now(),
                    data: {
                        storageFilePath,
                        contentType,
                        destination: storageFilePath,
                        error: {
                            name: error.name,
                            message: error.message,
                        },
                    },
                }],
            };
        }
    }

    async uploadFile({
        storageFilePath,
        filePath,
        deleteAfterUpload,
        contentType: contentTypeParam,
        makePublic,
        metadata,
    }: {
        storageFilePath: string,
        filePath: string,
        deleteAfterUpload: boolean,
        contentType?: string,
        makePublic?: boolean,
        metadata?: Record<string, object>,
    }): Promise<FileTransferData> {
        const contentType = contentTypeParam || mime.lookup(filePath);
        try {
            let publicUri = undefined;
            const bucket = this.storage.bucket();
            const infoCodes: InfoCode[] = [];
            const bucketUploadOptions: any = {
                destination: storageFilePath,
                metadata: isNonEmptyObject(metadata!) ? metadata : {},
            };

            if (contentType) {
                bucketUploadOptions.metadata.contentType = contentType;
            }

            if (makePublic) {
                bucketUploadOptions.acl = 'publicRead';
                bucketUploadOptions.gzip = true;
                bucketUploadOptions.metadata.cacheControl = 'public, max-age=31536000';

                publicUri = `/${bucket.name}/${storageFilePath}`;
            }

            await bucket.upload(filePath, bucketUploadOptions);

            if (makePublic) {
                await bucket.file(storageFilePath).makePublic();
            }

            if (deleteAfterUpload) {
                const deleted = deleteFile({filePath});

                if (!deleted) {
                    infoCodes.push({
                        code: EVENTS.ERROR_DELETING_FILE_AFTER_UPLOAD,
                        level: INFO_CODE_LEVELS.WARNING as InfoCode['level'],
                        timestamp: Date.now(),
                        data: {filePath, destination: storageFilePath},
                    });
                }
            }

            return {
                success: true,
                infoCodes,
                data: {
                    destination: storageFilePath,
                    publicUri,
                },
            };
        } catch (e) {
            const error = e as Error;

            return {
                success: false,
                infoCodes: [{
                    code: EVENTS.GS_ERROR_UPLOAD,
                    level: INFO_CODE_LEVELS.ERROR as InfoCode['level'],
                    timestamp: Date.now(),
                    data: {
                        format: contentType,
                        filePath,
                        destination: storageFilePath,
                        error: {
                            name: error.name,
                            message: error.message,
                        },
                    },
                }],
            };
        }
    }
}

export default GCPClient;
