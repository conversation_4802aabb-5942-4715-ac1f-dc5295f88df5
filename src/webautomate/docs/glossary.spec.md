# Glossary

**Actionability Checks**

When checking the actionability of elements, the following happens:

* Check if the element is visible.
* Ensure that the element is stable, meaning it is not currently being animated.
* Check that the element can receive events, i.e., ensure that the element is not covered by other elements and that the "disabled" attribute is not set.

**The Solution Configuration**

A Solution Configuration settings required to run a solution.  
