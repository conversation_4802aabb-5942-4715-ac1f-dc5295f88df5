# Our ethics

We implement ethical control in the following forms:

1. We respect robots.txt directives on accessible URLS, User-Agent and Crawl Delays.
2. To deactivate ethical control, users can verify their domain ownership. The ownership can be verified as follows:
    1. Collaborator: a collaborator must exist, whose email domain matches the domain of any given URL.
    2. HTML-Snippet: user is prompted to enter a certain snippet into a URL, which will be used for domain verification.
    3. Robots.txt: users will have to enter a certain directive in their robots.txt file, to grant Webautomate access to processing the page.

All the methods are due for revalidation after 12 months. The revalidation either happens manually, when running an automation on any given URL or can also be done manually by the user from the account page.

Domain verification has two states, namely:

* Verified: means the user has proved their domain ownership, which means that no ethical control will be applied in the next 12 months.
* Pending: means the user has applied for a proof of ownership, but the conditions are not yet met.

After successful domain verification, a record is kept for the user. Users have the option of revoking the ownership, which means the record gets deleted, and the URLs of the domain have to pass through ethical control and every other restriction applied to foreign domains.  
