In the webautomate.app the following roles exist:

1. Account Holder
2. Admin
3. Editor
4. Viewer

When creating an account, an organization has to be specified. The creator of an account has the eternal role of “Account Holder.” The account holder role cannot be changed and cannot be removed.

Other possible roles can be summarized as follows

1. Account holder: the account creator with the following rights
    1. Can do everything
        1. Create, edit and delete projects and builds
        2. Subscribe to packages
    2. Can invite, remove collaborators and edit collaborator roles
2. <PERSON><PERSON> (has same rights account holder)
3. Editor
    1. Create, edit and delete projects and builds
4. Viewer
    1. Can only view build results

After creating an account, the account holder can:

1. Invite users (if the appropriate collaborator package has been purchased)
2. Remove user
3. Delete collaborators

When a user accepts an invitation (creates an account), they become “collaborators” and are automatically part of the organization.

If a collaborator is deleted, on next login, they will be prompted to either delete their account, create their own organization or request from an admin to add them in an existing organisation.

Full table of rights:

| Action | Account Holder | Admin | Editor | Viewer |
| :---- | :---- | :---- | :---- | :---- |
| Manage Collaborators | Yes | Yes | No | No |
| Manage Templates | Yes | Yes | Yes | No |
| Manage Projects | Yes | Yes | Yes | No |
| View Projects and Builds | Yes | Yes | Yes | Yes |
| Manage Transactions and Invoices | Yes | Yes | No | No |
| Manage Domains | Yes | Yes | Yes | No |
