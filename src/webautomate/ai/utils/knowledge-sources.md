The automation process can provide the following additional files:

### The fullPage screenshot
A screenshot of the entire page taken at the time of evaluation
- Location: message content (`{type: 'image_url', ...}`)
- File name: `fullPageScreenshot`

### The viewport screenshot
A screenshot of the viewport taken at the time of evaluation
- Location: message content (`{type: 'image_url', ...}`)
- File name: `viewportScreenshot`

### The baseline screenshot
The baseline screenshot for comparison (included only for `VisualDiffEvaluation` requests)
- Location: message content (`{type: 'image_url', ...}`)
- File name: `baselineScreenshot`

### The current screenshot
The current screenshot for comparison (included only for `VisualDiffEvaluation` requests)
- Location: message content (`{type: 'image_url', ...}`)
- File name: `currentScreenshot`

### The automation state
Multiple serialized snapshots of the automated browser session recorded by our web automation framework. They include information about the project, build, session start time, vendor type (manual or auto), and a list of page build data.
The WebAutomate state object stores metadata for a web automation session. It details all properties and nested objects, including:

* browserContextList: Tracks all browser contexts with IDs and creation timestamps.
* buildId, projectId, sessionStart, vendor: Store project/session metadata.
* pageBuildSessionList: Contains data for each browser page, including:
    * init/open: Metadata and events for page opening, including URL, headers, authentication, and timestamps.
    * responseSummary: Details about the server response, such as status, content type, and timing.
    * events: Logs for page events like close, console logs, crashes, dialogs, exceptions, WebSockets, and workers, each with detailed subfields (timestamps, messages, actors, etc.).
    * runtime: Browser storage (cookies, local/session storage), and runtime config (e.g., dialog handling).
    * documentRequestDataList: Top-level document requests with request/response data, timing, headers, body, and security details.
    * networkActivityDataList: All sub-requests/resources loaded during the page session, with the same structure as document requests.

This documentation provides a comprehensive schema for tracking all aspects of a browser automation session, including navigation, network activity, storage, and error handling.

- Location: knowledge files (`file_ids: [the file_id]`)
- File name: `automationState`

### The HTML page snapshot
A snapshot of the HTML page at the exact time the request should be evaluated.
- Location: knowledge files (`file_ids: [the file_id]`)
- File name: `domSnapshot`

### The Lighthouse report for desktop
A snapshot of the page's Lighthouse report for desktop at the exact time the request should be evaluated.
- Location: knowledge files (`file_ids: [the file_id]`)
- File name: `lighthouseDesktop`

### The Lighthouse report for mobile
A snapshot of the page's Lighthouse report for mobile at the exact time the request should be evaluated.
- Location: knowledge files (`file_ids: [the file_id]`)
- File name: `lighthouseMobile`
