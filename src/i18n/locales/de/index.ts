export default {
    account: {
        label: '<PERSON><PERSON>',
        pageTitle: '<PERSON><PERSON>',
        holder: '<PERSON><PERSON>',
        created: '<PERSON>rstellt am',
        overview: {
            label: 'Übersicht',
        },
        settings: {
            label: 'Einstellungen',
            timeZone: {
                label: 'Zeitzone',
                filter: 'Fangen Sie an zu tippen...',
                autoDetect: 'Automatisch erkennen',
            },
            language: {
                label: 'Bevorzugte Sprache',
                description: 'Wählen Sie die bevorzugte Sprache für Ihr Konto, in der Sie Benachrichtigungen erhalten.',
            },
            builds: {
                timeoutHandling: {
                    label: 'Umgang bei fehlgeschlagenen Projektdurchläufen',
                    description: 'Was soll passieren, wenn der Build abläuft?',
                    options: {
                        noop: 'Nichts tun',
                        retry: 'Der Projektdurchlauf Wiederholen',
                    },
                },
            },
        },
        domains: {
            label: 'Domains',
            goto: 'Zur Domain Übersicht',
            blacklisted: {
                label: 'Blockierte Domains',
                description: 'Hier finden Sie eine Liste der Domains, die für Ihr Konto blockiert wurden. Überprüfen Sie die Details und den Status jeder Domain und verwalten Sie sie nach Bedarf.',
                info: 'Blockierte Domains werden von unserem AI-Browser nicht bearbeitet.',
                target: {
                    options: {
                        global: 'Global gesperrt',
                        account: 'Nur für Ihr Konto gesperrt',
                    },
                },
                contactUs: {
                    label: 'Bei Fragen zu diesen blockierten Domains kontaktieren Sie uns bitte über unseren Support-Kanal.',
                },
                items: {
                    count: 'Für Ihr Konto sind {{count}} Domains blockiert.',
                    count_one: 'Für Ihr Konto ist eine Domain blockiert.',
                },
            },
            projectBuild: {
                label: 'Projekt-Domains',
                description: 'Hier finden Sie eine Liste der Domains, die Sie bisher für Ihre Projekte verwendet haben. Überprüfen Sie die Details und den Status jeder Domain und verwalten Sie sie nach Bedarf.',
                domain: 'Domain',
                firstBuilt: 'Erster Projektdurchlauf',
                latestBuilt: 'Letzter Projektdurchlauf',
                gotoProject: 'Zum Projekt',
                items: {
                    count: 'Sie haben bisher mit {{count}} Domains gebaut.',
                    count_one: 'Sie haben bisher mit einer Domain gebaut.',
                },
            },
            verification: {
                label: 'Domain-Verifizierung',
                cta: 'Verifizieren Sie Ihre Domains, um die volle Kontrolle über Ihre Website-Automatisierungen zu erhalten.',
                description: 'Hier finden Sie eine Liste der Domains, die Sie zur Verifizierung eingereicht haben. Überprüfen Sie den Status und die Details der Verifizierung für jede Domain. Wenn Sie die Verifizierung aufheben möchten, klicken Sie auf "{{revoke}}".',
                info: 'Verifizieren Sie Ihre Domains, um die volle Kontrolle über Ihre Website-Automatisierungen zu erhalten. Wählen Sie eine Verifizierungsmethode aus und folgen Sie den Anweisungen, um Ihre Domain zu verifizieren. Wenn Sie Fragen haben, wenden Sie sich bitte an unser Support-Team.',
                domainToVerify: 'Zu verifizierende Domain',
                startVerification: 'Domain verifizieren',
                VERIFY: 'Jetzt Verifizieren',
                REVOKE: 'Verifizierung aufheben',
                state: {
                    VERIFIED: 'Verifiziert',
                    PENDING: 'Ausstehend',
                },
                methods: {
                    label: 'Methoden',
                    options: {
                        COLLABORATOR: {
                            label: 'Mitarbeiter',
                            description: 'Wir erkennen Ihr Domain-Eigentum, wenn Ihre E-Mail oder die E-Mail Ihres Mitarbeiters mit der Domain verknüpft ist.',
                            notices: [
                                'Die E-Mail-Adresse muss mit der Domain verknüpft sein.',
                                'Wenn der Mitarbeiter entfernt wird, wird die Domain-Verifizierung aufgehoben.',
                            ],
                        },
                        META_TAG: {
                            label: 'Meta-Tag',
                            description: 'Wir erkennen Ihr Domain-Eigentum, wenn Sie ein spezielles Meta-Tag in den Quellcode Ihrer Website einfügen.',
                            specifyUrl: 'Bitte geben Sie die URL an, auf der das Meta-Tag eingefügt wurde.',
                            copy: 'Meta-Tag kopieren',
                            notices: [
                                'Bitte stellen Sie sicher, dass die URL öffentlich zugänglich ist.',
                                'Der Meta-Tag muss vom Server ausgeliefert werden und nicht nachträglich per JavaScript hinzugefügt werden.',
                                'Die URL muss mit "http://" oder "https://" beginnen.',
                            ],
                        },
                        ROBOTS_TXT: {
                            label: 'robots.txt',
                            description: 'Wir erkennen Ihr Domain-Eigentum, wenn Sie {{app}} Zugriff auf Ihre Website in der robots.txt-Datei gewähren.',
                            copy: 'Text kopieren',
                        },
                    },
                },
                items: {
                    label: 'Eingereichte Domains zur Verifizierung',
                    count: 'Sie haben bisher {{count}} Domains zur Verifizierung eingereicht.',
                    count_one: 'Sie haben bisher eine Domain zur Verifizierung eingereicht.',
                    revoke: 'Verifizierung aufheben',
                    ts: 'Aktualisiert am',
                    verifiedBy: 'Beantragt von',
                    verifiedTill: 'Verifiziert bis',
                    notifications: {
                        VERIFIED: 'Ihre Domain wurde erfolgreich verifiziert.',
                        REVOKED: 'Die Verifizierung Ihrer Domain wurde aufgehoben.',
                        PENDING: 'Die Verifizierung Ihrer Domain ist ausstehend.',
                    },
                },
                ethicalControl: {
                    label: 'Ethische Kontrolle',
                    description: 'Wie auf unsere Nutzungsbedingungen und Datenschutzrichtlinie angegeben, respektieren wir die robots.txt-Dateien von Websites. Dies versteht sich als ethische Kontrolle. Durch die Einhaltung der robots.txt-Dateien können wir sicherstellen, dass wir Websiten nicht überlasten oder gegen die Wünsche des Website-Betreibers verstoßen. Wenn es sich hierbei um Ihr eigenes eigene Webseite handelt, können Sie die ethische Kontrolle deaktivieren, indem Sie Ihre Domain verifizieren.',
                },
            },
        },
        history: {
            label: 'Logs',
            projectEvents: 'Projekt-Logs ({{count}})',
        },
        states: {
            active: {
                label: 'Aktiv',
                shortLabel: 'Aktiv',
                description: 'Ihr Konto ist aktiv und einsatzbereit.',
            },
            new: {
                label: '{{star}}Willkommen bei {{app}}{{star}}',
                shortLabel: 'Neu',
                description: 'Es freut uns, dass Sie sich für {{app}} entschieden haben.',
                usps: [
                    'Schöpfen Sie das volle Potenzial Ihrer Website mit unserer intuitiven, no-code/low-code Automatisierungsplattform aus. Egal, ob Sie Links validieren, Google Lighthouse Audits durchführen oder URL-Challenges und Datenextraktion automatisieren möchten, {{app}} ermöglicht es Ihnen, all dies mit Leichtigkeit und Effizienz zu tun.',
                    'Unsere Plattform wurde für Unternehmen, Entwickler und digitale Enthusiasten gleichermaßen entwickelt und macht Web-Automatisierung für jeden zugänglich. Beginnen Sie mit dem Erstellen, Ausführen und Verwalten Ihrer Projekte mit nur wenigen Klicks und werden Sie Zeuge der Veränderung, wie Sie mit dem Web interagieren.',
                    'Tauchen Sie jetzt in ein nahtloses Erlebnis ein und sehen Sie, welchen Unterschied Automatisierung machen kann. Lassen Sie uns noch heute Ihre Webaufgaben erneuern, automatisieren und verbessern!',
                ],
                footer: 'Beginnen Sie Ihre Automatisierungsreise mit uns. {{rocket}}',
            },
            suspended: {
                label: 'Konto gesperrt',
                shortLabel: 'Gesperrt',
                description: 'Ihr Konto wurde gesperrt, sie werden automatisch ausgeloggt. Bitte kontaktieren Sie unseren Support.',
            },
            gracePeriod: {
                label: 'Konto im Gnadenzeitraum',
                shortLabel: 'Gnadenzeitraum',
                description: 'Ihr Konto befindet sich im Gnadenzeitraum, weil Sie weder Automation-Credits noch ein Abonnement haben. Füllen Sie Ihr Guthaben auf oder abonnieren Sie ein Paket {{numberOfDaysLeft}}, um eine Kontoschließung zu vermeiden.',
            },
            inactive: {
                label: 'Konto nicht mehr aktiv',
                shortLabel: 'Nicht aktiv',
                description: 'Ihr Konto ist nicht mehr aktiv. Bitte kontaktieren Sie den Support, um Ihr Konto wieder zu aktivieren.',
            },
        },
        customerPortal: {
            label: 'Kundenportal',
            ready: 'Eine Verbindung zum Kundenportal wurde hergestellt.',
            view: 'Klicken Sie hier, um Ihr Kundenportal zu besuchen',
        },
    },
    accountNotifications: {
        label: 'Meine Benachrichtigungen',
        pageTitle: 'Ihre Benachrichtigungen',
        noNotification: 'Sie haben keine Benachrichtigungen',
        description: 'Sie haben {{count}} neue Benachrichtigung',
        description_one: 'Sie haben 1 neue Benachrichtigung',
        new: 'Neue Benachrichtigungen',
        old: 'Gelesene Benachrichtigungen',
        viewAll: 'Alle anzeigen',
        states: {
            new: 'Neu',
            old: 'Gelesen',
        },
        actions: {
            label: 'Aktionen',
            options: {
                markAsRead: 'Als gelesen markieren',
                markAsUnread: 'Als ungelesen markieren',
                delete: 'Löschen',
            },
        },
        confirmDelete: {
            label: 'Benachrichtigung löschen',
            message: 'Sind Sie sicher, dass Sie die Benachrichtigung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
        },
        types: {
            blacklistedDomainsInProject: 'Eingeschränkte Domains',
            demoSubscription: 'Ihr Demo-Anfrage',
            custom: '{{app}} Benachrichtigung',
            buildNotification: 'Update zum Projektdurchlauf',
        },
    },
    actionability: {
        label: 'Hinsweis auf Aktionsfähigkeit',
        description: 'Die K.I führt zunächst eine Reihe von Prüfungen der Aktionsfähigkeit der Elemente durch, bevor wir Aktionen durchführen, um sicherzustellen, dass sich diese Aktionen wie erwartet verhalten.',
        alternative: 'Sie können dies umgehen, indem Sie auf {{javaScript}} umstellen.',
        failureNotice: 'Dieser Aktion wird fehlschlagen, wenn eine Prüfung der Aktionsfähigkeit fehlschlägt.',
        itemsLabel: 'Prüfungen der Aktionsfähigkeit',
        items: [
            'Element ist sichtbar',
            'Das Element ist stabil, d.h. nicht gerade animiert',
            'Element kann Ereignisse empfangen, d. h. es wird unter Anderem sicher gestellt, dass das Element nicht von anderen Elementen verdeckt ist und das "disabled" Attribut nicht gesetzt ist.',
        ],
    },
    actions: {
        label: 'Aktionen',
        selectOption: 'Aktion auswählen',
        newItem: 'Aktion hinzufügen',
        search: 'Aktionen filtern',
        noMatch: 'Keine Treffer!',
        unavailable: 'Diese Aktion steht nicht mehr zur Verfügung.',
        failStopLabel: 'Der Projektdurchlauf abbrechen, wenn die Aktion nicht erfolgreich ausgeführt werden kann.',
        options: {
            mouseInteraction: {
                label: 'Mouse Gesten',
                description: 'Einfachklicken, Doppelklicken, Mouse-Bewegen, usw. ausführen lassen.',
            },
            elementHover: {
                label: 'Mouse über Element bewegen',
                description: 'Mouse über ein DOM-Element bewegen lassen.',
            },
            elementClick: {
                label: 'Auf Element klicken',
                description: 'Lösen Sie einen Klick auf einem DOM-Element aus.',
            },
            keyboardPress: {
                label: 'Tasten drucken',
                description: 'Eine oder mehrere Tastenkombinationen auslösen.',
            },
            elementFocus: {
                label: 'Auf Element fokussieren',
                description: 'Fokussieren Sie auf ein DOM-Element.',
            },
            tap: {
                label: 'Antippen',
                description: 'Simulieren Sie eine Berührung des Bildschirms.',
            },
            elementTap: {
                label: 'Element antippen',
                description: 'Lassen Sie auf ein DOM-Element antippen.',
            },
            elementDragAndDrop: {
                label: 'Element Drag & Drop',
                description: 'Element ziehen und in einen anderen ablegen.',
                fields: {
                    sourceSelector: 'Element',
                    targetSelector: 'Ziel',
                    redundancy: 'Element und Ziel dürfen nicht gleich sein.',
                },
            },
            selectOption: {
                label: 'Option selektieren',
                selectOption: 'Select-Query',
                description: 'Selektieren Sie eine oder mehrere Optionen.',
                type: {
                    label: 'Wie sollen die Optionen identifiziert werden?',
                    fields: {
                        label: {
                            label: 'Elemente mit folgenden "label" selektieren',
                            fields: {
                                items: 'Label',
                                newItem: 'Neue "label"-Werte hier eingeben',
                            },
                            notification: {
                                empty: 'Bitte mindestens ein "label" eingeben.',
                            },
                        },
                        value: {
                            label: 'Elemente mit folgenden "value" selektieren',
                            fields: {
                                items: 'Value',
                                newItem: 'Neue "value"-Werte hier eingeben',
                            },
                            notification: {
                                empty: 'Bitte mindestens ein "value" eingeben.',
                            },
                        },
                    },
                },
            },
            fillInputOrTextarea: {
                label: 'Text ausfüllen',
                description: 'Auf ein Textbereich oder Eingabefeld fokussieren und Text eingeben lassen.',
                text: 'Texteingabe',
                inputType: {
                    label: 'Eingabestyp',
                    single: 'Einzeilige Eingabe',
                    multi: 'Mehrzeilige Eingabe',
                },
                notification: {
                    text: 'Text eingeben.',
                },
            },
            scrollIntoView: {
                label: 'Auf Element scrollen',
                description: 'Auf die Position des Elementes scrollen, bis diese in den sichtbaren Bereich des Browsers ist.',
            },
            uploadFile: {
                label: 'Datei hochladen',
                description: 'Wählen Sie eine Datei, die hochgeladen werden sollte.',
                multipleInfo: 'Beachten Sie bitte: Wenn das Formular das Hochladen mehrerer Dateien nicht unterstützt, wird nur die erste ausgewählte Datei hochgeladen. Wenn das Zielelement kein Formular ist, wird ein Fehler gemeldet.',
                selectFile: 'Datei auswählen',
            },
            waitForSelector: {
                label: 'Auf Element warten.',
                description: 'Warten, bis dieses Element im HTML-DOM vorhanden ist.',
                config: {
                    timeout: 'Timeout',
                },
                notification: {
                    timeout: 'Wenn das Element nicht innerhalb von {{timeout}} gefunden wird, wird die Aktion automatisch mit einem Timeout-Fehler beendet.',
                },
            },
            booleanFunction: {
                label: 'Auf Bedingung warten.',
                description: 'Warten, bis diese JavaScript-Bedingung erfüllt ist.',
                config: {
                    timeout: 'Timeout',
                },
                notification: {
                    timeout: 'Der Code wird alle {{interval}} ausgeführt, bis der Rückgabewert wahr ist, andernfalls wird automatisch nach {{timeout}} false zurückgegeben.',
                    timeoutInfo: 'Nach {{timeout}} wird die Aktion abgebrochen und ein Timeout-Fehler wird protokolliert.',
                },
            },
            request: {
                label: 'Warten auf Netwerkanfrage.',
                description: 'Warten, bis eine Netwerkanfrage an eine URL gestellt wird.',
                config: {
                    timeout: 'Timeout',
                    method: {
                        label: 'Methode',
                    },
                },
                notification: {
                    timeout: 'Nach {{timeout}} wird die Aktion automatisch mit Timeout-Fehler beendet.',
                },
            },
            response: {
                label: 'Warten auf Netwerkantwort.',
                description: 'Warten, bis eine Netzwerkanfrage auf eine URL beantwortet wird.',
                config: {
                    timeout: 'Timeout',
                    method: {
                        label: 'Methode',
                    },
                },
                notification: {
                    timeout: 'Nach {{timeout}} wird die Aktion automatisch mit Timeout-Fehler beendet.',
                },
            },
            timeout: {
                label: 'Ruhezeit.',
                description: 'Warten bis die angegebene Zeit verstrichen ist.',
                config: {
                    timeout: 'Ruhezeit',
                },
            },
            reloadPage: {
                label: 'Seite reloaden.',
                description: 'Die aktuelle Seite neuladen.',
            },
            goBack: {
                label: 'Zurückgehen',
                description: 'Im Browserverlauf einen Step zurückgehen',
            },
            goForward: {
                label: 'Vorwärtsgehen',
                description: 'Im Browserverlauf einen Step vorwärts gehen',
            },
            goTo: {
                label: 'Auf URL navigieren',
                description: 'Rufen Sie die folgende URL auf',
                config: {
                    url: 'URL eingeben',
                },
            },
            scroll: {
                label: 'Scrollen',
                description: 'Zur DOM-Position scrollen',
                config: {
                    xy: {
                        xAxis: 'X-Achse',
                        yAxis: 'Y-Achse',
                    },
                    behaviour: {
                        label: 'Scroll-Verhalten',
                    },
                },
            },
            js: {
                label: 'JavaScript ausführen',
                description: 'JavaScript im Browser-Console ausführen.',
            },
            clearAllCookies: {
                label: 'Alle Cookies löschen',
                description: 'Alle Cookies im Browser-Kontext löschen.',
            },
            clearSessionStorage: {
                label: 'SessionStorage leeren',
                description: 'SessionStorage Speicher löschen.',
            },
            clearLocalStorage: {
                label: 'LocalStorage leeren',
                description: 'LocalStorage Speicher löschen.',
            },
            dialogHandling: {
                label: 'Dialog Entscheidung',
                description: 'Hier bestimmen, ob Dialoge, die vom Browser angezeigt werden, werden automatisch akzeptiert oder abgelehnt.',
                config: {
                    title: 'Wenn ein Dialog angezeigt wird',
                    decision: {
                        accept: {
                            label: 'Automatisch Akzeptieren',
                        },
                        dismiss: {
                            label: 'Automatisch Ablehnen',
                        },
                    },
                    text: {
                        label: 'Dialog beantworten mit:',
                    },
                },
            },
        },
        categories: {
            userInteractionEvents: {
                label: 'Benutzerinteraktionsereignisse',
            },
            formElementManipulation: {
                label: 'Formular- & Elementmanipulation',
            },
            navigationAndBrowsing: {
                label: 'Navigation & Browsen',
            },
            dataProcessingAndRequests: {
                label: 'Datenverarbeitung & Anfragen',
            },
            advancedBrowserApiOperations: {
                label: 'Fortgeschrittene Browser-API-Operationen',
            },
            settings: {
                label: 'Einstellungen',
            },
        },
        groups: {
            mouse: {
                label: 'Mouse Interaktionen',
                target: {
                    fields: {
                        button: 'Maus klicken',
                        move: 'Maus bewegen',
                        wheel: 'Mausrad tätigen',
                    },
                },
                buttonType: {
                    label: 'Welche Maustaste?',
                    fields: {
                        left: 'Linke Maustaste',
                        middle: 'Mittlere  Maustaste',
                        right: 'Rechte Maustaste',
                    },
                },
                buttonAction: {
                    label: 'Was tun?',
                    fields: {
                        click: 'Anklicken',
                        down: 'Drucken und Halten',
                        up: 'Loslassen',
                    },
                },
                config: {
                    delay: {
                        label: 'Wartezeit vor Loslassen der Taste beim klicken',
                    },
                    count: {
                        label: 'Wie oft klicken?',
                        fields: {
                            1: 'Einfach',
                            2: 'Doppel',
                        },
                    },
                    xy: {
                        xAxis: 'X-Achse ({{min}} / {{max}}px)',
                        yAxis: 'Y-Achse ({{min}} / {{max}}px)',
                        description: 'Mauszeiger auf folgende XY-Position bewegen.',
                    },
                    dXdY: {
                        xAxis: 'X-Achse ({{min}} / {{max}}px)',
                        yAxis: 'Y-Achse ({{min}} / {{max}}px)',
                        description: 'Mauszeiger entlang der X- oder Y-Achse verschieben.',
                    },
                },
            },
            keyboard: {
                label: 'Tastatur Interaktionen',
                action: 'Aktion',
                actions: {
                    press: 'Drucken',
                    down: 'Drucken und Halten',
                    up: 'Los lassen',
                    type: 'Zeichen mit {{delay}}s Verz. eingeben',
                },
                customCharacter: {
                    label: 'Zeichenfolgen eingeben',
                    delay: 'Verzögerung nach Eingabe einzelner Zeichen.',
                    title: 'Senden Sie eigene Zeichenfolgen auch mit (Sonderzeichen, wie z.B: ä, Ö, é, ô, usw.) zum Browser.',
                },
            },
            touchScreen: {
                label: 'Touchscreen Interaktionen',
                config: {
                    xy: {
                        xAxis: 'X-Achse ({{min}} / {{max}}px)',
                        yAxis: 'Y-Achse ({{min}} / {{max}}px)',
                        description: 'Mauszeiger auf folgende XY-Position bewegen.',
                    },
                },
            },
            element: {
                label: 'Element Interaktionen',
            },
            waitFor: {
                label: 'Auf Ereignisse warten',
            },
            custom: {
                label: 'Technisch',
            },
        },
        notifications: {
            mandatoryField: 'Bitte geben Sie einen Gruppen-Namen ein',
            duplicateGroupName: 'Es gibt bereits eine Bedingungsgruppe mit dem Namen',
            deleteSuccess: 'Die Aktion "{{label}}" wurde gelöscht',
            editSuccess: 'Die Aktion wurde erfolgreich bearbeitet',
            saveSuccess: 'Die Aktion wurde erfolgreich gespeichert',
            copySuccess: 'Die Aktion wurde erfolgreich kopiert',
        },
        labels: {
            failed: {
                label: 'Fehlgeschlagen',
                description: 'Ein Fehler ist aufgetreten, während die Aktion ausgeführt wurde',
            },
            voided: {
                label: 'Ungültig',
                description: 'Die Aktion wurde ungültig gemacht',
            },
            aborted: {
                label: 'Abgebrochen',
                description: 'Die Aktion wurde abgebrochen',
            },
            executed: {
                label: 'Ausgeführt',
                description: 'Die Aktion wurde erfolgreich ausgeführt',
            },
        },
        label_one: 'Aktion',
    },
    activatePackage: 'Paket aktivieren',
    activated: 'Aktiviert',
    active: 'aktiv',
    add: 'Hinzufügen',
    addTestLink: 'Test URL hinzufügen',
    allLabel: '- und -',
    anOptionIsMandatory: 'Sie müssen mindestens eine Auswahl treffen.',
    and: 'UND',
    anyLabel: '- oder -',
    assistant: {
        label: 'AI-Assistent',
        ask: 'Fragen Sie unser AI-Assistent',
        description: 'Willkommen bei {{app}}, Ihrem AI-Assistenten für einfache und effiziente Webautomatisierung. Stellen Sie mir Ihre Fragen und entdecken Sie die Möglichkeiten!',
        input: {
            placeholder: 'Fragen Sie mich etwas...',
            send: 'Senden',
        },
        roles: {
            user: 'Benutzer',
            assistant: 'AI-Assistent',
        },
        prompts: {
            exploreFeatures: {
                label: 'Funktionen mit einem AI-Agenten entdecken',
                description: 'Hallo! Ich bin neu bei {{app}} – kannst du mir zeigen, was ich hier machen kann? Ich möchte die Hauptfunktionen kennenlernen und verstehen, wie ich Browser-Aufgaben automatisieren kann.',
            },
            exploreSpecificFeature: {
                label: 'AI-Agent zu {{solution}} befragen',
                description: 'Hallo! Ich möchte mehr über die Funktion {{solution}} bei {{app}} erfahren. Kannst du mir erklären, wofür sie gedacht ist, wie ich sie einrichten kann, und mir einige typische Anwendungsbeispiele geben?',
            },
            learnAboutFeature: {
                label: 'Mehr erfahren...',
                description: 'Ich möchte verstehen, was "{{problem}}" ist und wie {{app}} mir dabei helfen kann.',
            },
        },
    },
    attentionRequired: {
        label: 'Aufmerksamkeit erforderlich',
    },
    averageValues: 'Durchschnittswerte',
    backTo: 'Zurück zu {{label}}',
    cancel: 'Abbrechen',
    change: 'Ändern',
    checkout: {
        metrics: {
            label: 'Verbrauch',
            title: 'Einheit',
            usage: {
                label: 'Nutzung',
            },
            allowance: {
                label: 'Kontingent',
            },
            remaining: {
                label: 'Verbleibend',
            },
            overage: {
                label: 'Überzug',
            },
            items: {
                dataExtractions: {
                    label: 'Data Extractions',
                },
                e2eVisualTests: {
                    label: 'Visual-Tests',
                },
                screenshots: {
                    label: 'Screenshot-Documentation',
                },
                screenVideos: {
                    label: 'Video-Documentation',
                },
                lighthouse: {
                    label: 'Google-Lighthouse-Audits',
                },
                urlChallenge: {
                    label: 'URL-Challenge',
                },
                aiRequests: {
                    label: 'AI-Requests',
                },
                buildSteps: {
                    label: 'Projektdurchlauf-Steps',
                },
                builds: {
                    label: 'Projektdurchläufe',
                },
                httpRequests: {
                    label: 'HTTP-Requests',
                },
                buildRuntimeMinutes: {
                    label: 'Verarbeitungszeit (Minuten)',
                },
                storageInGB: {
                    label: 'Speicher in GB',
                },
                parallelBuilds: {
                    label: 'Parallele Builds',
                },
            },
        },
    },
    clearAll: 'Alle löschen',
    clipboard: {
        copyTitle: '{{contentType}} kopieren',
        copySuccess: '{{contentType}} wurde erfolgreich in die Zwischenablage kopiert.',
    },
    close: 'Schliessen',
    codeField: {
        js: {
            counterLabel: '({{rest}})',
            items: [
                'Diese Funktion wird von der AI-Agenten ausgeführt.',
                'Es stehen Ihnen die volle die Browser-API zur Verfügung.',
                'Sie können hier auch auf eigene Bibliothek-Funktionen zugreifen.',
            ],
            validation: {
                label: 'Bitte zuerst JavaScript-Code validieren lassen, bevor Sie speichern kann.',
                isInvalid: 'Der Javascript-Codes ist möglicherweise Fehlerhaft.',
                isValid: 'Der Javascript-Code ist valid.',
                requestError: 'Der Javascript-Code kann zur Zeit nicht validiert werden. Bitte versuchen Sie es später erneuert.',
            },
            notifications: {
                empty: 'Bitte geben Sie JavaScript-Code ein.',
                incomplete: 'Bitte geben Sie einen Wert zurück "return ...;".',
            },
        },
        css: {
            counterLabel: '({{rest}})',
            placeholder: '/**\n  CSS-Code hier eingeben\n*/',
        },
        html: {
            counterLabel: '({{rest}})',
            placeholder: '<!-- HTML Code hier eingeben -->',
        },
        json: {
            counterLabel: '({{rest}})',
            placeholder: '/**\n JSON Code hier eingeben \n*/',
            validation: {
                isInvalid: 'Bitte geben Sie einen gültigen JSON ein.',
            },
        },
        aiText: {
            counterLabel: '({{rest}})',
            placeholder: 'AI-Text hier eingeben',
            validation: {
                impossibleActions: 'Unerlaubte Aktionen',
                characterLimit: 'Der Text darf maximal {{characterLimit}} Zeichen lang sein.',
                yes: 'Die Eingabe ist gültig und kann gespeichert werden.',
                no: 'Die Eingabe ist ungültig, bitte korrigieren Sie die Eingabe.',
                unknown: 'Die Eingabe ist nicht klar und spezifisch genug, aber akzeptabel, Sie können die Eingabe speichern oder präzisieren.',
                error: 'Der AI-Test kann zur Zeit nicht validiert werden. Bitte versuchen Sie es später erneuert.',
                suggestions: 'Gültige Vorschläge sind (anklicken zum Übernehmen):',
            },
            input: 'Anfrage an AI-Agent',
            output: 'Antwort vom AI-Agenten',
        },
        vanilla: {
            counterLabel: '({{rest}})',
            placeholder: '/**\n Geben Sie hier Ihren etwas ein \n*/',
        },
    },
    collaborators: {
        label: 'Mitarbeiter',
        pageTitle: 'Alle Mitarbeiter',
        invitedBy: 'Eingeladen durch',
        created: 'Konto eingerichtet am',
        role: {
            label: 'Rolle',
            options: {
                ACCOUNT_HOLDER: 'Kontoinhaber',
                ADMIN: 'Admin',
                EDITOR: 'Editor',
                VIEWER: 'Viewer',
            },
        },
        you: 'Sie',
        confirmation: {
            title: 'E-Mail-Bestätigung erforderlich',
            description: 'Ihr Konto ist noch nicht aktiviert. Es besteht eine ausstehende Bestätigung der E-Mail-Adresse {{email}}. Sollten Sie keine E-Mail in Ihrem Posteingang vorfinden, schauen Sie bitte auch in Ihrem Spam-Ordner nach.',
            doneDescription: 'Sie werden von dem Konto abgemeldet und zur Anmeldeseite weitergeleitet.',
            resend: 'E-Mail erneut senden',
            wasResent: 'E-Mail wurde versendet',
            notification: {
                success: 'Ihr Konto wurde erfolgreich aktiviert.',
            },
        },
        orphanUser: {
            label: 'Benutzer ohne Organisation',
            description: 'Für Ihr Benutzer: {{email}} wurde keine Organisation gefunden. Wenn Sie einer Organisation angehört haben, wurden Sie möglicherweise als Kollaborateur entfernt. Bitte kontaktieren Sie den Administrator.',
            options: {
                label: 'Wie soll es weitergehen?',
                contactAdmin: {
                    label: 'Ausloggen und Ihren {{app}}-Administrator kontaktieren',
                    description: 'Sie werden ausgeloggt, um den Administrator zu kontaktieren. Sie können sich wieder einloggen, nachdem Sie von Ihrem Administrator wieder aufgenommen wurden.',
                },
                createAccount: {
                    label: 'Eigene {{app}}-Organisation erstellen',
                    description: 'Eine neue Organisation wird für Sie erstellt, und Sie sind der Kontoinhaber. Sie können andere Benutzer einladen, Ihrer Organisation beizutreten.',
                },
                deleteUser: {
                    label: 'Benutzer löschen',
                    description: 'Wenn Sie ihren Benutzer löschen, werden alle Ihre Daten gelöscht und können nicht wiederhergestellt werden. Bitte seien Sie sicher, bevor Sie fortfahren.',
                    sensitiveOperation: {
                        title: 'Hinweis',
                        description: 'Dieser Vorgang ist sensibel und erfordert eine kürzliche Authentifizierung. Melden Sie sich erneut an, bevor Sie diesen Vorgang erneut versuchen.',
                    },
                },
            },
        },
        restrictedContent: {
            label: 'Beschränkter Inhalt',
            description: 'Sie haben keinen Zugang auf diesen Inhalt.',
            descriptionArchived: 'Sie haben keinen Zugang auf diesen Inhalt, weil Ihr Konto schon deaktiviert wurde. Bitte kontaktieren Sie ihren Admin: {{accountHolder}}.',
        },
        state: {
            label: 'Konto Status',
            options: {
                VERIFIED: {
                    label: 'Verifiziert',
                    description: 'Das Konto ist verifiziert.',
                },
                PENDING: {
                    label: 'Ausstehen',
                    description: 'Die Einladung ist noch ausstehen',
                },
                UNVERIFIED: {
                    label: 'Nicht verifiziert',
                    description: 'Die Einladung wurde schon angenommen, aber das Konto wurde noch nicht verifiziert.',
                },
            },
        },
        invitation: {
            label: 'Mitarbeiter einladen',
            invite: {
                label: 'Mitarbeiter einladen',
                notifications: {
                    success: 'E-Mail Einladung an {{email}} wurde erfolgreich versandt.',
                    failure: 'E-Mail Einladung an {{email}} konnte nicht gesendet werden.',
                },
            },
            reinvite: {
                label: 'Einladung erneut senden',
                notifications: {
                    success: 'E-Mail Einladung an {{email}} wurde erneut erfolgreich versandt.',
                    failure: 'E-Mail Einladung an {{email}} konnte nicht gesendet werden.',
                },
            },
            delete: {
                label: 'Einladung löschen',
                notifications: {
                    success: 'E-Mail Einladung an {{email}} wurde gelöscht.',
                    failure: 'E-Mail Einladung an {{email}} konnte nicht gelöscht werden.',
                },
            },
            edit: {
                label: 'Mitarbeiter bearbeiten',
                labelInvitation: 'Einladung bearbeiten',
                notifications: {
                    success: 'Erfolgreich gespeichert.',
                    failure: 'Es ist ein Fehler aufgetreten.',
                },
            },
        },
        registrationByServiceProvidedAccount: {
            label: 'Willkommen bei {{app}}!',
            accountRequired: 'Sie sind nur noch einen Step davon entfernt, Ihre Registrierung abzuschließen. Um fortzufahren, müssen Sie eine Organisation erstellen oder zu einer bestehenden eingeladen werden',
        },
    },
    components: {
        label: 'Komponenten',
        pageTitle: 'Komponenten',
    },
    conditions: {
        label: 'Bedingung',
        newItem: 'Bedingung',
        selectOption: 'Bitte wählen Sie die Bedingungsart',
        unavailable: 'Diese Bedingung steht nicht mehr zur Verfügung.',
        rule: 'Mindestens eine Bedingung innerhalb der Bedingungsgruppen muss erfüllt sein.',
        preview: 'Konfiguration anzeigen',
        failStopLabel: 'Der Projektdurchlauf abbrechen, wenn die Bedingung nicht erfolgreich ausgeführt werden kann.',
        options: {
            cookie: {
                label: 'Cookie',
                namePlaceholder: 'Cookie',
                description: 'Prüfen, ob ein bestimmtes Cookie gesetzt ist.',
                existenceCheckInfo: 'Lassen Sie Felder leer, wenn diese beliebig sein können',
                fields: {
                    key: {
                        label: 'Cookie-Name',
                        invalid: 'Bitte geben Sie einen gültigen Cookie-Name ein.',
                        mandatory: 'Der Cookie-Name muss angegeben werden.',
                    },
                    value: {
                        label: 'Cookie-Wert',
                    },
                },
            },
            elementPresent: {
                label: 'HTML Element',
                namePlaceholder: 'HTML Element',
                description: 'Prüfen, ob HTML Element im DOM vorhanden ist.',
                config: {
                    timeout: {
                        label: 'Timeout',
                        description: 'Nach {{timeout}} wird die Aktion automatisch mit Timeout-Fehler beendet.',
                    },
                },
            },
            js: {
                label: 'JavaScript-Bedingung',
                namePlaceholder: 'JavaScript',
                description: 'Prüfen, ob eine JavaScript-Bedingung erfüllt ist.',
            },
            responseHeader: {
                label: 'Response im Header',
                namePlaceholder: 'Response-Header',
                description: 'Prüfen, ob ein bestimmtes Header im Response vorhanden ist.',
                existenceCheckInfo: 'Lassen Sie den Header-Wert leer, wenn der beliebig sein kann',
                fields: {
                    key: {
                        label: 'Header-Name',
                        invalid: 'Bitte geben Sie einen gültigen Header-Name ein.',
                    },
                    value: {
                        label: 'Header-Wert',
                    },
                },
            },
            ai: {
                label: 'AI-geprüfte Bedingung',
                namePlaceholder: 'AI-gestützte Bedingung',
                title: 'AI-geprüfte Bedingung',
                inputNotice: 'Formulieren Sie eine einfache Aussage oder Frage, die vom AI-Agenten mit WAHR oder FALSCH bewertet werden kann.',
                description: 'Verwenden Sie diese Option, damit der AI-Agent überprüft, ob etwas auf der Seite zutrifft. Die Bedingung sollte so formuliert sein, dass die AI sie eindeutig mit Ja oder Nein beantworten kann.',
                helperText: 'Dieser Bedingungstyp wird mithilfe von künstlicher Intelligenz ausgewertet. Sie können eine geschlossene Frage oder eine klare Aussage wie "Das Banner zeigt "Willkommen" oder "Es gibt 3 Buttons auf der Seite" verwenden. Der AI-Agent analysiert die Seite und antwortet mit WAHR, wenn die Bedingung erfüllt ist, oder mit FALSCH, wenn nicht.',
                confidenceLevel: {
                    label: 'Vertrauenswert',
                    description: 'Legt fest, wie sicher sich der AI-Agent sein muss, bevor sie die Bedingung als erfüllt (WAHR) markiert.',
                    interpretation: {
                        low: 'Ein Wert von {{value}} ist sehr niedrig. Der AI-Agent könnte die Bedingung als erfüllt ansehen, obwohl sie es nicht ist (falsche Treffer).',
                        medium: 'Ein Wert von {{value}} ist ausgewogen und eignet sich gut für die meisten Bedingungen.',
                        high: 'Ein Wert von {{value}} ist sehr streng. der AI-Agent erkennt die Bedingung nur als erfüllt, wenn sie sehr sicher ist – dabei können gültige Fälle übersehen werden (falsche Auslassungen).',
                    },
                },
                disclaimer: {
                    label: 'Hinweis',
                    description: 'Diese Bedingung wird vom AI-Agenten anhand trainierter Modelle bewertet. Sie ist flexibel und einfach, aber nicht immer 100% genau. Für exakte Logik verwenden Sie bitte einen anderen Bedingungstyp.',
                },
                expectationExamples: [
                    'Es gibt 3 Katzenbilder auf der Seite',
                    'Die Seite ist auf Igbo geschrieben',
                    'Das Banner zeigt den Text "Willkommen"',
                    'Ist der Call-to-Action-Button sichtbar?',
                    'Enthält der Footer Kontaktinformationen?',
                ],
            },
        },
        notifications: {
            deleteSuccess: 'Die Bedingung "{{label}}" wurde gelöscht',
            editSuccess: 'Die Bedingung "{{label}}" wurde erfolgreich bearbeitet',
            saveSuccess: 'Die Bedingung "{{label}}" wurde erfolgreich gespeichert',
            copySuccess: 'Die Bedingung "{{label}}" wurde erfolgreich kopiert',
        },
        label_one: 'Bedingung',
        labels: {
            failed: {
                label: 'Gescheitert',
                description: 'Nicht ausgeführt werden',
            },
            voided: {
                label: 'Ungültig',
                description: 'Nicht ausgeführt',
            },
            aborted: {
                label: 'Abgebrochen',
                description: 'Abgebrochen',
            },
            passed: {
                label: 'Bestanden',
                description: 'Die Bedingung wurde erfüllt',
            },
            notPassed: {
                label: 'Nicht bestanden',
                description: 'Die Bedingung wurde nicht erfüllt',
            },
        },
    },
    configuration: {
        showPreview: 'Konfiguration anzeigen',
        expand: 'Erweiterte Konfiguration',
        collapse: 'Eingeklappte Konfiguration',
    },
    contactUs: {
        label: 'Kontaktieren Sie uns',
        shortLabel: 'Kontakt',
        pageTitle: 'Kontaktieren Sie uns',
        whereToFindUs: 'Wo Sie uns finden?',
        otherInquiries: 'Andere Anfragen',
        notifications: {
            success: 'Die Anfrage wurde erfolgreich versandt. Wir werden sie so schnell wie möglich bearbeiten und uns umgehend bei Ihnen melden.',
        },
        contactForm: {
            label: 'Kontaktformular',
            roleOptions: {
                ceo: 'Geschäftsführer - CEO',
                cto: 'Technikvorstand - CTO',
                itManager: 'IT-Leiter',
                projectManager: 'Projektmanager',
                productManager: 'Produktmanager',
                dataAnalyst: 'Datenanalyst',
                systemAdministrator: 'Systemadministrator',
                softwareDeveloper: 'Softwareentwickler',
                securityOfficer: 'Sicherheitsbeauftragter',
                qualityManager: 'Qualitätsmanager',
                applicationDeveloper: 'Anwendungsentwickler',
                frontendDeveloper: 'Frontend-Entwickler',
                backendDeveloper: 'Backend-Entwickler',
                fullStackDeveloper: 'Full-Stack-Entwickler',
                devopsEngineer: 'DevOps-Ingenieur',
                uxDesigner: 'UX/UI-Designer',
                seoSpecialist: 'SEO-Spezialist',
                digitalMarketingManager: 'Digital Marketing Manager',
                salesManager: 'Vertriebsleiter',
                salesRepresentative: 'Vertriebsmitarbeiter',
                marketingManager: 'Marketing Manager',
                other: 'Andere',
            },
            interestOptions: {
                websitePerformance: 'Website Performance',
                userExperience: 'User Experience',
                seoOptimization: 'SEO Optimization',
                dataAnalysis: 'Data Analysis',
                qualityAssurance: 'Quality Assurance',
                compliance: 'Compliance',
                competitiveAdvantage: 'Competitive Advantage',
                costEfficiency: 'Cost Efficiency',
                brandReputation: 'Brand Reputation',
                digitalMarketing: 'Digital Marketing',
                mobileOptimization: 'Mobile Optimization',
                security: 'Security',
            },
            requestOptions: {
                demo: 'Demo beantragen',
                support: 'Supportanfrage',
                account: 'Kontoanfrage',
                violations: 'Missbrauch melden',
                other: 'Sonstige Anfrage',
            },
        },
    },
    contingent: 'Kontingent',
    continue: 'Weiter',
    cookieConsent: {
        message: 'Um das Erlebnis auf unserer Webseite zu verbessern, analysiert {{app}} den Datenverkehr und personalisiert Inhalte und Werbung durch den Einsatz von Cookies und vergleichbaren Technologien.',
        moreInfo: 'Mehr Infos in unserer Datenschutzerklärung',
    },
    cookies: {
        comparing: {
            showExtendedFields: 'Erweiterte Konfiguration anzeigen',
            hideExtendedFields: 'Erweiterte Konfiguration ausblenden',
        },
        fields: {
            name: 'Name',
            value: 'Wert',
            domain: 'Domain',
            path: 'Path',
            maxAge: 'Max-Age',
            httpOnly: 'HTTP Only',
            secure: 'Secure',
            sameSite: 'Samesite',
        },
    },
    copied: 'Erfolgreich kopiert!',
    copy: 'Kopieren',
    copySuffix: 'Kopie',
    copyright: '{{year}} {{app}} - Alle Rechte vorbehalten.',
    dangerZone: {
        label: 'Gefahrenzone',
    },
    dashboard: {
        label: 'Dashboard',
        pageTitle: 'Dashboard',
        overview: 'Dashboard',
    },
    dateFrom: 'Ab {{startDate}}',
    dateFromTo: '{{startDate}} - {{endDate}}',
    days: 'Tagen',
    delete: 'Löschen',
    demo: {
        label: 'Vorführung',
        closeCTA: 'Demo beenden',
        application: {
            label: 'Meine Demo-Anfrage',
            created: 'Beantragt am',
            approved: 'Freigegeben am',
            activated: 'Aktiviert am',
            completed: 'Erledigt am',
            applicant: 'Eingetragen von',
            state: {
                label: 'Status',
                options: {
                    PENDING: 'Ausstehend',
                    IN_PROGRESS: 'In Bearbeitung',
                    COMPLETED: 'Erledigt',
                    REJECTED: 'Abgelehnt',
                    EXPIRED: 'Abgelaufen',
                    APPROVED: 'Freigegeben',
                    ACTIVATED: 'Aktiviert',
                },
            },
            cta: 'Demo beantragen',
            activate: 'Demo Starten',
            clearNotification: 'Benachrichtigung löschen',
        },
    },
    deselectAll: 'Alle abwählen',
    details: 'Details',
    developers: {
        label: 'Entwickler',
    },
    device: {
        mobile: 'Mobile',
        desktop: 'Desktop',
    },
    directives: {
        notifications: {
            accountExists: 'Es existiert bereits ein User mit der E-Mail-Adresse {{email}}.',
            automationCreditsDepleted: 'Sie haben nicht genügend Guthaben, um diese Automatisierung auszuführen. Bitte füllen Sie Ihr Guthaben auf, um fortzufahren.',
            automationCreditsToppedUpForDemo: 'Ihr Konto wurde automatisch mit {{automationCredits}} Automation-Credits aufgeladen. Wir wünschen Ihnen viel Erfolg!',
            cannotUpdateProjectWhenBuilding: 'Das Projekt "{{projectName}}" wird gerade ausgeführt und kann während dessen nicht aktualisiert werden. Bitte versuchen Sie es später.',
            cannotUpdateEmailAfterAcceptance: 'Die E-Mail-Adresse {{email}} kann nicht mehr geändert werden, weil die Einladung schon angenommen wurde.',
            authCannotBeDeleted: 'Fehler beim Löschen des Benutzers {{email}}, bitte kontaktieren Sie den Support.',
            collaboratorPackageRequired: 'Um weitere Mitarbeiter hinzuzufügen, ist das Paket "{{package}}" erforderlich.',
            customerPortalError: 'Der Kundenportal-Service ist derzeit nicht erreichbar, bitte versuchen Sie es später erneut.',
            fileNotFound: 'Die Datei "{{fileName}}" wurde nicht gefunden.',
            fileTooBig: 'Die Datei "{{fileName}}" darf {{maxFileSize}} nicht überschreiten.',
            faqPromptMissing: 'Bitte Stellen Sie eine Frage.',
            genericError: 'Es ist ein unerwarteter Fehler aufgetreten.',
            genericErrorWithRetry: 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneuert.',
            invalidDemoCartItem: 'Das Demo-Paket ist nicht mehr verfügbar, bitte kontaktieren Sie unseren Support.',
            loginRequired: 'Sie müssen sich einloggen, um diese Aktion auszuführen.',
            noRights: 'Sie haben keine Berechtigung, um die Aktion auszuführen. Bitte kontaktieren Sie ihrem Admin {{email}}.',
            noInternet: 'Bitte überprüfen Sie Ihre Internet Verbindung',
            oneDemoRequestPolicy: 'Wir haben eine Ein-Abonnement-Demo-Richtlinie. Wenn Sie bereits eine Demo angefordert haben, überprüfen Sie bitte Ihre E-Mail auf die nächsten Steps.',
            projectBuildCanceled: 'Projektdurchlauf "{{projectName}}" wurde abgebrochen.',
            projectBuildCannotBeDeletedByAge: 'Projektdurchlauf "#{{buildId}}" kann nicht gelöscht werden, weil es ein neuerer Projektdurchlauf gibt.',
            projectBuildCannotBeDequeued: 'Projektdurchlauf "{{projectName}}" kann nicht zurückgestellt werden, weil es bereits begonnen hat.',
            projectBuildDequeued: 'Projektdurchlauf wurde aus der Warteschlange entfernt.',
            projectBuildNotFound: 'Das Projektdurchlauf wurde nicht gefunden.',
            projectBuildQueued: 'Der Projektbuild wurde in die Warteschlange gestellt.',
            projectNotFound: 'Das Projekt wurde nicht gefunden.',
            redirectTo: 'Sie wurden umgeleitet.',
            showError: 'Es ist ein unerwarteter Fehler aufgetreten.',
            subscription: 'Sie benötigen eine aktive Abonnement mit folgenden Paketen:',
            subscriptionChange: 'Sie benötigen eine aktive Abonnement mit folgenden Paketen:',
            subscriptionSelection: 'Bitte wählen Sie das Abonnement aus, mit dem das Projekt ausgeführt werden soll.',
            unverified: 'Es besteht eine ausstehende Bestätigung der E-Mail-Adresse {{email}}.',
            userIsArchived: 'Ihr Konto wurde archiviert, bitte kontaktieren Sie ihren Admin: {{accountHolder}}.',
            DEVICE_CONFIG_INVALID: 'Das Gerät ist nicht korrekt konfiguriert.',
            LIGHT_HOUSE_CATEGORY_LIST_MISSING: 'Die Kategorie-Liste für Lighthouse-Tests fehlt.',
            STEPS_MISSING: 'Die Steps fehlen.',
            URLS_MISSING: 'Die URLs fehlen.',
            INVALID_CONFIG: 'Die Konfiguration ist ungültig.',
            PROJECT_NAME_MISSING: 'Der Projektname fehlt.',
            PROJECT_NAME_TOO_LONG: 'Der Projektname ist zu lang.',
            PROJECT_NAME_ALREADY_EXISTS: 'Der Projektname "{{projectName}}" wurde bereits verwendet.',
            accountCardsExpiredOrMissing: 'Ihre Kreditkarte ist abgelaufen oder fehlt. Bitte aktualisieren Sie Ihre Zahlungsinformationen.',
            oneGenericRequestPolicy: 'Sie haben bereits eine ausstehende {{requestOption}}-Anfrage. Bitte überprüfen Sie Ihre E-Mail auf die nächsten Steps.',
            aiServiceNotAvailable: 'Der AI-Agent ist momentan nicht verfügbar. Bitte versuchen Sie es später erneut.',
            cannotUpdateProjectWhenAuditing: 'Das Projekt "{{projectName}}" wird gerade überprüft und kann währenddessen nicht aktualisiert werden. Bitte versuchen Sie es später.',
            PROJECT_SOLUTION_MISSING_MISSING: 'Bitte wähle eine Lösungsart, um fortzufahren.',
            PROJECT_CONFIG_OK: 'Das Projekt ist ordnungsgemäß konfiguriert.',
        },
    },
    duration: 'Dauer',
    edit: 'Bearbeiten',
    emails: {
        sendProjectPausedEmail: {
            subject: 'Wichtige Mitteilung zur Projektunterbrechung',
            message: {
                value: 'wir möchten Sie informieren, dass Ihr Projekt: {{projectName}} aufgrund der Erkennung gesperrter Domain(s) vorübergehend angehalten wurde. Dies ist eine Sicherheitsmaßnahme, um die Integrität unserer Dienste zu wahren.',
            },
            domainList: {
                label: 'Betreffende Domain(s)',
            },
            instructions: {
                value: 'Bitte überprüfen Sie die URL(s) Ihres Projekts und entfernen oder ersetzen Sie alle URLs, die auf der Sperrliste stehen. Sobald dies erfolgt ist, können Sie Ihr Projekt wieder aktivieren.',
            },
            apology: {
                value: 'Wir entschuldigen uns für etwaige Unannehmlichkeiten und stehen Ihnen gerne zur Verfügung, um Ihnen bei der Lösung dieses Problems zu helfen.',
            },
        },
        domainAddedToBlacklistEmail: {
            subject: 'Update zu einer Domain',
            body: [
                {
                    value: 'wir möchten Sie darüber informieren, dass die Domain: {{domain}}, auf unsere Sperrliste gesetzt wurde. Aufgrund dieser Maßnahme können zukünftige Projekte, die auf dieser Domain aufbauen, nicht mehr ausgeführt werden.',
                },
                {
                    value: 'Diese Entscheidung steht im Einklang mit unseren Allgemeinen Geschäftsbedingungen (AGB), die besagen, dass wir das Recht behalten, die Ausführung von Projekten auf bestimmten Domains zu unterbinden, um die Sicherheit und Integrität unserer Dienste zu gewährleisten.',
                },
                {
                    value: 'Wir verstehen, dass dies Unannehmlichkeiten verursachen kann und stehen Ihnen gerne zur Verfügung, um alternative Lösungen zu besprechen.',
                },
                {
                    value: 'Bitte kontaktieren Sie uns, wenn Sie Fragen haben oder zusätzliche Informationen benötigen.',
                },
            ],
        },
        domainRemovedFromBlacklistEmail: {
            subject: 'Freigabe einer zuvor gesperrten Domain',
            body: [
                {
                    value: 'wir freuen uns, Ihnen mitteilen zu können, dass die Domain: {{domain}}, die zuvor auf unsere Sperrliste gesetzt wurde, nun freigegeben wurde. Entsprechend unseren Allgemeinen Geschäftsbedingungen (AGB) können alle Projekte, die auf dieser Domain basieren, wieder aufgenommen werden.',
                },
                {
                    value: 'Wir danken Ihnen für Ihre Geduld während der Sperrzeit und freuen uns darauf, Ihre Projekte weiterhin zu unterstützen.',
                },
                {
                    value: 'Bei weiteren Fragen stehen wir Ihnen gerne zur Verfügung.',
                },
            ],
        },
        projectBuildEmail: {
            subject: 'Projekt-Run-Bericht auf {{app}}',
            body: [
                {
                    value: 'Ihr Projekt {{projectName}} wurde soeben ausgeführt. Die folgenden Informationen geben Ihnen einen Überblick über den Ablauf und die Ergebnisse.',
                },
            ],
            nextExecution: {
                label: 'Nächste automatische Ausführung',
                value: '{{nextExecution}}',
            },
        },
        userDeletedSelfEmail: {
            salutation: 'Hallo,',
            subject: 'Bestätigung der Löschung Ihres {{app}}-Kontos!',
            body: [
                {
                    value: 'wir bestätigen, dass Ihr {{app}} Konto erfolgreich gelöscht wurde. Es tut uns leid, Sie gehen zu sehen, und wir hoffen, Ihnen in Zukunft wieder dienen zu können.',
                },
            ],
            additionalInfos: {
                label: 'Bitte beachten Sie folgende Punkte',
                itemList: [
                    {
                        label: 'Datenlöschung',
                        value: 'Alle mit Ihrem Konto verbundenen Daten wurden dauerhaft von unseren Servern entfernt.',
                    },
                    {
                        label: 'Verlust von Abonnements',
                        value: 'Alle offenen Abonnements sind nun geschlossen und können nicht wiederhergestellt werden.',
                    },
                    {
                        label: 'Zukünftige Kontoerstellung',
                        value: 'Sollten Sie sich entscheiden, zurückzukommen, können Sie jederzeit ein neues Konto mit derselben E-Mail-Adresse erstellen. Bitte beachten Sie jedoch, dass Ihr neues Konto keinerlei Verbindung zu oder Referenz von Ihrem gelöschten Konto haben wird.',
                    },
                ],
            },
            closing: [
                {
                    value: 'Wenn Sie weitere Fragen haben oder Unterstützung benötigen, zögern Sie nicht, unser Support-Team zu kontaktieren.',
                },
                {},
            ],
        },
        requestEmail: {
            subject: 'Bestätigung Ihrer Supportanfrage bei {{app}}!',
            body: [
                {
                    value: 'vielen Dank, dass Sie uns kontaktiert haben! Wir haben Ihre Anfrage erhalten und prüfen diese derzeit. Unser Support-Team arbeitet daran und wird sich so schnell wie möglich bei Ihnen melden.',
                },
            ],
            additionalInfos: {
                label: 'Eine Zusammenfassung Ihrer Anfrage',
            },
        },
        demoRequestReceivedEmail: {
            subject: 'Bestätigung Ihrer Demo-Anfrage bei {{app}}!',
            body: [
                {
                    value: 'vielen Dank für Ihr Interesse an unseren Dienstleistungen. Wir haben Ihre Demo-Anfrage erhalten und prüfen diese derzeit.',
                },
                {
                    value: 'Unser Support-Team arbeitet daran und wird sich so schnell wie möglich bei Ihnen melden.',
                },
            ],
        },
        demoRequestApprovedEmail: {
            subject: 'Update zu Ihrem Demo-Anfrage bei {{app}}!',
            body: [
                {
                    value: 'vielen Dank für Ihr Interesse an unseren Dienstleistungen. Wir freuen uns, Ihnen mitteilen zu können, dass Ihre Demo-Anfrage genehmigt wurde.',
                },
                {
                    label: 'Um das Demo zu aktivieren',
                    value: 'Hier klicken!',
                    link: '{{routes.accountDashboard}}',
                },
                {},
                {
                    value: 'Wir sind zuversichtlich, dass Sie unseren Service als äußerst vorteilhaft empfinden werden, und wir freuen uns darauf, Sie als geschätzten Kunden zu haben. Sollten Sie Fragen haben oder Hilfe benötigen, zögern Sie bitte nicht, unser Support-Team zu kontaktieren.',
                },
            ],
        },
        demoRequestDeclinedEmail: {
            subject: 'Update zu Ihrem Demo-Anfrage bei {{app}}!',
            body: [
                {
                    value: 'vielen Dank für Ihr Interesse an unseren Dienstleistungen. Wir schätzen Ihren Eifer, unsere Plattform zu erkunden.',
                },
                {
                    value: 'Wir bedauern, Ihnen mitteilen zu müssen, dass wir Ihre Anfrage aufgrund von Bedenken bezüglich der Authentizität Ihres Kontos nicht genehmigen konnten.',
                },
                {
                    value: 'Diese Entscheidung ist eine Standard-Vorsichtsmaßnahme, um die Sicherheit unserer Plattform zu gewährleisten.',
                },
                {},
                {
                    value: 'Wir sind bestrebt, alle Probleme zu lösen und Ihre Anfrage zu überdenken. Bitte kontaktieren Sie unser Support-Team für weitere Unterstützung und Verifizierungsschritte.',
                },
            ],
        },
        userInvitationEmail: {
            salutation: 'Hallo,',
            subject: 'Einladung zur Teilnahme an einem {{app}}-Konto!',
            body: [
                {
                    value: 'du wurdest eingeladen um die Organisation: {{organisation}} beizutreten, um gemeinsam Projekte zu erstellen und zu verwalten.',
                },
                {
                    label: 'Um loszulegen, folge bitte diesem Link',
                    value: 'Hier klicken!',
                    link: '{{routes.userInvitationLink}}',
                },
            ],
        },
        userRemovedFromAccountEmail: {
            salutation: 'Hallo,',
            subject: 'Update zu Ihrem {{app}}-Konto',
            body: [
                {
                    value: 'wir möchten Sie informieren, dass Ihr Zugang zur Organisation {{organisation}} auf {{app}} durch einen Administrator entfernt wurde. Sie haben keinen Zugriff mehr auf die Daten dieser Organisation.',
                },
                {
                    value: 'Bitte beachten Sie, dass Sie weiterhin Zugriff auf das {{app}} System haben und die Möglichkeit besitzen, eigene Organisationen zu erstellen und zu verwalten.',
                },
            ],
            additionalInfos: {
                label: 'Weitere Informationen',
                itemList: [
                    {
                        label: 'Eigene Organisation erstellen?',
                        value: 'Hier klicken.',
                        link: '{{routes.createOrganisation}}',
                    },
                ],
            },
        },
        welcomeEmail: {
            subject: 'Willkommen bei {{app}} - Starten Sie Ihre Automatisierungsreise!',
            body: [
                {
                    value: 'wir freuen uns sehr, Sie an Bord zu haben und Ihnen dabei zu helfen, das Potenzial der einfachen und zugänglichen Web-Automatisierung zu erschließen.',
                },
                {
                    value: 'Als neues Mitglied unserer Community haben Sie den ersten Step zur Rationalisierung Ihrer Online-Aufgaben und -Workflows getan.',
                },
            ],
        },
        standard: {
            salutation: 'Hallo,',
            additionalInfos: {
                label: 'Weitere Informationen',
                itemList: [
                    {
                        label: 'Features',
                        value: 'Entdecken Sie die Vielfalt der Funktionen und ihre optimale Nutzung.',
                        link: '{{routes.features}}',
                    },
                    {},
                    {
                        label: 'Leicht zu bedienende Oberfläche',
                        value: 'Konfigurieren Sie eine Web-Automatisierung mit Leichtigkeit und Einfachheit.',
                        link: '{{routes.projectWizard}}',
                    },
                    {},
                    {
                        label: 'Ressourcen-Bibliothek',
                        value: 'Greifen Sie auf Tutorials, Leitfäden und Tipps zu, um Ihre Erfahrung zu maximieren.',
                        link: '{{routes.documentation}}',
                    },
                    {},
                    {
                        label: 'Haben Sie Fragen?',
                        value: 'Wir helfen Ihnen gerne weiter.',
                        link: '{{routes.faqs}}',
                    },
                    {},
                    {
                        label: 'Für Entwickler',
                        value: 'Schauen Sie sich unsere Dokumentation an.',
                        link: '{{routes.documentation}}',
                    },
                    {},
                    {
                        label: 'Unsere AGBs',
                        value: 'Schauen Sie sich hier unsere Nutzungsbedingungen an.',
                        link: '{{routes.termsAndConditions}}',
                    },
                ],
            },
            closing: [
                {},
                {
                    value: 'Viel Erfolg und Spaß beim Automatisieren deiner Webprojekte mit {{app}}!',
                },
                {},
                {
                    value: 'Mit freundlichen Grüßen,',
                },
                {},
                {},
                {
                    value: 'Ihr {{app}} Team.',
                },
            ],
            footer: {
                address: '{{address.street}} {{address.city}}, {{address.state}} - {{address.country}}',
                visitUs: 'Besuchen Sie uns',
                url: '{{url}}',
                domain: '{{domain}}',
                socials: {
                    youtube: '{{socials.youtube}}',
                    x: '{{socials.x}}',
                    whatsapp: '{{socials.whatsapp}}',
                },
            },
        },
        projectOverageEmail: {
            salutation: 'Hallo,',
            subject: 'Projekt konnte nicht auf {{app}} ausgeführt werden',
            body: [
                {
                    value: 'Projekt-Build konnte nicht ausgeführt werden, da Ihre erlaubten monatlichen Kontingente erschöpft sind und die automatische Nachladung nicht aktiviert ist.',
                },
                {
                    value: 'Um das Projekt auszuführen und diese Unannehmlichkeit in Zukunft zu vermeiden, können Sie Folgendes tun:',
                },
                {
                    label: '1. Sie aktivieren entweder die automatische Nachladung in Ihren Kontoeinstellungen',
                    value: 'Hier klicken!',
                    link: '{{routes.automationCreditsEdit}}',
                },
                {
                    label: '2. Oder Sie ändern das Abonnement, mit dem Ihr Projekt ausgeführt wird, in eines, bei dem die Kontingente nicht erschöpft sind',
                    value: 'Hier klicken!',
                    link: '{{routes.projectSettingsLinkToDashboard}}',
                },
            ],
            closing: [
                {
                    value: 'Wenn Sie Hilfe benötigen, wenden Sie sich bitte an unser Support-Team. Wir helfen Ihnen gerne!',
                },
            ],
        },
        automationCreditsResetPending: {
            subject: 'Ihre Credits laufen bald ab – jetzt erneuern!',
            body: [
                {
                    value: 'Wir möchten Sie daran erinnern, dass Ihre Automation-Credits am {{expirationDate}} ablaufen.',
                },
                {
                    value: 'Durch die Erneuerung Ihrer Credits jetzt können Sie:',
                },
                {
                    value: 'Sicherstellen, dass Ihre Automatisierungen ohne Unterbrechung weiterlaufen.',
                },
                {
                    value: 'Die Flexibilität behalten, Automatisierungen jederzeit mit Credits auszuführen.',
                },
                {
                    value: 'Die Gültigkeit Ihrer verbleibenden Credits verlängern.',
                },
                {},
                {
                    label: 'Aktuelle Credits',
                    value: '{{currentCredits}}',
                },
                {
                    label: 'Ablaufdatum',
                    value: '{{expirationDate}}',
                },
                {},
                {
                    value: 'Um sicherzustellen, dass Ihre Automatisierungen reibungslos weiterlaufen, besuchen Sie unsere Shop-Seite und kaufen Sie zusätzliche Credits, bevor sie ablaufen.',
                },
                {
                    label: 'Shop',
                    value: 'Klicken Sie hier, um zur Shop-Seite zu gelangen',
                    link: '{{routes.shop}}',
                },
                {
                    label: 'Automatisierungs-Credits',
                    value: 'Klicken Sie hier, um Ihre Automatisierungs-Credits anzusehen',
                    link: '{{routes.automationCredits}}',
                },
            ],
        },
        automationCreditsReset: {
            subject: 'Ihre Credits sind abgelaufen – jetzt erneuern, um Unterbrechungen zu vermeiden!',
            body: [
                {
                    value: 'Wir möchten Sie darüber informieren, dass Ihre Automation-Credits am {{expirationDate}} abgelaufen sind.',
                },
                {
                    value: 'Durch die Erneuerung Ihrer Automation-Credits jetzt können Sie:',
                },
                {
                    value: 'Sicherstellen, dass Übernutzungen (Overages) weiterhin abgerechnet werden können.',
                },
                {
                    value: 'Die reibungslose Ausführung Ihrer automatisierten Projekte aufrechterhalten.',
                },
                {},
                {
                    label: 'Aktuelle Credits',
                    value: '{{currentCredits}}',
                },
                {
                    label: 'Ablaufdatum',
                    value: '{{expirationDate}}',
                },
                {},
                {
                    value: 'Um Störungen in Ihrem Workflow zu vermeiden, besuchen Sie unsere Shop-Seite und erwerben Sie jetzt neue Credits.',
                },
                {},
                {
                    label: 'Shop',
                    value: 'Klicken Sie hier, um zur Shop-Seite zu gelangen',
                    link: '{{routes.shop}}',
                },
                {
                    label: 'Automatisierungs-Credits',
                    value: 'Klicken Sie hier, um Ihre Automatisierungs-Credits anzusehen',
                    link: '{{routes.automationCreditsEdit}}',
                },
            ],
        },
        sendRequestEmail: {
            salutation: 'Hallo,',
            subject: 'Bestätigung Ihrer Demo-Anfrage bei {{app}}!',
            body: [
                {
                    value: 'vielen Dank für Ihr Interesse an unseren Dienstleistungen. Wir haben Ihre Anfrage erhalten und prüfen diese derzeit.',
                },
                {
                    value: 'Unser Support-Team arbeitet daran und wird sich so schnell wie möglich bei Ihnen melden.',
                },
            ],
            additionalInfos: {
                label: 'Ihre Anfrage',
            },
        },
        sendPaymentFailedEmail: {
            subject: 'Wichtige Mitteilung: Ihre Zahlung konnte nicht verarbeitet werden',
            body: [
                {
                    value: 'leider konnte die Zahlung für Ihre Rechnung mit der ID {{invoiceId}} nicht erfolgreich verarbeitet werden.',
                },
                {},
                {
                    value: 'Um sicherzustellen, dass Ihre Automatisierungen weiterhin reibungslos ausgeführt werden, bitten wir Sie, Ihre Zahlungsinformationen zu überprüfen und die Zahlung manuell abzuschließen.',
                },
                {
                    label: 'Rechnung anzeigen & Zahlung abschließen',
                    value: 'Hier klicken',
                    link: '{{routes.invoiceDetails}}',
                },
                {},
                {
                    value: 'Falls Sie Fragen haben oder Unterstützung benötigen, steht Ihnen unser Support-Team jederzeit gerne zur Verfügung.',
                },
            ],
        },
    },
    entries: 'Einträge',
    entry: 'Eintrag',
    events: {
        ACCOUNT_COLLABORATOR_EDITED: 'Mitarbeiter {{email}} bearbeitet durch {{editor}}',
        ACCOUNT_COLLABORATOR_REMOVED: 'Mitarbeiter {{email}} wurde entfernt durch {{remover}}',
        ACCOUNT_CREATED: 'Konto erstellt',
        ACCOUNT_NEW_COLLABORATOR_CONFIRMS_ACCOUNT: 'Mitarbeiter {{email}} hat das Konto bestätigt',
        ACCOUNT_NEW_COLLABORATOR_INVITED: 'Neuer Mitarbeiter {{email}} (Rolle: {{role}}) eingeladen durch {{invitedBy}}',
        ACCOUNT_NEW_COLLABORATOR_RE_INVITED: 'Einladung an Mitarbeiter {{email}} erneuert versendet',
        ACCOUNT_NEW_PROJECT: 'Neues Projekt "{{projectName}}" angelegt',
        ACCOUNT_NEW_SUBSCRIPTION: 'Neues Abonnement',
        ACCOUNT_PRE_PAID_SUBSCRIPTION_CREATED: 'Prepaid-Abonnement wurde angelegt, um das Kaufen von Automation-Credits zu ermöglichen.',
        ACTIONS_TIMEOUT: 'Die Aktion "{{name}}" wurde nach Überschreitung des {{duration}}-Limits abgebrochen (Timeout-Fehler).',
        ACTION_FILE_UPLOAD_ONLY_SINGLE_FILES: 'Das Formular unterstützt das Hochladen von mehreren Dateien nicht. Es wurde nur das erste Element hochgeladen.',
        ACTION_NOT_ALLOWED_ON_DESKTOP: 'Die Aktion "{{label}}" kann nicht auf Desktop ausgeführt werden.',
        ACTION_NOT_ALLOWED_ON_FOREIGN_HOST: 'Die Aktion "{{label}}" darf nicht auf einem fremden Host ({{url}}) ausgeführt werden und wurde daher übersprungen.',
        ACTION_NOT_ALLOWED_ON_MOBILE: 'Die Aktion "{{label}}" kann nicht auf Mobile ausgeführt werden.',
        AI_THRESHOLD_NOT_MET: 'Der eingestellte Schwellenwert ({{confidenceThreshold}}) für die AI-Ergebnisse wurde nicht erreicht ({{confidence}}).',
        BLOCKED_DOMAINS: 'Aktion kann nicht ausgeführt werden um {{dateTime}}, weil das Projekt auf mindestens eine Domain auf der schwarzen Liste verweist:',
        BROWSER_NOT_AVAILABLE: 'Browser konnte nicht gestartet werden.',
        BROWSER_STARTED: 'Browser wurde gestartet.',
        CANNOT_EXECUTE_INACTIVE_PROJECT: 'Das Projekt ist inaktiv und kann nicht ausgeführt werden. Bitte aktivieren Sie das Projekt zuerst.',
        CANNOT_FIND_PROJECT_CONFIG: 'Projekt ist falsch konfiguriert.',
        CLIENT_ERROR: 'Es werden Fehler bei der Anfrage an URL: {{url}} angezeigt.',
        DUPLICATE_PROJECT_QUEUED: 'Für dieses Projekt wurde bereits ein Projektdurchlauf in die Warteschlange eingetragen.',
        DUPLICATE_PROJECT_RUNNING: 'Es läuft bereits ein Projektdurchlauf für dieses Projekt und er muss zuerst beendet werden, bevor Sie einen neuen starten können.',
        ELEMENT_NOT_FOUND: 'Es wurde kein passendes Element zum Selektor "{{selector}}" gefunden.',
        ERROR_DELETING_FILE_AFTER_UPLOAD: 'Fehler beim Löschen der Datei nach dem Upload.',
        FILE_NOT_FOUND: 'Die verknüpfte Datei wurde nicht gefunden.',
        GS_ERROR_DOWNLOAD: 'Es liegt ein Problem bei der Kommunikation mit dem CDN.',
        GS_ERROR_NOT_FOUND: 'Fehler beim Herunterladen von Dateien aus dem CDN.',
        GS_ERROR_UPLOAD: 'Fehler beim Speichern des {{format}}-Berichts.',
        INVALID_ACTION_CONFIGURATION: 'Eine Aktion ist falsch konfiguriert.',
        INVALID_CONDITION_CONFIGURATION: 'Eine Bedingung ist falsch konfiguriert.',
        INVALID_DATA_EXTRACTION_CONFIGURATION: 'Projekt ist falsch konfiguriert.',
        INVALID_SCREENSHOT_CONFIGURATION: 'Projekt ist falsch konfiguriert.',
        INVALID_E2E_VISUAL_TEST_CONFIGURATION: 'Projekt ist falsch konfiguriert.',
        INVALID_JS_CODE: 'Ihr Javascript Code war fehlerhaft.',
        INVALID_LOOP_CONFIGURATION: 'Eine Schleife ist falsch konfiguriert.',
        INVALID_SOLUTION_CONFIGURATION: 'Es ist ein unerwarteter Fehler aufgetreten.',
        INVALID_URL_CHALLENGE_CONFIGURATION: 'URL-Challenge ist falsch konfiguriert.',
        LIGHTHOUSE_BUILD_FAILED: 'Lighthouse Test auf URL: {{url}} ({{device}}) ist fehlgeschlagen.',
        LIGHTHOUSE_BUILD_STARTED: 'Lighthouse Test auf URL: {{url}} ({{device}}) in Bearbeitung.',
        LINK_CHECKER_BUILD_COMPLETED: 'Page-Load beendet.',
        LINK_CHECKER_BUILD_FAILED: 'Page-Load fehlgeschlagen.',
        LINK_CHECKER_SOLUTION_STARTED: 'Page-Load gestartet.',
        LINKS_FOUND_ON_PAGE: 'Es wurden {{count}} Link(s) auf der Seite {{pageUrl}} gefunden, {{qualifiedCount}} der Links qualifizierten sich für den Projektdurchlauf.',
        MANAGER_ERROR_MISSING_JOB_REFERENCE: 'Project-Manager-Fehler: Es wurde keine Job-Referenz übergeben.',
        PAGE_CUSTOM_TIMEOUT: 'Die festgelegte Zeit {{linkRequestTimeout}} bis das gesamte HTML auf {{url}} vollständig geladen ist, wurde überschritten.',
        PAGE_CUSTOM_TIMEOUT_JS_ERROR: 'Die festgelegte Zeit {{linkRequestTimeout}} bis das geladene HTML auf {{url}} vollständig geladen ist und die JavaScript Bedingung erfüllt wird, wurde überschritten.',
        PAGE_TIMEOUT: 'Die URL: {{url}} reagiert nicht auf Anfragen.',
        PAGE_UNAVAILABLE: 'Die URL: {{url}} war entweder nicht erreichbar oder die Anfrage wurde abgebrochen,',
        PAGE_UNKNOWN_HOST: 'Die URL: {{url}} war nicht erreichbar.',
        PROJECT_PROJECT_BUILD_ABORTED_BY_STEP: 'Projektdurchlauf abgebrochen, weil ein Step fehlgeschlagen ist.',
        PROJECT_BUILD_AUTO: 'Projektdurchlauf wurde automatisch ausgelöst',
        PROJECT_PROJECT_BUILD_BY_MANAGEMENT_API: 'Projektdurchlauf wurde über das Management-API ausgelöst',
        PROJECT_BUILD_MANUAL: 'Projektdurchlauf wurde manuell ausgelöst durch {{author.email}}.',
        PROJECT_BUILD_CANCEL: 'Projektdurchlauf wurde abgebrochen',
        PROJECT_BUILD_DATA_EXTRACTION_NO_RESULT: 'Der AI-Assistent konnte keine Daten extrahieren oder keine geeignete Lösung für die Anfrage finden.',
        PROJECT_BUILD_DELETED: 'Projektdurchlauf wurde gelöscht',
        PROJECT_BUILD_STEP_DATA_EXTRACTION_AI_ERROR: 'Fehler bei der Datenextraktion mit dem AI-Agenten',
        PROJECT_CREATED: 'Projekt wurde angelegt',
        PROJECT_DELETED: 'Projekt wurde gelöscht',
        PROJECT_EDITED: 'Projekt wurde bearbeitet durch {{email}} (Sitzung: {{loginSessionId}}).',
        PROJECT_NAME_TOO_LONG: 'Der Projektname ist zu lang {{length}} Zeichen. Erlaubt ist max {{maxLength}} Zeichen.',
        PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST: 'Ein Projekt wurde am {{dateTime}} pausiert, weil es auf einer schwarzen Liste stehende Domain baut:',
        PROJECT_STATUS_CHANGED: 'Projektstatus geändert von alt: {{old}} zu neu: {{new}} von {{author.email}}',
        REPORT_EXPORT_ERROR: 'Der Bericht konnte nicht exportiert werden.',
        REQUEST_BLOCKED: 'HTTP-Request Modifikation ab URL: {{url}} wurde blockiert (id = {{id}}).',
        REQUEST_MOCK_COMPLETED: 'HTTP-Request Modifikation wurde für URL: {{url}} erfolgreich abgeschlossen (id = {{id}}).',
        REQUEST_MODIFICATION_FAILED: 'HTTP-Request Modifikation ist fehlgeschlagen für URL: {{url}} (id = {{id}}).',
        REQUEST_MODIFICATION_JS_CODE_INVALID: 'HTTP-Request Modifikation für URL: {{url}} wurde ignoriert, weil die Reduzierungsfunktion ungültig ist (id = {{id}}).',
        RESOURCE_RELEASE_ERROR: 'Es ist ein Laufzeitfehler aufgetreten.',
        RESPONSE_MODIFICATION_COMPLETED: 'Response Modifikation für URL: {{url}} erfolgreich abgeschlossen (id = {{id}}).',
        RESPONSE_MODIFICATION_FAILED: 'Response Modifikation ist fehlgeschlagen für URL: {{url}} (id = {{id}}).',
        RESPONSE_MODIFICATION_JS_CODE_INVALID: 'Response Modifikation für URL: {{url}} wurde ignoriert, weil die Reduzierungsfunktion ungültig ist (id = {{id}}).',
        RUNTIME_ERROR: 'Es ist ein Laufzeitfehler aufgetreten.',
        SERVER_ERROR: 'Der Server hat einen Fehler bei der Anfrage an URL: {{url}} zurückgegeben.',
        SESSION_CANCELLED: 'Der Projektdurchlauf-Job wurde abgebrochen.',
        SESSION_CANCELLED_BY_MANAGER: 'Der Projektdurchlauf ist ins Timeout reingelaufen und wurde vom Webeagle-Manager abgebrochen.',
        SESSION_CANCELLED_MANUALLY: 'Der Projektdurchlauf wurde abgebrochen.',
        SESSION_DATABASE_ERROR: 'Fehler beim Zugriff auf die Datenbank.',
        SESSION_ENDED: 'Der Projektdurchlauf Session beendet',
        SESSION_ERROR: 'Es ist ein unerwarteter Fehler passiert.',
        SESSION_STARTED: 'Der Projektdurchlauf Session gestartet',
        SOLUTION_URL_CHALLENGE_ELIMINATION: 'Die URL "{{url}}" wurde aus der Challenge im Step "{{label}}" eliminiert.',
        STEP_ABORTED: 'Step "{{label}}" ({{stepType}}) abgebrochen.',
        STEP_COMPLETED: 'Step "{{label}}" ({{stepType}}) done.',
        STEP_FAILED: 'Step "{{label}}" ({{stepType}}) fehlgeschlagen.',
        STEP_INACTIVE: 'Step "{{label}}" ({{stepType}}) ist inaktiv und wurde übersprungen.',
        STEP_LABELLING_FUNCTION_FAILED: 'Die Funktion zur dynamischen Benennung von Step "{{label}}" ({{stepType}}) ist fehlgeschlagen (id = {{id}}).',
        STEP_LOOP_BROKEN: 'Die Schleife im Step "{{label}}" wurde bei der Iteration {{position}} gestoppt.',
        STEP_LOOP_EMPTY: 'Die Schleife im Step "{{label}}" ({{stepType}}) ist leer.',
        STEP_REFERENCE_LOST: 'Element-Referenz im Step "{{label}}" ({{stepType}}) ist verloren gegangen.',
        STEP_SOLUTION_URL_CHALLENGE_FAILED: 'Die URL-Challenge im Step "{{label}}" ist für {{url}} fehlgeschlagen.',
        STEP_STARTED: 'Step "{{label}}" ({{stepType}}) in Bearbeitung.',
        STRIPE_UNKNOWN_CUSTOMER: 'STRIPE: Unbekannter Kunde',
        STRIPE_UNKNOWN_SUBSCRIPTION: 'STRIPE: Unbekanntes Abonnement',
        SUBSCRIPTION_CONTINGENT_UPDATE_ERROR: 'Es ist ein Fehler aufgetreten.',
        SUBSCRIPTION_DOMAIN_LIMIT_REACHED: 'Das Limit der Domains {{domainContingent}} im Abonnement: "{{licenseName}}" wurde schon erreicht. Bisher geprüfte Domains "{{domainList}}". Ihre Angaben "{{targetDomainList}}". Bitte passen Sie das Projekt an oder wählen Sie ein anderes Abonnement.',
        SUBSCRIPTION_MISSING: 'Wir können keine aktive Abonnement finden, mit dem das Projekt ausgeführt werden kann.',
        TIMEOUT: 'Zeitüberschreitung',
        TOOL_ERROR: 'Es ist ein unerwarteter Fehler.',
        UNKNOWN: 'Es ist ein unerwarteter Fehler aufgetreten.',
        UNKNOWN_ACCOUNT: 'Konto nicht gefunden',
        UNKNOWN_CHECK_TYPE: 'Der Typ "{{checkType}}" ist nicht bekannt.',
        URL_AUTHORIZATION: 'Die Autorisierung für die Anfrage an URL: {{url}} ist fehlgeschlagen,',
        URL_BAD_SSL: 'Es besteht ein Problem mit installierten Zertifikat auf: {{url}}.',
        URL_CLIENT_ERROR: 'Client-Fehler bei der Anfrage an URL: {{url}}.',
        URL_DISALLOWED_BY_ETHICAL_ACCESS_CHECK: 'Die Verarbeitung der URL: {{url}} wurde wegen unseren ethischen Zugriffsbeschränkungen abgebrochen.',
        URL_GONE: 'Die angeforderte URL: {{url}} ist nicht mehr verfügbar.',
        URL_NOT_RESPONDING: 'URL: {{url}} nicht erreichbar.',
        URL_PROCESSED: 'URL: {{url}} bearbeitet.',
        URL_PROCESSED_DEVICE: 'URL: {{url}} auf {{device}} Gerät bearbeitet.',
        URL_PROCESSING: 'URL: {{url}} wird bearbeitet.',
        URL_REDIRECTED: 'URL: {{url}} wurde umgeleitet.',
        WEB_RESOURCE_INJECTION_COMPLETED: '{{contentType}} (id = {{id}}) Ressource wurde erfolgreich in die Seite eingefügt.',
        WEB_RESOURCE_INJECTION_FAILED: '{{contentType}} (id = {{id}}) Ressource konnte nicht in die Seite eingefügt werden.',
        PROJECT_BUILD_FINISHED: 'Projekt fertiggestellt - Status: {{state}}',
        PROJECT_SUBSCRIPTION_CHANGED: 'Projekt-Abonnement geändert alt: {{old}} => neu: {{new}}.',
        PROJECT_BUILD_RETRY: 'Projektdurchlauf automatisch wiederholt ({{attempt}} von {{maxAttempts}}).',
        PROJECT_BUILD_RETRY_EXHAUSTED: 'Projektdurchlauf kann nicht mehr automatisch wiederholt werden, da die maximale Anzahl von Wiederholungen {{maxAttempts}} erreicht wurde.',
        PAYMENT_DETAILS_NEED_UPDATE: 'Ihre Zahlungsdetails sind abgelaufen. Bitte aktualisieren Sie Ihre Zahlungsdetails, um Ihren Service nicht zu unterbrechen.',
        PROJECT_BUILD_SKIPPED_DUE_TO_OVERAGE: 'Projektdurchlauf kann nicht gestartet werden, da das Kontingent für den aktuellen Abrechnungszeitraum erschöpft ist und Sie haben keine automatische Verlängerung aktiviert.',
        ELEMENT_NOT_VISIBLE: 'Element kann nicht erfasst werden, da es nicht sichtbar ist. ({{selector}}, {{html}})',
        AUTOMATION_CREDITS_AUTO_RENEW_SUCCESS: 'Automatische Verlängerung von Automation-Credits-Guthaben erfolgreich',
        AUTOMATION_CREDITS_AUTO_RENEW_FAILED: 'Automatische Verlängerung von Automation-Credits-Guthaben fehlgeschlagen',
        AUTOMATION_CREDITS_AUTO_RENEW_BLOCKED_DUE_TO_BALANCE: 'Automatische Verlängerung für Automation-Credits kann nicht deaktiviert werden, solange Ihr Guthaben negativ ist. Bitte fügen Sie Guthaben hinzu, indem Sie Credits aus dem Shop kaufen, bevor Sie die automatische Verlängerung deaktivieren.',
        AUTOMATION_CREDITS_REQUIRED: 'Diese Aktion kann aufgrund unzureichender Automation-Credits nicht durchgeführt werden. Bitte fügen Sie Automation-Credits hinzu, indem Sie Automation-Credits kaufen, bevor Sie es erneut versuchen.',
        PROJECT_BUILD_SCREENSHOT_NO_DOM_SELECTOR_FOUND_BY_AI: 'Der AI-Agent konnte keinen DOM-Selektor für die Screenshot-Erfassung finden',
        AI_SERVICE_FAILED: 'Der AI-Dienst ist nicht verfügbar',
        DOWNLOAD_PROJECT_RUNNING: 'Download ist nicht verfügbar, während das Projekt läuft.',
        PROJECT_BUILD_DELETED_ALL: 'Es wurden alle Builds gelöscht (Actor: {{deletedBy}}, Zeitstempel: {{deletedOn}}, Sitzung: {{loginSessionId}}, Projekt: {{projectId}}).',
        PROJECT_BUILD_STEP_CONDITION_AI_ERROR: 'Fehler bei der Auswertung der Bedingung mit AI',
        PROJECT_BUILD_STEP_URL_CHALLENGE_AI_ERROR: 'Fehler beim Verarbeiten der AI-URL-Herausforderung',
        PROJECT_BUILD_STEP_URL_CHALLENGE_NO_RESULT: 'Der AI-Agent konnte die URL-Herausforderung nicht erfolgreich abschließen.',
        PROJECT_BUILD_STEP_E2E_VISUAL_TEST_AI_ERROR: 'Ein Fehler ist bei der Verarbeitung des visuellen Tests mit AI aufgetreten.',
        PROJECT_BUILD_STEP_DATA_EXTRACTION_NO_RESULT: 'Der AI-Assistent konnte keine Daten von der Seite extrahieren. Bitte überprüfen Sie die Konfiguration des Extraktionsschritts.',
        PROJECT_BUILD_STEP_E2E_VISUAL_TEST_NO_REVIEW: 'Der AI-Assistent konnte keine Überprüfung für den visuellen Test bereitstellen. Bitte überprüfen Sie den Test manuell.',
    },
    example: 'Zum Beispiel',
    exampleCode: 'Beispiel-Code',
    extendedSettingsBlock: 'Erweiterte Konfiguration',
    faqs: {
        label: 'FAQs',
        pageTitle: 'Frequently Asked Questions',
        title: 'Haben Sie Fragen? Wir haben die Antworten!',
        description: 'Willkommen auf unserer FAQ-Seite! Hier finden Sie eine umfassende Sammlung von Antworten auf die häufigsten Fragen zu {{app}}. Von den ersten Steps mit Ihrem ersten Projekt bis hin zum Verständnis der Feinheiten unserer erweiterten Funktionen – unsere FAQs sind darauf ausgerichtet, Sie bei jedem Step zu unterstützen. Durchsuchen Sie die Kategorien oder verwenden Sie die Suchfunktion, um schnell spezifische Lösungen zu finden. Falls Sie nicht finden, wonach Sie suchen, denken Sie daran, unser Support-Team ist nur einen Klick entfernt unter',
        noResults: 'Nichts gefunden.',
        items: {
            whatIsWebautomate: {
                label: 'Was ist {{app}}?',
                description: '{{app}} ist eine No-Code/Low-Code-Plattform, die darauf ausgelegt ist, Aktionen auf Websites zu automatisieren. Sie ermöglicht es Benutzern, mit Webseiten zu interagieren und verschiedene Aufgaben auszuführen, ohne umfangreiche Programmierkenntnisse zu benötigen.',
            },
            needCodingSkills: {
                label: 'Benötige ich Programmierkenntnisse, um {{app}} zu nutzen?',
                description: 'Nein, {{app}} ist als No-Code/Low-Code-Lösung konzipiert, was bedeutet, dass sie für die meisten ihrer Funktionen minimale bis keine Programmierkenntnisse erfordert.',
            },
            startNewProject: {
                label: 'Wie starte ich ein neues Projekt auf {{app}}?',
                description: 'Um ein neues Projekt zu starten, besuchen Sie die Projektassistent-Seite unter "https://{{app}}/project-wizard" und folgen Sie den angeleiteten Steps zur Projektkonfiguration.',
            },
            automateBrowserActions: {
                label: 'Kann ich Browseraktionen wie Klicks und Tastatureingaben automatisieren?',
                description: 'Ja, {{app}} ermöglicht es Ihnen, verschiedene Benutzer- und Browseraktionen wie Klicks, Tastatureingaben, Mausbewegungen und mehr zu simulieren.',
            },
            captureScreenshots: {
                label: 'Kann ich mit {{app}} Screenshots aufnehmen oder Videos meiner Website aufzeichnen?',
                description: 'Ja, {{app}} bietet Lösungen zur Erfassung von Screenshots und zur Aufzeichnung von Bildschirmvideos von Website-Interaktionen an.',
            },
            scheduleAutomations: {
                label: 'Wie plane ich automatisierte Ausführungen meiner Projekte?',
                description: 'Im Abschluss-Step Ihrer Projektkonfiguration können Sie automatisierte Ausführungen einrichten, mit Optionen wie Start- und Enddatum, Häufigkeit und Zeitplantyp.',
            },
            receiveNotifications: {
                label: 'Kann ich Benachrichtigungen über die Ausführungsergebnisse meines Projekts erhalten?',
                description: 'Ja, abhängig von Ihrem Abonnement-Paket können Sie Benachrichtigungen per E-Mail oder Webhook einrichten, um Updates über die Ausführungsergebnisse Ihres Projekts zu erhalten.',
            },
            mobileVsDesktop: {
                label: 'Wie behandelt {{app}} Mobile- im Vergleich zu Desktop-Simulationen?',
                description: 'Die Plattform ermöglicht es Ihnen, Benutzeraktionen sowohl für mobile Geräte als auch für Desktops zu simulieren, mit spezifischen Aktionen, die für jeden Typ verfügbar sind.',
            },
            screenshotFunctionality: {
                label: 'Wie genau funktioniert die Screenshot-Dokumentation?',
                description: 'Bei der Screenshot-Dokumentation können Sie festlegen, was in einem Screenshot erfasst werden soll – ob der gesamte Bildschirm, ein bestimmtes DOM-Element oder nur der Ansichtsbereich des Browsers.',
            },
            videoRecordingBenefits: {
                label: 'Welche Vorteile bietet die Videoaufnahme-Funktion?',
                description: 'Die Bildschirmvideoaufnahme zeichnet Interaktionen auf Ihrer Website auf, was hilfreich für UX-Tests, Kundensupport und das Verständnis des Benutzerverhaltens sein kann.',
            },
            whereAreTheAutomationsExecuted: {
                label: 'Wo werden die Automatisierungen ausgeführt?',
                description: 'Die Automatisierungen werden auf unseren Servern ausgeführt. Wir haben Servern an unterschiedlichen Standorten. Sie können die Automatisierungen von jedem Gerät aus starten, das über einen Webbrowser verfügt. Sie müssen keine Software installieren oder konfigurieren. Sie können die Automatisierungen auch planen, um sie zu einem bestimmten Zeitpunkt auszuführen.',
            },
        },
    },
    features: {
        label: 'Alle Features im Überblick',
        pageTitle: 'Alle Features im Überblick',
    },
    form: {
        optionalField: '(Optional)',
        hint: 'Hinweis',
        name: 'Name',
        lastName: 'Familienname',
        email: 'E-Mail-Adresse',
        whatsapp: 'WhatsApp',
        phone: 'Telefonnummer',
        company: 'Firma',
        website: 'Website',
        role: 'Rolle',
        interests: 'Interesten',
        organisation: 'Organization',
        mobilePhone: 'Mobiltelefonnummer',
        username: 'Kontoname',
        password: 'Passwort',
        passwordRepeat: 'Passwort wiederholen',
        passwordReset: 'Passwort zurücksetzen',
        message: 'Ihre Nachricht',
        send: 'Senden',
        request: 'Anfrage',
        validation: {
            emailInvalid: 'Bitte valide E-Mail Adresse angeben, z.B. <EMAIL>.',
            emailRequired: 'Bitte geben Sie Ihre E-Mail Adresse an.',
            mobilePhoneInvalid: 'Bitte gültige Mobiltelefonnummer eingeben.',
            mobilePhoneRequired: 'Bitte Mobiltelefonnummer eingeben.',
            passwordRequired: 'Bitte geben Sie das Passwort an.',
            passwordRepeatRequired: 'Bitte Passwort hier auch eingeben.',
            passwordRepeatInvalid: 'Die eingegebene Passwörter stimmen nicht überein.',
            domain: 'Bitte geben Sie einen gültigen Domain an.',
            usernameInvalid: 'Bitte Benutzername eintragen.',
            fieldRequired: 'Bitte machen Sie hier eine Angabe.',
            selectionRequired: 'Bitte treffen Sie eine Auswahl.',
            toBeDefined: 'Nicht angegeben.',
            script: 'Code hier eingeben.',
            min: 'Der Wert muss grösser {{min}} sein.',
            minMax: 'Der Wert muss zwischen {{min}} und {{max}} liegen.',
            minMaxStep: 'Der Wert muss zwischen {{min}} und {{max}} liegen und ein Vielfaches von {{step}} sein.',
            http: 'Die URL sollte mit "http://" oder "https://" beginnen.',
            url: 'Bitte geben Sie eine gültige URL an.',
            duplicateEntry: 'Der Eintrag existiert bereits.',
        },
    },
    genericFailure: '{{action}}: Nicht erfolgreich!',
    genericSuccess: '{{action}}: Erfolgreich!',
    getStarted: 'Loslegen',
    greeting: 'Hallo, {{organisation}}',
    groupName: 'Gruppennamen',
    intro: {
        title: 'Schluss mit wiederholenden Web-Aufgaben!',
        startNow: 'Automatisieren Sie Ihre Website in Minuten — Kein Programmieren erforderlich!',
        subtitle: 'Mit {{app}} kannst du leistungsstarke Browser-Automationen erstellen und planen – ganz ohne Programmierkenntnisse. Ob URL-Tests, Datenextraktion, Lighthouse-Audits oder visuelle und Video-Dokumentation – unser intuitiver Projektassistent hilft dir, alles in wenigen Steps aufzusetzen. Führe deine Workflows manuell oder automatisch aus und erhalte zu jedem Build einen detaillierten Bericht.',
        easySetup: 'In 2 Minuten eingerichtet',
        automated: 'Läuft Automatisiert',
        noCoding: 'Kein Code-Schreiben',
        maintenance: 'Keine Wartung',
        description: 'Automatisiere Browser-Workflows in wenigen Minuten und lasse sie automatisch ablaufen, wann immer du möchtest.',
    },
    home: {
        label: 'Startseite',
        title: 'webAutomate',
    },
    hours: 'Stunden',
    id: 'ID',
    idleTime: {
        title: 'Sitzung abgelaufen',
        description: 'Sie waren eine Weile inaktiv. Bitte laden Sie die Seite neu, um fortzufahren.',
        ctaLabel: 'Seite jetzt neu laden',
    },
    imageLoadErrorMessage: 'Dieses Bild konnte nicht geladen werden.',
    inactive: 'Nicht aktiv',
    index: 'Position',
    invalidField: 'Das Feld "{{field}}" ist ungültig.',
    key: 'Parameter',
    itemLabelling: {
        label: 'Benennung',
        labelInfo: 'Benennungen dienen dazu, die Steps auf Berichten wiederzuerkennen.',
        mandatoryField: 'Bitte geben Sie eine Benennung ein.',
        duplicateGroupName: 'Diese Benennung ist bereits vergeben.',
        strategies: {
            JS: {
                label: 'Mit JS-Funktion',
                description: 'Die Funktion soll eine alphanumerische Zeichenkette zurückgeben. Andernfalls wird der Fallback "{{fallbackLabel}}" verwendet.',
            },
            LABEL: {
                label: 'Mit Label',
            },
        },
    },
    languages: {
        de: {
            label: 'Deutsch',
        },
        fr: {
            label: 'Französisch',
        },
        en: {
            label: 'Englisch',
        },
    },
    linkTable: {
        url: 'URL',
        tag: 'Tag',
        selector: 'Query',
        html: 'Snippet',
    },
    linkedResources: {
        querySelector: {
            label: 'DOM-Selektor',
            notice: '"$" steht für document.querySelectorAll. Es ist eine Liste mit Verweis auf DOM-Elemente, die zu Ihrem Selektor passen.',
            noticeSingle: '"$" steht für document.querySelector. Es verweist auf das erste Element, das zu Ihrem Selektor passt.',
            url: 'https://developer.mozilla.org/de/docs/Web/API/Document/querySelectorAll',
            urlSingle: 'https://developer.mozilla.org/de/docs/Web/API/Document/querySelector',
            validation: 'Bitte geben Sie einen gültigen Selektor ein.',
        },
        nodeList: {
            notice: 'NodeList ist die Ansammlung aller DOM-Nodes, die zu Ihrem Selector passen.',
            readMore: 'Mehr zu NodeList erfahren',
            url: 'https://developer.mozilla.org/en-US/docs/Web/API/NodeList',
        },
        domNode: {
            notice: 'Ein DOM-Node ist das erste DOM-API-Objekt, das zu ihrem Selektor passt.',
            readMore: 'Mehr zu Dom-Node erfahren',
            url: 'https://developer.mozilla.org/en-US/docs/Web/API/Node',
        },
        string: {
            label: 'String',
            url: 'https://developer.mozilla.org/de/docs/Web/JavaScript/Reference/Global_Objects/String',
        },
        object: {
            label: 'Objekt',
            url: 'https://developer.mozilla.org/de/docs/Web/JavaScript/Reference/Global_Objects/Object',
        },
    },
    links: {
        broken: 'Fehlerhafte URLs',
        success: 'Erfolgreiche URLs',
    },
    load: {
        all: 'Load all',
    },
    login: {
        label: 'Login',
        pageTitle: 'Login',
        logout: 'Ausloggen',
        accountQuestion: 'Haben Sie noch kein Konto?',
        rememberMe: 'Login merken',
        forgotPasswordQuestion: 'Passwort vergessen?',
        loginHere: 'Hier anmelden',
        teaser: 'Melden Sie sich an mit ihrem Google, Facebook oder Twitter Konto an oder mit Ihrer E-Mail-Adresse.',
        validation: {
            success: 'Sie sind nun erfolgreich in Ihr Konto eingeloggt.',
            error: 'Bitte geben Sie gültige Logindaten an.',
        },
    },
    logs: {
        label: 'Vorkommnisse',
        timestamp: 'Zeitpunkt',
        noEvents: 'Keine Vorfälle verzeichnet.',
        type: {
            label: 'Typ',
            options: {
                INFO: {
                    label: 'Hinweis',
                    description: 'Informationen',
                },
                WARNING: {
                    label: 'Warnung',
                    description: 'Warnungen',
                },
                SUCCESS: {
                    label: 'Erfolg',
                    description: 'Erfolgreiche Aktionen',
                },
                ERROR: {
                    label: 'Fehler',
                    description: 'Fehler',
                    info: 'Fehler werden unbedingt geloggt, wenn sie auftreten. Sie können die Details in den Logs einsehen.',
                },
            },
        },
    },
    loops: {
        label: 'Schleifen',
        selectOption: 'Bitte wählen Sie die Schleifenart',
        stepsLabel: 'Steps in der Schleife',
        iteration: 'Iteration: {{iteration}}',
        break: 'Iteration: {{iteration}}',
        failStopLabel: 'Der Projektdurchlauf abbrechen, wenn dieses Loop nicht erfolgreich ausgeführt werden kann.',
        options: {
            selector: {
                label: 'Wiederholung für jedes passendes Element',
                namePlaceholder: 'Element Loop',
                description: 'Ausführen einer Folge von Steps für jedes übereinstimmende Element in einer DOM-Abfrage.',
            },
            range: {
                label: 'Wiederholung innerhalb eines Zahlenbereichs',
                namePlaceholder: 'Zahlenbereich Loop',
                description: 'Ausführen einer Folge von Steps für jeden Index in einem Bereich.',
                start: 'Start',
                end: 'End',
                step: 'Steps',
            },
            js: {
                label: 'Wiederholen, bis eine Bedingung erfüllt ist',
                namePlaceholder: 'JavaScript Loop Funktion',
                description: 'Ausführen einer Folge von Steps basiert auf den Rückgabewert einer boolischen Funktion.',
                function: {
                    label: 'Loop Funktion',
                    description: 'Diese Funktion wird vor jeder Iteration neu ausgewertet, und der Block wird nur ausgeführt, wenn er einen wahren Wert liefert.',
                    items: [
                        '"index" steht für die aktuelle Iteration. Es beginnt bei 0 und endet bei n-1, wenn die Funktion n-mal ausgeführt wird.',
                    ],
                },
            },
            jsList: {
                label: 'Wiederholung mit einer wechselnden Liste',
                namePlaceholder: 'JavaScript-Liste',
                description: 'Ausführen einer Folge von Steps für jedes Element in einer dynmisch ermittelte Liste.',
                function: {
                    label: 'JavaScript Listen Funktion',
                    description: 'Diese Funktion wird einmal ausgeführt und soll eine Liste zurückgeben, die im Anschluss iteriert wird.',
                },
            },
            customList: {
                label: 'Wiederholung mit einer festgelegten Liste',
                namePlaceholder: 'Custom List Loop',
                description: 'Ausführen einer Folge von Steps für jedes Element in einer selbst definierten Liste.',
                labels: {
                    newItem: 'Neue Werte hier eingeben',
                },
                notification: {
                    empty: 'Bitte mindestens einen Wert eingeben.',
                },
            },
        },
        notifications: {
            deleteSuccess: 'Die Schleife "{{label}}" wurde gelöscht',
            editSuccess: 'Die Schleife "{{label}}" wurde erfolgreich bearbeitet',
            saveSuccess: 'Die Schleife "{{label}}" wurde erfolgreich gespeichert',
            copySuccess: 'Die Schleife "{{label}}" wurde erfolgreich kopiert',
        },
        label_one: 'Schleife',
        repetitions: {
            label: 'Wiederholungen',
            view: {
                label: 'Wiederholungen anzeigen',
                label_one: 'Wiederholung anzeigen',
            },
            labels: {
                completed: {
                    label: 'Abgeschlossen',
                    description: 'Erfolgreich ausgeführt',
                },
                broken: {
                    label: 'Unterbrochen',
                    description: 'Ausführung unterbrochen',
                },
                failed: {
                    label: 'Gescheitert',
                    description: 'Ausführung fehlgeschlagen',
                },
                voided: {
                    label: 'Annulliert',
                    description: 'Nicht ausgeführt',
                },
                aborted: {
                    label: 'Abgebrochen',
                    description: 'Abgebrochen.',
                },
            },
            label_one: 'Wiederholung',
        },
        labels: {
            failed: {
                label: 'Fehlgeschlagen',
                description: 'Konnte nicht ausgeführt werden',
            },
            voided: {
                label: 'Voided',
                description: 'Nicht ausgeführt',
            },
            aborted: {
                label: 'Aborted',
                description: 'Abgebrochen.',
            },
            success: {
                label: 'Erfolgreich',
                description: 'Erfolgreich ausgeführt',
            },
        },
    },
    maintenance: {
        label: 'Ups, da lief was schief',
        description: 'Wir haben ein technisches Problem festgestellt und arbeiten bereits mit Hochdruck an der Behebung.',
        serverDown: 'Die Verbindung zum Server wurde unterbrochen, bitte laden Sie die Seite neu.',
        serverOk: 'Die Verbindung zum Server wurde wieder hergestellt.',
    },
    management: {
        label: 'Verwaltung',
    },
    mandatoryField: 'Das Feld "{{field}}" ist ein Pflichtfeld.',
    memoryUsage: 'Speicherverbrauch',
    mindTheErrors: 'Bitte beachten Sie die fehlerhafte Felder (rot markiert)',
    minutes: 'Minuten',
    name: 'Schlüssel',
    nameValue: {
        fields: {
            name: 'Name',
            value: 'Wert',
        },
    },
    needHelp: 'Brauchen Sie Hilfe?',
    negateRule: 'Regel umkehren',
    new: 'Neu',
    customDevice: {
        label: 'Eigenes Gerät',
        add: 'Eigenes Gerät hinzufügen',
        edit: 'Gerät bearbeiten',
        fields: {
            name: 'Gerätname',
            userAgent: 'Useragent',
            viewport: {
                label: 'Ansichtsfenster',
                width: 'Breite',
                height: 'Höhe',
                deviceScaleFactor: 'Skalierungsfaktor',
                isMobile: 'Mobile',
                hasTouch: 'Touch sensitive',
                orientation: {
                    label: 'Ausrichtung',
                    portrait: 'Portrait',
                    landscape: 'Landscape',
                },
            },
        },
        validation: {
            name: 'Der Name muss zwischen {{start}} und {{end}} lang sein.',
            nameDuplicate: 'Es existiert bereits ein Gerät mit dem gleichen Namen.',
            userAgent: 'Der User-Agent muss zwischen {{start}} und {{end}} lang sein.',
            viewport: {
                width: 'Die Breite muss zwischen {{start}} und {{end}} liegen.',
                height: 'Die Höhe muss zwischen {{start}} und {{end}} liegen.',
                deviceScaleFactor: 'Der Skalierungsfaktor muss zwischen {{start}} und {{end}} liegen.',
            },
        },
    },
    no: 'Nein',
    noAccessToFunction: 'Diese Funktion ist in Ihrer aktuellem Abonnement nicht verfügbar.',
    notActivated: 'Nicht Aktiviert',
    notAvailable: 'N/A',
    notSavedDueToErrors: 'Ihre letzten Änderungen konnten aufgrund von Eingabefehlern nicht gespeichert werden',
    notice: 'Hinweis',
    or: 'ODER',
    overage: 'Überziehung',
    overview: 'Übersicht',
    packages: 'Pakete',
    page: 'Seite',
    page404: {
        pageTitle: '404 - Seite nicht gefunden',
        description: 'Die Seite, die Sie suchen, existiert nicht.',
        backHome: 'Zur Startseite',
    },
    passwordReset: {
        label: 'Passwort zurücksetzen',
        pageTitle: 'Passwort zurücksetzen',
        title: 'Passwort vergessen?',
        notice: 'Bitte geben Sie die E-Mail-Adresse ein, die mit Ihrem Konto verbunden ist, und wir senden Ihnen einen Link zum Zurücksetzen Ihres Passworts.',
        resend: 'E-Mail erneut senden',
        wasResent: 'Anfrage wurde erfolgreich an die E-Mail-Adresse {{email}} versendet.',
        willBeSent: 'Wir senden Ihnen einen Link zum Zurücksetzen Ihres Passworts an die E-Mail-Adresse {{email}}.',
        validation: {
            registrationError: 'Das Password zurücksetzen hat nicht funktioniert, überprüfen Sie, ob Sie die richtige E-Mail-Adresse eingegeben haben.',
        },
    },
    pasteJSCodeHere: 'Fügen Sie Ihre Logik hier',
    payment: {
        title: 'Gleich ist es soweit!',
        subTitle: 'Ihr Paket ist für Sie vorbereitet.',
        pageTitle: 'Checkout',
        cta: 'Checkout',
        notifications: {
            cancelled: 'Die Transaktion wurde abgebrochen',
            errorPleaseRetry: 'Es ist ein Fehler aufgetreten, bitte versuchen Sie es noch einmal.',
        },
        confirmation: {
            pageTitle: 'Bestätigung',
            title: 'Zahlung erfolgreich!',
            completed: 'Unser Partner {{paymentProvider}} hat eine erfolgreiche Abwicklung der Transaktion gemeldet. Sie erhalten die Rechnung per E-Mail an die Adresse {{email}}.',
            inProgress: 'Ihre Anfrage wird bearbeitet.',
            invoiceDetails: 'Rechnung einsehen',
            startProject: 'Projekt starten',
        },
    },
    position: 'Position',
    previewImageMissing: 'Kein Vorschau',
    pricing: {
        label: 'Shop',
        pageTitle: 'Shop',
        cta: 'Pläne & Preise ansehen',
        title: 'Der passende Plan für jedes Unternehmen und jede Website-Größe',
        description: 'Wählen Sie einen Plan, die zu ihrer private Webseite oder Ihr Unternehmen.',
        buildModeLicenses: {
            label: 'Lizenz Typ',
            types: {
                linkMode: {
                    label: 'Link-Licence',
                },
                pageMode: {
                    label: 'Page-Licence',
                },
            },
        },
        demo: {
            label: 'Demo Paketen',
            payment: {
                label: 'Einmalig',
                paymentDescription: 'Sie zahlen einmalig ein symbolischer Betrag.',
            },
        },
        otp: {
            label: 'Zusatz-Pakete',
            noActiveLabel: 'Paket fehlt!',
            payment: {
                label: 'Einmalig',
                paymentDescription: 'Sie zahlen einmalig',
            },
            collaboratorSeats: {
                label: 'Seats für Mitarbeiter',
                occupied: '{{used}} / {{total}} Seat(s) vergeben',
                description: 'Jeder Mitarbeiter benötigt eine Lizenz, um auf die Plattform zugreifen zu können. Die Lizenzen sind einmalige Käufe und bieten volle Flexibilität bei der Zuweisung und Neuzuweisung von Rollen.',
            },
            automationCredits: {
                label: 'Automation-Credits',
                restContingent: 'Restguthaben: {{rest}} Automation-Credits',
                description: 'Automation-Credits sind Guthaben, die Sie für die Ausführung von Automatisierungsprozessen auf unserer Plattform gebrauchen können. Unterschiedliche Prozesse und Aktionen verbrauchen unterschiedlich viele Credits. Sie können die Credits beliebig oft wieder aufladen.',
                use: 'Automation-Credits verwenden',
                project: 'Projekt wird mit Automation-Credits ausgeführt',
                label_one: 'Automation Credit',
            },
        },
        subscriptions: {
            label: 'Subscriptions',
            saveInfo: 'Sie sparen bis zu {{savingInPercent}}% monatlich mit einer jährlichen Rechnungsstellung.',
            multiPackageInfo: 'Wenn Sie mehr als ein Paket abonnieren möchten, wählen Sie bitte Pakete mit derselben Laufzeit. Für Pakete mit einer anderen Laufzeit schließen Sie bitte zuerst diese Bestellung ab und kommen dann zurück. Vielen Dank für Ihr Verständnis!',
            intervals: {
                monthlyX1: {
                    label: 'Monatlich',
                    perLabel: ' / Monat',
                    paymentDescription: 'Sie zahlen monatlich',
                },
                monthlyX3: {
                    label: 'Quartalsweise',
                    perLabel: ' / Quartal',
                    paymentDescription: 'Sie zahlen alle 3 Monate',
                },
                monthlyX6: {
                    label: 'Halbjährlich',
                    perLabel: ' / 6 Monate',
                    paymentDescription: 'Sie zahlen alle 6 Monate',
                },
                monthlyX12: {
                    label: 'Jährlich',
                    perLabel: ' / Jahr',
                    paymentDescription: 'Sie zahlen alle 12 Monate',
                },
            },
            goto: 'Zu den Abonnements',
        },
        support: {
            label: 'Haben Sie weitere Anliegen?',
            description: 'Kontaktieren Sie uns für individuelle Lösungen oder, wenn Sie eine Demo anfordern möchten.',
        },
        planSelection: 'In den Warenkorb',
        totalPrice: 'Gesamtpreis',
        includesTaxesInfo: '* Zuzüglich Steuern',
        totalSum: 'Gesamtsumme',
        stripeInfo: {
            title: 'Hinweis zur Zahlung',
            description: 'Wir arbeiten mit {{paymentProvider}} zusammen, um Ihnen die sichere Abwicklung Ihrer Zahlung zu ermöglichen.',
            billViaEmail: 'Sie erhalten im Anschluss die Rechnung per E-Mail.',
        },
        cart: {
            items: {
                product: 'Produkt',
                quantity: 'Menge',
                price: 'Preis',
                subTotal: 'Zwischensumme',
            },
        },
        overages: {
            label: 'Katalog für Überzüge / Einheitspreise',
            description: 'Folgend sind sie Überzüge und Einzelpreise für die verschiedenen Dienste. Diese Preise werden angewendet, wenn Sie die Grenzen Ihres Plans überschreiten oder wenn Sie Automatisierungen mit Ihren Automation-Credits ausführen.',
            upTo: 'Bis zu',
            infinity: 'Aufwärts',
            unitAmount: 'Einzelbetrag',
            item: 'Einheit',
            billing: {
                label: 'Die Überzüge werden am Ende jedes Abrechnungszeitraums in Rechnung gestellt.',
                autoRenewal: 'Aktivieren Sie die automatische Verlängerung, um Serviceunterbrechungen zu vermeiden.',
            },
        },
    },
    privacyPolicy: {
        label: 'Datenschutzerklärung',
        pageTitle: 'Datenschutzerklärung',
    },
    products: {
        ai: {
            label: 'AI-Integration',
            activated: 'AI-Integration ist im Paket.',
            notActivated: 'AI-Integration ist nicht im Paket.',
            info: 'Mit der AI-Integration können Sie Ihre Automatisierungsprozesse mit maschinellem Lernen optimieren.',
        },
        notificationProducts: {
            label: 'Benachrichtigungspakete',
            items: {
                emailNotification: {
                    label: 'E-Mail-Benachrichtigungen',
                    activated: 'E-Mail-Benachrichtigungen ist im Paket.',
                    notActivated: 'E-Mail-Benachrichtigungen ist nicht im Paket.',
                    info: 'Sie erhalten nach jedem Projektdurchlauf ein ausführliches Bericht per E-Mail.',
                },
                webhooksNotification: {
                    label: 'Webhook-Benachrichtigungen',
                    activated: 'Webhook-Benachrichtigungen ist im Paket.',
                    notActivated: 'Webhook-Benachrichtigungen ist nicht im Paket.',
                    info: 'Sie erhalten nach jedem Projektdurchlauf die Daten und Ergebnisse an Webhook.',
                },
            },
        },
    },
    project: {
        build: {
            schedule: {
                label: 'Zeitplan',
                edit: 'Zeitplan bearbeiten',
                inactive: 'Geplante Projekt-Läufe sind inaktiv',
                nextBuild: 'Nächster Zeitplan',
                nextBuildList: 'Nächster {{count}} Zeitpläne',
                nextBuildList_one: 'Nächster Zeitplan',
                provisioningServers: 'Der Build beginnt in Kürze.',
            },
            cancellation: {
                label: 'Buildabbruch in Bearbeitung',
                inProgress: 'Abgebrochen von {{email}} am {{dateTime}}.',
                cta: 'Projektdurchlauf abbrechen',
            },
            config: 'Konfiguration',
            confirmation: {
                preparingBuild: 'Der Projektdurchlauf wird vorbereitet.',
                question: 'Projekt "{{projectName}}" wird sofort gebaut werden.',
                continue: 'Forsetzen',
                cancel: 'Abbrechen',
            },
            ctaLabel: 'Zum Projektdurchlauf-Bericht',
            date: 'Datum',
            delete: {
                label: 'Der Projektdurchlauf #{{buildId}} löschen!',
                impact: {
                    irreversible: 'Ich bin mir bewusst, dass diese Aktion nicht rückgängig gemacht werden kann.',
                    relatedData: 'Alle Daten (Bilder, Videos, Projektdurchlauf-Daten, usw.), die mit diesem Projektdurchlauf verknüpft sind, werden gelöscht.',
                },
                notifications: {
                    deleteSuccess: 'Der Projektdurchlauf wurde gelöscht',
                    deleteFailure: 'Der Projektdurchlauf kann nicht gelöscht gelöscht',
                },
            },
            domainApprovalError: {
                label_one: '1 Domain wurde nicht genehmigt',
                label: '{{count}} Domains wurden nicht genehmigt',
                processedDomainList: 'Bereits bearbeitete Domains',
                targetDomainList: 'Die in Ihrem Projektdurchlauf enthaltenen Domains',
                neu: 'Nicht genehmigt',
            },
            domainError: {
                label: 'Der Projektdurchlauf kann nicht durchgeführt werden, weil das Domain-Limit ({{domainContingent}}) erreicht ist.',
                checkedDomainList: 'Bisher geprüfte Domains',
                targetDomainList: 'Die in Ihrem Projektdurchlauf enthaltenen Domains',
                neu: 'Neu',
                recommendation: 'Sie können die neue Domains entfernen oder ein anderes Abonnement verknüpfen.',
            },
            downloads: {
                label: 'Berichte herunterladen',
                optionsLabel: 'Exportoptionen',
                typesLabel: 'Exporttyp',
                options: {
                    basics: {
                        label: 'Basisdaten',
                        description: 'Basisdaten des Projekts, z.B Projektname, Projektstatus, Erstellungsdatum, usw.',
                    },
                    settings: {
                        label: 'Einstellungen',
                        description: 'Einstellungen des Projekts, z.B. URL, Browser, Gerät, usw.',
                    },
                    results: {
                        label: 'Ergebnisse',
                        description: 'Ergebnisse des Projekts, z.B. Anzahl der geprüften URLs, Anzahl der fehlerhaften URLs, usw.',
                    },
                    subscription: {
                        label: 'Abonnement',
                        description: 'Abonnement des Projekts, z.B. Abonnementtyp, Abonnementdauer, usw.',
                    },
                    logs: {
                        label: 'Logs',
                        description: 'Logs des Projekts, z.B. Projekt wurde angelegt, Projekt wurde bearbeitet, usw.',
                    },
                },
                types: {
                    pdf: {
                        label: 'PDF',
                    },
                    json: {
                        label: 'JSON',
                    },
                },
                ready: 'Ihr Download wurde vorbereitet und steht Ihnen jetzt zum Herunterladen zur Verfügung.',
                view: 'Laden Sie Ihren Download herunter',
            },
            duration: 'Dauer',
            endOn: 'Ende des Projektdurchlaufs',
            erroneous: 'Der Projektdurchlauf konnte nicht erfolgreich abgeschlossen werden.',
            failStop: {
                label: 'Fail-Stop',
                description: 'Wenn ein Step fehlschlägt, wird das Projekt abgebrochen.',
                trigger: 'Fail-Stopper',
                triggered: 'Der Projektdurchlauf wurde abgebrochen, weil dieser Step fehlgeschlagen ist.',
            },
            id: 'Nummer des Projektdurchlaufs',
            indexedBuild: 'Der Projektdurchlauf #{{buildId}}: {{state}}',
            label: 'Projektdurchläufe',
            lastFailure: 'Letzter Fehlschlag',
            lastSuccessful: 'Letzter Erfolg',
            latestBuild: 'Letzter Projektdurchlauf',
            loadData: 'Daten laden',
            navigation: {
                first: 'Ältester Projektdurchlauf',
                previous: 'Vorheriger Projektdurchlauf',
                next: 'Nächster Projektdurchlauf',
                last: 'Neuester Projektdurchlauf',
            },
            noData: 'Es liegen noch keine Projektdurchläufe für dieses Projekt vor.',
            notEditableByIsRunning: 'Das Projekt baut gerade und kann daher nicht bearbeitet werden.',
            notFound: 'Der Projektdurchlauf ({{buildId}}) könnte nicht gefunden werden',
            report: {
                error: 'Fehler beim Speichern des Berichts',
                title: '{{app}} Projektdurchlaufsbericht vom {{dateTime}} ({{tz}})',
                buildInfo: {
                    index: '#',
                    label: 'Der Projektdurchlauf Daten',
                },
                results: {
                    total: 'Gesamte Versuche',
                    success: 'Erfolgreich abgeschlossen',
                    failure: 'Gescheiterte Versuche',
                },
                summary: {
                    title: 'Zusammenfassung',
                    subTitle: 'Übersicht',
                    label: '{{solution}} Übersicht',
                    resultLabel: '{{solution}} Bericht',
                    total: 'Qualifizierter URLs',
                    buildTime: 'Verarbeitungszeit',
                    ignored: 'Ignorierte URLs',
                    invalid: 'Ungültige URLs',
                    success: 'Erfolgreiche URLs',
                    failure: 'Fehlerhafte URLs',
                    share: 'Anteil',
                    delta: 'Δ',
                },
                nextExecution: {
                    label: '{{dateTime}} ({{timeZone}})',
                },
                buildReference: {
                    label: 'Projektdurchlauf-Referenz',
                },
            },
            result: {
                label: 'Ergebnis',
                loadError: 'Die Ergebnisdaten können nicht geladen werden oder sind nicht mehr verfügbar',
                selectResultUrlInfo: 'URL auswählen',
                selectResultSubUrlInfo: 'Unterseite auswählen',
                noUrlSelectedInfo: 'Nichts ausgewählt, nichts anzuzeigen!',
                levelInfo: '{{level}} Ebene',
                moreLevel: 'Unterseiten',
                exclusion: {
                    cta: '{{count}} ausgeschlossene URLs anzeigen',
                    title: 'Es wurden {{count}} URLs ausgeschlossen',
                    placeholder: 'URL auswählen, um Details anzuzeigen',
                    label: 'Ausschlussgrund',
                    reasons: {
                        format: 'URL Format.',
                        visited: 'URL wurde schon berabeitet.',
                        normalization: 'Duplikat nach Normalisierung.',
                        regex: 'Zeichenfolgen Einstellungen.',
                        subdomain: 'Subdomain Einstellungen.',
                        external: 'Ist externer URL.',
                        internal: 'Ist interner URL.',
                    },
                },
                html: {
                    label: 'Quellcode',
                    hierarchy: 'URL-Hierarchie',
                    sourceCode: 'Quellcode-Auszug',
                    markup: 'Markup',
                    htmlSelector: 'Query/Selektor',
                    normalizedUrl: 'URL nach Normalisierung',
                },
            },
            screenshot: {
                label: 'Screenshot',
            },
            settingsNotEditableByHasRun: 'Bereich "{{label}}" kann nicht mehr bearbeitet werden, weil das Projekt wurde schon mindestens einmal ausgeführt wurde.',
            showConfig: 'Konfiguration aufrufen',
            startedOn: 'Der Projektdurchlauf Start',
            stop: 'Der Projektdurchlauf abbrechen',
            timeline: 'Logs ({{count}})',
            trigger: {
                label: 'Auslösung',
                vendors: {
                    manual: {
                        label: 'Manuell',
                        description: 'Dieser Projektdurchlauf wurde manuell ausgelöst',
                    },
                    auto: {
                        label: 'Geplant',
                        description: 'Dieser Projektdurchlauf wurde durch einen geplanten Job ausgelöst.',
                    },
                    manager: {
                        label: 'WebAutomate AI',
                        description: 'Dieser Projektdurchlauf wurde durch den AI-Agent von WebAutomate ausgelöst.',
                    },
                },
            },
            states: {
                none: {
                    label: 'Ausstehend',
                    description: 'Der Projekt-Lauf steht aus',
                },
                timeout: {
                    label: 'Zeitüberschreitung',
                    description: 'Der Projekt-Lauf hat eine Zeitüberschreitung',
                },
                running: {
                    label: 'Läuft',
                    description: 'Der Projekt läuft',
                },
                queue: {
                    label: 'In Warteschlange',
                    description: 'Der Projekt-Lauf steht in der Warteschlange',
                },
                cancel: {
                    label: 'Abgebrochen',
                    description: 'Der Projekt-Lauf wurde abgebrochen',
                },
                success: {
                    label: 'Erfolgreich',
                    description: 'Der Projekt-Lauf war erfolgreich',
                },
                failure: {
                    label: 'Fehlgeschlagen',
                    description: 'Der Projekt-Lauf ist fehlgeschlagen',
                },
                mixed: {
                    label: 'Gemischt',
                    description: 'Der Projekt-Lauf hat gemischte Ergebnisse',
                },
                terminated: {
                    label: 'Abgebrochen',
                    description: 'Der Build wurde vom System abgebrochen.',
                },
            },
            deleteAll: {
                label: 'Alle Builds löschen!',
                impact: {
                    irreversible: 'Ich bin mir bewusst, dass diese Aktion nicht rückgängig gemacht werden kann.',
                    relatedData: 'Alle Daten (Bilder, Videos, Projektdurchlauf-Daten, usw.), die mit den Builds verknüpft sind, werden gelöscht.',
                },
                notifications: {
                    deleteSuccess: 'Alle Builds wurden gelöscht',
                    deleteFailure: 'Builds konnten nicht gelöscht werden',
                },
            },
        },
        buildPlan: 'Projektdurchlauf-Plan',
        clone: {
            label: 'Projekt klonen',
            prefix: 'Klon von',
            info: 'Klicken Sie auf ein Projekt, um dies zu klonen.',
            notice: 'Ihre aktuelle Einstellungen im Projektassistenten werden überschrieben.',
            success: 'Projekt "{{projectName}}" wurde erfolgreich geklont.',
        },
        configuration: {
            label: 'Projektkonfiguration',
            error: 'Fehler in der Konfiguration erkannt. Bitte überprüfen Sie Ihre Einstellungen.',
            preview: 'Projektkonfiguration einblenden',
            version: 'Version',
        },
        configuratorTutorial: 'Projekt Konfigurator Tutorial',
        create: 'Neues Projekt erstellen',
        createdBy: 'Erstellt von',
        createdOn: 'Erstellt am',
        delete: {
            label: 'Projekt Löschen!',
            impact: {
                irreversible: 'Dieser Vorgang kann nicht rückgängig gemacht werden.',
                relatedData: 'Alle Daten (Bilder, Videos, Projektdurchlauf-Daten, usw.), die mit diesem Projekt verknüpft sind, werden gelöscht.',
            },
            notifications: {
                deleteSuccess: 'Projekt wurde gelöscht',
                deleteFailure: 'Projekt kann nicht gelöscht gelöscht',
            },
        },
        edit: {
            label: 'Projekt bearbeiten',
            save: 'Projekt aktualisieren',
        },
        editedBy: 'Erstellt von',
        editedOn: 'Bearbeitet am',
        empty: 'Keine Projekte gefunden',
        emptyInfo: 'Die Liste aller Projekte wird hier angezeigt. Es sieht so aus, als hätten Sie noch kein Projekt begonnen.',
        cta: 'Zum Projekt',
        helpRequiredQuestion: 'Benötigen Sie Hilfe beim Aufsetzen eines Projekts?',
        info: 'Steuerung',
        label: 'Assistent für neue Projekte',
        list: {
            label: 'Projekte',
            filter: 'Projekte filtern...',
            fields: {
                name: 'Projektname',
                solution: 'Lösung',
                state: 'Status',
                creator: 'Erstellt von',
                created: 'Erstellt am',
                duration: 'Dauer',
            },
            addField: 'Spalten bearbeiten',
            showArchived: 'Archivierte Projekte anzeigen',
            search: {
                emptyLabel: 'Keine Treffer',
                notice: 'Keine Ergebnisse für "{{searchQuery}}" gefunden. Versuchen Sie, nach Tippfehlern zu suchen oder vollständige Wörter zu verwenden.',
            },
        },
        load: {
            label: 'Projekt laden',
            info: 'Klicken Sie auf ein Projekt, um dies zu laden.',
            notice: 'Die Einstellungen im Projektassistenten werden überschrieben.',
            success: 'Projekt "{{projectName}}" wurde erfolgreich geladen.',
        },
        motivation: 'In jeder Branche ist die Website die erste Anlaufstelle für viele Interessengruppen. Ein positives Nutzererlebnis und eine SEO-optimierte Webpräsenz sind daher unumgänglich.',
        motivation2: 'Verschwende keine Zeit mit wiederholenden Aufgaben auf deiner Website. Lass {{app}} das für dich erledigen!',
        name: {
            label: 'Projektname',
            notifications: {
                required: 'Projektname ist erforderlich.',
                length: 'Projektname darf nicht länger als {{max}} Zeichen sein.',
                enterProjectName: 'Geben Sie eine gültigen Projektnamen ein, mit der Sie dieses Projekt in Berichten und Benachrichtigungen leicht wiedererkennen.',
            },
        },
        new: {
            label: 'Neues Projekt',
            resetPrompt: {
                label: 'Konfiguration zurücksetzen',
                description: 'Wenn Sie die Konfiguration zurücksetzen, werden alle Einstellungen im Projektassistenten gelöscht.',
                cancel: 'Abbrechen',
                confirm: 'Ja, zurücksetzen',
            },
            success: 'Konfiguration wurde zurückgesetzt.',
            save: 'Projekt speichern',
        },
        notFound: 'Projekt wurde nicht gefunden',
        originalURL: 'URL vor Normalisierung',
        pageTitle: 'Assistent für neue Projekte',
        requestedURL: 'Angefragte URL',
        resolvedURL: 'Aufgelöste URL',
        resolvedURLFromCache: {
            title: 'Sie sehen gecachte Daten.',
            message: 'Die angeforderte URL "{{url}}" wird zu der zuvor verarbeiteten URL "{{resolvedUrl}}" aufgelöst.',
        },
        result: {
            overview: {
                label: 'Übersicht',
            },
            settings: {
                label: 'Einstellungen',
                timeZone: {
                    label: 'Zeitzone',
                },
            },
            history: {
                label: 'Logs',
            },
        },
        run: 'Projekt ausführen',
        runShort: 'Ausführen',
        setupPageTitle: 'Projekt Konfigurator',
        state: 'Zustand',
        stateTypes: {
            draft: {
                label: 'Entwurf',
            },
            active: {
                label: 'Aktiv',
                cta: 'Jetzt aktivieren',
            },
            pause: {
                label: 'Pausiert',
                cta: 'Jetzt pausieren',
            },
            archive: {
                label: 'Archiviert',
                cta: 'Jetzt archivieren',
                info: 'Das Projekt wurde archiviert, Sie müssen es wiederherstellen, um daran weiterzuarbeiten.',
                unarchive: 'Unarchivieren',
            },
        },
        url: 'URL',
        wizard: 'Projektassistenten',
        wizardLoginRequired: 'Sie müssen sich anmelden, um ein Projekt zu erstellen.',
        wizardSteps: {
            session: {
                started: 'Projektassistenten-Session mit ID "{{sessionId}}" wurde gestartet.',
            },
            solutionSelection: {
                label: 'Lösung',
                previewLabel: 'Lösungsart',
                select: 'Lösungsart auswählen',
                description: 'Klicken Sie auf die jeweilige Optionen, um zu aktivieren.',
                change: {
                    label: 'Sie sind dabei, die Lösungsart von "{{solution1}}" nach "{{solution2}}" zu wechseln. Was soll mit den Steps geschehen?',
                    deleteAll: 'Alle Steps löschen',
                    deleteSolution: 'Nur Steps vom Type {{solution1}} löschen.',
                    stepsNotRequired: 'Es werden alle Steps gelöscht, weil für {{solution2}} keine Steps benötigt werden.',
                },
            },
            target: {
                label: 'Grundeinstellungen',
                previewLabel: 'Grundeinstellungen',
                checkType: {
                    label: 'Modus',
                    mode: 'Modus',
                    groups: {
                        link: {
                            label: 'Einzelne URLs',
                            ctaLabel: 'Geben Sie eine URL ein.',
                            description: 'Alle eingetragenen URLs werden verarbeitet.',
                        },
                        page: {
                            label: 'Seiten',
                            ctaLabel: 'Geben Sie eine Seitenadresse ein.',
                            description: 'Sie geben eine URL ein. Unserer AI-Agent analysiert die Seite anhand einiger vordefinierter Regeln und extrahiert alle qualifizierte URLs zur Verarbeitung.',
                        },
                    },
                    moreLabel: 'URL hinzufügen',
                },
                executionStrategy: {
                    label: 'Projektdurchlaufsstrategie',
                    useCases: 'Anwendungsfälle',
                    groups: {
                        sharedBrowserInstance: {
                            label: 'Geteilte Browser-Instanzen',
                            description: 'Beim Projektdurchlauf wird jede URL in einem einzigen Browser-Kontext geöffnet. Dies bedeutet, dass Cookies, Sitzungsspeicher und lokaler Speicher zwischen URLs geteilt werden.',
                            useCase: 'Dies ist geeignet für das Testen mehrerer unabhängiger Seiten, die möglicherweise dieselben Sitzungsinformationen teilen, aber gleichzeitig für Vergleichs- oder Paralleltests geöffnet werden müssen.',
                        },
                        isolatedBrowserInstances: {
                            label: 'Isolierte Browser-Instanzen',
                            description: 'Beim Projektdurchlauf wird jede URL in einem vollständig isolierten Browser-Kontext geöffnet. Dies bedeutet, dass keine Cookies, Sitzungsspeicher oder lokaler Speicher zwischen URLs geteilt werden.',
                            useCase: 'Ideal für Szenarien, in denen vollständige Isolation erforderlich ist, z. B. das Testen unabhängiger Sitzungen, das Vermeiden von Querseitenkontamination oder das Sicherstellen eindeutiger Umgebungen für jede URL.',
                        },
                    },
                },
                browser: {
                    label: 'Browser',
                    description: 'Wählen Sie das Browser, mit dem das Projekt ausgeführt wird.',
                    lighthouseDescription: 'Lighthouse Projekte werden nur mit dem Chrome-Browser ausgeführt.',
                    groups: {
                        chromium: {
                            label: 'Chrome',
                        },
                        firefox: {
                            label: 'Firefox',
                        },
                        safari: {
                            label: 'Safari',
                        },
                    },
                },
                device: {
                    label: 'Gerät',
                    labelMultiple: 'Geräte',
                    description: 'Wählen Sie das Gerät, mit dem das Projekt ausgeführt wird.',
                    errorMessage: 'Bitte legen Sie ein Gerät fest.',
                    lighthouse: {
                        targets: {
                            mobile: 'Mobil',
                            desktop: 'Desktop',
                        },
                    },
                },
            },
            linkOptions: {
                label: 'URL Konfiguration',
                previewLabel: 'URL Konfiguration',
                groups: {
                    urlSelection: {
                        label: 'URLs finden',
                        title: 'wo nach URLs suchen?',
                        description: 'Unserer AI-Agent durchsucht alle aufgefundene URLs. In diesem Bereich können Sie jedoch festlegen, auf welchen Bereichen in ihren HTML-Dokumenten die Suche nach URLs eingeschränkt werden soll.',
                        fields: {
                            selector: {
                                label: 'Linkabfrage-Selektor',
                                description: 'Mit JavaScript Kenntnisse können Sie hier eigene Selektoren angeben',
                            },
                            tags: {
                                label: 'Link Source Tags',
                                description: 'Zusätzlich können Sie die HTML-Tags auswählen, wo die interessante URLs ausgelesen werden.',
                                selectAll: 'Alle auswählen',
                                deselectAll: 'Alle abwählen',
                            },
                        },
                    },
                    urlNormalization: {
                        label: 'URLs normalisieren',
                        title: 'Umgang mit ähnlichen URLs.',
                        description: 'Legen Sie in diesem Bereich fest, wie URLs normalisiert werden, damit festgestellt werden kann, ob zwei syntaktisch unterschiedliche URLs gleichwertig sind.',
                        fields: {
                            stripAuthentication: {
                                label: 'Authentication Teil entfernen',
                            },
                            stripHash: {
                                label: 'Hash entfernen',
                            },
                            stripWWW: {
                                label: '"www." auf allen Links entfernen',
                            },
                            removeQueryParameters: {
                                label: 'Query-Parameter entfernen',
                            },
                            removeTrailingSlash: {
                                label: 'Hinterer Schrägstriche bei Pfadnamen entfernen',
                                helperText: 'Schrägstriche werden immer entfernt, wenn keine Pfade vorhanden sind.',
                            },
                        },
                        result: 'Ergebnis nach der Normalisierung: {{result}}',
                    },
                    urlFiltering: {
                        label: 'URLs filtern',
                        title: 'URLs filtern',
                        description: 'Unserer AI-Agent führt die Services per default auf allen URLs aus. Falls Sie dies nicht möchten, so können Sie hier Filter setzen. URLs, die nicht zu den Filter-Regel passen, werden ignoriert.',
                        fields: {
                            useResolvedURLBase: {
                                label: 'Filter Basis',
                                description: 'Sollte die eingebene URL anders aufgelöst werden (z-B Umleitung von https://www.meine-webseite.com auf https://m.meine-webseite.com auf Mobilgeräten). Können Sie hier bestimmen, ob die originale URL als Basis erhalten bleibt oder die umgeleitete URL als Basis genommen wird.',
                                option: 'Aufgelöste URL als Basis verwenden',
                                optionAlt: 'Ursprungliche URL als Basis beibehalten',
                            },
                            subdomains: {
                                label: 'Subdomains',
                                description: 'Deaktivieren Sie diese Option, um Subdomains auszuschliessen.',
                                notification: 'Diese Option greift nicht, wenn die "{{useResolvedURLBaseLabel}}" ein Subdomain ist.',
                                option: 'Subdomain einschliessen',
                                optionAlt: 'Subdomain einschliessen',
                            },
                            externalLinks: {
                                label: 'Externe URLs',
                                description: 'Aktivieren Sie diese Option, um URLs fremder Domains auf Ihrer Webseite einzuschliessen.',
                                option: 'Externe URLs einschliessen',
                                optionAlt: 'Externe URLs einschliessen',
                            },
                            internalLinks: {
                                label: 'Interne URLs',
                                description: 'Deaktivieren Sie diese Option, um interne URLs auszuschliessen.',
                                option: 'Interne URLs einschliessen',
                                optionAlt: 'Interne URLs einschliessen',
                            },
                            urlMatch: {
                                label: 'URL',
                                title: 'In diesem Bereich legen Sie Regeln für die Zeichenfolgen der URLs an, die von der AI berücksichtigt werden.',
                            },
                        },
                    },
                },
            },
            requestOptions: {
                label: 'Request & Browser',
                previewLabel: 'Request und Browser Einstellungen',
                groups: {
                    browser: {
                        label: 'Browser Einstellungen',
                        title: '',
                        description: 'Legen Sie die Browser-Sprache hier fest',
                        fields: {
                            language: {
                                label: 'Browser Sprache',
                            },
                            deactivateJS: {
                                label: 'JavaScript deaktivieren',
                                description: 'Deaktivieren Sie die Ausführung von JavaScript.',
                            },
                            waitUntil: {
                                label: 'Eine URL-Aufruf ist fertig',
                                description: 'Legen Sie fest, wann die Seite als vollständig geladen betrachtet wird.',
                                options: {
                                    domcontentloaded: {
                                        label: 'Wenn das Event "DOMContentLoaded" ausgelöst wird.',
                                    },
                                    load: {
                                        label: 'Wenn das Event "load" ausgelöst wird.',
                                    },
                                    networkidle0: {
                                        label: 'Wenn von der Seite aus 500ms keine Anfragen rausgeht.',
                                    },
                                    commit: {
                                        label: 'Wenn das Netzwerk geantwortet hat und das Dokument angefangen hat zu laden.',
                                    },
                                    js: {
                                        label: 'Wenn diese JavaScript Bedingung erfüllt ist.',
                                        code: {
                                            label: 'JavaScript Code',
                                            edit: 'Code bearbeiten',
                                            description: 'JavaScript Code, der ausgeführt wird, um zu prüfen, ob die Seite fertig geladen ist.',
                                        },
                                    },
                                },
                            },
                            linkRequestTimeout: {
                                label: 'Zeitüberschreitung beim URL-Aufruf',
                                description: 'Wenn unsere AI beim URL-Aufruf nach {{duration}} keine Antwort erhält, wird der URL als fehlerhaft gemeldet.',
                            },
                        },
                    },
                    storage: {
                        label: 'Browser Speicher',
                        title: '',
                        description: 'In dieser Sandbox können Sie Parameter, Anfrage-Header, Cookies und Speichermechanismen für Ihr Projekt anpassen.',
                        fields: {
                            requestCookies: {
                                title: 'Cookies',
                                keyLabel: 'Name',
                                valueLabel: 'Wert',
                                label: 'Cookies',
                                description: 'Cookies zum Browser hinzufügen.',
                                moreLabel: 'Request Cookie Eintrag hinzufügen',
                            },
                            sessionStorage: {
                                title: 'Sessionstorage Einträge',
                                keyLabel: 'Schlüssel',
                                valueLabel: 'Wert',
                                label: 'Sessionstorage Eintrag',
                                description: 'Sessionstorage Einträge zum Browser hinzufügen.',
                                moreLabel: 'Sessionstorage Eintrag hinzufügen',
                            },
                            localStorage: {
                                title: 'Localstorage Einträge',
                                keyLabel: 'Schlüssel',
                                valueLabel: 'Wert',
                                label: 'Localstorage Eintrag',
                                description: 'Localstorage Einträge zum Browser hinzufügen.',
                                moreLabel: 'Localstorage Eintrag hinzufügen',
                            },
                        },
                    },
                    resources: {
                        label: 'Netzwerkaktivität',
                        title: '',
                        description: 'Steuern Sie die Netzwerkaktivität, indem Sie bestimmen, wie Webressourcen modifiziert oder sogar gesperrt werden. Webressourcen sind Bilder, CSS-, JS- und andere Daten, die für die ordnungsgemäße Anzeige und Funktionalität der Webseite erforderlich sind.',
                        fields: {
                            requestHeaders: {
                                title: 'Request Headers',
                                label: 'Request Header einfügen',
                                keyLabel: 'Schlüssel',
                                valueLabel: 'Wert',
                                emptyValueInfo: 'Lassen Sie den Header-Wert leer, um das Header zu löschen',
                                description: 'Legen Sie hier fest, welche Header bei URL-Aufruf mitgesendet werden.',
                                moreLabel: 'Request Header hinzufügen',
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                            },
                            requestParameters: {
                                title: 'Request Parameters',
                                label: 'Request Parameter einfügen',
                                keyLabel: 'Parameter',
                                valueLabel: 'Wert',
                                description: 'Legen Sie hier fest, welche Parameter beim URL-Aufruf an die URL angehängt werden.',
                                moreLabel: 'Request Parameter hinzufügen',
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                            },
                            resourcesInjection: {
                                title: 'Ressourcen',
                                label: 'Ressourcen einfügen',
                                description: 'Fügen Sie Ressourcen (CSS oder JS) in jede Seite ein.',
                                source: {
                                    label: 'URL',
                                    placeholder: 'Geben Sie hier die URL der Ressourcen ein',
                                },
                                content: {
                                    label: 'Inhalt',
                                    placeholder: 'Hier den den Inhalt eingeben',
                                },
                                contentType: {
                                    label: 'Inhaltstyp',
                                    js: 'JavaScript',
                                    css: 'CSS',
                                    html: 'HTML Snippet',
                                },
                                htmlResourceLocation: {
                                    label: 'Einfügen in',
                                    head: {
                                        label: '<head>...',
                                    },
                                    body: {
                                        label: '<body>...',
                                    },
                                },
                                htmlResourcePosition: {
                                    label: 'Position',
                                    start: {
                                        label: 'Anfang',
                                    },
                                    end: {
                                        label: 'End',
                                    },
                                },
                                injectionType: {
                                    label: 'Art',
                                    url: 'URL einfügen',
                                    urlLabel: '{{contentType}} aus "{{source}}" einfügen.',
                                    customContent: 'Eigene Inhalte einfügen',
                                    customContentLabel: 'Eigene Inhalte einfügen',
                                    customContentView: 'Inhalt anzeigen',
                                },
                                validation: {
                                    invalid: 'Die URL muss eine gültige URL sein.',
                                    duplicated: 'Die URL wurde schon erfasst.',
                                    content: 'Der Inhalt ist erforderlich.',
                                },
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                                urlFilter: {
                                    label: 'URL Filter',
                                    options: {
                                        allUrls: {
                                            label: 'Alle URLs',
                                        },
                                        projectUrls: {
                                            label: 'Nur Projekt-URLs',
                                        },
                                        regex: {
                                            label: 'Regex',
                                        },
                                    },
                                },
                            },
                            requestBlockers: {
                                title: 'HTTP-Blockers',
                                label: 'HTTP Request Blockieren',
                                description: 'Sperren Sie API-Calls gegen Endpoints.',
                                itemListTitle: 'HTTP Request blockieren',
                                firstMatchInfo: 'Es wird immer die erste passende Blockierung angewendet.',
                                preview: {
                                    standard: '{{method}} Requests an "{{value}}" ({{matcher}}) blockieren',
                                },
                                errorCode: {
                                    label: 'Fehlercode',
                                    options: {
                                        aborted: 'Der Vorgang wurde abgebrochen (aufgrund einer Benutzeraktion).',
                                        accessdenied: 'Die Erlaubnis zum Zugriff auf eine andere Ressource als das Netzwerk wurde verweigert.',
                                        addressunreachable: 'Die IP-Adresse ist unerreichbar. Dies bedeutet in der Regel, dass es keine Route zu dem angegebenen Host oder Netzwerk gibt.',
                                        blockedbyclient: 'Der Client hat sich entschieden, die Anfrage zu blockieren.',
                                        blockedbyresponse: 'Die Anfrage ist fehlgeschlagen, weil die Antwort mit Anforderungen geliefert wurde, die nicht erfüllt sind.',
                                        connectionaborted: 'Die Verbindung wurde unterbrochen, weil keine ACK für die gesendeten Daten empfangen wurde.',
                                        connectionclosed: 'Die Verbindung wurde geschlossen.',
                                        connectionfailed: 'Der Verbindungsversuch ist fehlgeschlagen.',
                                        connectionrefused: 'Der Verbindungsversuch wurde abgelehnt.',
                                        connectionreset: 'Die Verbindung wurde zurückgesetzt.',
                                        internetdisconnected: 'Die Internetverbindung ist unterbrochen worden.',
                                        namenotresolved: 'Der Hostname konnte nicht aufgelöst werden.',
                                        timedout: 'Der Vorgang wurde abgebrochen.',
                                        failed: 'Ein allgemeiner Fehler ist aufgetreten.',
                                    },
                                },
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                            },
                            requestMocks: {
                                title: 'HTTP-Requests mocken',
                                label: 'HTTP-Requests Mocks hinzufügen',
                                description: 'Sie können API-Calls abfangen und eine selbst definierte Antwort zurückgeben, ohne dass der eigentliche Endpoint aufgerufen wird.',
                                itemListTitle: 'API mocken',
                                firstMatchInfo: 'Nur das erste passende Mock wird angewendet.',
                                preview: {
                                    standard: '{{method}} Requests an "{{value}}" ({{matcher}}) mocken',
                                },
                                options: {
                                    method: {
                                        label: 'Methode',
                                    },
                                    status: {
                                        label: 'Status Code',
                                        validation: {
                                            invalid: 'Sie müssen einen Status-Code angeben.',
                                            range: 'Status-Code muss eine 3 Stellige Zahl sein.',
                                        },
                                    },
                                    contentType: {
                                        label: 'Content-Type',
                                        content: 'Response-Daten',
                                    },
                                    headers: {
                                        title: 'Headers',
                                        label: 'Headers hinzufügen',
                                        keyLabel: 'Schlüssel',
                                        valueLabel: 'Wert',
                                    },
                                    body: {
                                        label: 'Response Text',
                                    },
                                    json: {
                                        label: 'JSON',
                                    },
                                },
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                            },
                            requestOverrides: {
                                title: 'HTTP-Requests',
                                label: 'HTTP-Requests modifizieren',
                                description: 'Sie können die Headers und Body von HTTP-Anfragen ändern, bevor sie gesendet werden.',
                                reducer: 'Reduzierungsfunktion',
                                ctaLabel: 'Die Funktion bekommt die originale Request-Daten bereitgestellt und muss die Daten (mit oder ohne Änderungen) im gleichen Typ zurückgeben, andernfalls werden die originale Daten beibehalten.',
                                itemListTitle: 'HTTP-Requests Modifikationen',
                                firstMatchInfo: 'Nur die erste passende Modifikation wird angewendet.',
                                preview: {
                                    standard: '{{method}} Requests an "{{value}}" ({{matcher}}).',
                                },
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                            },
                            responseOverrides: {
                                title: 'HTTP-Responses',
                                label: 'HTTP-Responses modifizieren',
                                description: 'Sie können hier die Headers und Body von HTTP-Responses modifizieren, bevor dies auf dem Browser ankommen.',
                                reducer: 'Reduzierungsfunktion',
                                ctaLabel: 'Die Funktion bekommt die originale Response-Daten bereitgestellt und muss die Daten (mit oder ohne Änderungen) im gleichen Typ zurückgeben, andernfalls werden die originale Daten beibehalten.',
                                itemListTitle: 'HTTP-Responses Modifikationen',
                                firstMatchInfo: 'Nur die erste passende Modifikation wird angewendet.',
                                preview: {
                                    standard: '{{method}} Responses vom "{{value}}" ({{matcher}}).',
                                },
                                notifications: {
                                    deleteSuccess: 'Eintrag wurde gelöscht',
                                },
                            },
                        },
                    },
                },
            },
            solutionsConfigs: {
                label: 'Projekt konfiguration',
                previewLabel: '{{solution}} Workflow-Konfiguration',
                steps: {
                    label: 'Der Projektdurchlauf Steps',
                    labelCount: '{{count}} Steps',
                    labelCount_one: '1 Step',
                    labelCount_zero: '',
                },
                groups: {
                    overview: {
                        label: 'Projektinfo',
                    },
                    linkChecker: {
                        label: 'Page-Load',
                        captured: 'Es wurden {{count}} Links geprüft.',
                        captured_zero: 'Es wurden keine Links geprüft.',
                        captured_one: 'Es wurde 1 Link geprüft.',
                        resultBlock: 'Page-Load Bericht',
                        teaser: {
                            descriptions: [
                                'Stellen Sie mit unserem automatisierten Link-Prüfer sicher, dass alle Links auf Ihrer Website korrekt funktionieren und frei von Fehlern sind. Ein unverzichtbares Tool, um die Integrität Ihrer Website zu gewährleisten.',
                            ],
                        },
                        status: 'Status',
                        statusText: 'Status-Text',
                        contentType: 'Content-Type',
                        start: 'Verarbeitungsbeginn',
                        end: 'Verarbeitungsende',
                        result: {
                            label: 'Ergebnis',
                            success: 'Link wurde erfolgreich verarbeitet',
                            failure: 'Verarbeitung des Links fehlgeschlagen',
                        },
                    },
                    urlChallenge: {
                        label: 'URL-Challenge',
                        summary: 'Mit der "URL-Challenge"-Funktion können URLs auf eine Reihe von Herausforderungen getestet werden.',
                        title: 'Stellen Sie Ihre URLs jetzt auf die Probe!',
                        failStopLabel: 'Challenge abbrechen, wenn dieser Step fehlschlägt.',
                        failStopAppliedLabel: 'Challenge wurde abgebrochen, weil der Step "{{label}}" fehlschlägen ist.',
                        selectOption: 'Option auswählen',
                        captured: 'Es wurden {{count}} Challenges an {{participants}} gestellt.',
                        captured_zero: 'Es wurden keine Challenges gestellt.',
                        captured_one: 'Es wurde 1 Challenge an {{participants}} gestellt.',
                        modes: {
                            label: 'Modus',
                            options: {
                                eliminationMode: {
                                    label: 'Elimination Mode',
                                    description: 'Jede URL wird durch die Tests geführt. Wenn eine URL einen Test nicht besteht, wird sie eliminiert und nicht weiter getestet. Der Testzyklus endet, wenn alle URLs entweder alle Tests bestanden haben oder eliminiert wurden.',
                                },
                                comprehensiveMode: {
                                    label: 'Comprehensive Mode',
                                    description: 'Jede URL durchläuft alle definierten Tests unabhängig davon, ob sie einen Test besteht oder nicht.',
                                },
                            },
                        },
                        teaser: {
                            descriptions: [
                                'Die URL-Challenge ermöglicht es Ihnen, die Leistung und Zuverlässigkeit von URLs auf Ihrer Website anhand einer Reihe vordefinierter Herausforderungen zu testen. Sie stellt sicher, dass Ihre URLs den Qualitätsstandards entsprechen und wie erwartet funktionieren.',
                                'Sie können die URL-Challenge auf Seiteninteraktionen, Ladeverhalten oder spezifische Seitenelemente stützen. Alternativ können Sie eine individuelle Herausforderung beschreiben, und unsere AI wird Ihre URLs entsprechend bewerten.',
                            ],
                        },
                        items: {
                            title: 'Challenge Steps',
                        },
                        itemLabelling: {
                            distinct: 'URL-Challenges müssen eindeutig benannt werden.',
                        },
                        labels: {
                            passed: {
                                label: 'Bestanden',
                                latest: 'Dieser Challenge wurde bestanden.',
                            },
                            notPassed: {
                                label: 'Nicht bestanden',
                                latest: 'Dieser Challenge wurde nicht bestanden.',
                            },
                            failed: {
                                label: 'Fehlerhaft',
                                latest: 'Es ist ein Fehler bei der Ausführung des Challenges passiert.',
                            },
                            aborted: {
                                label: 'Abgebrochen',
                                latest: 'Dieser Challenge wurde abgebrochen.',
                            },
                            skipped: {
                                label: 'Übersprungen',
                                latest: 'Dieser Challenge wurde übersprungen.',
                            },
                            voided: {
                                label: 'Nicht angetreten',
                                latest: 'Dieser Challenge wurde nicht angetreten.',
                            },
                            participants: {
                                label: 'Anzahl der Teilnehmer',
                                latest: 'Anzahl der Teilnehmer an dieser Challenge.',
                            },
                            totalChallenges: {
                                label: 'Anzahl der Challenges',
                                latest: 'Anzahl der gestellten Challenges.',
                            },
                            passedChallenges: {
                                label: 'Bestandene Challenges',
                                latest: 'Anzahl der bestandenen Challenges.',
                            },
                            failedOrVoidChallenges: {
                                label: 'Nicht bestandene Challenges.',
                                latest: 'Anzahl der fehlgeschlagenen oder übersprungenen Challenges.',
                            },
                        },
                        groups: {
                            automated: {
                                label: 'Natürliche Sprache',
                                options: {
                                    ai: {
                                        label: 'AI-Powered URL-Challenge',
                                        title: 'Herausforderung spezifizieren',
                                        description: 'Sie beschreiben Herausforderungen in jeder Sprache, die vom AI-Assistenten nach Analyse der aktiven Seite und sonstigen technischen Informationen ausgewertet werden.',
                                        indecisiveCase: {
                                            label: 'Wenn sich das AI-Assistent nicht sicher ist',
                                            description: 'Wählen Sie aus, wie diese Herausforderung bewertet werden soll, wenn sich der AI-Assistent nicht sicher ist.',
                                            options: {
                                                isPassed: 'Herausforderung als Bestanden markieren',
                                                isFailed: 'Herausforderung als Gescheitert markieren',
                                                isVoided: 'Herausforderung überspringen und nicht bewerten',
                                            },
                                        },
                                        disclaimer: {
                                            label: 'Haftungsausschluss',
                                            description: 'Herausforderung mit AI-Unterstützung basieren auf maschinellem Lernen und können nicht immer die Erwartungen erfüllen. Wenn Sie spezifische Tests benötigen, die nicht von AI durchgeführt werden können, verwenden Sie bitte JavaScript-Tests.',
                                        },
                                        expectationExamples: [
                                            'Es gibt 3 Katzenbilder auf der Seite',
                                            'Die Seite ist in Igbo Sprache',
                                            'usw...',
                                        ],
                                    },
                                },
                            },
                            custom: {
                                label: 'Selbst definierte Herausforderungen',
                                options: {
                                    js: {
                                        label: 'Mit JavaScript',
                                        title: 'JavaScript-Herausforderungen Spezifikation',
                                        description: 'Sie spezifizieren mit JavaScript, was genau getestet wird. Dabei stehen aktuelle Kontext-Daten der aktiven Seite zur Verfügung (Request-Daten, Response-Daten, Cookies, Certificate-Daten usw.).',
                                    },
                                },
                            },
                        },
                        notifications: {
                            deleteSuccess: 'URL-Challenge wurde gelöscht',
                            editSuccess: 'URL-Challenge wurde erfolgreich bearbeitet',
                            saveSuccess: 'URL-Challenge wurde erfolgreich gespeichert',
                            copySuccess: 'URL-Challenge wurde erfolgreich kopiert',
                        },
                        reviewer: {
                            label: 'URL-Challenge Reviewer',
                            cta: 'URL-Challenge Reviewer öffnen',
                            participants: 'Teilnehmer',
                            position: 'Position',
                            winner: 'Gewinner',
                            rankingTable: 'Rangliste',
                            disqualified: {
                                label: 'Disqualifizierte Teilnehmer',
                                reason: 'Gründe',
                            },
                        },
                        label_one: 'URL-Challenge',
                        content: {
                            intro: {
                                label: 'Validiere die URLs deiner Website mit Vertrauen',
                                title: 'Automatisiertes URL-Testing für Qualität, Funktionalität & Performance',
                                description: 'URLs sind das Rückgrat jeder Web-Erfahrung. Mit der URL-Challenge-Lösung von webautomate.app kannst du automatisch die Zuverlässigkeit, das Verhalten und die Performance deiner Webseiten testen – skalierbar und ganz ohne Code.',
                            },
                            definition: {
                                label: 'Was ist die URL-Challenge?',
                                description: 'Die URL-Challenge hilft dir zu prüfen, ob jede Seite deiner Website die gewünschten Anforderungen erfüllt – ob sie korrekt lädt, wichtige Elemente darstellt oder geschäftskritische Tests besteht. Du definierst die Regeln – wir automatisieren die Validierung.',
                            },
                            usage: {
                                label: 'Zwei Testmethoden – AI-basiert oder benutzerdefiniert',
                                options: {
                                    ai: {
                                        label: 'AI-gesteuerte URL-Challenges',
                                        description: 'Beschreibe in natürlicher Sprache, was validiert werden soll. Unserer AI-Agent analysiert die Seite, prüft Screenshots und führt die Tests durch.',
                                        examples: {
                                            label: 'Beispiele',
                                            items: [
                                                'Prüfe, ob der Checkout-Button auf Mobilgeräten sichtbar und klickbar ist.',
                                                'Stelle sicher, dass der Seitentitel mit dem H1-Tag übereinstimmt.',
                                                'Überprüfe, ob die Seite in weniger als 2 Sekunden lädt.',
                                                'Bestätige, dass die Seite ein bestimmtes Bild enthält.',
                                            ],
                                        },
                                    },
                                    custom: {
                                        label: 'JavaScript-basierte URL-Challenges',
                                        description: 'Du brauchst mehr Kontrolle? Verwende benutzerdefiniertes JavaScript, um Cookies, Header oder DOM-Eigenschaften präzise zu testen.',
                                    },
                                },
                            },
                            modes: {
                                label: 'Teste auf deine Art',
                                options: {
                                    eliminationMode: {
                                        label: 'Eliminierungsmodus',
                                        description: 'Beende den Test einer URL, sobald ein Check fehlschlägt.',
                                    },
                                    comprehensiveMode: {
                                        label: 'Umfassender Modus',
                                        description: 'Führe alle Checks durch – auch wenn einige fehlschlagen, um ein vollständiges Bild zu erhalten.',
                                    },
                                },
                                note: 'Du wählst die Strategie – wir übernehmen die Automatisierung.',
                            },
                            reporting: {
                                label: 'Leistungsstarke Berichte auf Abruf',
                                description: 'Verfolge jedes Testergebnis mit detaillierten Build-Berichten: Screenshots',
                                items: [
                                    'Getestete URLs und Anzahl der bestandenen/fehlgeschlagenen Prüfungen',
                                    'Screenshots von Fehlern',
                                    'Ladezeiten, HTTP-Daten und Teststatus',
                                    'Herunterladbare Logs und Berichte',
                                ],
                                note: 'Ideal für Entwickler, QA-Teams, Marketer und SEO-Spezialisten.',
                            },
                            getStarted: {
                                label: 'Starte intelligenteres Testing',
                                description: 'Erstelle deine erste URL-Challenge in wenigen Minuten. Kein Code nötig. Kein Rätselraten mehr.',
                            },
                        },
                    },
                    lighthouse: {
                        label: 'Google-Lighthouse-Audits',
                        summary: 'Die Performance, Accessibility, Best-Practices SEO und PWA Merkmale meiner Webseite prüfen lassen.',
                        title: 'Fangen Sie jetzt an zu messen!',
                        scale: 'Punkteskala',
                        captured: 'Es wurden {{count}} Lighthouse-Audits erfasst.',
                        captured_zero: 'Es wurden keine Lighthouse-Audits erfasst.',
                        captured_one: 'Es wurde 1 Lighthouse Audit erfasst.',
                        solutionError: 'Dieses Google-Lighthouse-Audits wurde aufgrund eines Laufzeitfehlers übersprungen.',
                        teaser: {
                            descriptions: [
                                'Nutzen Sie Lighthouse-Audits, um die Leistung, Zugänglichkeit und SEO Ihrer Website zu optimieren. Erhalten Sie detaillierte Berichte und Empfehlungen, um Ihre Website kontinuierlich zu verbessern.',
                                'Mit Google-Lighthouse-Audits können Sie sehen, wie gut Ihre Website in verschiedenen Metriken abschneidet. Zusätzlich gibt es Ihnen Tipps, welche Best-Practices Sie implementieren können, um Ihre Seite zu verbessern und benutzerfreundlicher zu machen.',
                            ],
                        },
                        categories: {
                            label: 'Audit Kategorien',
                            previewLabel: 'Kategorien',
                            title: 'Lassen Sie uns wissen, welche Daten Sie interessieren.',
                            description: 'Klicken Sie auf die jeweilige Kategorie, um diese zu- oder abzuschalten.',
                            fields: {
                                performance: {
                                    label: 'Performance',
                                    hintTitle: 'Welche Fragen hiermit beantwortet werden kann?',
                                    hintQuestion: 'Wann werden die Erste DOM Elemente regendert? (First Contentful Paint), Wann werden die erste Hauptinhalten regendert? (First Meaningful Paint), Wie schnell ist die Webseite? (Speed Index), Wie ist die Zeit bis zur Interaktivität? (Time To Interactive), Erster CPU-Leerlauf (First CPU Idle), Geschätzte Eingabelatenz (Estimated Input Latency)',
                                    description: 'In dieser Kategorie wird ihre Webseite daraufhin analysiert, wie schnell sie lädt und wann Benutzer mit ihren Inhalten interagieren können.',
                                    url: 'https://de.wikipedia.org/wiki/Barrierefreies_Internet',
                                    urlLabel: 'Wikipedia Artikel',
                                },
                                accessibility: {
                                    label: 'Accessibility',
                                    hintTitle: 'Welche Fragen hiermit beantwortet werden kann?',
                                    hintQuestion: 'Wie benutzbar ist meine Webseite für Menschen mit Behinderungen?',
                                    description: 'In dieser Kategorie wird ihre Webseite daraufhin analysiert, wie gut die sie von Menschen mit Einschränkungen genutzt werden kann.',
                                    url: 'https://de.wikipedia.org/wiki/Barrierefreies_Internet',
                                    urlLabel: 'Wikipedia Artikel',
                                },
                                'best-practices': {
                                    label: 'Best Practices',
                                    hintTitle: 'Welche Fragen hiermit beantwortet werden kann?',
                                    hintQuestion: 'Sind HTTPS sowie HTTP/ genutzt? Stammen Ressourcen aus sicheren Quellen? Werden unsicherer Befehle wie etwa document.write() ausgeführt?',
                                    description: 'In dieser Kategorie wird ihre Webseite daraufhin analysiert, ob auf Ihrer Webseite Best Practices in Punkto Sicherheit und moderner Web-Entwicklung eingehalten wird.',
                                },
                                seo: {
                                    label: 'SEO',
                                    fullLabel: 'Search Engine Optimization (SEO)',
                                    hintTitle: 'Welche Fragen hiermit beantwortet werden kann?',
                                    hintQuestion: 'Wie SEO-Tauglich ist meine Webseite?',
                                    description: 'In dieser Kategorie wird ihre Webseite daraufhin analysiert, wie gut sie von Suchmaschinen gecrawlt und in deren Suchergebnissen darstellbar werden kann.',
                                    url: 'https://de.wikipedia.org/wiki/Suchmaschinenoptimierung',
                                    urlLabel: 'Wikipedia Artikel',
                                },
                                pwa: {
                                    label: 'PWA',
                                    fullLabel: 'Progressive Web Apps (PWA)',
                                    hintTitle: 'Welche Fragen hiermit beantwortet werden kann?',
                                    hintQuestion: 'Funktioniert sie auch ohne Internetzugang? Werden Service Worker angemeldet? Nützt Sie das HTTPS-Protokoll zur Kommunikation?',
                                    description: 'In dieser Kategorie wird die Progressive Web App (PWA) Merkmale Ihrer Webseite unter die Lupe genommen.',
                                    url: 'https://de.wikipedia.org/wiki/Progressive_Web_App',
                                    urlLabel: 'Wikipedia Artikel',
                                },
                            },
                        },
                        output: {
                            label: 'Anzeigen und Herunterladen von Berichten',
                            previewLabel: 'Berichte',
                            title: 'Einsehen, bearbeiten und verbessern Sie Ihre Webseite',
                            description: 'Lighthouse Berichten unterstützen Sie bei der Analyse der erfassten Daten. Diese sind entweder in den Formaten HTML, JSON oder EXCEL verfügbar.',
                            fields: {
                                html: {
                                    label: 'html',
                                },
                                json: {
                                    label: 'json',
                                },
                                csv: {
                                    label: 'excel',
                                },
                                none: {
                                    label: 'Nur Score ermitteln.',
                                    report: 'Es wurde keine Berichte erstellt.',
                                },
                            },
                            negativeValueInfo: 'Negative Werte werden angezeigt, wenn für einen bestimmten Projektdurchlauf kein Datenpunkt verfügbar ist.',
                            missingBuild: 'Nur erfolgreiche Builds werden berücksichtigt.',
                        },
                        reviewer: {
                            label: 'Lighthouse-Audits Reviewer',
                            cta: 'Lighthouse-Audits Reviewer öffnen',
                        },
                        content: {
                            intro: {
                                label: 'Website-Qualität in Sekunden prüfen – mit Google Lighthouse',
                                title: 'Automatisierte Performance-, Barrierefreiheits- & SEO-Audits – ganz ohne Erweiterungen',
                                description: 'Mit der Lighthouse-Audits-Lösung von webautomate.app analysierst du automatisch deine Website mit der Lighthouse-Engine von Google. Erhalte präzise Scores und umsetzbare Empfehlungen zu Performance, Barrierefreiheit, SEO und Best Practices – bei jedem Deploy, Commit oder Zeitplan.',
                            },
                            definition: {
                                label: 'Was ist ein Lighthouse-Audit?',
                                description: 'Diese Lösung führt Headless-Chrome-Tests mit Google Lighthouse durch. Du erhältst strukturierte Bewertungen für Performance, Barrierefreiheit, SEO, Best Practices und PWA-Kompatibilität.',
                            },
                            benefits: {
                                label: 'Warum Lighthouse Audits verwenden?',
                                items: [
                                    'Automatische Tests für Staging-, Produktiv- und Dev-Umgebungen',
                                    'Langsame Ladezeiten, fehlende Meta-Tags oder Barrierefehler aufdecken',
                                    'Website-Qualität vor dem Go-live validieren',
                                    'Mobile und Desktop-Audits simulieren',
                                    'Score-Verlauf und Trends verfolgen',
                                ],
                                note: 'Keine manuelle CLI-Nutzung. Keine Erweiterungen. Nur Ergebnisse – skalierbar.',
                            },
                            metrics: {
                                label: 'Was wird im Audit gemessen?',
                                items: [
                                    'Performance: Ladezeiten, FCP, LCP, TBT',
                                    'Barrierefreiheit: Kontrast, ARIA, Tastaturbedienung',
                                    'SEO: Meta-Tags, semantisches HTML, Links',
                                    'Best Practices: HTTPS, JS-Fehler, Sicherheits-Header',
                                    'PWA: Offline-Fähigkeit, Installierbarkeit (optional)',
                                ],
                                note: 'Die Audits basieren auf aktuellen Lighthouse-Versionen und Cloud-Browsern.',
                            },
                            reporting: {
                                label: 'Aussagekräftige Berichte zum Teilen',
                                items: [
                                    'Lighthouse-Scores mit Detailwerten',
                                    'Screenshot des geprüften Bereichs',
                                    'HTML- & JSON-Berichte zum Download',
                                    'Webautomate-spezifische Zusammenfassungen & Logs',
                                    'Trendanalyse über mehrere Builds hinweg',
                                ],
                                note: 'Ideal für Entwickler, Marketer, SEOs und Agenturen.',
                            },
                            useCases: {
                                label: 'Anwendungsfälle',
                                items: [
                                    'QA vor dem Go-live: SEO & Performance validieren',
                                    'Monitoring: Scores regelmäßig prüfen',
                                    'Barrierefreiheit: Probleme früh erkennen',
                                    'Marketing-Checks: Google-Optimierung sicherstellen',
                                    'Kundenberichte: Qualität mit Zahlen belegen',
                                ],
                            },
                            getStarted: {
                                label: 'Jetzt sicher auditieren',
                                description: 'Lighthouse-Audits mit webautomate.app sind automatisiert, skalierbar und codefrei. Starte jetzt dein erstes Projekt!',
                            },
                        },
                    },
                    e2eVisualTests: {
                        label: 'Visual-Tests',
                        summary: 'Die visuelle Konsistenz von Elemente auf meiner Webseite prüfen lassen.',
                        title: 'Vertrauen ist gut, aber (Visual-)Testen ist besser!',
                        captured: '{{count}} Visual-Test erfasst.',
                        captured_one: '1 Visual-Test erfasst.',
                        captured_zero: 'Es wurden keine Visual-Test erfasst.',
                        solutionError: 'Dieser Test wurde aufgrund eines Laufzeitfehlers übersprungen.',
                        acceptFirstShot: 'Erste Erfassung eines Screenshots automatisch akzeptieren.',
                        highlightColor: {
                            label: 'Hervorhebungsfarbe',
                            description: 'Legen Sie hier fest, mit welcher Farbe die visuelle Unterschiede markiert werden.',
                        },
                        clustersSize: {
                            label: 'Mindest-Clustergrösse',
                            description: 'Cluster sind Pixel-Gruppen, die als Einheit beim Vergleichen betrachtet werden.',
                        },
                        teaser: {
                            descriptions: [
                                'Vergleichen Sie visuelle Elemente auf verschiedenen Seiten und Sitzungen, um die Konsistenz zu gewährleisten und Probleme frühzeitig zu erkennen. Ein unverzichtbares Tool für die Qualitätssicherung und die Markenkonsistenz.',
                                'Visual-Tests helfen Ihnen, die visuelle Konsistenz Ihrer Website zu überprüfen und sicherzustellen, dass sie auf allen Geräten und Browsern korrekt dargestellt wird.',
                            ],
                        },
                        items: {
                            label: 'Screenshot',
                            title: 'Visual-Tests',
                            title_one: 'Visual-Test',
                            historyLabel: 'Test-Verlauf',
                            id: 'ID',
                            build: 'Projektdurchlauf',
                            loadHistory: 'Test-Verlauf laden',
                            baseline: 'View Baseline',
                            currentState: 'Zustand',
                            changeState: 'Zustand ändern',
                        },
                        preview: 'Konfiguration anzeigen',
                        noMatch: 'Keine Treffer!',
                        category: 'Kategorie',
                        newItem: 'Visual-Tests hinzufügen',
                        selectItem: 'Visual-Tests hinzufügen',
                        search: 'Visual-Tests filtern',
                        selectOption: 'Bitte wählen Sie den Type',
                        failStopLabel: 'Visual-Test abbrechen, wenn dieser Step fehlschlägt.',
                        labels: {
                            allStates: 'Alle',
                            states: {
                                new: {
                                    label: 'Neu',
                                    latest: 'Dieser Screenshot ist neu.',
                                },
                                accepted: {
                                    label: 'Akzeptiert',
                                    cta: 'Akzeptieren',
                                    latest: 'Dieser Screenshot wurde akzeptiert.',
                                },
                                rejected: {
                                    label: 'Abgelehnt',
                                    cta: 'Ablehnen',
                                    latest: 'Dieser Screenshot wurde abgelehnt.',
                                },
                                changed: {
                                    label: 'Geändert',
                                    latest: 'Dieser Screenshot hat sich geändert.',
                                },
                                failed: {
                                    label: 'Fehlerhaft',
                                    latest: 'Es ist ein Fehler beim Erfassen des Screenshots passiert.',
                                },
                            },
                        },
                        reviewer: {
                            label: 'Visual-Tests Reviewer',
                            cta: 'Visual-Test Reviewer öffnen',
                            exit: 'Beenden',
                            selectAll: 'Alle auswählen',
                            selectAllNewOrChanged: 'Alle neue oder geänderte auswählen',
                            deselectAll: 'Alle abwählen',
                            accept: {
                                label: 'Akzeptieren',
                                hint: 'Markierte Screenshots akzeptieren.',
                            },
                            reject: {
                                label: 'Ablehnen',
                                hint: 'Markierte Screenshots ablehnen.',
                            },
                            reset: {
                                label: 'Zurücksetzen',
                                hint: 'Markierte Screenshots auf ihren ursprünglichen Zustand zurücksetzen.',
                            },
                            marked: '{{count}} ausgewählt',
                            backToOverview: 'Zurück zur Übersicht',
                            activeFilterNoHits: 'Zu Ihrem Filter passen keine Screenshots',
                            expiredBuild: 'Die Screenshots können nicht mehr bearbeitet werden, weil es schon ein neuerer Projektdurchlauf gibt.',
                            gotoBuild: 'Zum Projektdurchlauf',
                            resetFilter: 'Alle Screenshots anzeigen',
                            url: {
                                label: 'Screenshots aus',
                                all: 'alle URLs',
                            },
                            itemView: {
                                label: 'Ansicht',
                                previous: 'Zuvor akzeptiert',
                                current: 'Aktuell',
                                updated: 'Aktualisierungsdatum',
                                openTab: 'Bild im neuen Fenster öffnen',
                                copyImageUrl: 'URL zum Bild kopieren',
                                differences_one: '1 Unterschied',
                                differences: '{{count}} Unterschiede',
                                capturedOn: 'Erfasst am',
                                options: {
                                    label: 'Toolbox',
                                    labelCTA: 'Toolbox aktivieren',
                                    diffImage: {
                                        label: 'Diff-Image',
                                        description: 'Aktivieren Sie diese Option, um das Bild mit Hervorhebung der Unterschiede zu verwenden.',
                                    },
                                    padding: {
                                        label: 'Padding',
                                        description: 'Abstand zur Umrandung.',
                                    },
                                    border: {
                                        label: 'Unterschiede einrahmen',
                                        description: 'Aktivieren Sie diese Option, um die Unterschiede mit einem Rahmen zu versehen.',
                                    },
                                    borderWeight: {
                                        label: 'Rahmenstärke',
                                        description: 'Legen Sie die Dicke des Rahmens fest.',
                                    },
                                    color: {
                                        label: 'Rahmenfarbe',
                                        description: 'Legen Sie die Farbe des Rahmens fest.',
                                    },
                                },
                                sideBySide: {
                                    label: 'Seite an Seite',
                                },
                                overlappedX: {
                                    label: 'Überlagert (Portrait)',
                                },
                                overlappedY: {
                                    label: 'Überlagert (Landscape)',
                                },
                                overlappedZ: {
                                    label: 'Überlagert (Transparent)',
                                    opacity: 'Transparenz',
                                },
                                baseline: {
                                    none: 'Es gibt keine Baseline',
                                },
                                imageError: 'Fehler beim Laden des Bildes, bitte laden Sie die Seite neu',
                            },
                            notifications: {
                                accepted: '{{count}} Screenshots akzeptiert.',
                                rejected: '{{count}} Screenshots abgelehnt.',
                                reset: '{{count}} Screenshots zurückgesetzt.',
                            },
                            notes: {
                                label: 'Review-Hinweise',
                                intent: {
                                    accepted_one: 'Sie sind dabei, 1 Screenshot zu akzeptieren.',
                                    accepted: 'Sie sind dabei, {{count}} Screenshots zu akzeptieren.',
                                    rejected_one: 'Sie sind dabei, 1 Screenshot abzulehnen.',
                                    rejected: 'Sie sind dabei, {{count}} Screenshots abzulehnen.',
                                },
                                reviewComments: {
                                    label: 'Review-Kommentare',
                                    description: 'Fügen Sie alle Kommentare oder Notizen hinzu, die Sie mit dem Team bezüglich dieses visuellen Tests teilen möchten.',
                                },
                                confidence: {
                                    label: 'Review Confidence',
                                    description: 'Bewerten Sie wie sicher Sie sind, um Ihre Entscheidung zum visuellen Testergebnis zu untermauern.',
                                },
                            },
                        },
                        itemLabelling: {
                            distinct: 'Visual-Test Screenshots müssen eindeutig benannt werden.',
                        },
                        tolerance: {
                            label: 'Toleranz',
                            strategy: {
                                strict: {
                                    label: 'Strikt',
                                    items: [
                                        'Jede auch so kleine Pixelunterschiede werden berücksichtigt.',
                                    ],
                                },
                                moderate: {
                                    label: 'Moderat',
                                    items: [
                                        'Unterschiede aufgrund Blinkender Cursor werden ignoriert.',
                                        'Unterschiede aufgrund von Anti-Aliasing werden ignoriert.',
                                    ],
                                },
                                open: {
                                    label: 'Offen',
                                    items: [
                                        'Unterschiede aufgrund Blinkender Cursor werden ignoriert.',
                                        'Unterschiede aufgrund von Anti-Aliasing werden ignoriert.',
                                        'Minimal Helligkeitsunterschiede zwischen Pixel werden ignoriert.',
                                    ],
                                },
                            },
                        },
                        groups: {
                            automated: {
                                options: {
                                    ai: {
                                        title: 'Anweisung spezifizieren',
                                        description: 'Erstelle Screenshots, die automatisch mit dem AI-Review auf visuellen Unterschiede bewertet werden. Visuelle Unterschiede werden basiert auf vordefinierten Anweisungen bewertet.',
                                        inputNotice: 'Geben Sie eine klare und spezifische Anweisungen, wie die visuellen Unterschiede bewertet werden sollen.',
                                        confidenceLevel: {
                                            label: 'Vertrauensschwelle',
                                            description: 'Die Vertrauensschwelle bestimmt, wie sicher sich AI sein muss, um die visuellen Unterschiede zu akzeptieren oder abzulehnen.',
                                            interpretation: {
                                                low: 'Der Wert {{value}} ist zu niedrig und kann zu false positives führen (Wenn die Bedingung als erfüllt markiert wird, obwohl sie nicht erfüllt ist).',
                                                medium: 'Der Wert {{value}} ist gut und wird empfohlen, wenn Sie false positives vermeiden möchten.',
                                                high: 'Der Wert {{value}} ist zu hoch und kann zu false negatives führen (Wenn die Bedingung als nicht erfüllt markiert wird, obwohl sie erfüllt ist).',
                                            },
                                        },
                                        disclaimer: {
                                            label: 'Haftungsausschluss',
                                            description: 'AI-Reviews basieren auf maschinellem Lernen und können nicht immer die Erwartungen erfüllen. Wenn Sie sicher sein möchten, dass die visuellen Unterschiede korrekt bewertet werden, verwenden Sie bitte die manuelle Überprüfung.',
                                        },
                                        expectationExamples: [
                                            '/**\nVergleiche den Screenshot mit der Baseline. Ignoriere kleine Unterschiede (Antialiasing, Helligkeit). Akzeptiere Änderungen unter 5% Fläche und außerhalb kritischer Bereiche (Navigation, Buttons). Aktualisiere die Baseline nur bei unkritischen Änderungen mit weniger als 3% Pixelabweichung.\n*/',
                                        ],
                                        label: 'AI-Reviewer',
                                    },
                                },
                                label: 'AI-UNTERSTÜTZT',
                            },
                            custom: {
                                label: 'Selbst definierte Visual-Tests',
                                options: {
                                    manual: {
                                        label: 'Manuelle Überprüfung',
                                        description: 'Erstelle Screenshots, vergleiche diese manuell und aktualisieren Sie selbst die Baselines.',
                                    },
                                },
                            },
                        },
                        urlFilter: {
                            title: 'URL Filter',
                            subTitle: 'Legen Sie hier fest, auf welchen URLs die Tests ausgeführt.',
                            testUrlSuccess: 'Auf diesem URL werden Tests ausgeführt.',
                            testUrlFailure: 'Auf diesem URL werden keine Tests ausgeführt.',
                        },
                        notifications: {
                            mandatoryField: 'Bitte geben Sie eine Bezeichnung des Test-Blocks an.',
                            duplicateGroupName: 'Der Name ist bereits vergeben.',
                            deleteSuccess: 'Visual-Test Konfiguration wurde gelöscht',
                            editSuccess: 'Visual-Test Konfiguration wurde erfolgreich bearbeitet',
                            saveSuccess: 'Visual-Test Konfiguration wurde erfolgreich gespeichert',
                            copySuccess: 'Visual-Test Konfiguration wurde erfolgreich kopiert',
                        },
                        label_one: 'Visual-Test',
                        content: {
                            intro: {
                                label: 'Erkenne visuelle Fehler, bevor es deine Nutzer tun',
                                title: 'Automatisiertes visuelles Testing für konsistente Benutzeroberflächen',
                                description: 'Mit der Visual-Tests-Lösung von webautomate.app kannst du visuelle Unterschiede über Zeiträume erkennen – von kleinsten Pixelabweichungen bis hin zu Layout-Verschiebungen. Vertraue jeder Version durch präzises, automatisiertes UI-Monitoring.',
                            },
                            definition: {
                                label: 'Was ist visuelles Testing?',
                                description: 'Beim visuellen Testing werden aktuelle Screenshots deiner Website mit früheren Builds oder genehmigten Baselines verglichen. Jede Abweichung – egal wie klein – wird erkannt.',
                            },
                            benefits: {
                                label: 'Warum visuelle Tests verwenden?',
                                items: [
                                    'Design-Fehler nach Deployments sofort erkennen',
                                    'Layoutprobleme auf verschiedenen Geräten vermeiden',
                                    'Änderungen im Side-by-Side- oder Overlay-Modus prüfen',
                                    'UI-Stabilität im Deployment-Prozess validieren',
                                    'Zeit sparen durch automatisierte Screenshot-Vergleiche',
                                ],
                                note: 'Visuelle Fehler sind subtil – unser System erkennt sie trotzdem.',
                            },
                            comparison: {
                                label: 'Vergleichsstrategien für jede Situation',
                                items: [
                                    'Baseline-Vergleich: Unterschiede zur letzten freigegebenen Version erkennen',
                                    'Build-Vergleich: Zwei Builds im selben Projekt vergleichen',
                                    'URL-Vergleich: Seiten zwischen Umgebungen vergleichen (z. B. Staging vs. Produktion)',
                                ],
                                note: 'Definiere Pixel-Toleranzen, Ignorierbereiche oder vergleiche gezielte Bereiche.',
                            },
                            reporting: {
                                label: 'Klar erkennbare Ergebnisse',
                                items: [
                                    'Vergleichsansicht nebeneinander',
                                    'Overlays mit visuellen Abweichungen',
                                    'Screenshots der aktuellen und vorherigen Version',
                                    'Statusberichte mit Metadaten',
                                    'Änderungen akzeptieren oder ablehnen',
                                ],
                                note: 'Ideal für Entwickler, Tester, Designer und CI/CD-Workflows.',
                            },
                            useCases: {
                                label: 'Anwendungsfälle',
                                items: [
                                    'UI-Regressionstest: Visuelle Fehler vor Livegang erkennen',
                                    'Design-Validierung: Pixelgenaue Umsetzung sicherstellen',
                                    'A/B-Testprüfung: Ungewollte Layout-Unterschiede erkennen',
                                    'Visuelles Monitoring: Dritte-Komponenten langfristig überwachen',
                                ],
                            },
                            getStarted: {
                                label: 'Teste, was du siehst – visuell und präzise',
                                description: 'Starte visuelle Tests für jeden Release, Commit oder Plan. Keine Vermutungen. Nur perfekte Pixel.',
                            },
                        },
                    },
                    screenshots: {
                        label: 'Screenshot-Dokumentation',
                        summary: 'Meine Website durch Screenshots dokumentieren.',
                        title: 'Ein Bild sagt mehr Wert als Tausend Worte!',
                        basicBlock: 'Screenshot Spezifikation',
                        fileName: 'Screenshot-Name',
                        captured: 'Es wurden {{count}} Screenshots erfasst.',
                        captured_zero: 'Es wurden keine Screenshots erfasst.',
                        captured_one: 'Es wurde 1 Screenshot erfasst.',
                        reviewer: {
                            label: 'Screenshot Reviewer',
                            cta: 'Screenshot Reviewer öffnen',
                        },
                        preview: 'Konfiguration anzeigen',
                        multiItem: 'Mehrere Screenshots',
                        failStopLabel: 'Screenshot-Dokumentation abbrechen, wenn dieser Step fehlschlägt.',
                        solutionError: 'Dieser Screenshot-Dokumentation wurde aufgrund eines Laufzeitfehlers übersprungen.',
                        teaser: {
                            descriptions: [
                                'Erfassen Sie einfach Screenshots Ihrer Webseiten, um ihren aktuellen Zustand zu dokumentieren. Ein nützliches Tool für die Fehlerbehebung und die Dokumentation von Design-Versionen.',
                                'Sie können Screenshots von dem sichtbare Teil der Seite, von einem bestimmten Element an jeder beliebigen Stelle oder sogar der gesamten Seite erfassen lassen.',
                            ],
                        },
                        items: {
                            title: 'Screenshots',
                            subTitle: 'Sie können eine Abfolge von Steps (Aktionen) festlegen, die vor der Screenshot-Dokumentation ausgeführt werden.',
                        },
                        newItem: 'Screenshot hinzufügen',
                        fileNameDisplay: {
                            format: '{{label}}.{{extension}}',
                            multipleInfo: 'Die Position eines jeden Screenshots wird am Dateiname angehängt.',
                            examples: {
                                label: 'Zum Beispiel:',
                                index: '{{label}}_{{index}}.{{extension}}',
                                rest: 'usw...',
                            },
                        },
                        selector: 'Element Selector',
                        target: {
                            title: 'Erfassungsbereich',
                            viewport: {
                                label: 'Viewport Screenshot',
                                description: 'Sichtbarer Bereich',
                            },
                            fullPage: {
                                label: 'Fullpage Screenshot',
                                description: 'Vollständige Seite',
                            },
                            element: {
                                label: 'DOM-Element Screenshot',
                                description: 'DOM-Element',
                            },
                        },
                        selectorTargets: {
                            first: 'Erster Treffer',
                            last: 'Letzter Treffer',
                            all: 'Alle Treffer',
                            custom: {
                                1: 'Erfasst das erste Element',
                                label: 'Benutzerdefiniert',
                                description: 'Elementposition getrennt durch "," eingeben.',
                                '1,4': 'Erfasst den ersten und vierten Elemente',
                                '2,3_5,7': 'Erfasst die zweite, dritte bis fünfte und siebte Elemente',
                            },
                        },
                        urlFilter: {
                            title: 'URL Filter',
                            subTitle: 'Legen Sie hier fest, auf welchen URLs die Screenshots erfasst werden.',
                            testUrlSuccess: 'Auf diesem URL werden Screenshots erstellt.',
                            testUrlFailure: 'Auf diesem URL werden keine Screenshots erstellt.',
                        },
                        validation: {
                            name: 'Der Name muss zwischen {{start}} und {{end}} lang sein.',
                            duplicateName: 'Es wurde bereits ein Screenshot mit dem Namen angelegt.',
                            selectorTargets: 'Bitte geben Sie einen gültige Werte ein.',
                        },
                        notifications: {
                            mandatoryField: 'Bitte geben Sie einen Gruppen-Namen ein',
                            duplicateGroupName: 'Es gibt bereits eine Screenshot-Gruppe mit dem Namen',
                            deleteSuccess: 'Screenshot Konfiguration wurde gelöscht',
                            editSuccess: 'Screenshot Konfiguration wurde erfolgreich bearbeitet',
                            saveSuccess: 'Screenshot Konfiguration wurde erfolgreich gespeichert',
                            copySuccess: 'Screenshot Konfiguration wurde erfolgreich kopiert',
                        },
                        label_one: 'Screenshot-Dokumentation',
                        content: {
                            intro: {
                                label: 'Automatisierte Web-Screenshots – für QA, Audits & Dokumentation',
                                title: 'Dokumentiere jede Seite visuell – präzise und einfach',
                                description: 'Mit der Screenshot-Dokumentationslösung von webautomate.app kannst du automatisch Screenshots ausgewählter Websitebereiche erstellen – sei es die gesamte Seite, ein spezifisches Element oder nur der sichtbare Bereich. Ideal für visuelle Audits, Content-Reviews oder Compliance-Berichte.',
                            },
                            definition: {
                                label: 'Was ist Screenshot-Dokumentation?',
                                description: 'Die Screenshot-Dokumentation hilft dir dabei, die Darstellung deiner Website auf verschiedenen Geräten und Browsern visuell zu erfassen. Nutze AI-gestützte oder benutzerdefinierte JavaScript-Anweisungen, um festzulegen, was erfasst werden soll.',
                            },
                            benefits: {
                                label: 'Warum Screenshot-Dokumentation verwenden?',
                                items: [
                                    'Dokumentieren Sie Ihre Seiten automatisch mit hochauflösenden Screenshots',
                                    'Erfassen Sie ganze Seiten, bestimmte Elemente oder nur den sichtbaren Bereich',
                                    'Vereinfachen Sie Audits, Reviews und Kundenberichte',
                                ],
                                note: 'Ob bei der Qualitätssicherung, der Verwaltung von Designsystemen oder der Erstellung von Compliance-Berichten – diese Lösung hält Ihre visuellen Inhalte stets aktuell.',
                            },
                            modes: {
                                label: 'Zwei Erfassungsmodi',
                                ai: {
                                    label: 'AI-gestützte Screenshot-Anweisungen',
                                    description: 'Beschreibe in natürlicher Sprache, was erfasst werden soll. Unserer AI-Agent erkennt die relevanten Elemente und erfasst sie.',
                                    example: 'Erfasse alle Preistabellen und Call-to-Action-Buttons.',
                                },
                                custom: {
                                    label: 'Flexible Screenshot-Abdeckung',
                                    description: 'Definiere per benutzerdefiniertem JavaScript genau, welche Elemente oder Seitenbereiche aufgenommen werden sollen.',
                                    items: [
                                        'Viewport – Erfasst den sichtbaren Bereich auf dem gewählten Gerät.',
                                        'Ganze Seite – Scrollt und erfasst die gesamte Seitenhöhe.',
                                        'DOM-Element – Erfasse ein bestimmtes Element per CSS-Selektor.',
                                    ],
                                },
                            },
                            reporting: {
                                label: 'Visuelle Berichte in Echtzeit',
                                items: [
                                    'Screenshots nach URL, Browser und Gerät gruppiert',
                                    'Zeitstempel für jede Aufnahme',
                                    'Ladezustand und Metadaten',
                                    'Berichte in verschiedenen Formaten herunterladen',
                                ],
                                note: 'Perfekt für QA, Designprüfungen und Archivierung.',
                            },
                            useCases: {
                                label: 'Anwendungsfälle',
                                items: [
                                    'Visuelles QA – Konsistenz bei UI-Updates prüfen',
                                    'Kundenberichte – Proof-of-Delivery für Design oder Entwicklung',
                                    'Compliance – Archivierung rechtlich relevanter Seiten',
                                    'Mehrsprachige Tests – Screenshots sprachübergreifend vergleichen',
                                ],
                            },
                            getStarted: {
                                label: 'Starte mit smarter Erfassung',
                                description: 'Erstelle dein erstes Screenshot-Projekt in wenigen Minuten. Keine manuellen Steps. Keine verpassten Details.',
                            },
                        },
                        groups: {
                            automated: {
                                label: 'AI-gestützte Screenshot-Erfassung',
                                options: {
                                    ai: {
                                        label: 'AI-erstellte Screenshots',
                                        description: 'Nutzen Sie AI, um in natürlicher Sprache zu beschreiben, was erfasst werden soll. Unserer AI-Agent erkennt die relevanten Elemente und erstellt Screenshots.',
                                        disclaimer: {
                                            label: 'Haftungsausschluss',
                                            description: 'AI-Vorhersagen basieren auf maschinellem Lernen und entsprechen möglicherweise nicht immer dem menschlichen Urteilsvermögen. Für maximale Präzision verwenden Sie den manuellen Modus der benutzerdefinierten Screenshot-Erfassung.',
                                        },
                                        expectationExamples: [
                                            'Erfasse alle Produkte auf der Seite, die mit "Sale" markiert sind',
                                            'Erstelle Screenshots von allen Elementen auf der Seite mit dem Titel "Bestseller"',
                                            'usw...',
                                        ],
                                        maintainInstruction: {
                                            label: 'Anweisung beibehalten',
                                            description: 'Soll die AI-Anweisung bei jedem Durchlauf neu ausgewertet oder nur beim ersten Mal verwendet werden?',
                                            options: {
                                                reuse: 'Screenshots aus der ersten Ausführung wiederverwenden',
                                                reEvaluate: 'Anweisung bei jedem Build neu auswerten',
                                            },
                                        },
                                        confidenceThreshold: {
                                            label: 'Vertrauensschwelle',
                                            description: 'Minimales Vertrauen (%) der AI, um ein Element zu erfassen',
                                        },
                                        confidenceLevel: {
                                            label: 'Vertrauenswert',
                                            description: 'Bestimmt, wie sicher sich die AI sein muss, bevor ein Screenshot gemacht wird. Niedrige Werte sind großzügiger, hohe Werte strenger.',
                                            interpretation: {
                                                low: 'Wert {{value}} ist sehr niedrig. Der AI-Agent könnte Dinge erfassen, die nicht ganz zu Ihrer Beschreibung passen (falsche Treffer). Nur empfohlen, wenn Sie damit leben können, zu viel zu erfassen.',
                                                medium: 'Wert {{value}} ist ausgewogen. Gute Wahl, wenn Sie genaue Ergebnisse ohne große Verluste wollen.',
                                                high: 'Wert {{value}} ist sehr streng. Der AI-Agent könnte wichtige Elemente übersehen (falsche Auslassungen), aber alles, was erfasst wird, passt gut.',
                                            },
                                        },
                                        cachedSelectors: {
                                            label: 'Selektoren zwischenspeichern',
                                            description: 'Verwenden Sie Selektoren aus früheren Durchläufen, um den Prozess zu beschleunigen.',
                                        },
                                        title: 'Formulieren Sie eine einfache Anweisung für die AI, die beschreibt, was Sie auf der aktuellen Seite erfassen möchten.',
                                    },
                                },
                            },
                            custom: {
                                label: 'Benutzerdefinierte Screenshot-Erfassung',
                                options: {
                                    manual: {
                                        label: 'Manuell konfigurierte Screenshots',
                                        description: 'Wählen Sie bestimmte DOM-Elemente, den sichtbaren Bereich oder die gesamte Seite zur Erfassung mithilfe von Selektoren oder Positionsregeln.',
                                    },
                                },
                            },
                        },
                        selectOption: 'Wählen Sie eine Option aus',
                    },
                    dataExtractions: {
                        label: 'Data-Extraction',
                        summary: 'Strukturierte Daten schnell und präzise extrahieren.',
                        newItem: 'Data-Extraction hinzufügen',
                        title: 'Extrahiere Daten aus ihrer Webseite.',
                        solutionError: 'Diese Data-Extraction wurde aufgrund eines Laufzeitfehlers übersprungen.',
                        teaser: {
                            descriptions: [
                                'Extrahieren und organisieren Sie mühelos die benötigten Daten von jeder Webseite. Unser Tool ermöglicht eine schnelle und präzise Datenextraktion, die Ihre Web-Analyse auf die nächste Stufe hebt.',
                                'Extrahiere Information zu den Netzwerkanfragen, zu Elementen im HTML-Dokument oder individuelle empirische Daten.',
                            ],
                        },
                        items: {
                            title: 'Data-Extraction konfigurieren.',
                            subTitle: 'Gruppieren Sie zusammengehörende Datenextraktionen zusammen in Blöcken.',
                            historyLabel: 'Datenextraktion-Verlauf',
                            id: 'DER-ID',
                            build: 'Projektdurchlauf',
                            verdict: 'Urteil',
                            loadHistory: 'Verlauf laden',
                            showResult: 'Ergebnis anzeigen',
                        },
                        failStopLabel: 'Data-Extraction abbrechen, wenn dieser Step fehlschlägt.',
                        unavailable: 'Diese Datenextraktion steht nicht mehr zu Verfügung.',
                        itemName: 'Extrakt',
                        selectItem: 'Daten extrahieren',
                        data: 'Extrahierierte Daten',
                        dataEmptyXHRResult: 'Es wurde bei diesem Projektdurchlauf keine Daten erfasst, weil die Einstellung zu keinem Netzwerk-URL passt.',
                        category: 'Kategorie',
                        context: 'Kontext',
                        preview: 'Konfiguration anzeigen',
                        search: 'Datenextraktion filtern',
                        selectOption: 'Bitte wählen Sie die Extraktionsart',
                        captured: '{{count}} Datenpunkte erfasst',
                        captured_zero: 'Es wurden keine Daten erfasst.',
                        captured_one: '1 Datenpunkt erfasst',
                        renameGroup: 'Block umbenennen',
                        deleteGroup: 'Block löschen',
                        multi: {
                            label: 'Multi-Extraction',
                            description: 'Dies ist ein Multi-Extraction, denn Daten werden bei alle passende XHR Anfragen separat extrahiert.',
                        },
                        groups: {
                            label: 'Gruppe',
                            custom: {
                                label: 'Browser',
                                options: {
                                    js: {
                                        label: 'JavaScript-Ausdruck',
                                        description: 'Extrahiere selbst Daten aus benutzerdefiniertem Javascript-Code.',
                                        fields: {
                                            reducer: 'Reduzierungsfunktion',
                                        },
                                    },
                                },
                            },
                            automated: {
                                label: 'AI-Powered Data-Extraction',
                                options: {
                                    ai: {
                                        label: 'AI-Datenerfassung',
                                        title: 'Formulieren Sie eine einfache Anweisung für die AI, die beschreibt, welche Daten Sie auf der aktuellen Seite erfassen möchten.',
                                        description: 'Extrahiere Daten mit Unterstützung eines AI-Assistentens. Sie beschreiben Ihre Anforderungen.',
                                        disclaimer: {
                                            label: 'Haftungsausschluss',
                                            description: 'Data-Extraction mit AI-Unterstützung basieren auf maschinellem Lernen und können nicht immer die Erwartungen erfüllen. Wenn Sie spezifische Daten benötigen, die nicht von AI durchgeführt werden können, verwenden Sie bitte alternative Lösungen.',
                                        },
                                        confidenceLevel: {
                                            label: 'Vertrauensstufe',
                                            description: 'Legt fest, wie sicher sich die AI sein muss, bevor sie Daten extrahiert.',
                                            interpretation: {
                                                low: 'Ein Wert von {{value}} bedeutet, dass die AI großzügiger ist und auch dann Daten extrahiert, wenn sie sich nicht ganz sicher ist. Das kann dazu führen, dass manchmal Dinge erkannt werden, die gar nicht vorhanden sind.',
                                                medium: 'Ein Wert von {{value}} ist ausgewogen. Der AI-Agent extrahiert Daten, wenn sie sich ziemlich sicher ist - ein guter Mittelweg zwischen zu viel und zu wenig Erkennung.',
                                                high: 'Ein Wert von {{value}} bedeutet, dass die AI sehr vorsichtig ist und nur dann Daten extrahiert, wenn sie sich sehr sicher ist. Das kann dazu führen, dass sie gelegentlich etwas Wichtiges übersieht.',
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        notifications: {
                            mandatoryField: 'Bitte geben Sie einen Namen ein',
                            duplicateGroupName: 'Es gibt bereits einen Block mit dem Namen',
                            deleteSuccess: 'Datenextraktion Konfiguration wurde gelöscht',
                            editSuccess: 'Datenextraktion Konfiguration wurde erfolgreich bearbeitet',
                            saveSuccess: 'Datenextraktion Konfiguration wurde erfolgreich gespeichert',
                            copySuccess: 'Datenextraktion Konfiguration wurde erfolgreich kopiert',
                        },
                        label_one: 'Datenextraktion',
                        reviewer: {
                            label: 'Datenextraktion Reviewer',
                            cta: 'Datenextraktion Reviewer öffnen',
                        },
                        viewExtractedData: 'Extrahierte Daten anzeigen',
                        extractedData: '{{label}}: Extrahierte Daten',
                        content: {
                            intro: {
                                label: 'Daten von jeder Website extrahieren – schnell, präzise und ohne Code',
                                title: 'Automatisierte Datenerfassung für intelligente Webanalysen',
                                description: 'Nutze das volle Potenzial deiner Webrecherche. Mit der Data-Extraction-Lösung von webautomate.app kannst du strukturierte Informationen von jeder Website extrahieren – mithilfe leistungsstarker AI oder benutzerdefinierter Logik, ganz ohne Programmierung.',
                            },
                            definition: {
                                label: 'Was ist Data-Extraction?',
                                description: 'Mit der Data-Extraction-Lösung kannst du Inhalte wie Produktdetails, Preise, Links, Artikel oder Metadaten automatisch von einer oder mehreren Webseiten sammeln. Egal ob Wettbewerbsbeobachtung oder Datensammlung – wir machen es skalierbar und einfach.',
                            },
                            benefits: {
                                label: 'Warum Data-Extraction nutzen?',
                                items: [
                                    'Inhalte in wenigen Minuten von mehreren URLs extrahieren',
                                    'Daten mit natürlicher Sprache oder JavaScript extrahieren',
                                    'Funktioniert auch mit dynamischen und JavaScript-lastigen Seiten',
                                    'Wiederkehrende Aufgaben automatisieren mit Zeitplänen und Benachrichtigungen',
                                ],
                                note: 'Von SEO-Daten bis hin zu Preisen – wenn es auf der Seite ist, kannst du es extrahieren.',
                            },
                            methods: {
                                label: 'Zwei Möglichkeiten zur Datenerfassung',
                                ai: {
                                    label: 'AI-gesteuerte Extraktion',
                                    description: 'Beschreibe einfach, was du brauchst. Unserer AI-Agent analysiert die Seite und extrahiert relevante Daten anhand des Kontexts und der Struktur.',
                                    example: 'Extrahiere alle Produktnamen, Preise und Bewertungen auf der Seite.',
                                },
                                custom: {
                                    label: 'JavaScript-basierte Extraktion',
                                    description: 'Für fortgeschrittene Anwendungsfälle kannst du benutzerdefiniertes JavaScript verwenden, um gezielt Elemente, Attribute oder strukturierte Daten zu extrahieren.',
                                },
                            },
                            reporting: {
                                label: 'Echtzeit-Berichte',
                                items: [
                                    'Anzahl der versuchten vs. erfolgreich extrahierten Datenpunkte',
                                    'Ladeinformationen der Seite und Screenshots',
                                    'Ergebnisse als CSV, JSON oder Excel herunterladen',
                                    'Fehlerverfolgung und Wiederholungsmechanismen',
                                ],
                                note: 'Ideal für Entwickler, Analysten, Marketer und QA-Teams.',
                            },
                            useCases: {
                                label: 'Anwendungsfälle',
                                items: [
                                    'Marktforschung: Wettbewerberpreise und Aktionen verfolgen',
                                    'SEO-Monitoring: Meta-Titel, Überschriften und Links extrahieren',
                                    'Produktfeeds: E-Commerce-Datenbanken erstellen oder erweitern',
                                    'Inhaltsaggregation: Artikel, Jobs oder Bewertungen sammeln',
                                    'QA-Automatisierung: Korrektheit von Frontend-Daten überwachen',
                                ],
                            },
                            getStarted: {
                                label: 'Starte mit intelligenter Datenerfassung',
                                description: 'Erstelle dein erstes Data-Extraction-Projekt in wenigen Minuten. Kein Scraper. Kein manuelles Parsing. Nur Ergebnisse.',
                            },
                        },
                        noMatch: 'Keine Treffer gefunden',
                    },
                    screenVideos: {
                        label: 'Video-Dokumentation',
                        summary: 'Meine Website durch Video-Aufzeichnungen dokumentieren.',
                        title: 'Eine Videoaufzeichnung der Webseite erstellen.',
                        captured: 'Es wurden {{count}} Videos erfasst.',
                        captured_zero: 'Es wurde kein Video aufgezeichnet.',
                        captured_one: 'Es wurde 1 Video erfasst.',
                        openVideo: 'Video öffnen',
                        solutionError: 'Diese Videoaufzeichnung wurde aufgrund eines Laufzeitfehlers übersprungen.',
                        failStopLabel: 'Videoaufzeichnung abbrechen, wenn dieser Step fehlschlägt.',
                        teaser: {
                            descriptions: [
                                'Dokumentieren Sie den Zustand Ihrer Website mit Videoaufzeichnungen. Ein leistungsstarkes Tool, um das Benutzerverhalten zu analysieren und Probleme zu beheben.',
                                'Die Videoaufzeichnung beginnt sofort, wenn die Seite geladen wird. Fügen Sie eine Abfolge von Steps hinzu, die während der Videoaufzeichnung ausgeführt werden sollen. Wenige Sekunden nach der letzten Aktion wird die Aufnahme gestoppt und das Video für Sie gespeichert.',
                            ],
                        },
                        label_one: 'Video-Dokumentation',
                        reviewer: {
                            label: 'Video Reviewer',
                            cta: 'Video Reviewer öffnen',
                        },
                        content: {
                            intro: {
                                label: 'Zeichne das Verhalten deiner Website in Echtzeit auf',
                                title: 'Automatisierte Videoaufnahmen für UX-Reviews, QA und Performance-Belege',
                                description: 'Mit der Video-Dokumentationslösung von webautomate.app kannst du das Lade- und Nutzerverhalten deiner Website in Echtzeit aufzeichnen – über Browser, Geräte und Bedingungen hinweg. Ideal für Animationstests, Verhaltensprüfungen oder Beweissicherung für Entwickler und Kunden.',
                            },
                            definition: {
                                label: 'Was ist Video-Dokumentation?',
                                description: 'Diese Lösung ermöglicht dir, eine vollständige Sitzung aufzuzeichnen – vom Laden der Seite bis zur Nutzerinteraktion. Dazu gehören Rendering, Animationen, Ladeindikatoren, dynamische Inhalte und Scrollverhalten.',
                            },
                            benefits: {
                                label: 'Warum Video-Dokumentation nutzen?',
                                items: [
                                    'Seitenladeverhalten in Echtzeit visuell erfassen',
                                    'Animationen, Übergänge und dynamische Inhalte aufnehmen',
                                    'UX-Videos mit Kunden oder Entwicklern teilen',
                                    'Layout-Verschiebungen, Flackern oder Darstellungsfehler erkennen',
                                    'Zeitverzögerungen oder Interaktionen als Steps hinzufügen',
                                ],
                                note: 'Ein Video sagt oft mehr als ein Screenshot – besonders bei Timing und Verhalten.',
                            },
                            capabilities: {
                                label: 'Einfache oder dynamische Seiten aufnehmen',
                                description: 'Du kannst einfache Seiten aufnehmen oder Bedingungsschritte konfigurieren, z.B:',
                                items: [
                                    'Zu einem Abschnitt scrollen',
                                    'Auf ein Modal warten',
                                    'Pause für Animationen',
                                    'Dynamische Inhalte auslösen',
                                ],
                                note: 'Diese Steps werden vor oder während der Aufnahme ausgeführt, um wichtige Momente festzuhalten.',
                            },
                            reporting: {
                                label: 'Alles im Blick – Bild für Bild',
                                items: [
                                    'Video-Wiedergabe der Sitzung',
                                    'Metadaten (Browser, Gerät, Zeitstempel, Lademethode)',
                                    'Darstellungsverhalten und Ladezeit sichtbar',
                                    'Downloadbare Videodateien für Archiv oder Weitergabe',
                                ],
                            },
                            useCases: {
                                label: 'Anwendungsfälle',
                                items: [
                                    'UX-Tests: Layout-Fehler, Animationen oder Timing-Probleme erkennen',
                                    'Kundendemos: Zeige reale Nutzererlebnisse',
                                    'Performance-Analyse: Ladeverhalten sichtbar machen',
                                    'UX-Monitoring: Veränderungen im Zeitverlauf verfolgen',
                                ],
                            },
                            getStarted: {
                                label: 'Beginne mit der Erfassung echter Erlebnisse',
                                description: 'Starte dein erstes Video-Projekt in wenigen Minuten. Echte Browser. Echte Ergebnisse.',
                            },
                        },
                    },
                },
            },
            summary: {
                label: 'Abschluss',
                previewLabel: 'Abschluss',
                description: '',
                fields: {
                    notifications: {
                        label: 'Benachrichtigungen',
                        description: 'Lassen Sie sich per E-Mail, SMS oder Webhooks benachrichtigen nach dem Projektdurchlauf diese Projekts.',
                    },
                    subscription: {
                        label: 'Projekt-Abonnement',
                        description: 'Dieses Projekt wird mit folgendem Abonnement ausgeführt:',
                    },
                    scheduler: {
                        label: 'Automatisierte Projektdurchläufe',
                        activate: 'Ich möchte das Projekt regelmässig automatisiert ausführen lassen.',
                        deactivated: 'Projekt wird nicht automatisiert ausgeführt.',
                        validity: 'Gültigkeit',
                        buildTime: 'Projektdurchlauf-Zeiten',
                        startDate: 'Startdatum',
                        endDate: 'Enddatum',
                        configurator: {
                            types: {
                                label: 'Zeitplan',
                                options: {
                                    random: {
                                        cta: 'Manuelle Einstellung',
                                        label: 'Zufälliger Zeitpunkt Generieren',
                                        description: 'Zufällig {{frequency}} mal pro {{frequencyQualifier}}',
                                        question: 'Wann erhalten Sie die meisten Traffic auf der Website?',
                                        questionInfo: 'Wir generieren jedesmal einen zufälligen Zeitpunkt außerhalb des angegebenen Zeitraums, um Störungen zu vermeiden und Ihren Server nicht zusätzlich zu belasten.',
                                        frequency: {
                                            label: 'Häufigkeit',
                                            qualifierLabel: 'Pro',
                                            qualifierOptions: {
                                                daily: 'Tag',
                                                weekly: 'Woche',
                                                monthly: 'Monat',
                                            },
                                        },
                                    },
                                    cron: {
                                        description: '{{cronPrompt}}',
                                        question: 'Stellen Sie ein, wann sie gerne regelmässig das Projekt ausführen lassen wollen.',
                                        cta: 'AI-gestützte Einstellung',
                                        nextBuild: 'nächste {{pageSize}} Projektdurchlauf-Zeiten',
                                        nextBuildMore: 'Mehr anzeigen',
                                        cronDescriptionInputNotice: 'Beschreiben Sie, wann und wie oft das Projekt ausgeführt werden soll',
                                        cronDescriptionPlaceholder: 'Führen Sie das Projekt jeden Montag, Mittwoch und Freitag um 4 Uhr morgens aus.',
                                        validationLabel: 'Beschreibung validieren',
                                    },
                                },
                            },
                            notifications: {
                                label: 'Sie haben automatische Projektdurchläufe aktiviert, bitte schließen Sie die Konfiguration der Projektdurchlauf-Zeiten ab.',
                            },
                        },
                    },
                    logs: {
                        label: 'Ereignisprotokoll',
                        description: 'Betsimmen Sie, welche Ereignisse protokolliert werden sollen.',
                    },
                },
                notifications: {
                    updateSuccess: 'Projekt "{{name}}" wurde erfolgreich aktualisiert.',
                    updateSuccessWithSubscriptionInfo: 'Projekt "{{name}}" wurde erfolgreich aktualisiert und wurde mit dem Abonnement "{{}}"',
                    saveSuccess: 'Projekt "{{name}}" wurde erfolgreich erstellt.',
                },
            },
        },
        subscription: {
            current: 'Aktuelles Abonnement',
            changed: {
                success: 'Projektabonnement erfolgreich aktualisiert',
                failure: 'Projekt-Abonnement-Update fehlgeschlagen',
            },
        },
        stateNotification: 'Projekt muss aktiv sein, um mit dem Zeitplaner oder manuell zu starten.',
        sync: 'Projekt Sync',
    },
    projects: {
        label: 'Projekte',
        pageTitle: 'Meine Projekte',
        list: 'Projekte',
        summary: {
            label: 'Sie haben {{count}} aktive Projekte',
            label_one: 'Sie haben ein aktives Projekt',
        },
        goto: 'Zu meinen Projekten',
    },
    readMore: 'Erfahren Sie mehr',
    register: {
        label: 'Konto erstellen',
        pageTitle: 'Konto erstellen',
        accountQuestion: 'Haben Sie schon ein Konto?',
        registerHere: 'Hier Konto erstellen',
        teaser: 'Erstellen Sie ein Konto mit Ihrer E-Mail-Adresse.',
        perInvite: {
            label: 'Einladung annehmen',
            teaser: 'Willkommen bei {{app}}! Bitte erstellen Sie Ihr Passwort, um Ihr Konto zu aktivieren und der Organisation {{organisation}} beizutreten.',
        },
        declaration: 'Mit der Registrierung erkläre ich mich mit der {{privacyPolicy}} und {{termsAndConditions}} von {{app}} einverstanden.',
        validation: {
            error: 'Fehler bei der Registrierung, bitte versuchen Sie es erneut.',
            methodError: 'Die Anmeldung mit {{method}} ist derzeit nicht möglich, bitte versuchen Sie eine andere Methode.',
            registrationError: 'Die Registrierung ist nicht möglich, bitte versuchen Sie sich anzumelden, falls die E-Mail Adresse schon registriert ist.',
            accountCreateSuccess: 'Herzlichen Glückwunsch, Sie sind nun erfolgreich in Ihr neues {{app}} Konto eingeloggt.',
        },
    },
    requestDemo: 'Demo anfragen',
    reset: 'Zurücksetzen',
    resetFilters: 'Alle filter Zurücksetzen',
    resetJS: 'Code zurücksetzen',
    resources: {
        label: 'Ressourcen',
        groups: {
            documentation: {
                label: 'Documentation',
            },
            legal: {
                label: 'Rechliches',
            },
            shortcuts: {
                label: 'Shortcuts',
            },
        },
    },
    result: 'Ergebnis',
    roundUpInfo: 'Der Wert wurde auf das nächste {{step}} aufgerundet.',
    ruleIndex: 'Regel {{index}}: {{type}}',
    running: 'Läuft',
    save: 'Speichern',
    saveAndContinue: 'Speichern & Weiter',
    saveAndContinueToProject: 'Speichern & zum Projekt',
    saved: 'Gespeichert!',
    scroll: {
        top: 'Nach oben',
        bottom: 'Nach unten',
    },
    seconds: 'Sekunden',
    security: {
        label: 'Sicherheit',
    },
    selectAll: 'Alle auswählen',
    selectedCount: '{{numSelected}} ausgewählt',
    selection: {
        label: 'Auswahl',
        options: {
            selectAll: 'Alle auswählen',
            deselectAll: 'Alle abwählen',
        },
    },
    selector: {
        label: 'Selektor',
        invalid: 'Bitte geben Sie einen gültigen Selektor an.',
    },
    settings: {
        title: 'Einstellungen',
        language: 'Meine Bevorzugte Sprache',
        appearance: 'Erscheinungsbild',
        direction: 'Ausrichtung',
        accent: 'Farbeinstellung',
        fullScreen: {
            enter: 'Vollbildmodus',
            exit: 'Vollbildmodus beenden',
        },
        fontSize: {
            label: 'Schriftgröße',
        },
    },
    sessionId: 'Sitzung',
    solution: 'Lösung',
    workflowSteps: {
        startAddingSteps: 'Workflow-Steps definieren',
        addStep: 'Neuer Step',
        delay: 'Verzögerung zwischen den Steps',
        addStepBefore: 'Neuer Step vorher',
        addStepAfter: 'Neuer Step nacher',
        dynamicLabel: 'Wird zu Laufzeit ermittelt!',
        state: {
            label: 'Status',
            description: 'Dieser Step ist: {{state}}',
            active: 'Aktiv',
            inactive: 'Nicht aktiv',
            notifications: {
                active: 'Der Step ist aktiv und wird beim Projektdurchlauf ausgeführt.',
                inactive: 'Der Step ist nicht aktiv und wird beim nächsten Projektdurchlauf nicht ausgeführt.',
            },
            cta: {
                activate: 'Step aktivieren',
                deactivate: 'Step deaktivieren',
            },
        },
        options: {
            action: {
                label: 'Benutzerinteraktion simulieren',
                ctaLabel: 'Aktionen hinzufügen oder Einstellungen ändern',
            },
            condition: {
                label: 'Bedingte Steps',
                ctaLabel: 'Eine bedingten Folge von Steps hinzufügen',
                subStepLabel_one: 'Basiert auf dieser Bedingung wird 1 weiterer Step ausgeführt.',
                subStepLabel: 'Basiert auf dieser Bedingung werden {{count}} weitere Steps ausgeführt.',
                saveNotification: 'Sie müssen mindestens einen Step hinzufügen, bevor der Block gespeichert werden kann.',
                labels: {
                    conditionConfig: 'Bedingung',
                    conditionSettings: 'Einstellungen',
                    conditionSteps: 'Steps',
                    abortion: 'Bedingungsblock abbrechen, wenn ein Step fehlschlägt.',
                    ignore: 'Fehler im diesem Step ignorieren.',
                },
                notifications: {
                    willBeIgnored: 'Fehler in diesem Step wird ignoriert (siehe Einstellung des Blocks).',
                },
            },
            loop: {
                label: 'Steps in Schleifen',
                ctaLabel: 'Schleife mit einer Sequenz von Steps hinzufügen',
                subStepLabel_one: 'In dieser Schleife wird 1 Step ausgeführt.',
                subStepLabel: 'In dieser Schleife werden {{count}} Steps ausgeführt.',
                saveNotification: 'Sie müssen mindestens einen Step hinzufügen, bevor der Block gespeichert werden kann.',
                labels: {
                    loopConfig: 'Schleife konfigurieren',
                    loopSettings: 'Einstellungen',
                    loopSteps: 'Steps',
                    abortion: 'Schleifenblock abbrechen, wenn ein Step fehlschlägt.',
                    ignore: 'Fehler im diesen Step ignorieren.',
                },
                notifications: {
                    indexReference: {
                        description: 'Ein Verweis auf das aktuelle Element wird erstellt und in jedem JS-Code durch den folgenden Parameter zur Verfügung gestellt:',
                        reload: 'Achtung! Die Verweise können verloren gehen, wenn die aktuelle Seite neu geladen wird oder wenn von der aktuellen URL weg navigiert wird.',
                    },
                    willBeIgnored: 'Fehler in diesem Step wird ignoriert (siehe Einstellung des Blocks).',
                },
                breakCondition: {
                    label: 'Abbruchbedingung',
                    description: {
                        ctaPre: 'In jeder Iteration werden alle Steps ausgeführt. Klicken Sie auf ',
                        ctaPost: ', um die Schleife basiert auf den Rückgabewert der Funktion abzubrechen.',
                    },
                },
            },
        },
        label: 'Workflow-Steps',
        solutionsOnly: 'Nur {{solution}} Steps anzeigen',
        view: 'Workflow-Step anzeigen',
        subItems: {
            label: 'Sub-Steps',
            label_one: 'Sub-Step',
            view: 'Sub-Steps anzeigen',
            view_one: 'Sub-Step anzeigen',
        },
        showInactiveItems: 'Inaktive anzeigen',
        step: 'Step',
    },
    solutions: {
        label: 'Lösungen',
    },
    solutionsLabel: 'Kernfunktionen',
    solutionsUsage: {
        automatizeIt: 'Automatisieren Sie diese Prozesse mit {{app}}!',
        executionAnytimeAnyBrowserAnyPlace: 'Sie können die automatisierten Prozesse jederzeit, mit unterschiedlichen Browsern - {{browsers}} ausführen.',
        examples: {
            auditingAndApprovingComments: {
                title: 'User-Kommentaren',
                label: 'Stellen Sie sich vor, Sie automatisieren diese Steps zur Überprüfung und Genehmigung von Benutzerkommentaren auf Ihrer Website:',
                items: [
                    'Laptop öffnen und Browser starten.',
                    'Adresse Ihres Website-Admin-Panels (z.B. https://www.yourwebsite.com/admin) eingeben.',
                    'Anmeldelink anklicken.',
                    '"Anmelden" anklicken.',
                    'Benutzernamen eingeben.',
                    'Passwort eingeben.',
                    'Warten bis Anmeldung abgeschlossen und Seite neu geladen ist.',
                    'Zum Kommentar-Moderationsbereich navigieren.',
                    'Neue Benutzerkommentare anzeigen und Inhalte prüfen.',
                    'Bei akzeptablen Kommentaren "Genehmigen" anklicken.',
                    'Bei problematischen Kommentaren "Ablehnen" oder "Zur Überprüfung markieren" anklicken.',
                    'Antworten auf genehmigte Kommentare schreiben und auf "Senden" klicken, falls erforderlich.',
                    'Die letzten 3 Steps für andere Kommentare wiederholen.',
                    '"Abmelden" anklicken.',
                ],
                timeSpent: 'Bis zu {{durationInMins}} Minuten könnten für diese Aufgabe mehrmals täglich benötigt werden.',
            },
            managingSocialMedia: {
                title: 'Social-Media',
                label: 'Stellen Sie sich vor, Sie automatisieren diese Steps auf Ihren Social-Media-Konten:',
                items: [
                    'Laptop öffnen und Browser starten.',
                    'Adresse der Social-Media-Plattform (z.B. https://www.facebook.com) eingeben.',
                    'Anmeldelink anklicken.',
                    '"Anmelden" anklicken.',
                    'Benutzernamen eingeben.',
                    'Passwort eingeben.',
                    'Warten bis Anmeldung abgeschlossen und Seite neu geladen ist.',
                    'Zum Post-Erstellungsbereich navigieren.',
                    'Neuen Beitrag schreiben und Bild/Video hochladen.',
                    '"Posten" anklicken.',
                    'Auf Benachrichtigungen klicken, um Kommentare und Likes zu überprüfen.',
                    'Antworten auf Kommentare schreiben und auf "Senden" klicken.',
                    'Die letzten 3 Steps für andere Posts und Plattformen wiederholen.',
                    '"Abmelden" anklicken.',
                ],
                timeSpent: 'Bis zu {{durationInMins}} Minuten könnten für diese Aufgabe mehrmals täglich benötigt werden.',
            },
            managingCustomerSupport: {
                title: 'Kundensupport',
                label: 'Stellen Sie sich vor, Sie automatisieren diese Steps in Ihrem Kundensupport:',
                items: [
                    'Laptop öffnen und Browser starten.',
                    'Adresse Ihres Support-Systems (z.B. https://www.yoursupport.com) eingeben.',
                    'Anmeldelink anklicken.',
                    '"Anmelden" anklicken.',
                    'Benutzernamen eingeben.',
                    'Passwort eingeben.',
                    'Warten bis Anmeldung abgeschlossen und Seite neu geladen ist.',
                    'Zum Ticket-Verwaltungsbereich navigieren.',
                    'Neue Support-Tickets anzeigen und Details lesen.',
                    'Antworten auf Tickets schreiben und auf "Senden" klicken.',
                    'Tickets nach Priorität sortieren und zuweisen.',
                    'Die letzten 3 Steps für andere Tickets wiederholen.',
                    '"Abmelden" anklicken.',
                ],
                timeSpent: 'Bis zu {{durationInMins}} Minuten könnten für diese Aufgabe mehrmals täglich benötigt werden.',
            },
        },
        label: 'Wofür Leute {{app}} nützen',
        options: {
            label: 'Beispiele',
            items: {
                linkValidation: {
                    label: 'Linküberprüfung',
                    description: 'Automatische Überprüfung und Validierung aller Links auf einer Webseite, um sicherzustellen, dass sie korrekt funktionieren.',
                },
                endToEndVisualTesting: {
                    label: 'E2E Visuelle Tests',
                    description: 'Vergleich visueller Elemente auf verschiedenen Seiten und Sitzungen, um Konsistenz zu gewährleisten und Probleme frühzeitig zu erkennen.',
                },
                screenshotCapturing: {
                    label: 'Screenshot-Dokumentation',
                    description: 'Automatisches Erfassen von Screenshots der Webseiten für Dokumentation, Fehlersuche oder Design-Versionierung.',
                },
                dataExtraction: {
                    label: 'Datenextraktion',
                    description: 'Automatisierung der Extraktion erforderlicher Daten von Webseiten für Analyse oder Berichterstellung.',
                },
                screenVideoRecording: {
                    label: 'Video-Dokumentation',
                    description: 'Aufzeichnung von Interaktionen auf einer Webseite, um das Benutzerverhalten besser zu verstehen oder Probleme zu diagnostizieren.',
                },
                automatedFormFilling: {
                    label: 'Automatisiertes Formularausfüllen',
                    description: 'Automatisches Ausfüllen von Webformularen für Testzwecke oder Dateneingabe.',
                },
                contentMonitoring: {
                    label: 'Inhaltsüberwachung',
                    description: 'Regelmäßige Überprüfung von Webseiten auf Inhaltsänderungen oder -aktualisierungen.',
                },
                seoAudits: {
                    label: 'SEO-Audits',
                    description: 'Durchführung automatisierter SEO-Audits zur Optimierung der Website-Sichtbarkeit und des Suchmaschinenrankings.',
                },
                performanceMonitoring: {
                    label: 'Leistungsüberwachung',
                    description: 'Kontinuierliche Überwachung von Leistungsmetriken der Webseite.',
                },
                userBehaviorSimulation: {
                    label: 'Simulation des Benutzerverhaltens',
                    description: 'Simulation verschiedener Benutzerinteraktionen, um die Reaktionsfähigkeit und Funktionalität der Webseite zu testen.',
                },
                accessibilityTesting: {
                    label: 'Zugänglichkeitstests',
                    description: 'Sicherstellung, dass Webseiten für alle Benutzer zugänglich sind, einschließlich Menschen mit Behinderungen.',
                },
                securityTesting: {
                    label: 'Sicherheitstests',
                    description: 'Überprüfung auf Schwachstellen und potenzielle Sicherheitsbedrohungen auf Webseiten.',
                },
                competitorAnalysis: {
                    label: 'Wettbewerbsanalyse',
                    description: 'Automatische Sammlung von Daten von Wettbewerber-Webseiten für Marktanalysen.',
                },
                eCommerceTesting: {
                    label: 'E-Commerce-Tests',
                    description: 'Testen von E-Commerce-Plattformen auf Funktionalität, Benutzererlebnis und Transaktionsprozesse.',
                },
                apiTesting: {
                    label: 'API-Tests',
                    description: 'Automatisierung des Testens von Web-APIs auf Funktionalität, Zuverlässigkeit, Leistung und Sicherheit.',
                },
                responsiveDesignTesting: {
                    label: 'Responsive Design-Tests',
                    description: 'Sicherstellung, dass Webseiten auf verschiedenen Geräten und Bildschirmgrößen korrekt dargestellt werden.',
                },
                contentScraping: {
                    label: 'Inhalts-Scraping',
                    description: 'Extraktion spezifischer Inhalte von Webseiten für Forschungszwecke oder Datenzusammenstellung.',
                },
                automatedReporting: {
                    label: 'Automatisiertes Berichtswesen',
                    description: 'Erstellung regelmäßiger Berichte über Website-Metriken, Leistung oder andere wichtige Datenpunkte.',
                },
            },
        },
    },
    state: 'Zustand',
    status: 'Status',
    subPage: 'Unterseite',
    submit: {
        label: 'Absenden',
    },
    subscribe: 'Jetzt Abonnieren',
    transactions: {
        label: 'Transaktionen',
        pageTitle: 'Meine Transaktionen',
        userItems: 'Meine Transaktionen',
        createdOn: 'Erstellt',
        license: 'Lizenz',
        validity: 'Gültigkeit',
        status: 'Status',
        automationCreditsConsumption: {
            label: 'Automation-Credits Verbrauch',
            fields: {
                billableAction: 'Abrechenbare Aktion',
                billingCycle: 'Abrechnungszyklus',
                context: 'Kontext',
                quantity: 'Menge',
                reference: 'Referenz',
                credits: 'Gutschriften',
                timestamp: 'Zeitstempel',
                billableActions: 'Abrechenbare Aktionen',
                consumption: 'Verbrauch',
            },
            empty: {
                label: 'Keine Automation-Credits Verbrauch gefunden',
            },
        },
        automationCreditsTopUp: {
            label: 'Automation-Credits Aufstockung',
            fields: {
                billingCycle: 'Abrechnungszyklus',
                credits: 'Gutschriften',
                date: 'Datum',
                expirationDate: 'Ablaufdatum',
                reference: 'Referenz',
            },
            empty: {
                label: 'Keine Automation-Credits Aufstockung gefunden',
            },
        },
        missingItem: 'Suchen Sie etwas?',
        billingCycle: {
            label: 'Abrechnungszyklus',
            current: 'Aktueller Abrechnungszeitraum',
            cycle: 'Abrechnungszeitraum',
            selection: 'Wählen Sie den Abrechnungszeitraum',
        },
        orderHistory: {
            label: 'Abrechnung',
            fields: {
                id: 'ID',
                created: 'Erstellt',
                status: 'Status',
                amount: 'Betrag',
                invoice: 'Rechnung',
                email: 'Email',
                context: 'Aktion',
                billingAction: {
                    CHECKOUT: 'Kasse',
                    AUTOMATION_CREDITS_AUTO_RENEW: 'Automatisches Aufladen',
                },
            },
            viewDetails: 'Zur Abrechnung',
        },
        payment: {
            status: {
                label: 'Zahlungsstatus',
                options: {
                    draft: {
                        label: 'Entwurf',
                        description: 'Die Rechnung wurde erstellt, aber noch nicht an den Kunden gesendet.',
                    },
                    open: {
                        label: 'Offen',
                        description: 'Die Rechnung wurde an den Kunden gesendet, aber die Zahlung wurde noch nicht erhalten.',
                    },
                    paid: {
                        label: 'Bezahlt',
                        description: 'Die Rechnung wurde bezahlt.',
                    },
                    uncollectible: {
                        label: 'Uneinbringlich',
                        description: 'Die Zahlung für die Rechnung wurde nicht erhalten und gilt als uneinbringlich.',
                    },
                    void: {
                        label: 'Ungültig',
                        description: 'Die Rechnung wurde ungültig gemacht.',
                    },
                },
            },
            trigger: {
                label: 'Jetzt bezahlen',
            },
        },
        creditEvents: {
            contexts: {
                event: 'Ereignis',
                options: {
                    DEMO: 'Kostenloses Guthaben für Demo',
                    PROJECT_ASSISTANT: 'Projekt Setup',
                    PROJECT_BUILD: 'Projekt ausführen',
                    CHECKOUT: 'Shop Kasse',
                    PROJECT_STORAGE: 'Speicherplatz',
                    EXPIRATION: 'Ablauf',
                    MONTHLY_SUBSCRIPTION_SETTLEMENT: 'Monatliche Abonnementabrechnung',
                    SUBSCRIPTION: 'Abonnementskauf',
                },
            },
            balanceBefore: 'Kontostand Vorher',
            balanceAfter: 'Kontostand Nachher',
        },
        subscriptions: {
            label: 'Abonnements',
            active: 'Dieses Paket ist für Sie aktiv',
            change: {
                label: 'Abonnement ändern',
                title: 'Ihre Aufmerksamkeit ist bezüglich des Abonnements erforderlich',
            },
            cycle: 'Abo-Zyklus',
            inActive: 'Dieses Paket ist nicht im Ihrem Abonnement enthalten',
            isCancelled: 'Dieses Abonnement ist bereits gekündigt und wird am {{endDate}} deaktiviert, sie können dies wieder in Ihrem Kundenportal reaktivieren.',
            items: {
                notFound: 'Die Abonnement mit der Rechnungsnummer {{invoiceNumber}} konnte nicht gefunden werden..',
                rest: 'Übrig',
                restTooltip: 'Noch {{valueWithUnit}} ({{percentageValue}}) übrig',
                usedUp: 'Verbraucht',
                usedUpTooltip: 'Schon {{valueWithUnit}} ({{percentageValue}}) verbraucht',
                halfUsedUp: 'Sie haben bisher 50% aufgebraucht.',
                states: {
                    active: 'Aktiv',
                    inActive: 'Inaktiv',
                },
            },
            missingPackageLabel: 'Funktion kann nicht aktiviert werden.',
            multipleActiveLabel: 'Abonnement spezifizieren',
            noActiveLabel: 'Keine aktive Abonnements gefunden.',
            openInvoice: 'Rechnungsdetails anzeigen',
            paidOn: 'Bezahlt am {{paymentDate}}',
            packageRecommendations: {
                label: 'Empfohlene Pakete',
                notifications: {
                    success: 'Paket wurde für Sie in den Warenkorb gelegt.',
                },
            },
            selectLabel: 'Auswählen',
            states: {
                active: 'Aktiv',
                inActive: 'Nicht aktiv',
            },
            tabs: {
                details: 'Übersicht',
                usage: 'Kontingente / Verbrauch',
            },
            validSince: 'Gültig seit',
            validTill: 'Gültig bis',
            viewItem: 'Abonnement anzeigen',
            userItems: 'Meine Abonnements',
            activeLabel: 'Sie haben {{count}} aktive Abonnements',
            usageReset: {
                label: 'Nächster monatlicher Reset',
                remainingDays: 'In {{days}} Tagen',
            },
            showOnlyActiveItems: 'Nur aktive Elemente anzeigen',
            isPastDue: 'Ihre Zahlung für dieses Abonnement ist überfällig und konnte nicht verarbeitet werden. Bitte aktualisieren Sie Ihre Zahlungsdetails in Ihrem Kundenportal, um den Service fortzusetzen.',
            hasEnteredOverage: {
                label: 'Achtung! Einige Ihrer Ressourcen haben das Planlimit überschritten. Kein Problem – Sie können mit Übernutzungsgebühren weitermachen oder ein Upgrade in Betracht ziehen!',
            },
        },
        automationCredits: {
            label: 'Automation-Credits',
            autoReload: 'Automatisches Aufladen',
            autoReloadDescription: 'Wenn aktiviert, wird Ihr Konto automatisch nachgeladen, wenn Ihr Guthaben unter den von Ihnen festgelegten Schwellenwert fällt.',
            autoReloadActive: 'Automatisches Aufladen aktiv',
            autoReloadActiveDescription: 'Wenn Ihr Automation-Credits-Guthaben unter {{threshold}} fällt, wird Ihr Konto mit {{amount}} Automation-Credits nachgeladen.',
            autoReloadInActive: 'Automatisches Aufladen inaktiv',
            autoReloadInActiveDescription: 'Aktivieren Sie die automatische Wiederaufladung, um Überziehungen für Ihre Projekt-Builds zu aktivieren und Unterbrechungen zu vermeiden.',
            productItems: 'Produktartikel, die automatisch gekauft werden sollen.',
            selectItem: 'Produktartikel auswählen',
            threshold: 'Schwellenwert',
            balanceAndExpiryInfo: 'Ihr Automation-Credits-Guthaben beträgt {{balance}} und läuft am {{expirationDate}} ab.',
        },
    },
    summary: 'Zusammenfassung',
    templates: {
        label: 'Vorlagen',
        pageTitle: 'Meine Vorlagen',
        actions: {
            label: 'Aktionen',
            options: {
                view: 'Ansehen',
                download: 'Herunterladen',
                delete: 'Löschen',
                edit: 'Bearbeiten',
            },
        },
        files: {
            label: 'Dateien',
            upload: {
                label: 'Datei hochladen',
            },
            edit: {
                label: 'Hochladene Datei bearbeiten',
                description: 'Sie bearbeiten die Datei: "{{name}}".',
            },
            delete: {
                label: 'Hochladene Datei löschen',
                description: 'Sind Sie sicher, dass Sie die Datei: "{{name}}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
            },
            empty: 'Sie haben noch keine Dateien abgelegt.',
            info: 'Sie können Dateien mit Grösse bis {{fileSize}} hochladen.',
            description: 'Wählen Sie eine Datei, die hochgeladen werden sollte.',
            multipleInfo: 'Beachten Sie bitte: Wenn das Formular auf Ihrer Seite das Hochladen mehrerer Dateien nicht unterstützt, wird nur die erste ausgewählte Datei hochgeladen. Bei Formularen, die mehrere Dateien akzeptieren, werden alle ausgewählten Dateien hochgeladen.',
            name: 'Dateiname',
            hint: 'Hinweis',
            uploadedAt: 'Hochgeladen am',
            uploadedBy: 'Hochgeladen von',
            mimeType: 'MIME-Typ',
            size: 'Größe',
            dropOrSelect: {
                preview: 'Vorschau',
                label: 'Auswählen oder Droppen',
                description: 'Legen Sie die Dateien hier per Drag & Drop oder klicken Sie hier, um Dateien aus ihren Filesystem zu laden.',
            },
            notification: {
                fileNotFound: 'Die Datei mit ID: "{{id}}" ist nicht mehr vorhanden.',
                uploaded: 'Die Datei {{name}} wurde erfolgreich gespeichert.',
                deleted: 'Die Datei {{name}} wurde erfolgreich gelöscht.',
                updated: 'Die Datei {{name}} wurde erfolgreich aktualisiert.',
                fileTooBig: 'Die Datei {{name}} ist {{fileSize}} gross. Die maximale Dateigröße beträgt {{maxFileSize}}.',
                fileInvalid: 'Die Datei {{name}} ist ungültig!',
            },
            tags: {
                label: 'Datei-Tags',
                description: 'Um Ihre Dateien zu organisieren und zu verwalten, fügen Sie beliebige Tags durck Komma "," getrennt.',
            },
        },
    },
    termsAndConditions: {
        label: 'Geschäftsbedingungen',
        pageTitle: 'Geschäftsbedingungen',
    },
    testLink: 'URL testen',
    themes: {
        light: 'Hell',
        dark: 'Dunkel',
    },
    timestamp: 'Zeitpunkt',
    unchangeable: 'Der Wert "{{reference}}" kann nicht mehr geändert werden.',
    upgradePlan: 'Jetzt upgraden',
    url: 'URLs',
    url_one: 'URL',
    urlCapture: {
        title: 'Testen Sie hier, ob URLs die Bedingung erfüllen würden.',
        label: 'URL-Bedingung',
        helperText: 'Es werden nur passende URLs berücksichtigt.',
        criteria: 'Kriterien für die Übereinstimmung',
        criteriaAll: 'Alle Regel müssen passen',
        criteriaAny: 'Mindestens ein Regel passt',
        moreLabel: 'Regel hinzufügen',
        description: 'Sie können festlegen ob bestimmte Zeichenfolgen in den URLs vorkommen "Substring", ob die URLs eine genaue Struktur hat "Exact" oder zu einem Regulärer Ausdruck passen "Regex".',
        testUrlSuccess: 'Der URL passt.',
        testUrlFailure: 'Der URL passt nicht.',
        testUrlFailureGlobal: 'Dieser URL wurde globalen ausgeschlossen.',
        gotoGlobalSetting: 'Zur globalen Filter Einstellung',
        valueRequired: 'Bitte geben Sie einen Wert ein',
        options: {
            exact: {
                label: 'Exakt',
                validationError: 'Bitte geben Sie einen gültigen URL-String ein',
            },
            substring: {
                label: 'Substring',
                validationError: 'Bitte geben Sie einen gültigen URL-String ein',
            },
            regex: {
                label: 'Regex',
                validationError: 'Bitte geben Sie einen gültigen Regex ein',
            },
        },
        externals: {
            url: 'URL',
            authorization: {
                label: 'Authorization',
                options: {
                    username: 'Username',
                    password: 'Password',
                },
            },
            headers: {
                label: 'Headers',
            },
            duplicateErrorMessage: 'Diese URL wurde schon erfasst',
        },
    },
    urlRequest: {
        context: {
            currentPage: 'Einstieg-Seite',
            xhr: 'Webresource',
        },
        url: {
            label: 'URL',
            notification: {
                empty: 'Geben Sie eine gültige URL ein.',
            },
        },
        method: {
            label: 'Methode',
        },
    },
    urls: 'URLs',
    usageRestrictionPolicy: {
        label: 'Nutzungsbeschränkungsrichtlinie',
        pageTitle: 'Nutzungsbeschränkungsrichtlinie',
    },
    used: 'Verbraucht',
    users: {
        label: 'Mitarbeiter',
        profile: {
            label: 'Mein Profil',
            overview: 'Übersicht',
            changePassword: 'Passwortänderung',
            languageChange: {
                label: 'Bevorzugte Sprache aktualisieren?',
                description: 'Sie haben die Sprache der Benutzeroberfläche geändert. Möchtest du dies auch als deine bevorzugte Sprache für den Erhalt von Benachrichtigungen und andere Kommunikationen festlegen?',
            },
        },
    },
    validate: 'Validieren',
    validateCode: {
        label: 'Code validieren',
        isRequiredForSave: 'Code muss valid sein, bevor er gespeichert werden kann.',
    },
    value: 'Wert',
    verifyUser: {
        label: 'Konto bestätigen',
        pageTitle: 'Konto bestätigen',
        loginRequired: 'Sie müssen sich zuerst einloggen, um die Ihr konto zu bestätigen.',
    },
    void: '',
    webAutomate: {
        description: 'Konfigurieren, planen und automatisieren Sie Tätigkeiten auf Ihrer Webseite mit {{app}}',
    },
    webAutomateUtils: 'Helper Funktionen',
    yes: 'Ja',
    yourOptions: 'Ihre Optionen',
    codeVerification: {
        sendCode: 'Code senden',
        enterCode: 'Geben Sie den erhaltenen Code ein',
        noCodeReceived: 'Keinen Code erhalten?',
    },
    clickHere: 'Hier klicken',
    accountSecurity: {
        label: 'Kontosicherheit',
    },
    validateAiText: {
        label: 'AI-Text validieren',
        isRequiredForSave: 'Der AI-Agent muss den Text verstehen, bevor er gespeichert werden kann.',
    },
    overages: {
        label: 'Überzüge',
        description: 'Überzüge entstehen, wenn Sie die in Ihrem Abonnement enthaltenen Ressourcen überschreiten.',
    },
    ai: {
        confidence: {
            label: 'AI-Score',
            description: 'Der AI-Agent ist {{value}}% sicher über die Antwort.',
        },
        step: {
            label: 'AI-gestützter Step',
            description: 'Dies ist ein Step, der von AI unterstützt wird.',
        },
    },
    view: {
        error: 'Fehler anzeigen',
        aiDialog: 'AI-Dialog',
    },
    caching: {
        label: 'Zwischenspeicherung',
        inUse: 'Zwischenspeicher verwendet',
        notInUse: 'Zwischenspeicher nicht verwendet',
        view: 'Zwischenspeicher anzeigen',
        clear: 'Zwischenspeicher leeren',
        cachedTime: 'Zwischengespeichert um',
        notifications: {
            cacheCleared: 'Zwischenspeicher erfolgreich geleert.',
        },
    },
    charCount: 'Zeichenanzahl',
    byteCount: 'Dateigröße',
    changes: 'Änderungen',
    changes_one: 'Änderung',
};
