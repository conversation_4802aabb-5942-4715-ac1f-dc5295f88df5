export default {
    account: {
        label: 'My Account',
        pageTitle: 'My Account',
        holder: 'Admin',
        created: 'Created on',
        overview: {
            label: 'Overview',
        },
        settings: {
            label: 'Settings',
            timeZone: {
                label: 'Time Zone',
                filter: 'Start typing...',
                autoDetect: 'Auto detect',
            },
            language: {
                label: 'Preferred Language',
                description: 'Choose the preferred language for your account in which you will receive notifications.',
            },
            builds: {
                timeoutHandling: {
                    label: 'Handling for failed builds',
                    description: 'What should happen if the build times out?',
                    options: {
                        noop: 'Do nothing',
                        retry: 'Retry Project-Run',
                    },
                },
            },
        },
        domains: {
            label: 'Domains',
            goto: 'Go to domain overview',
            blacklisted: {
                label: 'Blocked Domains',
                description: 'Here is a list of domains that have been blocked for your account. Check the details and status of each domain and manage them as needed.',
                info: 'Blocked domains are not handled by our AI browser.',
                target: {
                    options: {
                        global: 'Blocked globally',
                        account: 'Blocked for your account only',
                    },
                },
                contactUs: {
                    label: 'If you have questions about these blocked domains, please contact us through our support channel.',
                },
                items: {
                    count: '{{count}} domains are blocked for your account.',
                    count_one: 'One domain is blocked for your account.',
                },
            },
            projectBuild: {
                label: 'Project Domains',
                description: 'Here is a list of domains you have used for your projects so far. Check the details and status of each domain and manage them as needed.',
                domain: 'Domain',
                firstBuilt: 'First Project-Run',
                latestBuilt: 'Latest Project-Run',
                gotoProject: 'Go to project',
                items: {
                    count: 'You have built with {{count}} domains so far.',
                    count_one: 'You have built with one domain so far.',
                },
            },
            verification: {
                label: 'Domain Verification',
                cta: 'Verify your domains to gain full control over your website automations.',
                description: 'Here is a list of domains you have submitted for verification. Check the status and details of verification for each domain. If you wish to revoke the verification, click on "{{revoke}}".',
                info: 'Verify your domains to gain full control over your website automations. Choose a verification method and follow the instructions to verify your domain. If you have questions, please contact our support team.',
                domainToVerify: 'Domain to verify',
                startVerification: 'Verify domain',
                VERIFY: 'Verify now',
                REVOKE: 'Revoke verification',
                state: {
                    VERIFIED: 'Verified',
                    PENDING: 'Pending',
                },
                methods: {
                    label: 'Methods',
                    options: {
                        COLLABORATOR: {
                            label: 'Collaborator',
                            description: 'We recognize your domain ownership if your email or your collaborator\'s email is associated with the domain.',
                            notices: [
                                'The email address must be associated with the domain.',
                                'If the collaborator is removed, the domain verification will be revoked.',
                            ],
                        },
                        META_TAG: {
                            label: 'Meta Tag',
                            description: 'We recognize your domain ownership if you add a specific meta tag to your website\'s source code.',
                            specifyUrl: 'Please specify the URL where the meta tag was added.',
                            copy: 'Copy meta tag',
                            notices: [
                                'Please ensure the URL is publicly accessible.',
                                'The meta tag must be served by the server and not added later via JavaScript.',
                                'The URL must start with "http://" or "https://".',
                            ],
                        },
                        ROBOTS_TXT: {
                            label: 'robots.txt',
                            description: 'We recognize your domain ownership if you grant {{app}} access to your website in the robots.txt file.',
                            copy: 'Copy text',
                        },
                    },
                },
                items: {
                    label: 'Submitted domains for verification',
                    count: 'You have submitted {{count}} domains for verification so far.',
                    count_one: 'You have submitted one domain for verification so far.',
                    revoke: 'Revoke verification',
                    ts: 'Updated on',
                    verifiedBy: 'Requested by',
                    verifiedTill: 'Verified till',
                    notifications: {
                        VERIFIED: 'Your domain has been successfully verified.',
                        REVOKED: 'The verification of your domain has been revoked.',
                        PENDING: 'The verification of your domain is pending.',
                    },
                },
                ethicalControl: {
                    label: 'Ethical Control',
                    description: 'As per our terms of use and privacy policy, we respect the robots.txt files of websites. This is considered ethical control. Adhering to robots.txt ensures we do not overload sites or go against the wishes of the website owner. If this is your own website, you can disable ethical control by verifying your domain.',
                },
            },
        },
        history: {
            label: 'Logs',
            projectEvents: 'Project logs ({{count}})',
        },
        states: {
            active: {
                label: 'Active',
                shortLabel: 'Active',
                description: 'Your account is active and ready to use.',
            },
            new: {
                label: '{{star}}Welcome to {{app}}{{star}}',
                shortLabel: 'New',
                description: 'We are glad you chose {{app}}.',
                usps: [
                    'Harness the full potential of your website with our intuitive, no-code/low-code automation platform. Whether you want to validate links, conduct Google Lighthouse audits, or automate URL challenges and data extraction, {{app}} allows you to do all this with ease and efficiency.',
                    'Our platform is designed for businesses, developers, and digital enthusiasts alike, making web automation accessible to all. Start creating, running, and managing your projects with just a few clicks and witness the transformation in how you interact with the web.',
                    'Dive now into a seamless experience and see the difference automation can make. Let\'s renew, automate, and enhance your web tasks today!',
                ],
                footer: 'Start your automation journey with us. {{rocket}}',
            },
            suspended: {
                label: 'Account Suspended',
                shortLabel: 'Suspended',
                description: 'Your account has been suspended, you will be logged out automatically. Please contact our support team.',
            },
            gracePeriod: {
                label: 'Account in Grace Period',
                shortLabel: 'Grace Period',
                description: 'Your account is in grace period because you have neither Automation-Credits nor a subscription. Top up your balance or subscribe to a package {{numberOfDaysLeft}} to avoid account closure.',
            },
            inactive: {
                label: 'Account Inactive',
                shortLabel: 'Inactive',
                description: 'Your account is inactive. Please contact support to reactivate your account.',
            },
        },
        customerPortal: {
            label: 'Customer Portal',
            ready: 'A connection to the customer portal has been established.',
            view: 'Click here to visit your customer portal',
        },
    },
    accountNotifications: {
        label: 'My Notifications',
        pageTitle: 'Your Notifications',
        noNotification: 'You have no notifications',
        description: 'You have {{count}} new notification(s)',
        description_one: 'You have 1 new notification',
        new: 'New Notifications',
        old: 'Read Notifications',
        viewAll: 'View all',
        states: {
            new: 'New',
            old: 'Read',
        },
        actions: {
            label: 'Actions',
            options: {
                markAsRead: 'Mark as read',
                markAsUnread: 'Mark as unread',
                delete: 'Delete',
            },
        },
        confirmDelete: {
            label: 'Delete Notification',
            message: 'Are you sure you want to delete the notification? This action cannot be undone.',
        },
        types: {
            blacklistedDomainsInProject: 'Restricted Domains',
            demoSubscription: 'Your Demo Request',
            custom: '{{app}} Notification',
            buildNotification: 'Project-Run Update',
        },
    },
    actionability: {
        label: 'Notice on Actionability',
        description: 'The AI first conducts a series of actionability checks on the elements before we perform actions to ensure that these actions behave as expected.',
        alternative: 'You can bypass this by switching to {{javaScript}}.',
        failureNotice: 'This action will fail if an actionability check fails.',
        itemsLabel: 'Actionability Checks',
        items: [
            'Element is visible',
            'The element is stable, i.e., not currently animating',
            'Element can receive events, i.e., it is ensured that the element is not covered by other elements and the \'disabled\' attribute is not set.',
        ],
    },
    actions: {
        label: 'Actions',
        selectOption: 'Select Action',
        newItem: 'Add Action',
        search: 'Filter Actions',
        noMatch: 'No matches!',
        unavailable: 'This action is no longer available.',
        failStopLabel: 'Abort Project-Run if the action cannot be executed successfully.',
        options: {
            mouseInteraction: {
                label: 'Mouse Gestures',
                description: 'Perform single click, double click, mouse move, etc.',
            },
            elementHover: {
                label: 'Hover over Element',
                description: 'Move the mouse over a DOM element.',
            },
            elementClick: {
                label: 'Click Element',
                description: 'Trigger a click on a DOM element.',
            },
            keyboardPress: {
                label: 'Press Keys',
                description: 'Trigger one or more key presses.',
            },
            elementFocus: {
                label: 'Focus on Element',
                description: 'Focus on a DOM element.',
            },
            tap: {
                label: 'Tap',
                description: 'Simulate a touch on the screen.',
            },
            elementTap: {
                label: 'Tap Element',
                description: 'Simulate a tap on a DOM element.',
            },
            elementDragAndDrop: {
                label: 'Drag & Drop Element',
                description: 'Drag an element and drop it elsewhere.',
                fields: {
                    sourceSelector: 'Element',
                    targetSelector: 'Target',
                    redundancy: 'Element and target must not be the same.',
                },
            },
            selectOption: {
                label: 'Select Option',
                selectOption: 'Select Query',
                description: 'Select one or more options.',
                type: {
                    label: 'How should the options be identified?',
                    fields: {
                        label: {
                            label: 'Select elements by label',
                            fields: {
                                items: 'Label',
                                newItem: 'Enter new label values here',
                            },
                            notification: {
                                empty: 'Please enter at least one label.',
                            },
                        },
                        value: {
                            label: 'Select elements by value',
                            fields: {
                                items: 'Value',
                                newItem: 'Enter new value values here',
                            },
                            notification: {
                                empty: 'Please enter at least one value.',
                            },
                        },
                    },
                },
            },
            fillInputOrTextarea: {
                label: 'Fill Text',
                description: 'Focus on a text area or input field and enter text.',
                text: 'Text Input',
                inputType: {
                    label: 'Input Type',
                    single: 'Single-line Input',
                    multi: 'Multi-line Input',
                },
                notification: {
                    text: 'Enter text.',
                },
            },
            scrollIntoView: {
                label: 'Scroll into View',
                description: 'Scroll to the position of the element until it is in the visible area of the browser.',
            },
            uploadFile: {
                label: 'Upload File',
                description: 'Select a file to be uploaded.',
                multipleInfo: 'Note: If the form does not support multiple file uploads, only the first selected file will be uploaded. If the target element is not a form, an error will be reported.',
                selectFile: 'Select File',
            },
            waitForSelector: {
                label: 'Wait for Element',
                description: 'Wait until this element is present in the HTML DOM.',
                config: {
                    timeout: 'Timeout',
                },
                notification: {
                    timeout: 'If the element is not found within {{timeout}}, the action will automatically end with a timeout error.',
                },
            },
            booleanFunction: {
                label: 'Wait for Condition',
                description: 'Wait until this JavaScript condition is met.',
                config: {
                    timeout: 'Timeout',
                },
                notification: {
                    timeout: 'The code will run every {{interval}} until it\'s return value is truthy, else it automatically returns false after {{timeout}}.',
                    timeoutInfo: 'After {{timeout}} the action will be aborted and a timeout error will be recorded.',
                },
            },
            request: {
                label: 'Wait for Network Request',
                description: 'Wait until a network request is made to a URL.',
                config: {
                    timeout: 'Timeout',
                    method: {
                        label: 'Method',
                    },
                },
                notification: {
                    timeout: 'After {{timeout}}, the action will automatically end with a timeout error.',
                },
            },
            response: {
                label: 'Wait for Network Response',
                description: 'Wait until a network response is received from a URL.',
                config: {
                    timeout: 'Timeout',
                    method: {
                        label: 'Method',
                    },
                },
                notification: {
                    timeout: 'After {{timeout}}, the action will automatically end with a timeout error.',
                },
            },
            timeout: {
                label: 'Quiet Time',
                description: 'Wait until the specified time has passed.',
                config: {
                    timeout: 'Quiet Time',
                },
            },
            reloadPage: {
                label: 'Reload Page',
                description: 'Reload the current page.',
            },
            goBack: {
                label: 'Go Back',
                description: 'Navigate back one step in the browser history.',
            },
            goForward: {
                label: 'Go Forward',
                description: 'Navigate forward one step in the browser history.',
            },
            goTo: {
                label: 'Navigate to URL',
                description: 'Navigate to the following URL',
                config: {
                    url: 'Enter URL',
                },
            },
            scroll: {
                label: 'Scroll',
                description: 'Scroll to a DOM position',
                config: {
                    xy: {
                        xAxis: 'X Axis',
                        yAxis: 'Y Axis',
                    },
                    behaviour: {
                        label: 'Scroll Behaviour',
                    },
                },
            },
            js: {
                label: 'Execute JavaScript',
                description: 'Execute JavaScript in the browser console.',
            },
            clearAllCookies: {
                label: 'Clear All Cookies',
                description: 'Clear all cookies in the browser context.',
            },
            clearSessionStorage: {
                label: 'Clear Session Storage',
                description: 'Clear the session storage.',
            },
            clearLocalStorage: {
                label: 'Clear Local Storage',
                description: 'Clear the local storage.',
            },
            dialogHandling: {
                label: 'Dialog Decision',
                description: 'Determine here whether dialogs displayed by the browser are automatically accepted or dismissed.',
                config: {
                    title: 'When a dialog is displayed',
                    decision: {
                        accept: {
                            label: 'Automatically Accept',
                        },
                        dismiss: {
                            label: 'Automatically Dismiss',
                        },
                    },
                    text: {
                        label: 'Respond to the dialog with:',
                    },
                },
            },
        },
        categories: {
            userInteractionEvents: {
                label: 'User Interaction Events',
            },
            formElementManipulation: {
                label: 'Form & Element Manipulation',
            },
            navigationAndBrowsing: {
                label: 'Navigation & Browsing',
            },
            dataProcessingAndRequests: {
                label: 'Data Processing & Requests',
            },
            advancedBrowserApiOperations: {
                label: 'Advanced Browser API Operations',
            },
            settings: {
                label: 'Settings',
            },
        },
        groups: {
            mouse: {
                label: 'Mouse Interactions',
                target: {
                    fields: {
                        button: 'Click Mouse',
                        move: 'Move Mouse',
                        wheel: 'Operate Mouse Wheel',
                    },
                },
                buttonType: {
                    label: 'Which Mouse Button?',
                    fields: {
                        left: 'Left Mouse Button',
                        middle: 'Middle Mouse Button',
                        right: 'Right Mouse Button',
                    },
                },
                buttonAction: {
                    label: 'What to Do?',
                    fields: {
                        click: 'Click',
                        down: 'Press and Hold',
                        up: 'Release',
                    },
                },
                config: {
                    delay: {
                        label: 'Wait time before releasing the button after clicking',
                    },
                    count: {
                        label: 'How many clicks?',
                        fields: {
                            1: 'Single',
                            2: 'Double',
                        },
                    },
                    xy: {
                        xAxis: 'X Axis ({{min}} / {{max}}px)',
                        yAxis: 'Y Axis ({{min}} / {{max}}px)',
                        description: 'Move mouse pointer to the following XY position.',
                    },
                    dXdY: {
                        xAxis: 'X Axis ({{min}} / {{max}}px)',
                        yAxis: 'Y Axis ({{min}} / {{max}}px)',
                        description: 'Move the mouse pointer along the X or Y axis.',
                    },
                },
            },
            keyboard: {
                label: 'Keyboard Interactions',
                action: 'Action',
                actions: {
                    press: 'Press',
                    down: 'Press and Hold',
                    up: 'Release',
                    type: 'Type characters with {{delay}}s delay',
                },
                customCharacter: {
                    label: 'Enter Character Strings',
                    delay: 'Delay after entering individual characters.',
                    title: 'Send custom character strings, including special characters like ä, Ö, é, ô, etc., to the browser.',
                },
            },
            touchScreen: {
                label: 'Touchscreen Interactions',
                config: {
                    xy: {
                        xAxis: 'X Axis ({{min}} / {{max}}px)',
                        yAxis: 'Y Axis ({{min}} / {{max}}px)',
                        description: 'Move mouse pointer to the following XY position.',
                    },
                },
            },
            element: {
                label: 'Element Interactions',
            },
            waitFor: {
                label: 'Wait for Events',
            },
            custom: {
                label: 'Technical',
            },
        },
        notifications: {
            mandatoryField: 'Please enter a group name',
            duplicateGroupName: 'There is already a condition group with that name',
            deleteSuccess: 'The action "{{label}}" has been deleted',
            editSuccess: 'The action has been successfully edited',
            saveSuccess: 'The action has been successfully saved',
            copySuccess: 'The action has been successfully copied',
        },
        labels: {
            failed: {
                label: 'Failed',
                description: 'An error occurred while executing the action',
            },
            voided: {
                label: 'Voided',
                description: 'The action was voided',
            },
            aborted: {
                label: 'Aborted',
                description: 'The action was aborted',
            },
            executed: {
                label: 'Executed',
                description: 'The action was executed successfully',
            },
        },
        label_one: 'Action',
    },
    activatePackage: 'Activate Package',
    activated: 'Activated',
    active: 'active',
    add: 'Add',
    addTestLink: 'Add Test URL',
    allLabel: '- and -',
    anOptionIsMandatory: 'You must make at least one selection.',
    and: 'AND',
    anyLabel: '- or -',
    assistant: {
        label: 'AI Assistant',
        ask: 'Ask our AI Assistant',
        description: 'Welcome to {{app}}, your AI Assistant for easy and efficient web automation. Ask me your questions and discover the possibilities!',
        input: {
            placeholder: 'Ask me something...',
            send: 'Send',
        },
        roles: {
            user: 'User',
            assistant: 'AI Assistant',
        },
        prompts: {
            exploreFeatures: {
                label: 'Explore Features with AI',
                description: 'Hi! I’m new to {{app}} — can you walk me through what I can do here? I’d like to understand the main features and how I can use them to automate browser tasks',
            },
            exploreSpecificFeature: {
                label: 'Ask AI about {{solution}}',
                description: 'Hi! I\'m interested in learning more about the {{solution}} feature on {{app}}. Can you explain what it\'s used for, how I can set it up, and give me some common examples of how it\'s applied?',
            },
            learnAboutFeature: {
                label: 'Learn more...',
                description: 'I want to learn what "{{problem}}" is and how {{app}} can be useful to me.',
            },
        },
    },
    attentionRequired: {
        label: 'Attention required',
    },
    averageValues: 'Average Values',
    backTo: 'Back to {{label}}',
    cancel: 'Cancel',
    change: 'Change',
    checkout: {
        metrics: {
            label: 'Consumption',
            title: 'Item',
            usage: {
                label: 'Usage',
            },
            allowance: {
                label: 'Allowance',
            },
            remaining: {
                label: 'Remaining',
            },
            overage: {
                label: 'Overage',
            },
            items: {
                dataExtractions: {
                    label: 'Data Extractions',
                },
                e2eVisualTests: {
                    label: 'Visual Tests',
                },
                screenshots: {
                    label: 'Screenshot Documentation',
                },
                screenVideos: {
                    label: 'Video Documentation',
                },
                lighthouse: {
                    label: 'Google Lighthouse Audits',
                },
                urlChallenge: {
                    label: 'URL-Challenge',
                },
                aiRequests: {
                    label: 'AI Requests',
                },
                buildSteps: {
                    label: 'Project-Run Steps',
                },
                builds: {
                    label: 'Project-Runs',
                },
                httpRequests: {
                    label: 'HTTP Requests',
                },
                buildRuntimeMinutes: {
                    label: 'Processing time (minutes)',
                },
                storageInGB: {
                    label: 'Storage in GB',
                },
                parallelBuilds: {
                    label: 'Parallel Builds',
                },
            },
        },
    },
    clearAll: 'Clear All',
    clipboard: {
        copyTitle: 'Copy {{contentType}}',
        copySuccess: '{{contentType}} was successfully copied to the clipboard.',
    },
    close: 'Close',
    codeField: {
        js: {
            counterLabel: '({{rest}})',
            items: [
                'This function is executed by the AI.',
                'The full browser API is available to you.',
                'You can also access your own library functions here.',
            ],
            validation: {
                label: 'Please validate the JavaScript code first before saving.',
                isInvalid: 'The JavaScript code is possibly faulty.',
                isValid: 'The JavaScript code is valid.',
                requestError: 'The JavaScript code cannot be validated at this time. Please try again later.',
            },
            notifications: {
                empty: 'Please enter JavaScript code.',
                incomplete: 'Please return a value "return ...;".',
            },
        },
        css: {
            counterLabel: '({{rest}})',
            placeholder: '/**\n  Enter CSS code here\n*/',
        },
        html: {
            counterLabel: '({{rest}})',
            placeholder: '<!-- Enter HTML code here -->',
        },
        json: {
            counterLabel: '({{rest}})',
            placeholder: '/**\n Enter JSON code here \n*/',
            validation: {
                isInvalid: 'Please enter a valid JSON.',
            },
        },
        aiText: {
            counterLabel: '({{rest}})',
            placeholder: 'Enter AI text here',
            validation: {
                impossibleActions: 'Prohibited actions',
                characterLimit: 'The text can be a maximum of {{characterLimit}} characters long.',
                yes: 'The input is valid and can be saved.',
                no: 'The input is invalid, please correct the input.',
                unknown: 'The input is not clear and specific enough, but acceptable, you can save or clarify the input.',
                error: 'The AI text cannot be validated at this time. Please try again later.',
                suggestions: 'Valid suggestions are (click to adopt):',
            },
            input: 'Input to AI',
            output: 'Result from AI',
        },
        vanilla: {
            counterLabel: '({{rest}})',
            placeholder: '/**\n Enter your something here \n*/',
        },
    },
    collaborators: {
        label: 'Employee',
        pageTitle: 'All Employees',
        invitedBy: 'Invited by',
        created: 'Account created on',
        role: {
            label: 'Role',
            options: {
                ACCOUNT_HOLDER: 'Account Holder',
                ADMIN: 'Admin',
                EDITOR: 'Editor',
                VIEWER: 'Viewer',
            },
        },
        you: 'You',
        confirmation: {
            title: 'Email Confirmation Required',
            description: 'Your account is not yet activated. There is a pending email confirmation for the email address {{email}}. If you do not find an email in your inbox, please check your spam folder.',
            doneDescription: 'You will be logged out of the account and redirected to the login page.',
            resend: 'Resend Email',
            wasResent: 'Email was sent',
            notification: {
                success: 'Your account has been successfully activated.',
            },
        },
        orphanUser: {
            label: 'User without Organization',
            description: 'No organization found for your user: {{email}}. If you belonged to an organization, you may have been removed as a collaborator. Please contact the administrator.',
            options: {
                label: 'What should happen next?',
                contactAdmin: {
                    label: 'Log out and contact your {{app}} administrator',
                    description: 'You will be logged out to contact the administrator. You can log in again after you are reinstated by your administrator.',
                },
                createAccount: {
                    label: 'Create your own {{app}} organization',
                    description: 'An new organization will be created for you, and you will be the account holder. You can invite other users to join your organization.',
                },
                deleteUser: {
                    label: 'Delete user',
                    description: 'If you delete your user, all your data will be deleted and cannot be recovered. Please be sure before proceeding.',
                    sensitiveOperation: {
                        title: 'Notice',
                        description: 'This operation is sensitive and requires recent authentication. Log in again before retrying this request.',
                    },
                },
            },
        },
        restrictedContent: {
            label: 'Restricted Content',
            description: 'You do not have access to this content.',
            descriptionArchived: 'You do not have access to this content because your account has been deactivated. Please contact your admin: {{accountHolder}}.',
        },
        state: {
            label: 'Account Status',
            options: {
                VERIFIED: {
                    label: 'Verified',
                    description: 'The account is verified.',
                },
                PENDING: {
                    label: 'Pending',
                    description: 'The invitation is still pending.',
                },
                UNVERIFIED: {
                    label: 'Unverified',
                    description: 'The invitation has been accepted, but the account has not yet been verified.',
                },
            },
        },
        invitation: {
            label: 'Invite Employee',
            invite: {
                label: 'Invite Employee',
                notifications: {
                    success: 'Email invitation to {{email}} was sent successfully.',
                    failure: 'Email invitation to {{email}} could not be sent.',
                },
            },
            reinvite: {
                label: 'Resend Invitation',
                notifications: {
                    success: 'Email invitation to {{email}} was resent successfully.',
                    failure: 'Email invitation to {{email}} could not be sent.',
                },
            },
            delete: {
                label: 'Delete Invitation',
                notifications: {
                    success: 'Email invitation to {{email}} was deleted.',
                    failure: 'Email invitation to {{email}} could not be deleted.',
                },
            },
            edit: {
                label: 'Edit Employee',
                labelInvitation: 'Edit Invitation',
                notifications: {
                    success: 'Saved successfully.',
                    failure: 'An error occurred.',
                },
            },
        },
        registrationByServiceProvidedAccount: {
            label: 'Welcome to {{app}}!',
            accountRequired: 'You are just one step away from completing your registration. To continue, you need to create an organization or be invited to an existing one.',
        },
    },
    components: {
        label: 'Komponenten',
        pageTitle: 'Komponenten',
    },
    conditions: {
        label: 'Condition',
        newItem: 'Condition',
        selectOption: 'Please select the type of condition',
        unavailable: 'This condition is no longer available.',
        rule: 'At least one condition within the condition groups must be met.',
        preview: 'Show configuration',
        failStopLabel: 'Abort Project-Run if the condition cannot be successfully executed.',
        options: {
            cookie: {
                label: 'Cookie',
                namePlaceholder: 'Cookie',
                description: 'Check if a specific cookie is set.',
                existenceCheckInfo: 'Leave fields blank if they can be arbitrary',
                fields: {
                    key: {
                        label: 'Cookie name',
                        invalid: 'Please enter a valid cookie name.',
                        mandatory: 'The cookie name must be provided.',
                    },
                    value: {
                        label: 'Cookie value',
                    },
                },
            },
            elementPresent: {
                label: 'HTML Element',
                namePlaceholder: 'HTML Element',
                description: 'Check if an HTML element is present in the DOM.',
                config: {
                    timeout: {
                        label: 'Timeout',
                        description: 'After {{timeout}}, the action will automatically end with a timeout error.',
                    },
                },
            },
            js: {
                label: 'JavaScript Condition',
                namePlaceholder: 'JavaScript',
                description: 'Check if a JavaScript condition is met.',
            },
            responseHeader: {
                label: 'Response Header',
                namePlaceholder: 'Response Header',
                description: 'Check if a specific header is present in the response.',
                existenceCheckInfo: 'Leave the header value blank if it can be arbitrary',
                fields: {
                    key: {
                        label: 'Header name',
                        invalid: 'Please enter a valid header name.',
                    },
                    value: {
                        label: 'Header value',
                    },
                },
            },
            ai: {
                label: 'AI-Evaluated Condition',
                namePlaceholder: 'AI-Based Condition',
                title: 'AI-Evaluated Condition',
                inputNotice: 'Write a simple statement or question that the AI can evaluate as either TRUE or FALSE.',
                description: 'Use this to let the AI check whether something is true on the page. The condition should be clearly stated so that AI can confidently respond with yes or no.',
                helperText: 'This type of condition is evaluated using artificial intelligence. You can write a closed-ended question or a clear expectation like "The banner says Welcome" or "There are 3 buttons on the page." The AI will analyze the content and return TRUE if the condition is met, and FALSE if it is not.',
                confidenceLevel: {
                    label: 'Confidence Level',
                    description: 'Sets how confident the AI needs to be before confirming the condition as met (TRUE).',
                    interpretation: {
                        low: 'A value of {{value}} means the AI is more lenient and might say TRUE even if the condition isn’t clearly met (possible false positives).',
                        medium: 'A value of {{value}} is well-balanced and works well for most conditions.',
                        high: 'A value of {{value}} means the AI will be more strict and only confirm the condition if it’s very confident, possibly missing valid cases (false negatives).',
                    },
                },
                disclaimer: {
                    label: 'Note',
                    description: 'This condition is interpreted by AI using trained models. While it’s flexible and easy to use, it may not be 100% precise. For strict logic, use a manual condition type.',
                },
                expectationExamples: [
                    'There are 3 cat images on the page',
                    'The page is written in the Igbo language',
                    'The banner text says "Welcome"',
                    'Is the call-to-action button visible?',
                    'Does the footer include contact information?',
                ],
            },
        },
        notifications: {
            deleteSuccess: 'The condition "{{label}}" has been deleted',
            editSuccess: 'The condition "{{label}}" has been successfully edited',
            saveSuccess: 'The condition "{{label}}" has been successfully saved',
            copySuccess: 'The condition "{{label}}" has been successfully copied',
        },
        label_one: 'Condition',
        labels: {
            failed: {
                label: 'Failed',
                description: 'Failed to execute',
            },
            voided: {
                label: 'Voided',
                description: 'Not executed',
            },
            aborted: {
                label: 'Aborted',
                description: 'Aborted.',
            },
            passed: {
                label: 'Passed',
                description: 'The condition has been met',
            },
            notPassed: {
                label: 'Not Passed',
                description: 'The condition has not been met',
            },
        },
    },
    configuration: {
        showPreview: 'Show configuration',
        expand: 'Expanded Configuration',
        collapse: 'Collapsed Configuration',
    },
    contactUs: {
        label: 'Contact Us',
        shortLabel: 'Contact',
        pageTitle: 'Contact Us',
        whereToFindUs: 'Where to find us?',
        otherInquiries: 'Other Inquiries',
        notifications: {
            success: 'The request was successfully sent. We will process it as soon as possible and get back to you promptly.',
        },
        contactForm: {
            label: 'Contact Form',
            roleOptions: {
                ceo: 'CEO',
                cto: 'CTO',
                itManager: 'IT Manager',
                projectManager: 'Project Manager',
                productManager: 'Product Manager',
                dataAnalyst: 'Data Analyst',
                systemAdministrator: 'System Administrator',
                softwareDeveloper: 'Software Developer',
                securityOfficer: 'Security Officer',
                qualityManager: 'Quality Manager',
                applicationDeveloper: 'Application Developer',
                frontendDeveloper: 'Frontend Developer',
                backendDeveloper: 'Backend Developer',
                fullStackDeveloper: 'Full-Stack Developer',
                devopsEngineer: 'DevOps Engineer',
                uxDesigner: 'UX/UI Designer',
                seoSpecialist: 'SEO Specialist',
                digitalMarketingManager: 'Digital Marketing Manager',
                salesManager: 'Sales Manager',
                salesRepresentative: 'Sales Representative',
                marketingManager: 'Marketing Manager',
                other: 'Other',
            },
            interestOptions: {
                websitePerformance: 'Website Performance',
                userExperience: 'User Experience',
                seoOptimization: 'SEO Optimization',
                dataAnalysis: 'Data Analysis',
                qualityAssurance: 'Quality Assurance',
                compliance: 'Compliance',
                competitiveAdvantage: 'Competitive Advantage',
                costEfficiency: 'Cost Efficiency',
                brandReputation: 'Brand Reputation',
                digitalMarketing: 'Digital Marketing',
                mobileOptimization: 'Mobile Optimization',
                security: 'Security',
            },
            requestOptions: {
                demo: 'Request Demo',
                support: 'Support Request',
                account: 'Account Request',
                violations: 'Report Abuse',
                other: 'Other Request',
            },
        },
    },
    contingent: 'Contingent',
    continue: 'Continue',
    cookieConsent: {
        message: 'To improve the experience on our website, {{app}} analyzes traffic and personalizes content and advertising through the use of cookies and similar technologies.',
        moreInfo: 'More information in our privacy policy',
    },
    cookies: {
        comparing: {
            showExtendedFields: 'Show extended configuration',
            hideExtendedFields: 'Hide extended configuration',
        },
        fields: {
            name: 'Name',
            value: 'Value',
            domain: 'Domain',
            path: 'Path',
            maxAge: 'Max-Age',
            httpOnly: 'HTTP Only',
            secure: 'Secure',
            sameSite: 'Samesite',
        },
    },
    copied: 'Successfully copied!',
    copy: 'Copy',
    copySuffix: 'Copy',
    copyright: '{{year}} {{app}} - All rights reserved.',
    dangerZone: {
        label: 'Danger zone',
    },
    dashboard: {
        label: 'Dashboard',
        pageTitle: 'Dashboard',
        overview: 'Dashboard',
    },
    dateFrom: 'From {{startDate}}',
    dateFromTo: '{{startDate}} - {{endDate}}',
    days: 'Days',
    delete: 'Delete',
    demo: {
        label: 'Demonstration',
        closeCTA: 'End Demo',
        application: {
            label: 'My Demo Request',
            created: 'Requested on',
            approved: 'Approved on',
            activated: 'Activated on',
            completed: 'Completed on',
            applicant: 'Entered by',
            state: {
                label: 'Status',
                options: {
                    PENDING: 'Pending',
                    IN_PROGRESS: 'In Progress',
                    COMPLETED: 'Completed',
                    REJECTED: 'Rejected',
                    EXPIRED: 'Expired',
                    APPROVED: 'Approved',
                    ACTIVATED: 'Activated',
                },
            },
            cta: 'Request Demo',
            activate: 'Start Demo',
            clearNotification: 'Clear Notification',
        },
    },
    deselectAll: 'Deselect All',
    details: 'Details',
    developers: {
        label: 'Developer',
    },
    device: {
        mobile: 'Mobile',
        desktop: 'Desktop',
    },
    directives: {
        notifications: {
            accountExists: 'A user with the email address {{email}} already exists.',
            automationCreditsDepleted: 'You don\'t have sufficient credits to run this automation. Please top up your credits to continue.',
            automationCreditsToppedUpForDemo: 'Your account has been credited with {{automationCredits}} Automation-Credits for the demo. We wish you the best of luck!',
            cannotUpdateProjectWhenBuilding: 'The project "{{projectName}}" is currently running and cannot be updated during this time. Please try again later.',
            cannotUpdateEmailAfterAcceptance: 'The email address {{email}} cannot be changed anymore as the invitation has already been accepted.',
            authCannotBeDeleted: 'Error deleting the user {{email}}, please contact support.',
            collaboratorPackageRequired: 'To add more collaborators, the package "{{package}}" is required.',
            customerPortalError: 'The customer portal service is currently unavailable, please try again later.',
            fileNotFound: 'The file "{{fileName}}" was not found.',
            fileTooBig: 'The file "{{fileName}}" must not exceed {{maxFileSize}}.',
            faqPromptMissing: 'Please ask a question.',
            genericError: 'An unexpected error occurred.',
            genericErrorWithRetry: 'An error occurred. Please try again later.',
            invalidDemoCartItem: 'The demo package is not available anymore, please contact our support.',
            loginRequired: 'You need to log in to perform this action.',
            noRights: 'You do not have permission to perform this action. Please contact your admin {{email}}.',
            noInternet: 'Please check your internet connection.',
            oneDemoRequestPolicy: 'We have a one subscription demo policy. If you have already requested a demo, please check your email for the next steps.',
            projectBuildCanceled: 'Project-Run "{{projectName}}" was canceled.',
            projectBuildCannotBeDeletedByAge: 'Project-Run "#{{buildId}}" cannot be deleted because there is a more recent build.',
            projectBuildCannotBeDequeued: 'Project-Run "{{projectName}}" cannot be dequeued as it has already started.',
            projectBuildDequeued: 'Project-Run was removed from the queue.',
            projectBuildNotFound: 'The Project-Run was not found.',
            projectBuildQueued: 'The project build has been queued.',
            projectNotFound: 'The project was not found.',
            redirectTo: 'You have been redirected.',
            showError: 'An unexpected error occurred.',
            subscription: 'You need an active subscription with the following packages:',
            subscriptionChange: 'You need an active subscription with the following packages:',
            subscriptionSelection: 'Please select the subscription with which the project should be carried out.',
            unverified: 'There is a pending verification of the email address {{email}}.',
            userIsArchived: 'Your account has been archived, please contact your admin: {{accountHolder}}.',
            DEVICE_CONFIG_INVALID: 'The device is not configured correctly.',
            LIGHT_HOUSE_CATEGORY_LIST_MISSING: 'The category list for Lighthouse tests is missing.',
            STEPS_MISSING: 'The steps are missing.',
            URLS_MISSING: 'The URLs are missing.',
            INVALID_CONFIG: 'The configuration is invalid.',
            PROJECT_NAME_MISSING: 'The project name is missing.',
            PROJECT_NAME_TOO_LONG: 'The project name is too long.',
            PROJECT_NAME_ALREADY_EXISTS: 'The project name "{{projectName}}" has already been used.',
            accountCardsExpiredOrMissing: 'Your credit card has expired or is missing. Please update your payment information.',
            oneGenericRequestPolicy: 'You already have a pending {{requestOption}} request. Please check your email for the next steps.',
            aiServiceNotAvailable: 'The AI service is not available at the moment. Please try again later.',
            cannotUpdateProjectWhenAuditing: 'The project "{{projectName}}" is currently being audited and cannot be updated during this time. Please try again later.',
            PROJECT_SOLUTION_MISSING_MISSING: 'Please select a solution type to continue.',
            PROJECT_CONFIG_OK: 'The project is properly configured.',
        },
    },
    duration: 'Duration',
    edit: 'Edit',
    emails: {
        sendProjectPausedEmail: {
            subject: 'Important Notice Regarding Project Suspension',
            message: {
                value: 'We would like to inform you that your project: {{projectName}} has been temporarily suspended due to the detection of blocked domain(s). This is a security measure to maintain the integrity of our services.',
            },
            domainList: {
                label: 'Affected Domain(s)',
            },
            instructions: {
                value: 'Please review the URL(s) of your project and remove or replace any URLs that are on the blacklist. Once this is done, you can reactivate your project.',
            },
            apology: {
                value: 'We apologize for any inconvenience and are happy to assist you in resolving this issue.',
            },
        },
        domainAddedToBlacklistEmail: {
            subject: 'Update on a Domain',
            body: [
                {
                    value: 'We would like to inform you that the domain: {{domain}} has been added to our blacklist. Due to this action, future projects that rely on this domain can no longer be executed.',
                },
                {
                    value: 'This decision aligns with our Terms and Conditions (T&Cs), which state that we reserve the right to prevent the execution of projects on certain domains to ensure the security and integrity of our services.',
                },
                {
                    value: 'We understand that this may cause inconvenience and are available to discuss alternative solutions.',
                },
                {
                    value: 'Please contact us if you have any questions or need additional information.',
                },
            ],
        },
        domainRemovedFromBlacklistEmail: {
            subject: 'Release of a Previously Blocked Domain',
            body: [
                {
                    value: 'We are pleased to inform you that the domain: {{domain}}, which was previously on our blacklist, has now been released. According to our Terms and Conditions (T&Cs), any projects based on this domain can now be resumed.',
                },
                {
                    value: 'Thank you for your patience during the suspension period, and we look forward to continuing to support your projects.',
                },
                {
                    value: 'If you have any further questions, please feel free to contact us.',
                },
            ],
        },
        projectBuildEmail: {
            subject: 'Project-Run Report on {{app}}',
            body: [
                {
                    value: 'Your project {{projectName}} has just been executed. The following information gives you an overview of the process and the results.',
                },
            ],
            nextExecution: {
                label: 'Next Automatic Execution',
                value: '{{nextExecution}}',
            },
        },
        userDeletedSelfEmail: {
            salutation: 'Hello,',
            subject: 'Confirmation of Deletion of Your {{app}} Account!',
            body: [
                {
                    value: 'We confirm that your {{app}} account has been successfully deleted. We are sorry to see you go and hope to be of service to you again in the future.',
                },
            ],
            additionalInfos: {
                label: 'Please note the following points',
                itemList: [
                    {
                        label: 'Data Deletion',
                        value: 'All data associated with your account has been permanently removed from our servers.',
                    },
                    {
                        label: 'Loss of Subscriptions',
                        value: 'All active subscriptions have been closed and cannot be restored.',
                    },
                    {
                        label: 'Future Account Creation',
                        value: 'If you choose to return, you may create a new account with the same email address at any time. However, please note that your new account will have no connection to or reference from your deleted account.',
                    },
                ],
            },
            closing: [
                {
                    value: 'If you have any further questions or need assistance, please do not hesitate to contact our support team.',
                },
                {},
            ],
        },
        requestEmail: {
            subject: 'Confirmation of Your Support Request at {{app}}!',
            body: [
                {
                    value: 'Thank you for contacting us! We have received your request and are currently reviewing it. Our support team is working on it and will get back to you as soon as possible.',
                },
            ],
            additionalInfos: {
                label: 'A Summary of Your Request',
            },
        },
        demoRequestReceivedEmail: {
            subject: 'Confirmation of Your Demo Request at {{app}}!',
            body: [
                {
                    value: 'Thank you for your interest in our services. We have received your demo request and are currently reviewing it.',
                },
                {
                    value: 'Our support team is working on it and will get back to you as soon as possible.',
                },
            ],
        },
        demoRequestApprovedEmail: {
            subject: 'Update on Your Demo Request at {{app}}!',
            body: [
                {
                    value: 'Thank you for your interest in our services. We are pleased to inform you that your demo request has been approved.',
                },
                {
                    label: 'To activate the demo',
                    value: 'Click here!',
                    link: '{{accountDashboard}}',
                },
                {},
                {
                    value: 'We are confident that you will find our service to be highly beneficial and look forward to having you as a valued customer. If you have any questions or need assistance, please do not hesitate to contact our support team.',
                },
            ],
        },
        demoRequestDeclinedEmail: {
            subject: 'Update on Your Demo Request at {{app}}!',
            body: [
                {
                    value: 'Thank you for your interest in our services. We appreciate your eagerness to explore our platform.',
                },
                {
                    value: 'We regret to inform you that we were unable to approve your request due to concerns regarding the authenticity of your account.',
                },
                {
                    value: 'This decision is a standard precaution to ensure the security of our platform.',
                },
                {},
                {
                    value: 'We are committed to resolving any issues and reconsidering your request. Please contact our support team for further assistance and verification steps.',
                },
            ],
        },
        userInvitationEmail: {
            salutation: 'Hello,',
            subject: 'Invitation to Join a {{app}} Account!',
            body: [
                {
                    value: 'You have been invited to join the organization: {{organisation}} to collaborate on creating and managing projects.',
                },
                {
                    label: 'To get started, please follow this link',
                    value: 'Click here!',
                    link: '{{routes.userInvitationLink}}',
                },
            ],
        },
        userRemovedFromAccountEmail: {
            salutation: 'Hello,',
            subject: 'Update on Your {{app}} Account',
            body: [
                {
                    value: 'We would like to inform you that your access to the organization {{organisation}} on {{app}} has been removed by an administrator. You no longer have access to the data of this organization.',
                },
                {
                    value: 'Please note that you still have access to the {{app}} system and the ability to create and manage your own organizations.',
                },
            ],
            additionalInfos: {
                label: 'Further Information',
                itemList: [
                    {
                        label: 'Create Your Own Organization?',
                        value: 'Click here.',
                        link: '{{routes.createOrganisation}}',
                    },
                ],
            },
        },
        welcomeEmail: {
            subject: 'Welcome to {{app}} - Start Your Automation Journey!',
            body: [
                {
                    value: 'We are very pleased to have you on board and help you unlock the potential of simple and accessible web automation.',
                },
                {
                    value: 'As a new member of our community, you have taken the first step towards streamlining your online tasks and workflows.',
                },
            ],
        },
        standard: {
            salutation: 'Hello,',
            additionalInfos: {
                label: 'Further Information',
                itemList: [
                    {
                        label: 'Features',
                        value: 'Explore the range of features and their optimal use.',
                        link: '{{routes.features}}',
                    },
                    {},
                    {
                        label: 'User-Friendly Interface',
                        value: 'Configure web automation with ease and simplicity.',
                        link: '{{routes.projectWizard}}',
                    },
                    {},
                    {
                        label: 'Resource Library',
                        value: 'Access tutorials, guides, and tips to maximize your experience.',
                        link: '{{routes.documentation}}',
                    },
                    {},
                    {
                        label: 'Have Questions?',
                        value: 'We are here to help.',
                        link: '{{routes.faqs}}',
                    },
                    {},
                    {
                        label: 'For Developers',
                        value: 'Check out our documentation.',
                        link: '{{routes.documentation}}',
                    },
                    {},
                    {
                        label: 'Our T&Cs',
                        value: 'View our terms of service here.',
                        link: '{{routes.termsAndConditions}}',
                    },
                ],
            },
            closing: [
                {},
                {
                    value: 'Good luck and enjoy automating your web projects with {{app}}!',
                },
                {},
                {
                    value: 'Best regards,',
                },
                {},
                {},
                {
                    value: 'Your {{app}} Team.',
                },
            ],
            footer: {
                address: '{{address.street}} {{address.city}}, {{address.state}} - {{address.country}}',
                visitUs: 'Visit Us',
                url: '{{url}}',
                domain: '{{domain}}',
                socials: {
                    youtube: '{{socials.youtube}}',
                    x: '{{socials.x}}',
                    whatsapp: '{{socials.whatsapp}}',
                },
            },
        },
        projectOverageEmail: {
            salutation: 'Hello,',
            subject: 'Project could not be executed on {{app}}',
            body: [
                {
                    value: 'Project build could not be executed as your allowed monthly quotas have been exhausted and the automatic reload is not activated.',
                },
                {
                    value: 'To execute the project and avoid this inconvenience in the future, you can do the following:',
                },
                {
                    label: '1. You either activate the automatic reload in your account settings',
                    value: 'Click here!',
                    link: '{{routes.automationCreditsEdit}}',
                },
                {
                    label: '2. Or you change the subscription with which your project is executed to one, in which the quotas are not exhausted',
                    value: 'Click here!',
                    link: '{{routes.projectSettingsLinkToDashboard}}',
                },
            ],
            closing: [
                {
                    value: 'If you need assistance, please contact our support team. We are happy to help you!',
                },
            ],
        },
        automationCreditsResetPending: {
            subject: 'Your credits are expiring soon – renew now!',
            body: [
                {
                    value: 'We want to remind you that your Automation-Credits will expire on {{expirationDate}}.',
                },
                {
                    value: 'By renewing your credits now, you can:',
                },
                {
                    value: 'Ensure that your automations continue running without interruption.',
                },
                {
                    value: 'Retain the flexibility to run automations with credits at any time.',
                },
                {
                    value: 'Extend the validity of your remaining credits.',
                },
                {},
                {
                    label: 'Current Credits',
                    value: '{{currentCredits}}',
                },
                {
                    label: 'Expiration Date',
                    value: '{{expirationDate}}',
                },
                {},
                {
                    value: 'To ensure your automations continue running smoothly, visit our shop page and purchase additional credits before they expire.',
                },
                {
                    label: 'Shop',
                    value: 'Click here to go to the shop page',
                    link: '{{routes.shop}}',
                },
                {
                    label: 'Automation-Credits',
                    value: 'Click here to view your Automation-Credits',
                    link: '{{routes.automationCredits}}',
                },
            ],
        },
        automationCreditsReset: {
            subject: 'Your credits have expired – renew now to avoid interruptions!',
            body: [
                {
                    value: 'We want to inform you that your Automation-Credits have expired on {{expirationDate}}.',
                },
                {
                    value: 'By renewing your Automation-Credits now, you can:',
                },
                {
                    value: 'Ensure that overages can continue to be billed.',
                },
                {
                    value: 'Maintain the smooth execution of your automated projects.',
                },
                {},
                {
                    label: 'Current Credits',
                    value: '{{currentCredits}}',
                },
                {
                    label: 'Expiration Date',
                    value: '{{expirationDate}}',
                },
                {},
                {
                    value: 'To avoid disruptions in your workflow, visit our shop page and purchase new credits now.',
                },
                {},
                {
                    label: 'Shop',
                    value: 'Click here to go to the shop page',
                    link: '{{routes.shop}}',
                },
                {
                    label: 'Automation-Credits',
                    value: 'Click here to view your Automation-Credits',
                    link: '{{routes.automationCreditsEdit}}',
                },
            ],
        },
        sendRequestEmail: {
            salutation: 'Hello,',
            subject: 'Confirmation of your demo request at {{app}}!',
            body: [
                {
                    value: 'thank you very much for your interest in our services. We have received your request and are currently reviewing it.',
                },
                {
                    value: 'Our support team is working on it and will get back to you as soon as possible.',
                },
            ],
            additionalInfos: {
                label: 'Your request',
            },
        },
        sendPaymentFailedEmail: {
            subject: 'Important Notice: Your Payment Could Not Be Processed',
            body: [
                {
                    value: 'Unfortunately, the payment for your invoice with ID {{invoiceId}} could not be processed successfully.',
                },
                {},
                {
                    value: 'To ensure that your automations continue to run smoothly, please review your payment details and complete the payment manually.',
                },
                {
                    label: 'To View Invoice & Complete Payment',
                    value: 'Click here',
                    link: '{{routes.invoiceDetails}}',
                },
                {},
                {
                    value: 'If you have any questions or need assistance, our support team is always available to help you.',
                },
            ],
        },
    },
    entries: 'Entries',
    entry: 'Entry',
    events: {
        ACCOUNT_COLLABORATOR_EDITED: 'Collaborator {{email}} edited by {{editor}}',
        ACCOUNT_COLLABORATOR_REMOVED: 'Collaborator {{email}} removed by {{remover}}',
        ACCOUNT_CREATED: 'Account created',
        ACCOUNT_NEW_COLLABORATOR_CONFIRMS_ACCOUNT: 'Collaborator {{email}} has confirmed the account',
        ACCOUNT_NEW_COLLABORATOR_INVITED: 'New collaborator {{email}} (Role: {{role}}) invited by {{inviter}}',
        ACCOUNT_NEW_COLLABORATOR_RE_INVITED: 'Invitation to collaborator {{email}} renewed and sent',
        ACCOUNT_NEW_PROJECT: 'New project "{{projectName}}" created',
        ACCOUNT_NEW_SUBSCRIPTION: 'New subscription',
        ACCOUNT_PRE_PAID_SUBSCRIPTION_CREATED: 'Prepaid subscription created to allow the purchase of Automation-Credits.',
        ACTIONS_TIMEOUT: 'The action "{{name}}" was aborted after exceeding the {{duration}} limit (timeout error).',
        ACTION_FILE_UPLOAD_ONLY_SINGLE_FILES: 'The form does not support multiple file uploads. Only the first item was uploaded.',
        ACTION_NOT_ALLOWED_ON_DESKTOP: 'Action "{{label}}" cannot be executed on desktop.',
        ACTION_NOT_ALLOWED_ON_FOREIGN_HOST: 'Action "{{label}}" cannot be executed on a foreign host ({{url}}) and was therefore skipped.',
        ACTION_NOT_ALLOWED_ON_MOBILE: 'Action "{{label}}" cannot be executed on mobile.',
        AI_THRESHOLD_NOT_MET: 'The set threshold ({{confidenceThreshold}}) for AI results was not met ({{confidence}}).',
        BLOCKED_DOMAINS: 'Action cannot be executed at {{dateTime}} because the project refers to at least one domain on the blacklist:',
        BROWSER_NOT_AVAILABLE: 'Browser could not be started.',
        BROWSER_STARTED: 'Browser started.',
        CANNOT_EXECUTE_INACTIVE_PROJECT: 'The project is inactive and cannot be executed. Please activate the project first.',
        CANNOT_FIND_PROJECT_CONFIG: 'Project is misconfigured.',
        CLIENT_ERROR: 'Errors are being shown for the request to URL: {{url}}.',
        DUPLICATE_PROJECT_QUEUED: 'A Project-Run for this project has already been queued.',
        DUPLICATE_PROJECT_RUNNING: 'There is already a Project-Run in progress for this project and it has to finish before you can start a new one.',
        ELEMENT_NOT_FOUND: 'No matching element was found for the selector "{{selector}}".',
        ERROR_DELETING_FILE_AFTER_UPLOAD: 'Error deleting the file after upload.',
        FILE_NOT_FOUND: 'The linked file was not found.',
        GS_ERROR_DOWNLOAD: 'There is a problem with communicating with the CDN.',
        GS_ERROR_NOT_FOUND: 'Error downloading files from the CDN.',
        GS_ERROR_UPLOAD: 'Error saving the {{format}} report.',
        INVALID_ACTION_CONFIGURATION: 'An action is misconfigured.',
        INVALID_CONDITION_CONFIGURATION: 'A condition is misconfigured.',
        INVALID_DATA_EXTRACTION_CONFIGURATION: 'Project is misconfigured.',
        INVALID_SCREENSHOT_CONFIGURATION: 'Project is misconfigured.',
        INVALID_E2E_VISUAL_TEST_CONFIGURATION: 'Project is misconfigured.',
        INVALID_JS_CODE: 'Your JavaScript code was faulty.',
        INVALID_LOOP_CONFIGURATION: 'A loop is misconfigured.',
        INVALID_SOLUTION_CONFIGURATION: 'An unexpected error occurred.',
        INVALID_URL_CHALLENGE_CONFIGURATION: 'URL challenge is misconfigured.',
        LIGHTHOUSE_BUILD_FAILED: 'Lighthouse test on URL: {{url}} ({{device}}) failed.',
        LIGHTHOUSE_BUILD_STARTED: 'Lighthouse test on URL: {{url}} ({{device}}) in progress.',
        LINK_CHECKER_BUILD_COMPLETED: 'Page load completed.',
        LINK_CHECKER_BUILD_FAILED: 'Page load failed.',
        LINK_CHECKER_SOLUTION_STARTED: 'Page load started.',
        LINKS_FOUND_ON_PAGE: '{{count}} link(s) found on page {{pageUrl}}, {{qualifiedCount}} of the links qualified for the build.',
        MANAGER_ERROR_MISSING_JOB_REFERENCE: 'Project manager error: No job reference was provided.',
        PAGE_CUSTOM_TIMEOUT: 'The set time {{linkRequestTimeout}} for the complete loading of HTML on {{url}} has been exceeded.',
        PAGE_CUSTOM_TIMEOUT_JS_ERROR: 'The set time {{linkRequestTimeout}} for the complete loading of HTML on {{url}} and the fulfillment of the JavaScript condition has been exceeded.',
        PAGE_TIMEOUT: 'The URL: {{url}} is not responding to requests.',
        PAGE_UNAVAILABLE: 'The URL: {{url}} was either unreachable or the request was aborted.',
        PAGE_UNKNOWN_HOST: 'The URL: {{url}} was unreachable.',
        PROJECT_PROJECT_BUILD_ABORTED_BY_STEP: 'Project-Run aborted because a step failed.',
        PROJECT_BUILD_AUTO: 'Project-Run was triggered automatically',
        PROJECT_PROJECT_BUILD_BY_MANAGEMENT_API: 'Project-Run was triggered via the management API',
        PROJECT_BUILD_MANUAL: 'Project-Run was manually triggered by {{author.email}}.',
        PROJECT_BUILD_CANCEL: 'Project-Run was cancelled',
        PROJECT_BUILD_DATA_EXTRACTION_NO_RESULT: 'The AI assistant was unable to extract data or find a suitable solution for the request.',
        PROJECT_BUILD_DELETED: 'Project-Run was deleted',
        PROJECT_BUILD_STEP_DATA_EXTRACTION_AI_ERROR: 'Error in data extraction using AI',
        PROJECT_CREATED: 'Project was created',
        PROJECT_DELETED: 'Project was deleted',
        PROJECT_EDITED: 'Project was edited by {{email}} (Session: {{loginSessionId}}).',
        PROJECT_NAME_TOO_LONG: 'The project name is too long {{length}} characters. Maximum allowed is {{maxLength}} characters.',
        PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST: 'A project was paused at {{dateTime}} because it is building on a blacklisted domain:',
        PROJECT_STATUS_CHANGED: 'Project status changed from old: {{old}} to new: {{new}} by {{author.email}}',
        REPORT_EXPORT_ERROR: 'The report could not be exported.',
        REQUEST_BLOCKED: 'HTTP request modification from URL: {{url}} was blocked (id = {{id}}).',
        REQUEST_MOCK_COMPLETED: 'HTTP request modification for URL: {{url}} successfully completed (id = {{id}}).',
        REQUEST_MODIFICATION_FAILED: 'HTTP request modification failed for URL: {{url}} (id = {{id}}).',
        REQUEST_MODIFICATION_JS_CODE_INVALID: 'HTTP request modification for URL: {{url}} was ignored because the reduction function is invalid (id = {{id}}).',
        RESOURCE_RELEASE_ERROR: 'A runtime error occurred.',
        RESPONSE_MODIFICATION_COMPLETED: 'Response modification for URL: {{url}} successfully completed (id = {{id}}).',
        RESPONSE_MODIFICATION_FAILED: 'Response modification failed for URL: {{url}} (id = {{id}}).',
        RESPONSE_MODIFICATION_JS_CODE_INVALID: 'Response modification for URL: {{url}} was ignored because the reduction function is invalid (id = {{id}}).',
        RUNTIME_ERROR: 'A runtime error occurred.',
        SERVER_ERROR: 'The server returned an error for the request to URL: {{url}}.',
        SESSION_CANCELLED: 'The Project-Run job was cancelled.',
        SESSION_CANCELLED_BY_MANAGER: 'The Project-Run timed out and was cancelled by the Webeagle manager.',
        SESSION_CANCELLED_MANUALLY: 'The Project-Run was cancelled.',
        SESSION_DATABASE_ERROR: 'Error accessing the database.',
        SESSION_ENDED: 'Project-Run session ended',
        SESSION_ERROR: 'An unexpected error occurred.',
        SESSION_STARTED: 'Project-Run session started',
        SOLUTION_URL_CHALLENGE_ELIMINATION: 'The URL "{{url}}" was eliminated from the challenge in step "{{label}}".',
        STEP_ABORTED: 'Step "{{label}}" ({{stepType}}) aborted.',
        STEP_COMPLETED: 'Step "{{label}}" ({{stepType}}) completed.',
        STEP_FAILED: 'Step "{{label}}" ({{stepType}}) failed.',
        STEP_INACTIVE: 'Step "{{label}}" ({{stepType}}) is inactive and was skipped.',
        STEP_LABELLING_FUNCTION_FAILED: 'Dynamic labeling function for step "{{label}}" ({{stepType}}) failed (id = {{id}}).',
        STEP_LOOP_BROKEN: 'Loop in step "{{label}}" stopped at iteration {{position}}.',
        STEP_LOOP_EMPTY: 'The loop in step "{{label}}" ({{stepType}}) is empty.',
        STEP_REFERENCE_LOST: 'Element reference in step "{{label}}" ({{stepType}}) is lost.',
        STEP_SOLUTION_URL_CHALLENGE_FAILED: 'The URL challenge in step "{{label}}" failed for {{url}}.',
        STEP_STARTED: 'Step "{{label}}" ({{stepType}}) in progress.',
        STRIPE_UNKNOWN_CUSTOMER: 'STRIPE: Unknown customer',
        STRIPE_UNKNOWN_SUBSCRIPTION: 'STRIPE: Unknown subscription',
        SUBSCRIPTION_CONTINGENT_UPDATE_ERROR: 'An error occurred.',
        SUBSCRIPTION_DOMAIN_LIMIT_REACHED: 'The domain limit {{domainContingent}} in the subscription: "{{licenseName}}" has been reached. Domains checked so far "{{domainList}}". Your entries "{{targetDomainList}}". Please adjust the project or choose another subscription.',
        SUBSCRIPTION_MISSING: 'We cannot find an active subscription with which the project can be executed.',
        TIMEOUT: 'Timeout',
        TOOL_ERROR: 'An unexpected error.',
        UNKNOWN: 'An unexpected error occurred.',
        UNKNOWN_ACCOUNT: 'Account not found',
        UNKNOWN_CHECK_TYPE: 'The type "{{checkType}}" is not known.',
        URL_AUTHORIZATION: 'Authorization for the request to URL: {{url}} failed,',
        URL_BAD_SSL: 'There is a problem with the installed certificate on: {{url}}.',
        URL_CLIENT_ERROR: 'Client error for the request to URL: {{url}}.',
        URL_DISALLOWED_BY_ETHICAL_ACCESS_CHECK: 'Processing of URL: {{url}} was aborted due to our ethical access restrictions.',
        URL_GONE: 'The requested URL: {{url}} is no longer available.',
        URL_NOT_RESPONDING: 'URL: {{url}} not reachable.',
        URL_PROCESSED: 'URL: {{url}} processed.',
        URL_PROCESSED_DEVICE: 'URL: {{url}} processed on {{device}} device.',
        URL_PROCESSING: 'URL: {{url}} is being processed.',
        URL_REDIRECTED: 'URL: {{url}} was redirected.',
        WEB_RESOURCE_INJECTION_COMPLETED: '{{contentType}} (id = {{id}}) resource was successfully injected into the page.',
        WEB_RESOURCE_INJECTION_FAILED: '{{contentType}} (id = {{id}}) resource could not be injected into the page.',
        PROJECT_BUILD_FINISHED: 'Project build finished - Status: {{state}}',
        PROJECT_SUBSCRIPTION_CHANGED: 'Project subscription changed old: {{old}} => new: {{new}}.',
        PROJECT_BUILD_RETRY: 'Project-Run retried automatically ({{attempt}} of {{maxAttempts}}).',
        PROJECT_BUILD_RETRY_EXHAUSTED: 'Project-Run cannot be automatically retried anymore, because the maximum number of retries {{maxAttempts}} has been reached.',
        PAYMENT_DETAILS_NEED_UPDATE: 'Your payment details have expired. Please update your payment details to avoid any service interruption.',
        PROJECT_BUILD_SKIPPED_DUE_TO_OVERAGE: 'Project build cannot be started because the quota for the current billing period is exhausted and you do not have automatic renewal activated.',
        ELEMENT_NOT_VISIBLE: 'Element cannot be captured, because it is not visible. ({{selector}}, {{html}})',
        AUTOMATION_CREDITS_AUTO_RENEW_SUCCESS: 'Automation-credits auto-renewal successful',
        AUTOMATION_CREDITS_AUTO_RENEW_FAILED: 'Automation-credits auto-renewal failed',
        AUTOMATION_CREDITS_AUTO_RENEW_BLOCKED_DUE_TO_BALANCE: 'Auto-renewal for automation credits cannot be disabled while your balance is negative. Please add funds by purchasing credits from the shop before turning off auto-renewal.',
        AUTOMATION_CREDITS_REQUIRED: 'This action cannot be performed due to insufficient Automation-Credits. Please add funds by purchasing Automation-Credits before you try again.',
        PROJECT_BUILD_SCREENSHOT_NO_DOM_SELECTOR_FOUND_BY_AI: 'The AI could not find any DOM selector for screenshot capture',
        AI_SERVICE_FAILED: 'AI service is not available',
        DOWNLOAD_PROJECT_RUNNING: 'Download is not available while the project is running.',
        PROJECT_BUILD_DELETED_ALL: 'All Builds have been deleted (actor: {{deletedBy}}, timestamp: {{deletedOn}}, Session: {{loginSessionId}}, Project: {{projectId}}).',
        PROJECT_BUILD_STEP_CONDITION_AI_ERROR: 'Error evaluating condition with AI',
        PROJECT_BUILD_STEP_URL_CHALLENGE_AI_ERROR: 'Failed to process AI URL challenge',
        PROJECT_BUILD_STEP_URL_CHALLENGE_NO_RESULT: 'The AI agent was unable to successfully complete the URL challenge.',
        PROJECT_BUILD_STEP_E2E_VISUAL_TEST_AI_ERROR: 'An error occurred while processing the Visual Test with AI.',
        PROJECT_BUILD_STEP_DATA_EXTRACTION_NO_RESULT: 'The AI-Assistant was unable to extract data from the page. Please check the extraction step configuration.',
        PROJECT_BUILD_STEP_E2E_VISUAL_TEST_NO_REVIEW: 'The AI-Assistant was unable to provide a review for the Visual Test. Please review the test manually.',
    },
    example: 'For example',
    exampleCode: 'Example Code',
    extendedSettingsBlock: 'Advanced Configuration',
    faqs: {
        label: 'FAQs',
        pageTitle: 'Frequently Asked Questions',
        title: 'Do you have questions? We have the answers!',
        description: 'Welcome to our FAQ page! Here you will find a comprehensive collection of answers to the most frequently asked questions about {{app}}. From getting started with your first project to understanding the intricacies of our advanced features – our FAQs are designed to support you every step of the way. Browse the categories or use the search function to quickly find specific solutions. If you can\'t find what you\'re looking for, remember that our support team is just a click away.',
        noResults: 'Nothing found.',
        items: {
            whatIsWebautomate: {
                label: 'What is {{app}}?',
                description: '{{app}} is a no-code/low-code platform designed to automate actions on websites. It allows users to interact with web pages and perform various tasks without needing extensive programming knowledge.',
            },
            needCodingSkills: {
                label: 'Do I need programming skills to use {{app}}?',
                description: 'No, {{app}} is designed as a no-code/low-code solution, which means it requires minimal to no programming skills for most of its functions.',
            },
            startNewProject: {
                label: 'How do I start a new project on {{app}}?',
                description: 'To start a new project, visit the Project Assistant page at "https://{{app}}/project-wizard" and follow the guided steps for project setup.',
            },
            automateBrowserActions: {
                label: 'Can I automate browser actions like clicks and keyboard inputs?',
                description: 'Yes, {{app}} allows you to simulate various user and browser actions such as clicks, keyboard inputs, mouse movements, and more.',
            },
            captureScreenshots: {
                label: 'Can I take screenshots or record videos of my website with {{app}}?',
                description: 'Yes, {{app}} offers solutions for capturing screenshots and recording screen videos of website interactions.',
            },
            scheduleAutomations: {
                label: 'How do I schedule automated executions of my projects?',
                description: 'In the final step of your project setup, you can schedule automated executions with options like start and end date, frequency, and schedule type.',
            },
            receiveNotifications: {
                label: 'Can I receive notifications about the execution results of my project?',
                description: 'Yes, depending on your subscription package, you can set up notifications via email or webhook to receive updates on your project\'s execution results.',
            },
            mobileVsDesktop: {
                label: 'How does {{app}} handle mobile vs. desktop simulations?',
                description: 'The platform allows you to simulate user actions for both mobile devices and desktops, with specific actions available for each type.',
            },
            screenshotFunctionality: {
                label: 'How exactly does the screenshot documentation work?',
                description: 'In screenshot documentation, you can specify what to capture in a screenshot—whether the whole screen, a specific DOM element, or just the browser\'s viewport.',
            },
            videoRecordingBenefits: {
                label: 'What are the benefits of the video recording feature?',
                description: 'Screen recording captures interactions on your website, which can be helpful for UX testing, customer support, and understanding user behavior.',
            },
            whereAreTheAutomationsExecuted: {
                label: 'Where are the automations executed?',
                description: 'The automations are executed on our servers. We have servers at different locations. You can start the automations from any device with a web browser. You don\'t need to install or configure any software. You can also schedule the automations to run at a specific time.',
            },
        },
    },
    features: {
        label: 'Overview of All Features',
        pageTitle: 'Overview of All Features',
    },
    form: {
        optionalField: '(Optional)',
        hint: 'Hint',
        name: 'Name',
        lastName: 'Last Name',
        email: 'Email Address',
        whatsapp: 'WhatsApp',
        phone: 'Phone Number',
        company: 'Company',
        website: 'Website',
        role: 'Role',
        interests: 'Interests',
        organisation: 'Organization',
        mobilePhone: 'Mobile Phone Number',
        username: 'Username',
        password: 'Password',
        passwordRepeat: 'Repeat Password',
        passwordReset: 'Reset Password',
        message: 'Your Message',
        send: 'Send',
        request: 'Request',
        validation: {
            emailInvalid: 'Please provide a valid email address, e.g., <EMAIL>.',
            emailRequired: 'Please provide your email address.',
            mobilePhoneInvalid: 'Please provide a valid mobile phone number.',
            mobilePhoneRequired: 'Please provide your mobile phone number.',
            passwordRequired: 'Please provide a password.',
            passwordRepeatRequired: 'Please repeat the password here.',
            passwordRepeatInvalid: 'The entered passwords do not match.',
            domain: 'Please provide a valid domain.',
            usernameInvalid: 'Please provide a username.',
            fieldRequired: 'Please provide information here.',
            selectionRequired: 'Please make a selection.',
            toBeDefined: 'Not specified.',
            script: 'Enter code here.',
            min: 'The value must be greater than {{min}}.',
            minMax: 'The value must be between {{min}} and {{max}}.',
            minMaxStep: 'The value must be between {{min}} and {{max}} and be a multiple of {{step}}.',
            http: 'The URL should start with "http://" or "https://".',
            url: 'Please provide a valid URL.',
            duplicateEntry: 'The entry already exists.',
        },
    },
    genericFailure: '{{action}}: Unsuccessful!',
    genericSuccess: '{{action}}: Successful!',
    getStarted: 'Get Started',
    greeting: 'Hello, {{organisation}}',
    groupName: 'Group Name',
    intro: {
        title: 'Say goodbye to repetitive web tasks!',
        startNow: 'Automate Your Website in Minutes — No Coding Required!',
        subtitle: 'With {{app}}, you can build and schedule powerful browser automations — no coding needed. Whether it’s testing URLs, extracting data, running Lighthouse audits, or creating visual and video documentation, our intuitive project assistant helps you set it all up in just a few steps. Run your workflows on demand or automatically, using flexible scheduling, and get detailed reports with every build.',
        easySetup: 'Set up in 2 minutes',
        automated: 'Runs automated',
        noCoding: 'No coding',
        maintenance: 'No maintenance',
        description: 'Automate browser workflows in minutes and run them automatically whenever you need.',
    },
    home: {
        label: 'Homepage',
        title: 'webAutomate',
    },
    hours: 'Hours',
    id: 'ID',
    idleTime: {
        title: 'Session Expired',
        description: 'You were inactive for a while. Please reload the page to continue.',
        ctaLabel: 'Reload Page Now',
    },
    imageLoadErrorMessage: 'This image could not be loaded.',
    inactive: 'Inactive',
    index: 'Position',
    invalidField: 'The field "{{field}}" is invalid.',
    key: 'Parameter',
    itemLabelling: {
        label: 'Label',
        labelInfo: 'Labels are used to recognize the steps in reports.',
        mandatoryField: 'Please enter a label.',
        duplicateGroupName: 'This label is already taken.',
        strategies: {
            JS: {
                label: 'With JS function',
                description: 'The function should return an alphanumeric string. Otherwise, the fallback "{{fallbackLabel}}" will be used.',
            },
            LABEL: {
                label: 'With Label',
            },
        },
    },
    languages: {
        de: {
            label: 'German',
        },
        fr: {
            label: 'French',
        },
        en: {
            label: 'English',
        },
    },
    linkTable: {
        url: 'URL',
        tag: 'Tag',
        selector: 'Query',
        html: 'Snippet',
    },
    linkedResources: {
        querySelector: {
            label: 'DOM Selector',
            notice: '"$" stands for document.querySelectorAll. It is a list referencing DOM elements that match your selector.',
            noticeSingle: '"$" stands for document.querySelector. It refers to the first element that matches your selector.',
            url: 'https://developer.mozilla.org/en/docs/Web/API/Document/querySelectorAll',
            urlSingle: 'https://developer.mozilla.org/en/docs/Web/API/Document/querySelector',
            validation: 'Please enter a valid selector.',
        },
        nodeList: {
            notice: 'NodeList is the collection of all DOM nodes that match your selector.',
            readMore: 'Learn more about NodeList',
            url: 'https://developer.mozilla.org/en-US/docs/Web/API/NodeList',
        },
        domNode: {
            notice: 'A DOM Node is the first DOM API object that matches your selector.',
            readMore: 'Learn more about DOM Node',
            url: 'https://developer.mozilla.org/en-US/docs/Web/API/Node',
        },
        string: {
            label: 'String',
            url: 'https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/String',
        },
        object: {
            label: 'Object',
            url: 'https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Object',
        },
    },
    links: {
        broken: 'Broken URLs',
        success: 'Successful URLs',
    },
    load: {
        all: 'Load all',
    },
    login: {
        label: 'Login',
        pageTitle: 'Login',
        logout: 'Logout',
        accountQuestion: 'Don\'t have an account?',
        rememberMe: 'Remember login',
        forgotPasswordQuestion: 'Forgot password?',
        loginHere: 'Login here',
        teaser: 'Sign in with your Google, Facebook, or Twitter account or with your email address.',
        validation: {
            success: 'You have successfully logged into your account.',
            error: 'Please provide valid login details.',
        },
    },
    logs: {
        label: 'Events',
        timestamp: 'Timestamp',
        noEvents: 'No incidents recorded.',
        type: {
            label: 'Type',
            options: {
                INFO: {
                    label: 'Information',
                    description: 'Information',
                },
                WARNING: {
                    label: 'Warning',
                    description: 'Warnings',
                },
                SUCCESS: {
                    label: 'Success',
                    description: 'Successful actions',
                },
                ERROR: {
                    label: 'Error',
                    description: 'Errors',
                    info: 'Errors are always logged when they occur. You can view the details in the logs.',
                },
            },
        },
    },
    loops: {
        label: 'Loops',
        selectOption: 'Please select the type of loop',
        stepsLabel: 'Steps in the loop',
        iteration: 'Iteration: {{iteration}}',
        break: 'Iteration: {{iteration}}',
        failStopLabel: 'Abort Project-Run if this loop cannot be executed successfully.',
        options: {
            selector: {
                label: 'Repeat for each matching element',
                namePlaceholder: 'Element Loop',
                description: 'Execute a sequence of steps for every matching element in a DOM query.',
            },
            range: {
                label: 'Repeat within a numeric range',
                namePlaceholder: 'Numeric Range Loop',
                description: 'Execute a sequence of steps for every index in a range.',
                start: 'Start',
                end: 'End',
                step: 'Steps',
            },
            js: {
                label: 'Repeat until a condition is met',
                namePlaceholder: 'JavaScript Loop Function',
                description: 'Execute a sequence of steps based on the return value of a boolean function.',
                function: {
                    label: 'Loop Function',
                    description: 'This function is re-evaluated before each iteration and the block is only executed if it returns a true value.',
                    items: [
                        '"index" refers to the current iteration. It starts at 0 and ends at n-1 when the function is executed n times.',
                    ],
                },
            },
            jsList: {
                label: 'Repeat with a changing list',
                namePlaceholder: 'JavaScript List',
                description: 'Execute a sequence of steps for every element in a dynamically determined list.',
                function: {
                    label: 'JavaScript List Function',
                    description: 'This function is executed once and should return a list that will be iterated over subsequently.',
                },
            },
            customList: {
                label: 'Repeat with a fixed list',
                namePlaceholder: 'Custom List Loop',
                description: 'Execute a sequence of steps for every element in a user-defined list.',
                labels: {
                    newItem: 'Enter new values here',
                },
                notification: {
                    empty: 'Please enter at least one value.',
                },
            },
        },
        notifications: {
            deleteSuccess: 'The loop "{{label}}" was deleted',
            editSuccess: 'The loop "{{label}}" was successfully edited',
            saveSuccess: 'The loop "{{label}}" was successfully saved',
            copySuccess: 'The loop "{{label}}" was successfully copied',
        },
        label_one: 'Loop',
        repetitions: {
            label: 'Repetitions',
            view: {
                label: 'View Repetitions',
                label_one: 'View Repetition',
            },
            labels: {
                completed: {
                    label: 'Completed',
                    description: 'Successfully executed',
                },
                broken: {
                    label: 'Broken',
                    description: 'Execution interrupted',
                },
                failed: {
                    label: 'Failed',
                    description: 'Failed to execute',
                },
                voided: {
                    label: 'Voided',
                    description: 'Not executed',
                },
                aborted: {
                    label: 'Aborted',
                    description: 'Aborted.',
                },
            },
            label_one: 'Repetition',
        },
        labels: {
            failed: {
                label: 'Failed',
                description: 'Failed to execute',
            },
            voided: {
                label: 'Voided',
                description: 'Not executed',
            },
            aborted: {
                label: 'Aborted',
                description: 'Aborted.',
            },
            success: {
                label: 'Success',
                description: 'Successfully executed',
            },
        },
    },
    maintenance: {
        label: 'Oops, something went wrong',
        description: 'We have detected a technical problem and are working hard to fix it.',
        serverDown: 'The connection to the server was interrupted, please reload the page.',
        serverOk: 'The connection to the server has been reestablished.',
    },
    management: {
        label: 'Administration',
    },
    mandatoryField: 'The field "{{field}}" is required.',
    memoryUsage: 'Memory Usage',
    mindTheErrors: 'Please note the erroneous fields (highlighted in red)',
    minutes: 'Minutes',
    name: 'Key',
    nameValue: {
        fields: {
            name: 'Name',
            value: 'Value',
        },
    },
    needHelp: 'Need Help?',
    negateRule: 'Reverse Rule',
    new: 'New',
    customDevice: {
        label: 'Own Device',
        add: 'Add Own Device',
        edit: 'Edit Device',
        fields: {
            name: 'Device Name',
            userAgent: 'User Agent',
            viewport: {
                label: 'Viewport',
                width: 'Width',
                height: 'Height',
                deviceScaleFactor: 'Device Scale Factor',
                isMobile: 'Mobile',
                hasTouch: 'Touch Sensitive',
                orientation: {
                    label: 'Orientation',
                    portrait: 'Portrait',
                    landscape: 'Landscape',
                },
            },
        },
        validation: {
            name: 'The name must be between {{start}} and {{end}} characters long.',
            nameDuplicate: 'A device with the same name already exists.',
            userAgent: 'The User Agent must be between {{start}} and {{end}} characters long.',
            viewport: {
                width: 'The width must be between {{start}} and {{end}}.',
                height: 'The height must be between {{start}} and {{end}}.',
                deviceScaleFactor: 'The device scale factor must be between {{start}} and {{end}}.',
            },
        },
    },
    no: 'No',
    noAccessToFunction: 'This function is not available in your current subscription.',
    notActivated: 'Not Activated',
    notAvailable: 'N/A',
    notSavedDueToErrors: 'Your recent changes could not be saved due to input errors',
    notice: 'Notice',
    or: 'OR',
    overage: 'Overage',
    overview: 'Overview',
    packages: 'Packages',
    page: 'Page',
    page404: {
        pageTitle: '404 - Page Not Found',
        description: 'The page you are looking for does not exist.',
        backHome: 'Back to Homepage',
    },
    passwordReset: {
        label: 'Reset password',
        pageTitle: 'Reset password',
        title: 'Forgot your password?',
        notice: 'Please enter the email address associated with your account, and we\'ll send you a link to reset your password.',
        resend: 'Resend email',
        wasResent: 'Request was successfully sent to the email address {{email}}.',
        willBeSent: 'We will send you a link to reset your password to the email address {{email}}.',
        validation: {
            registrationError: 'Resetting the password did not work, please check if you entered the correct email address.',
        },
    },
    pasteJSCodeHere: 'Paste your logic here',
    payment: {
        title: 'It\'s almost time!',
        subTitle: 'Your package is prepared for you.',
        pageTitle: 'Checkout',
        cta: 'Checkout',
        notifications: {
            cancelled: 'The transaction was cancelled',
            errorPleaseRetry: 'An error occurred, please try again.',
        },
        confirmation: {
            pageTitle: 'Confirmation',
            title: 'Payment successful!',
            completed: 'Our partner {{paymentProvider}} has reported a successful transaction. You will receive the invoice by email at the address {{email}}.',
            inProgress: 'Your request is being processed.',
            invoiceDetails: 'View invoice',
            startProject: 'Start project',
        },
    },
    position: 'Position',
    previewImageMissing: 'No Preview',
    pricing: {
        label: 'Shop',
        pageTitle: 'Shop',
        cta: 'See Plans & Pricing',
        title: 'The right plan for every business and every website size',
        description: 'Choose a plan that suits your personal website or your business.',
        buildModeLicenses: {
            label: 'License Type',
            types: {
                linkMode: {
                    label: 'Link License',
                },
                pageMode: {
                    label: 'Page License',
                },
            },
        },
        demo: {
            label: 'Demo Packages',
            payment: {
                label: 'One-time',
                paymentDescription: 'You pay a symbolic amount once.',
            },
        },
        otp: {
            label: 'Extra Packages',
            noActiveLabel: 'Package missing!',
            payment: {
                label: 'One-time',
                paymentDescription: 'You pay once',
            },
            collaboratorSeats: {
                label: 'Seats for Employees',
                occupied: '{{used}} / {{total}} Seat(s) occupied',
                description: 'Employee licenses are one-time purchases, providing full flexibility in assigning and reassigning roles as often as necessary.',
            },
            automationCredits: {
                label: 'Automation-Credits',
                restContingent: 'Remaining Credit: {{rest}} Automation-Credits',
                description: 'Automation-Credits can be used to execute automation processes on our platform. Different processes and actions consume different amounts of credits. You can recharge the credits as often as you like.',
                use: 'Use Automation-Credits',
                project: 'Project will be executed with Automation-Credits',
                label_one: 'Automation Credit',
            },
        },
        subscriptions: {
            label: 'Subscriptions',
            saveInfo: 'You save up to {{savingInPercent}}% monthly with annual billing.',
            multiPackageInfo: 'If you’d like to subscribe to multiple packages, please choose ones with the same duration. For packages with a different duration, please complete this order first and then come back. Thank you for your understanding!',
            intervals: {
                monthlyX1: {
                    label: 'Monthly',
                    perLabel: ' / Month',
                    paymentDescription: 'You pay monthly',
                },
                monthlyX3: {
                    label: 'Quarterly',
                    perLabel: ' / Quarter',
                    paymentDescription: 'You pay every 3 months',
                },
                monthlyX6: {
                    label: 'Semi-Annually',
                    perLabel: ' / 6 months',
                    paymentDescription: 'You pay every 6 months',
                },
                monthlyX12: {
                    label: 'Annually',
                    perLabel: ' / Year',
                    paymentDescription: 'You pay every 12 months',
                },
            },
            goto: 'Go to subscriptions',
        },
        support: {
            label: 'Do you have any concerns?',
            description: 'Contact us for customized solutions or if you would like to request a demo.',
        },
        planSelection: 'Add to cart',
        totalPrice: 'Total price',
        includesTaxesInfo: '* Plus taxes',
        totalSum: 'Total sum',
        stripeInfo: {
            title: 'Payment Notice',
            description: 'We work with {{paymentProvider}} to enable secure handling of your payment.',
            billViaEmail: 'You will receive the invoice by email afterward.',
        },
        cart: {
            items: {
                product: 'Product',
                quantity: 'Quantity',
                price: 'Price',
                subTotal: 'Subtotal',
            },
        },
        overages: {
            label: 'Catalog for overages / unit prices',
            description: 'Below are the overages and individual prices for the various services. These prices will be applied when you exceed the limits of your plan or when you run automations with your Automation-Credits.',
            upTo: 'Up to',
            infinity: 'Upwards',
            unitAmount: 'Unit Amount',
            item: 'Unit',
            billing: {
                label: 'The overages are billed at the end of every subscription period.',
                autoRenewal: 'Activate automatic renewal to avoid service interruptions.',
            },
        },
    },
    privacyPolicy: {
        label: 'Privacy Policy',
        pageTitle: 'Privacy Policy',
    },
    products: {
        ai: {
            label: 'AI Integration',
            activated: 'AI Integration is in the package.',
            notActivated: 'AI Integration is not in the package.',
            info: 'With AI Integration, you can optimize your automation processes with machine learning.',
        },
        notificationProducts: {
            label: 'Notification Packages',
            items: {
                emailNotification: {
                    label: 'Email Notifications',
                    activated: 'Email notifications are in the package.',
                    notActivated: 'Email notifications are not in the package.',
                    info: 'You will receive a detailed report via email after each build.',
                },
                webhooksNotification: {
                    label: 'Webhook Notifications',
                    activated: 'Webhook notifications are in the package.',
                    notActivated: 'Webhook notifications are not in the package.',
                    info: 'You will receive the Project-Run data and results via webhook after each build.',
                },
            },
        },
    },
    project: {
        build: {
            cancellation: {
                label: 'Project-Run Cancellation in Progress',
                inProgress: 'Cancelled by {{email}} on {{dateTime}}.',
                cta: 'Cancel Project-Run',
            },
            config: 'Configuration',
            confirmation: {
                preparingBuild: 'Project-Run is being prepared.',
                question: 'Project "{{projectName}}" will be built immediately.',
                continue: 'Continue',
                cancel: 'Cancel',
            },
            ctaLabel: 'To Project-Run report',
            date: 'Date',
            delete: {
                label: 'Delete Project-Run #{{buildId}}!',
                impact: {
                    irreversible: 'I am aware that this action is irreversible.',
                    relatedData: 'All data (images, videos, Project-Run data, etc.) associated with this Project-Run will be deleted.',
                },
                notifications: {
                    deleteSuccess: 'Project-Run has been deleted',
                    deleteFailure: 'Project-Run could not be deleted',
                },
            },
            domainApprovalError: {
                label_one: '1 Domain not approved',
                label: '{{count}} Domains not approved',
                processedDomainList: 'Processed Domains',
                targetDomainList: 'Domains included in your Project-Run',
                new: 'Not Approved',
            },
            domainError: {
                label: 'Project-Run cannot be performed because the domain limit ({{domainContingent}}) has been reached.',
                checkedDomainList: 'Domains checked so far',
                targetDomainList: 'Domains included in your Project-Run',
                new: 'New',
                recommendation: 'You can remove the new domains or link a different subscription.',
            },
            downloads: {
                label: 'Download Reports',
                optionsLabel: 'Export Options',
                typesLabel: 'Export Type',
                options: {
                    basics: {
                        label: 'Basic Data',
                        description: 'Basic project data, e.g., project name, project status, creation date, etc.',
                    },
                    settings: {
                        label: 'Settings',
                        description: 'Project settings, e.g., URL, browser, device, etc.',
                    },
                    results: {
                        label: 'Results',
                        description: 'Project results, e.g., number of checked URLs, number of failed URLs, etc.',
                    },
                    subscription: {
                        label: 'Subscription',
                        description: 'Project subscription, e.g., subscription type, subscription duration, etc.',
                    },
                    logs: {
                        label: 'Logs',
                        description: 'Project logs, e.g., project created, project edited, etc.',
                    },
                },
                types: {
                    pdf: {
                        label: 'PDF',
                    },
                    json: {
                        label: 'JSON',
                    },
                },
                ready: 'Your download has been prepared and is ready for you to download.',
                view: 'View your download',
            },
            duration: 'Duration',
            endOn: 'Project-Run End',
            erroneous: 'The Project-Run could not be completed successfully.',
            failStop: {
                label: 'Fail-Stop',
                description: 'If a step fails, the project is aborted.',
                trigger: 'Fail-Stopping',
                triggered: 'Project-Run was aborted because this step failed.',
            },
            id: 'Project-Run Number',
            indexedBuild: 'Project-Run #{{buildId}}: {{state}}',
            label: 'Project-Runs',
            lastFailure: 'Last Failure',
            lastSuccessful: 'Last Success',
            latestBuild: 'Latest Project-Run',
            loadData: 'Load Data',
            navigation: {
                first: 'Oldest Project-Run',
                previous: 'Previous Project-Run',
                next: 'Next Project-Run',
                last: 'Newest Project-Run',
            },
            noData: 'No builds are available for this project yet.',
            notEditableByIsRunning: 'The project is currently building and cannot be edited.',
            notFound: 'Project-Run ({{buildId}}) could not be found',
            report: {
                error: 'Error saving the report',
                title: '{{app}} Project-Run Report from {{dateTime}} ({{tz}})',
                buildInfo: {
                    index: '#',
                    label: 'Project-Run Data',
                },
                results: {
                    total: 'Total Attempts',
                    success: 'Successfully Completed',
                    failure: 'Failed Attempts',
                },
                summary: {
                    title: 'Summary',
                    subTitle: 'Overview',
                    label: '{{solution}} Overview',
                    resultLabel: '{{solution}} Report',
                    total: 'Qualified URLs',
                    buildTime: 'Processing time',
                    ignored: 'Ignored URLs',
                    invalid: 'Invalid URLs',
                    success: 'Successful URLs',
                    failure: 'Failed URLs',
                    share: 'Share',
                    delta: 'Δ',
                },
                nextExecution: {
                    label: '{{dateTime}} ({{timeZone}})',
                },
                buildReference: {
                    label: 'Project Run Reference',
                },
            },
            result: {
                label: 'Result',
                loadError: 'Result data cannot be loaded or is no longer available',
                selectResultUrlInfo: 'Select URL',
                selectResultSubUrlInfo: 'Select Subpage',
                noUrlSelectedInfo: 'Nothing selected, nothing to display!',
                levelInfo: '{{level}} Level',
                moreLevel: 'Subpages',
                exclusion: {
                    cta: 'View {{count}} excluded URLs',
                    title: '{{count}} URLs have been excluded',
                    placeholder: 'Select URL to view details',
                    label: 'Exclusion Reason',
                    reasons: {
                        format: 'URL Format.',
                        visited: 'URL has already been processed.',
                        normalization: 'Duplicate after normalization.',
                        regex: 'String settings.',
                        subdomain: 'Subdomain settings.',
                        external: 'Is an external URL.',
                        internal: 'Is an internal URL.',
                    },
                },
                html: {
                    label: 'Source Code',
                    hierarchy: 'URL Hierarchy',
                    sourceCode: 'Source Code Snippet',
                    markup: 'Markup',
                    htmlSelector: 'Query/Selector',
                    normalizedUrl: 'URL after Normalization',
                },
            },
            schedule: {
                label: 'Schedule',
                edit: 'Edit Schedule',
                inactive: 'Scheduled Project-Runs are inactive',
                nextBuild: 'Next schedule',
                nextBuildList: 'Next {{count}} schedules',
                nextBuildList_one: 'Next schedule',
                provisioningServers: 'Build will start momentarily.',
            },
            screenshot: {
                label: 'Screenshot',
            },
            settingsNotEditableByHasRun: 'The section "{{label}}" cannot be edited anymore as the project has already been run at least once.',
            showConfig: 'View Configuration',
            startedOn: 'Project-Run Start',
            stop: 'Abort Project-Run',
            timeline: 'Logs ({{count}})',
            trigger: {
                label: 'Trigger',
                vendors: {
                    manual: {
                        label: 'Manual',
                        description: 'This Project-Run was triggered manually',
                    },
                    auto: {
                        label: 'Scheduled',
                        description: 'This Project-Run was triggered by a scheduled job.',
                    },
                    manager: {
                        label: 'WebAutomate AI.',
                        description: 'This Project-Run was triggered by the WebAutomate AI.',
                    },
                },
            },
            states: {
                none: {
                    label: 'Pending',
                    description: 'The Project-Run is pending',
                },
                timeout: {
                    label: 'Timed out',
                    description: 'The Project-Run timed out',
                },
                running: {
                    label: 'Running',
                    description: 'The Project is running',
                },
                queue: {
                    label: 'Queued',
                    description: 'The Project-Run is queued',
                },
                cancel: {
                    label: 'Cancelled',
                    description: 'The Project-Run was cancelled',
                },
                success: {
                    label: 'Successful',
                    description: 'The Project-Run was successful',
                },
                failure: {
                    label: 'Failed',
                    description: 'The Project-Run failed',
                },
                mixed: {
                    label: 'Mixed',
                    description: 'The Project-Run has mixed results',
                },
                terminated: {
                    label: 'Terminated',
                    description: 'The build has been terminated by the system.',
                },
            },
            deleteAll: {
                label: 'Delete all Builds!',
                impact: {
                    irreversible: 'I am aware that this action is irreversible.',
                    relatedData: 'All data (images, videos, Project-Run data, etc.) associated with Builds will be deleted.',
                },
                notifications: {
                    deleteSuccess: 'All Builds have been deleted',
                    deleteFailure: 'Builds could not be deleted',
                },
            },
        },
        buildPlan: 'Project-Run Plan',
        clone: {
            label: 'Clone Project',
            prefix: 'Clone of',
            info: 'Click on a project to clone it.',
            notice: 'Your current settings in the project assistant will be overwritten.',
            success: 'Project "{{projectName}}" has been successfully cloned.',
        },
        configuration: {
            label: 'Project Configuration',
            error: 'Configuration error detected. Please check your settings.',
            preview: 'Show Project Configuration',
            version: 'Version',
        },
        configuratorTutorial: 'Project Configurator Tutorial',
        create: 'Create new project',
        createdBy: 'Created by',
        createdOn: 'Created on',
        delete: {
            label: 'Delete Project!',
            impact: {
                irreversible: 'This action cannot be undone.',
                relatedData: 'All data (images, videos, Project-Run data, etc.) associated with this project will be deleted.',
            },
            notifications: {
                deleteSuccess: 'Project has been deleted',
                deleteFailure: 'Project could not be deleted',
            },
        },
        edit: {
            label: 'Edit Project',
            save: 'Update Project',
        },
        editedBy: 'Edited by',
        editedOn: 'Edited on',
        empty: 'No projects found',
        emptyInfo: 'The list of all projects will be displayed here. It looks like you haven\'t started a project yet.',
        cta: 'To Project',
        helpRequiredQuestion: 'Do you need help setting up a project?',
        info: 'Control',
        label: 'New Project Assistant',
        list: {
            label: 'Projects',
            filter: 'Filter Projects...',
            fields: {
                name: 'Project Name',
                solution: 'Solution',
                state: 'Status',
                creator: 'Created by',
                created: 'Created on',
                duration: 'Duration',
            },
            addField: 'Edit Columns',
            showArchived: 'Show Archived Projects',
            search: {
                emptyLabel: 'No results',
                notice: 'No results found for "{{searchQuery}}". Try checking for typos or using complete words.',
            },
        },
        load: {
            label: 'Load Project',
            info: 'Click on a project to load it.',
            notice: 'Settings in the project assistant will be overwritten.',
            success: 'Project "{{projectName}}" has been successfully loaded.',
        },
        motivation: 'In every industry, the website is the first point of contact for many stakeholders. A positive user experience and SEO-optimized web presence are therefore essential.',
        motivation2: 'Don\'t waste time on repetitive tasks on your website. Let {{app}} do that for you!',
        name: {
            label: 'Project Name',
            notifications: {
                required: 'Project name is required.',
                length: 'Project name cannot be longer than {{max}} characters.',
                enterProjectName: 'Enter a valid project name that makes it easy to identify this project in reports and notifications.',
            },
        },
        new: {
            label: 'New Project',
            resetPrompt: {
                label: 'Reset Configuration',
                description: 'Resetting the configuration will delete all settings in the project assistant.',
                cancel: 'Cancel',
                confirm: 'Yes, reset',
            },
            success: 'Configuration has been reset.',
            save: 'Save Project',
        },
        notFound: 'Project not found',
        originalURL: 'URL before Normalization',
        pageTitle: 'New Project Assistant',
        requestedURL: 'Requested URL',
        resolvedURL: 'Resolved URL',
        resolvedURLFromCache: {
            title: 'You are seeing cached data.',
            message: 'The requested URL "{{url}}" resolves to the previously processed URL "{{resolvedUrl}}".',
        },
        result: {
            overview: {
                label: 'Overview',
            },
            settings: {
                label: 'Settings',
                timeZone: {
                    label: 'Time Zone',
                },
            },
            history: {
                label: 'Logs',
            },
        },
        run: 'Run Project',
        runShort: 'Run',
        setupPageTitle: 'Project Configurator',
        state: 'State',
        stateTypes: {
            draft: {
                label: 'Draft',
            },
            active: {
                label: 'Active',
                cta: 'Activate Now',
            },
            pause: {
                label: 'Paused',
                cta: 'Pause Now',
            },
            archive: {
                label: 'Archived',
                cta: 'Archive Now',
                info: 'The project has been archived, you need to unarchive it to continue working on it.',
                unarchive: 'Unarchive',
            },
        },
        url: 'URL',
        wizard: 'Project Assistant',
        wizardLoginRequired: 'You need to log in to create a project.',
        wizardSteps: {
            session: {
                started: 'Project Assistant session with ID "{{sessionId}}" has started.',
            },
            solutionSelection: {
                label: 'Solution',
                previewLabel: 'Solution Type',
                select: 'Select Solution Type',
                description: 'Click on the respective options to activate.',
                change: {
                    label: 'You are about to change the solution type from "{{solution1}}" to "{{solution2}}". What should happen to the steps?',
                    deleteAll: 'Delete all steps',
                    deleteSolution: 'Delete only steps of type {{solution1}}.',
                    stepsNotRequired: 'All steps will be deleted because {{solution2}} does not require any steps.',
                },
            },
            target: {
                label: 'Basic Settings',
                previewLabel: 'Basic Settings',
                checkType: {
                    label: 'Mode',
                    mode: 'Mode',
                    groups: {
                        link: {
                            label: 'Single URLs',
                            ctaLabel: 'Enter a URL.',
                            description: 'All entered URLs will be processed.',
                        },
                        page: {
                            label: 'Pages',
                            ctaLabel: 'Enter a page address.',
                            description: 'You enter a URL. Our AI analyzes the page based on predefined rules and extracts all qualifying URLs for processing.',
                        },
                    },
                    moreLabel: 'Add URL',
                },
                executionStrategy: {
                    label: 'Project Execution Strategy',
                    useCases: 'Use Cases',
                    groups: {
                        sharedBrowserInstance: {
                            label: 'Shared Browser Instances',
                            description: 'During the build, each URL is opened in a single browser context. This means cookies, session storage, and local storage are shared between URLs.',
                            useCase: 'Suitable for testing multiple independent pages that might share session information but need to be opened simultaneously for comparison or parallel testing.',
                        },
                        isolatedBrowserInstances: {
                            label: 'Isolated Browser Instances',
                            description: 'During the build, each URL is opened in a completely isolated browser context. This means no cookies, session storage, or local storage are shared between URLs.',
                            useCase: 'Ideal for scenarios requiring full isolation, such as testing independent sessions, avoiding cross-page contamination, or ensuring unique environments for each URL.',
                        },
                    },
                },
                browser: {
                    label: 'Browser',
                    description: 'Choose the browser with which the project will be executed.',
                    lighthouseDescription: 'Lighthouse projects are only executed with the Chrome browser.',
                    groups: {
                        chromium: {
                            label: 'Chrome',
                        },
                        firefox: {
                            label: 'Firefox',
                        },
                        safari: {
                            label: 'Safari',
                        },
                    },
                },
                device: {
                    label: 'Device',
                    labelMultiple: 'Devices',
                    description: 'Choose the device with which the project will be executed.',
                    errorMessage: 'Please select a device.',
                    lighthouse: {
                        targets: {
                            mobile: 'Mobile',
                            desktop: 'Desktop',
                        },
                    },
                },
            },
            linkOptions: {
                label: 'URL Configuration',
                previewLabel: 'URL Configuration',
                groups: {
                    urlSelection: {
                        label: 'Find URLs',
                        title: 'Where to search for URLs?',
                        description: 'Our AI will scan all found URLs. However, you can specify which areas in your HTML documents should be restricted for URL searching.',
                        fields: {
                            selector: {
                                label: 'Link Query Selector',
                                description: 'If you have JavaScript knowledge, you can specify your own selectors here.',
                            },
                            tags: {
                                label: 'Link Source Tags',
                                description: 'Additionally, you can select the HTML tags where interesting URLs are extracted.',
                                selectAll: 'Select All',
                                deselectAll: 'Deselect All',
                            },
                        },
                    },
                    urlNormalization: {
                        label: 'Normalize URLs',
                        title: 'Handling Similar URLs.',
                        description: 'Specify how URLs should be normalized to determine if two syntactically different URLs are equivalent.',
                        fields: {
                            stripAuthentication: {
                                label: 'Remove Authentication Part',
                            },
                            stripHash: {
                                label: 'Remove Hash',
                            },
                            stripWWW: {
                                label: 'Remove \'www.\' from all links',
                            },
                            removeQueryParameters: {
                                label: 'Remove Query Parameters',
                            },
                            removeTrailingSlash: {
                                label: 'Remove Trailing Slashes from Path Names',
                                helperText: 'Slashes are always removed if no paths are present.',
                            },
                        },
                        result: 'Result after normalization: {{result}}',
                    },
                    urlFiltering: {
                        label: 'Filter URLs',
                        title: 'Filter URLs',
                        description: 'Our AI performs services by default on all URLs. If you don\'t want this, you can set filters here. URLs that do not match the filter rules will be ignored.',
                        fields: {
                            useResolvedURLBase: {
                                label: 'Filter Base',
                                description: 'If the entered URL is resolved differently (e.g., redirect from https://www.my-website.com to https://m.my-website.com on mobile devices), you can determine here whether the original URL remains as the base or the redirected URL is used as the base.',
                                option: 'Use Resolved URL as Base',
                                optionAlt: 'Keep Original URL as Base',
                            },
                            subdomains: {
                                label: 'Subdomains',
                                description: 'Disable this option to exclude subdomains.',
                                notification: 'This option does not apply if the "{{useResolvedURLBaseLabel}}" is a subdomain.',
                                option: 'Include Subdomain',
                                optionAlt: 'Include Subdomain',
                            },
                            externalLinks: {
                                label: 'External URLs',
                                description: 'Enable this option to include URLs from external domains on your website.',
                                option: 'Include External URLs',
                                optionAlt: 'Include External URLs',
                            },
                            internalLinks: {
                                label: 'Internal URLs',
                                description: 'Disable this option to exclude internal URLs.',
                                option: 'Include Internal URLs',
                                optionAlt: 'Include Internal URLs',
                            },
                            urlMatch: {
                                label: 'URL',
                                title: 'In this area, you set rules for the URL patterns considered by the AI.',
                            },
                        },
                    },
                },
            },
            requestOptions: {
                label: 'Request & Browser',
                previewLabel: 'Request and Browser Settings',
                groups: {
                    browser: {
                        label: 'Browser Settings',
                        title: '',
                        description: 'Set the browser language here',
                        fields: {
                            language: {
                                label: 'Browser Language',
                            },
                            deactivateJS: {
                                label: 'Deactivate JavaScript',
                                description: 'Disable the execution of JavaScript.',
                            },
                            waitUntil: {
                                label: 'When a URL request is complete',
                                description: 'Specify when the page is considered fully loaded.',
                                options: {
                                    domcontentloaded: {
                                        label: 'When the \'DOMContentLoaded\' event is triggered.',
                                    },
                                    load: {
                                        label: 'When the \'load\' event is triggered.',
                                    },
                                    networkidle0: {
                                        label: 'When there are no network requests for 500ms from the page.',
                                    },
                                    commit: {
                                        label: 'When the network has responded and the document has started loading.',
                                    },
                                    js: {
                                        label: 'When this JavaScript condition is met.',
                                        code: {
                                            label: 'JavaScript Code',
                                            edit: 'Edit Code',
                                            description: 'JavaScript code that is executed to check if the page has finished loading.',
                                        },
                                    },
                                },
                            },
                            linkRequestTimeout: {
                                label: 'URL Request Timeout',
                                description: 'If our AI does not receive a response within {{duration}} during the URL request, the URL will be reported as faulty.',
                            },
                        },
                    },
                    storage: {
                        label: 'Browser Storage',
                        title: '',
                        description: 'In this sandbox, you can customize parameters, request headers, cookies, and storage mechanisms for your project.',
                        fields: {
                            requestCookies: {
                                title: 'Cookies',
                                keyLabel: 'Name',
                                valueLabel: 'Value',
                                label: 'Cookies',
                                description: 'Add cookies to the browser.',
                                moreLabel: 'Add Request Cookie Entry',
                            },
                            sessionStorage: {
                                title: 'Session Storage Entries',
                                keyLabel: 'Key',
                                valueLabel: 'Value',
                                label: 'Session Storage Entry',
                                description: 'Add session storage entries to the browser.',
                                moreLabel: 'Add Session Storage Entry',
                            },
                            localStorage: {
                                title: 'Local Storage Entries',
                                keyLabel: 'Key',
                                valueLabel: 'Value',
                                label: 'Local Storage Entry',
                                description: 'Add local storage entries to the browser.',
                                moreLabel: 'Add Local Storage Entry',
                            },
                        },
                    },
                    resources: {
                        label: 'Network Activity',
                        title: '',
                        description: 'Control network activity by determining how web resources are modified or even blocked. Web resources include images, CSS, JS, and other data necessary for proper display and functionality of the website.',
                        fields: {
                            requestHeaders: {
                                title: 'Request Headers',
                                label: 'Insert Request Header',
                                keyLabel: 'Key',
                                valueLabel: 'Value',
                                emptyValueInfo: 'Leave the header value empty to delete the header.',
                                description: 'Specify which headers should be sent with the URL request.',
                                moreLabel: 'Add Request Header',
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                            },
                            requestParameters: {
                                title: 'Request Parameters',
                                label: 'Insert Request Parameter',
                                keyLabel: 'Parameter',
                                valueLabel: 'Value',
                                description: 'Specify which parameters should be appended to the URL during the request.',
                                moreLabel: 'Add Request Parameter',
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                            },
                            resourcesInjection: {
                                title: 'Resources',
                                label: 'Insert Resources',
                                description: 'Insert resources (CSS or JS) into any page.',
                                source: {
                                    label: 'URL',
                                    placeholder: 'Enter the resource URL here.',
                                },
                                content: {
                                    label: 'Content',
                                    placeholder: 'Enter the content here.',
                                },
                                contentType: {
                                    label: 'Content Type',
                                    js: 'JavaScript',
                                    css: 'CSS',
                                    html: 'HTML Snippet',
                                },
                                htmlResourceLocation: {
                                    label: 'Insert Into',
                                    head: {
                                        label: '<head>...',
                                    },
                                    body: {
                                        label: '<body>...',
                                    },
                                },
                                htmlResourcePosition: {
                                    label: 'Position',
                                    start: {
                                        label: 'Start',
                                    },
                                    end: {
                                        label: 'End',
                                    },
                                },
                                injectionType: {
                                    label: 'Type',
                                    url: 'Insert URL',
                                    urlLabel: 'Insert {{contentType}} from "{{source}}".',
                                    customContent: 'Insert Custom Content',
                                    customContentLabel: 'Insert Custom Content',
                                    customContentView: 'View Content',
                                },
                                validation: {
                                    invalid: 'The URL must be a valid URL.',
                                    duplicated: 'The URL has already been recorded.',
                                    content: 'Content is required.',
                                },
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                                urlFilter: {
                                    label: 'URL Filter',
                                    options: {
                                        allUrls: {
                                            label: 'All URLs',
                                        },
                                        projectUrls: {
                                            label: 'Only project URLs',
                                        },
                                        regex: {
                                            label: 'Regex',
                                        },
                                    },
                                },
                            },
                            requestBlockers: {
                                title: 'HTTP Blockers',
                                label: 'Block HTTP Request',
                                description: 'Block API calls to endpoints.',
                                itemListTitle: 'Block HTTP Request',
                                firstMatchInfo: 'Only the first matching block will be applied.',
                                preview: {
                                    standard: 'Block {{method}} Requests to "{{value}}" ({{matcher}})',
                                },
                                errorCode: {
                                    label: 'Error Code',
                                    options: {
                                        aborted: 'The operation was aborted (due to user action).',
                                        accessdenied: 'Permission to access a resource other than the network was denied.',
                                        addressunreachable: 'The IP address is unreachable. This usually means there is no route to the specified host or network.',
                                        blockedbyclient: 'The client has opted to block the request.',
                                        blockedbyresponse: 'The request failed because the response came with unmet requirements.',
                                        connectionaborted: 'The connection was aborted because no ACK was received for the sent data.',
                                        connectionclosed: 'The connection was closed.',
                                        connectionfailed: 'The connection attempt failed.',
                                        connectionrefused: 'The connection attempt was refused.',
                                        connectionreset: 'The connection was reset.',
                                        internetdisconnected: 'The internet connection was interrupted.',
                                        namenotresolved: 'The hostname could not be resolved.',
                                        timedout: 'The operation was aborted.',
                                        failed: 'A general error occurred.',
                                    },
                                },
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                            },
                            requestMocks: {
                                title: 'Mock HTTP Requests',
                                label: 'Add HTTP Request Mocks',
                                description: 'Intercept API calls and return a custom response without calling the actual endpoint.',
                                itemListTitle: 'Mock API',
                                firstMatchInfo: 'Only the first matching mock will be applied.',
                                preview: {
                                    standard: 'Mock {{method}} Requests to "{{value}}" ({{matcher}})',
                                },
                                options: {
                                    method: {
                                        label: 'Method',
                                    },
                                    status: {
                                        label: 'Status Code',
                                        validation: {
                                            invalid: 'You must specify a status code.',
                                            range: 'Status code must be a 3-digit number.',
                                        },
                                    },
                                    contentType: {
                                        label: 'Content-Type',
                                        content: 'Response Data',
                                    },
                                    headers: {
                                        title: 'Headers',
                                        label: 'Add Headers',
                                        keyLabel: 'Key',
                                        valueLabel: 'Value',
                                    },
                                    body: {
                                        label: 'Response Text',
                                    },
                                    json: {
                                        label: 'JSON',
                                    },
                                },
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                            },
                            requestOverrides: {
                                title: 'HTTP Requests',
                                label: 'Modify HTTP Requests',
                                description: 'You can change the headers and body of HTTP requests before they are sent.',
                                reducer: 'Reducer Function',
                                ctaLabel: 'The function receives the original request data and must return the data (with or without changes) in the same type; otherwise, the original data will be kept.',
                                itemListTitle: 'HTTP Request Modifications',
                                firstMatchInfo: 'Only the first matching modification will be applied.',
                                preview: {
                                    standard: '{{method}} Requests to "{{value}}" ({{matcher}}).',
                                },
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                            },
                            responseOverrides: {
                                title: 'HTTP Responses',
                                label: 'Modify HTTP Responses',
                                description: 'You can modify the headers and body of HTTP responses before they arrive in the browser.',
                                reducer: 'Reducer Function',
                                ctaLabel: 'The function receives the original response data and must return the data (with or without changes) in the same type; otherwise, the original data will be kept.',
                                itemListTitle: 'HTTP Response Modifications',
                                firstMatchInfo: 'Only the first matching modification will be applied.',
                                preview: {
                                    standard: '{{method}} Responses from "{{value}}" ({{matcher}}).',
                                },
                                notifications: {
                                    deleteSuccess: 'Entry deleted successfully.',
                                },
                            },
                        },
                    },
                },
            },
            solutionsConfigs: {
                label: 'Project Configuration',
                previewLabel: '{{solution}} Workflow Configuration',
                steps: {
                    label: 'Project-Run Steps',
                    labelCount: '{{count}} Steps',
                    labelCount_one: '1 Step',
                    labelCount_zero: '',
                },
                groups: {
                    overview: {
                        label: 'Project Info',
                    },
                    linkChecker: {
                        label: 'Page-Load',
                        captured: '{{count}} links have been checked.',
                        captured_zero: 'No links have been checked.',
                        captured_one: '1 link has been checked.',
                        resultBlock: 'Page-Load Report',
                        teaser: {
                            descriptions: [
                                'Ensure that all links on your website are functioning correctly and free of errors with our automated link checker. An essential tool to maintain the integrity of your website.',
                            ],
                        },
                        status: 'Status',
                        statusText: 'Status Text',
                        contentType: 'Content-Type',
                        start: 'Processing Start',
                        end: 'Processing End',
                        result: {
                            label: 'Result',
                            success: 'Link was processed successfully',
                            failure: 'Link processing failed',
                        },
                    },
                    urlChallenge: {
                        label: 'URL-Challenge',
                        summary: 'With the \'URL-Challenge\' feature, one or more URLs can be tested against a range of challenges.',
                        title: 'Put Your URLs to the Test Now!',
                        failStopLabel: 'Abort challenge if this step fails.',
                        failStopAppliedLabel: 'Challenge was aborted because the step \'{{label}}\' failed.',
                        selectOption: 'Select Option',
                        captured: '{{count}} challenges were issued to {{participants}}.',
                        captured_zero: 'No challenges were issued.',
                        captured_one: '1 challenge was issued to {{participants}}.',
                        modes: {
                            label: 'Mode',
                            options: {
                                eliminationMode: {
                                    label: 'Elimination Mode',
                                    description: 'Each URL is subjected to the tests. If a URL fails a test, it is eliminated and not tested further. The test cycle ends when all URLs have either passed all tests or been eliminated.',
                                },
                                comprehensiveMode: {
                                    label: 'Comprehensive Mode',
                                    description: 'Each URL undergoes all defined tests regardless of whether it passes a test or not.',
                                },
                            },
                        },
                        teaser: {
                            descriptions: [
                                'The URL-Challenge allows you to test the performance and reliability of URLs on your website against a set of predefined challenges. It helps ensure that your URLs meet quality standards and function as expected.',
                                'You can base the URL-Challenge on page interactions, loading behaviors, or specific website elements. Alternatively, describe a custom challenge, and our AI will evaluate your URLs accordingly.',
                            ],
                        },
                        items: {
                            title: 'Challenge Steps',
                        },
                        itemLabelling: {
                            distinct: 'URL-Challenges must be uniquely named.',
                        },
                        labels: {
                            passed: {
                                label: 'Passed',
                                latest: 'This challenge was passed.',
                            },
                            notPassed: {
                                label: 'Not Passed',
                                latest: 'This challenge was not passed.',
                            },
                            failed: {
                                label: 'Failed',
                                latest: 'An error occurred while executing the challenge.',
                            },
                            aborted: {
                                label: 'Aborted',
                                latest: 'This challenge was aborted.',
                            },
                            skipped: {
                                label: 'Skipped',
                                latest: 'This challenge was skipped.',
                            },
                            voided: {
                                label: 'Not Attempted',
                                latest: 'This challenge was not attempted.',
                            },
                            participants: {
                                label: 'Number of Participants',
                                latest: 'Number of participants in this challenge.',
                            },
                            totalChallenges: {
                                label: 'Number of Challenges',
                                latest: 'Number of challenges issued.',
                            },
                            passedChallenges: {
                                label: 'Passed Challenges',
                                latest: 'Number of challenges passed.',
                            },
                            failedOrVoidChallenges: {
                                label: 'Failed or Voided Challenges',
                                latest: 'Number of challenges failed or skipped.',
                            },
                        },
                        groups: {
                            automated: {
                                label: 'Natural Language',
                                options: {
                                    ai: {
                                        label: 'AI-Powered URL-Challenge',
                                        title: 'Specify Challenge',
                                        description: 'Describe challenges in any language, which will be evaluated by the AI assistant after analyzing the active page and other technical information.',
                                        indecisiveCase: {
                                            label: 'If the AI assistant is unsure',
                                            description: 'Choose how this challenge should be evaluated if the AI assistant is unsure.',
                                            options: {
                                                isPassed: 'Mark challenge as Passed',
                                                isFailed: 'Mark challenge as Failed',
                                                isVoided: 'Skip challenge and not evaluate',
                                            },
                                        },
                                        disclaimer: {
                                            label: 'Disclaimer',
                                            description: 'AI-assisted challenges are based on machine learning and may not always meet expectations. For specific tests that cannot be performed by AI, please use JavaScript tests.',
                                        },
                                        expectationExamples: [
                                            'There are 3 cat images on the page',
                                            'The page is in Igbo language',
                                            'etc...',
                                        ],
                                    },
                                },
                            },
                            custom: {
                                label: 'Custom',
                                options: {
                                    js: {
                                        label: 'With JavaScript',
                                        title: 'JavaScript Challenge Specification',
                                        description: 'Specify with JavaScript what exactly is tested. Current context data of the active page is available (Request data, Response data, Cookies, Certificate data, etc.).',
                                    },
                                },
                            },
                        },
                        notifications: {
                            deleteSuccess: 'URL-Challenge has been deleted',
                            editSuccess: 'URL-Challenge has been successfully edited',
                            saveSuccess: 'URL-Challenge has been successfully saved',
                            copySuccess: 'URL-Challenge has been successfully copied',
                        },
                        reviewer: {
                            label: 'URL-Challenge Reviewer',
                            cta: 'Open URL-Challenge Reviewer',
                            participants: 'Participants',
                            position: 'Position',
                            winner: 'Winner',
                            rankingTable: 'Ranking Table',
                            disqualified: {
                                label: 'Disqualified Participants',
                                reason: 'Reasons',
                            },
                        },
                        label_one: 'URL-Challenge',
                        content: {
                            intro: {
                                label: 'Validate Your Website’s URLs with Confidence',
                                title: 'Automated URL Testing to Ensure Quality, Functionality & Performance',
                                description: 'URLs are the backbone of every website experience. With webautomate.app’s URL-Challenge solution, you can automatically test the reliability, behavior, and performance of your web pages—at scale, with no code required.',
                            },
                            definition: {
                                label: 'What is URL Challenge?',
                                description: 'The URL-Challenge solution helps you verify that every page on your site meets expectations—whether it’s loading correctly, rendering key elements, or passing business-critical checks. You define the rules; we automate the validation.',
                            },
                            usage: {
                                label: 'Two Ways to Test—AI-Powered or Custom',
                                options: {
                                    ai: {
                                        label: 'AI-Powered URL Challenges',
                                        description: 'Describe what should be validated in plain language. Our AI will inspect the page, analyze screenshots, and run your checks.',
                                        examples: {
                                            label: 'Examples',
                                            items: [
                                                'Check that the checkout button is visible and clickable on mobile.',
                                                'Ensure the page title matches the H1 tag',
                                                'Verify that the page loads in under 2 seconds',
                                                'Confirm that the page includes a specific image',
                                            ],
                                        },
                                    },
                                    custom: {
                                        label: 'JavaScript-Based URL Challenges',
                                        description: 'Need more control? Use custom JS to test cookies, headers, or DOM properties with precision.',
                                    },
                                },
                            },
                            modes: {
                                label: 'Test the Way You Work',
                                options: {
                                    eliminationMode: {
                                        label: 'Elimination Mode',
                                        description: 'Stop testing a URL as soon as one check fails.',
                                    },
                                    comprehensiveMode: {
                                        label: 'Comprehensive Mode',
                                        description: 'Run all checks—even if some fail—to get a complete picture.',
                                    },
                                },
                                note: 'You choose the strategy. We handle the automation.',
                            },
                            reporting: {
                                label: 'Powerful Reporting at Your Fingertips',
                                description: 'Track every test result with detailed build reports: screenshots',
                                items: [
                                    'URLs tested and passed/failed counts',
                                    'Screenshots of failures',
                                    'Load timing, HTTP data, and test status',
                                    'Downloadable logs and reports',
                                ],
                                note: 'Perfect for developers, QA teams, marketers, and SEO specialists alike.',
                            },
                            getStarted: {
                                label: 'Start Testing Smarter',
                                description: 'Set up your first URL-Challenge in minutes. No code required. No more guesswork.',
                            },
                        },
                    },
                    lighthouse: {
                        label: 'Google-Lighthouse-Audits',
                        summary: 'Check the performance, accessibility, best practices, SEO, and PWA features of my website.',
                        title: 'Start Measuring Now!',
                        scale: 'Score Scale',
                        captured: '{{count}} Lighthouse audits have been recorded.',
                        captured_zero: 'No Lighthouse audits have been recorded.',
                        captured_one: '1 Lighthouse audit has been recorded.',
                        solutionError: 'This Google Lighthouse audit was skipped due to a runtime error.',
                        teaser: {
                            descriptions: [
                                'Use Lighthouse audits to optimize your website\'s performance, accessibility, and SEO. Get detailed reports and recommendations to continuously improve your site.',
                                'With Google Lighthouse audits, you can see how well your website performs in various metrics. Additionally, it provides tips on best practices you can implement to improve and make your site more user-friendly.',
                            ],
                        },
                        categories: {
                            label: 'Audit Categories',
                            previewLabel: 'Categories',
                            title: 'Let us know which data interests you.',
                            description: 'Click on each category to enable or disable it.',
                            fields: {
                                performance: {
                                    label: 'Performance',
                                    hintTitle: 'What questions can this answer?',
                                    hintQuestion: 'When are the first DOM elements rendered? (First Contentful Paint), When is the first meaningful content rendered? (First Meaningful Paint), How fast is the website? (Speed Index), What is the time to interactivity? (Time To Interactive), First CPU idle (First CPU Idle), Estimated input latency (Estimated Input Latency)',
                                    description: 'This category analyzes how quickly your website loads and when users can interact with its content.',
                                    url: 'https://en.wikipedia.org/wiki/Web_accessibility',
                                    urlLabel: 'Wikipedia Article',
                                },
                                accessibility: {
                                    label: 'Accessibility',
                                    hintTitle: 'What questions can this answer?',
                                    hintQuestion: 'How usable is my website for people with disabilities?',
                                    description: 'This category analyzes how well your website can be used by people with disabilities.',
                                    url: 'https://en.wikipedia.org/wiki/Web_accessibility',
                                    urlLabel: 'Wikipedia Article',
                                },
                                'best-practices': {
                                    label: 'Best Practices',
                                    hintTitle: 'What questions can this answer?',
                                    hintQuestion: 'Are HTTPS and HTTP/2 used? Are resources from secure sources? Are unsafe commands like document.write() executed?',
                                    description: 'This category analyzes whether your website follows best practices in terms of security and modern web development.',
                                },
                                seo: {
                                    label: 'SEO',
                                    fullLabel: 'Search Engine Optimization (SEO)',
                                    hintTitle: 'What questions can this answer?',
                                    hintQuestion: 'How SEO-friendly is my website?',
                                    description: 'This category analyzes how well your website can be crawled by search engines and how well it appears in search results.',
                                    url: 'https://en.wikipedia.org/wiki/Search_engine_optimization',
                                    urlLabel: 'Wikipedia Article',
                                },
                                pwa: {
                                    label: 'PWA',
                                    fullLabel: 'Progressive Web Apps (PWA)',
                                    hintTitle: 'What questions can this answer?',
                                    hintQuestion: 'Does it work without internet access? Are service workers registered? Does it use HTTPS for communication?',
                                    description: 'This category examines the Progressive Web App (PWA) features of your website.',
                                    url: 'https://en.wikipedia.org/wiki/Progressive_web_app',
                                    urlLabel: 'Wikipedia Article',
                                },
                            },
                        },
                        output: {
                            label: 'View and Download Reports',
                            previewLabel: 'Reports',
                            title: 'View, Edit, and Improve Your Website',
                            description: 'Lighthouse reports help you analyze the recorded data. They are available in HTML, JSON, or EXCEL formats.',
                            fields: {
                                html: {
                                    label: 'html',
                                },
                                json: {
                                    label: 'json',
                                },
                                csv: {
                                    label: 'excel',
                                },
                                none: {
                                    label: 'Only determine score.',
                                    report: 'No reports were created.',
                                },
                            },
                            negativeValueInfo: 'Negative values are shown, when there is not data point available for a given project build.',
                            missingBuild: 'Only successful builds are considered.',
                        },
                        reviewer: {
                            label: 'Lighthouse Audits Reviewer',
                            cta: 'Open Lighthouse Audits Reviewer',
                        },
                        content: {
                            intro: {
                                label: 'Audit Website Quality in Seconds—With Google Lighthouse',
                                title: 'Automated Performance, Accessibility & SEO Audits—No Extensions Needed',
                                description: 'With webautomate.app’s Lighthouse Audits solution, you can automatically evaluate your website using Google’s Lighthouse engine. Get precise scores and actionable insights for performance, accessibility, SEO, and best practices—on every deploy, commit, or schedule.',
                            },
                            definition: {
                                label: 'What Is Lighthouse Auditing?',
                                description: 'The Lighthouse Audits solution runs headless Chrome tests using Google\'s open-source auditing tool. You get structured feedback and scores for performance, accessibility, SEO, best practices, and PWA support.',
                            },
                            benefits: {
                                label: 'Why Use Lighthouse Audits?',
                                items: [
                                    'Automatically test across environments (staging, prod, dev)',
                                    'Spot slowdowns, missing meta tags, accessibility violations',
                                    'Benchmark site quality before go-live',
                                    'Run audits on mobile and desktop setups',
                                    'Track scores over time with history and trends',
                                ],
                                note: 'No more manual CLI runs. No extensions. Just results—at scale.',
                            },
                            metrics: {
                                label: 'What’s Measured in Each Audit?',
                                items: [
                                    'Performance: Speed index, first contentful paint, LCP, TBT',
                                    'Accessibility: Contrast, ARIA, keyboard navigation',
                                    'SEO: Meta tags, semantic HTML, links',
                                    'Best Practices: HTTPS, errors, security headers',
                                    'PWA: Offline readiness, installability (optional)',
                                ],
                                note: 'Audits use the latest Lighthouse versions and cloud browsers.',
                            },
                            reporting: {
                                label: 'Insightful Reports You Can Share',
                                items: [
                                    'Lighthouse scores and breakdowns',
                                    'Audit viewport screenshot',
                                    'Downloadable HTML/JSON reports',
                                    'Webautomate-native build summaries and logs',
                                    'Trend data and comparison charts',
                                ],
                                note: 'Great for developers, marketers, SEO experts, and clients.',
                            },
                            useCases: {
                                label: 'Use Cases',
                                items: [
                                    'Pre-Launch QA: Validate performance & SEO before release',
                                    'Continuous Monitoring: Track Lighthouse scores on schedule',
                                    'Accessibility Compliance: Flag usability issues early',
                                    'Marketing Readiness: Ensure Google-friendly pages',
                                    'Client Reports: Deliver quality proofs with real data',
                                ],
                            },
                            getStarted: {
                                label: 'Start Auditing with Confidence',
                                description: 'Lighthouse audits with webautomate.app are automated, scalable, and no-code friendly. Create your first test now and boost your website’s quality.',
                            },
                        },
                    },
                    e2eVisualTests: {
                        label: 'Visual-Tests',
                        summary: 'Check the visual consistency of elements on my website.',
                        title: 'Trust is good, but (Visual) Testing is better!',
                        captured: '{{count}} visual tests captured.',
                        captured_one: '1 visual test captured.',
                        captured_zero: 'No visual tests captured.',
                        solutionError: 'This test was skipped due to a runtime error.',
                        acceptFirstShot: 'Automatically accept the first screenshot capture.',
                        highlightColor: {
                            label: 'Highlight Color',
                            description: 'Specify the color used to mark visual differences.',
                        },
                        clustersSize: {
                            label: 'Minimum Cluster Size',
                            description: 'Clusters are groups of pixels considered as a unit when comparing.',
                        },
                        teaser: {
                            descriptions: [
                                'Compare visual elements across different pages and sessions to ensure consistency and identify issues early. An essential tool for quality assurance and brand consistency.',
                                'Visual tests help you verify the visual consistency of your website and ensure it is displayed correctly across all devices and browsers.',
                            ],
                        },
                        items: {
                            label: 'Screenshot',
                            title: 'Visual Tests',
                            title_one: 'Visual Test',
                            historyLabel: 'Test History',
                            id: 'ID',
                            build: 'Project-Run',
                            loadHistory: 'Load Test History',
                            baseline: 'View Baseline',
                            currentState: 'State',
                            changeState: 'Change State',
                        },
                        preview: 'Show Configuration',
                        noMatch: 'No Matches!',
                        category: 'Category',
                        newItem: 'Add Visual Test',
                        selectItem: 'Add Visual Test',
                        search: 'Filter Visual Tests',
                        selectOption: 'Please select the type',
                        failStopLabel: 'Stop visual test if this step fails.',
                        labels: {
                            allStates: 'All',
                            states: {
                                new: {
                                    label: 'New',
                                    latest: 'This screenshot is new.',
                                },
                                accepted: {
                                    label: 'Accepted',
                                    cta: 'Accept',
                                    latest: 'This screenshot has been accepted.',
                                },
                                rejected: {
                                    label: 'Rejected',
                                    cta: 'Reject',
                                    latest: 'This screenshot has been rejected.',
                                },
                                changed: {
                                    label: 'Changed',
                                    latest: 'This screenshot has changed.',
                                },
                                failed: {
                                    label: 'Failed',
                                    latest: 'An error occurred while capturing the screenshot.',
                                },
                            },
                        },
                        reviewer: {
                            label: 'Visual Tests Reviewer',
                            cta: 'Open Visual Tests Reviewer',
                            exit: 'Exit',
                            selectAll: 'Select All',
                            selectAllNewOrChanged: 'Select All New or Changed',
                            deselectAll: 'Deselect All',
                            accept: {
                                label: 'Accept',
                                hint: 'Accept selected screenshots.',
                            },
                            reject: {
                                label: 'Reject',
                                hint: 'Reject selected screenshots.',
                            },
                            reset: {
                                label: 'Reset',
                                hint: 'Reset selected screenshots to their original state.',
                            },
                            marked: '{{count}} selected',
                            backToOverview: 'Back to Overview',
                            activeFilterNoHits: 'No screenshots match your filter',
                            expiredBuild: 'Screenshots can no longer be edited because there is a newer build.',
                            gotoBuild: 'Go to Project-Run',
                            resetFilter: 'Show All Screenshots',
                            url: {
                                label: 'Screenshots from',
                                all: 'all URLs',
                            },
                            itemView: {
                                label: 'View',
                                previous: 'Previously Accepted',
                                current: 'Current',
                                updated: 'Update Date',
                                openTab: 'Open Image in New Window',
                                copyImageUrl: 'Copy Image URL',
                                differences_one: '1 Difference',
                                differences: '{{count}} Differences',
                                capturedOn: 'Captured On',
                                options: {
                                    label: 'Toolbox',
                                    labelCTA: 'Activate Toolbox',
                                    diffImage: {
                                        label: 'Diff Image',
                                        description: 'Enable this option to use the image with highlighted differences.',
                                    },
                                    padding: {
                                        label: 'Padding',
                                        description: 'Spacing around the border.',
                                    },
                                    border: {
                                        label: 'Frame Differences',
                                        description: 'Enable this option to frame the differences.',
                                    },
                                    borderWeight: {
                                        label: 'Frame Thickness',
                                        description: 'Set the thickness of the frame.',
                                    },
                                    color: {
                                        label: 'Frame Color',
                                        description: 'Set the color of the frame.',
                                    },
                                },
                                sideBySide: {
                                    label: 'Side by Side',
                                },
                                overlappedX: {
                                    label: 'Overlapped (Portrait)',
                                },
                                overlappedY: {
                                    label: 'Overlapped (Landscape)',
                                },
                                overlappedZ: {
                                    label: 'Overlapped (Transparent)',
                                    opacity: 'Transparency',
                                },
                                baseline: {
                                    none: 'There is no baseline',
                                },
                                imageError: 'Error loading image, please reload the page',
                            },
                            notifications: {
                                accepted: '{{count}} screenshots accepted.',
                                rejected: '{{count}} screenshots rejected.',
                                reset: '{{count}} screenshots reset.',
                            },
                            notes: {
                                label: 'Review Notes',
                                intent: {
                                    accepted_one: 'You are about to accept 1 screenshot.',
                                    accepted: 'You are about to accept {{count}} screenshots.',
                                    rejected_one: 'You are about to reject 1 screenshot.',
                                    rejected: 'You are about to reject {{count}} screenshots.',
                                },
                                reviewComments: {
                                    label: 'Review Comments',
                                    description: 'Add any comments or notes you want to share with the team regarding this visual test.',
                                },
                                confidence: {
                                    label: 'Review Confidence',
                                    description: 'Rate your confidence level to support your decision on the visual test result.',
                                },
                            },
                        },
                        itemLabelling: {
                            distinct: 'Visual test screenshots must be uniquely named.',
                        },
                        tolerance: {
                            label: 'Tolerance',
                            strategy: {
                                strict: {
                                    label: 'Strict',
                                    items: [
                                        'All pixel differences, no matter how small, are considered.',
                                    ],
                                },
                                moderate: {
                                    label: 'Moderate',
                                    items: [
                                        'Differences due to blinking cursors are ignored.',
                                        'Differences due to anti-aliasing are ignored.',
                                    ],
                                },
                                open: {
                                    label: 'Open',
                                    items: [
                                        'Differences due to blinking cursors are ignored.',
                                        'Differences due to anti-aliasing are ignored.',
                                        'Minimal brightness differences between pixels are ignored.',
                                    ],
                                },
                            },
                        },
                        groups: {
                            automated: {
                                options: {
                                    ai: {
                                        title: 'Specify Instructions',
                                        description: 'Create screenshots that are automatically evaluated for visual differences using AI review. Visual differences are assessed based on predefined instructions.',
                                        inputNotice: 'Provide clear and specific instructions on how visual differences should be evaluated.',
                                        confidenceLevel: {
                                            label: 'Confidence Threshold',
                                            description: 'The confidence threshold determines how confident AI needs to be to accept or reject visual differences.',
                                            interpretation: {
                                                low: 'The value {{value}} is too low and may lead to false positives (when the condition is marked as met even though it is not).',
                                                medium: 'The value {{value}} is good and recommended if you want to avoid false positives.',
                                                high: 'The value {{value}} is too high and may lead to false negatives (when the condition is marked as not met even though it is).',
                                            },
                                        },
                                        disclaimer: {
                                            label: 'Disclaimer',
                                            description: 'AI reviews are based on machine learning and may not always meet expectations. If you want to ensure visual differences are evaluated correctly, please use manual review.',
                                        },
                                        expectationExamples: [
                                            '/**\nCompare the screenshot with the baseline. Ignore minor differences (antialiasing, brightness). Accept changes under 5% area and outside critical areas (navigation, buttons). Update the baseline only for non-critical changes with less than 3% pixel deviation.\n*/',
                                        ],
                                        label: 'AI-Reviewer',
                                    },
                                },
                                label: 'AI-Powered',
                            },
                            custom: {
                                label: 'Custom Visual Tests',
                                options: {
                                    manual: {
                                        label: 'Manual Review',
                                        description: 'Create screenshots, manually compare them, and update the baselines yourself.',
                                    },
                                },
                            },
                        },
                        urlFilter: {
                            title: 'URL Filter',
                            subTitle: 'Specify which URLs to run the tests on.',
                            testUrlSuccess: 'Tests are run on this URL.',
                            testUrlFailure: 'No tests are run on this URL.',
                        },
                        notifications: {
                            mandatoryField: 'Please provide a label for the test block.',
                            duplicateGroupName: 'The name is already taken.',
                            deleteSuccess: 'Visual test configuration has been deleted.',
                            editSuccess: 'Visual test configuration has been successfully edited.',
                            saveSuccess: 'Visual test configuration has been successfully saved.',
                            copySuccess: 'Visual test configuration has been successfully copied.',
                        },
                        label_one: 'Visual-Test',
                        content: {
                            intro: {
                                label: 'Catch Visual Bugs Before Your Users Do',
                                title: 'Automated Visual Testing to Ensure UI Consistency Across Builds',
                                description: 'With webautomate.app’s Visual Tests solution, you can compare how your website looks over time—detecting even the slightest visual regressions, layout shifts, or component anomalies. Build trust in every release with pixel-level precision.',
                            },
                            definition: {
                                label: 'What Is Visual Testing?',
                                description: 'Visual Testing lets you compare current screenshots of your site with previous builds or baselines. If something looks different—intentionally or not—we’ll catch it.',
                            },
                            benefits: {
                                label: 'Why Use Visual Tests?',
                                items: [
                                    'Detect design inconsistencies after updates or deployments',
                                    'Prevent layout breaks across screen sizes and browsers',
                                    'Review changes with side-by-side and overlay comparisons',
                                    'Validate UI integrity during automated workflows',
                                    'Save time over manual screenshot comparisons',
                                ],
                                note: 'Visual bugs are often subtle. Our comparison engine is not.',
                            },
                            comparison: {
                                label: 'Comparison Strategies That Work for You',
                                items: [
                                    'Baseline Comparison: Detect any difference from the last approved build',
                                    'Build-to-Build Comparison: Compare consecutive runs in the same project',
                                    'URL-to-URL Comparison: Evaluate pages across environments (e.g., staging vs production)',
                                ],
                                note: 'Set pixel tolerance, ignore zones, or target specific screen regions.',
                            },
                            reporting: {
                                label: 'Get Clear, Visual Results',
                                items: [
                                    'Side-by-side comparison views',
                                    'Difference overlays highlighting visual changes',
                                    'Screenshots from both versions',
                                    'Pass/fail status with detailed metadata',
                                    'Ability to accept or reject changes',
                                ],
                                note: 'Perfect for developers, testers, designers, and CI pipelines.',
                            },
                            useCases: {
                                label: 'Use Cases',
                                items: [
                                    'UI Regression Testing: Detect visual defects during continuous deployment',
                                    'Design QA: Ensure pixel-perfect implementation across pages',
                                    'A/B Test Monitoring: Check for unintended layout shifts',
                                    'Visual Monitoring: Catch third-party rendering issues over time',
                                ],
                            },
                            getStarted: {
                                label: 'Test What You See—Visually and Accurately',
                                description: 'Run visual tests on every release, commit, or schedule. No more guessing. Just perfect pixels.',
                            },
                        },
                    },
                    screenshots: {
                        label: 'Screenshot Documentation',
                        summary: 'Document my website through screenshots.',
                        title: 'A picture is worth a thousand words!',
                        basicBlock: 'Screenshot Specification',
                        fileName: 'Screenshot Name',
                        captured: '{{count}} screenshots captured.',
                        captured_zero: 'No screenshots captured.',
                        captured_one: '1 screenshot captured.',
                        reviewer: {
                            label: 'Screenshot Reviewer',
                            cta: 'Open Screenshot Reviewer',
                        },
                        preview: 'Show Configuration',
                        multiItem: 'Multiple Screenshots',
                        failStopLabel: 'Abort screenshot documentation if this step fails.',
                        solutionError: 'This screenshot documentation was skipped due to a runtime error.',
                        teaser: {
                            descriptions: [
                                'Easily capture screenshots of your web pages to document their current state. A useful tool for troubleshooting and documenting design versions.',
                                'You can capture screenshots of the visible part of the page, a specific element anywhere, or even the entire page.',
                            ],
                        },
                        items: {
                            title: 'Screenshots',
                            subTitle: 'You can specify a sequence of steps (actions) to be performed before the screenshot documentation.',
                        },
                        newItem: 'Add Screenshot',
                        fileNameDisplay: {
                            format: '{{label}}.{{extension}}',
                            multipleInfo: 'The position of each screenshot is appended to the filename.',
                            examples: {
                                label: 'For example:',
                                index: '{{label}}_{{index}}.{{extension}}',
                                rest: 'etc...',
                            },
                        },
                        selector: 'Element Selector',
                        target: {
                            title: 'Capture Area',
                            viewport: {
                                label: 'Viewport Screenshot',
                                description: 'Visible Area',
                            },
                            fullPage: {
                                label: 'Fullpage Screenshot',
                                description: 'Entire page',
                            },
                            element: {
                                label: 'DOM Element Screenshot',
                                description: 'DOM element',
                            },
                        },
                        selectorTargets: {
                            first: 'First Match',
                            last: 'Last Match',
                            all: 'All Matches',
                            custom: {
                                1: 'Captures the first element',
                                label: 'Custom',
                                description: 'Enter element positions separated by \',\'',
                                '1,4': 'Captures the first and fourth elements',
                                '2,3_5,7': 'Captures the second, third to fifth, and seventh elements',
                            },
                        },
                        urlFilter: {
                            title: 'URL Filter',
                            subTitle: 'Specify the URLs on which screenshots will be captured.',
                            testUrlSuccess: 'Screenshots are captured on this URL.',
                            testUrlFailure: 'No screenshots are captured on this URL.',
                        },
                        validation: {
                            name: 'The name must be between {{start}} and {{end}} characters long.',
                            duplicateName: 'A screenshot with this name already exists.',
                            selectorTargets: 'Please enter valid values.',
                        },
                        notifications: {
                            mandatoryField: 'Please enter a group name.',
                            duplicateGroupName: 'There is already a screenshot group with this name.',
                            deleteSuccess: 'Screenshot configuration has been deleted.',
                            editSuccess: 'Screenshot configuration has been successfully edited.',
                            saveSuccess: 'Screenshot configuration has been successfully saved.',
                            copySuccess: 'Screenshot configuration has been successfully copied.',
                        },
                        label_one: 'Screenshot-Documentation',
                        content: {
                            intro: {
                                label: 'Capture Web Screenshots Automatically—For QA, Audits & Documentation',
                                title: 'Visually Document Every Page with Precision and Ease',
                                description: 'With webautomate.app’s Screenshot Documentation solution, you can automatically take screenshots of selected parts of your website—whether it\'s the full page, a specific component, or just the visible area. Perfect for visual audits, content reviews, compliance tracking, or team reporting.',
                            },
                            definition: {
                                label: 'What Is Screenshot Documentation?',
                                description: 'The Screenshot Documentation solution helps you visually record how your website appears across different devices and browsers. Choose between AI-powered or custom JavaScript instructions to define what should be captured.',
                            },
                            benefits: {
                                label: 'Why Use Screenshot Documentation?',
                                items: [
                                    'Automatically document your pages with high-resolution screenshots',
                                    'Capture full pages, specific elements, or just the visible viewport',
                                    'Simplify audits, reviews, and client reporting',
                                ],
                                note: 'Whether you’re running QA, managing design systems, or preparing a compliance report, this solution keeps your visuals up to date.',
                            },
                            modes: {
                                label: 'Two Capture Modes',
                                ai: {
                                    label: 'AI-Powered Screenshot Instructions',
                                    description: 'Describe the visuals you want to capture in natural language. Our AI identifies the right elements and captures them.',
                                    example: 'Capture all pricing tables and call-to-action buttons.',
                                },
                                custom: {
                                    label: 'Flexible Screenshot Coverage',
                                    description: 'Define the elements or page sections to capture using custom JS. Ideal for targeting specific DOM elements or applying advanced logic.',
                                    items: [
                                        'Viewport – Capture what the user sees (based on selected device).',
                                        'Full Page – Scroll and capture the entire page height.',
                                        'DOM Element – Capture a specific element using a CSS selector.',
                                    ],
                                },
                            },
                            reporting: {
                                label: 'Rich Visual Reporting',
                                items: [
                                    'Screenshots grouped by URL, browser, and device',
                                    'Timestamped snapshots',
                                    'Load status and page metadata',
                                    'Downloadable reports in your preferred format',
                                ],
                                note: 'Perfect for QA reviews, design audits, and archival.',
                            },
                            useCases: {
                                label: 'Use Cases',
                                items: [
                                    'Visual QA – Verify UI consistency across updates.',
                                    'Client Reports – Provide proof-of-delivery for design or development work.',
                                    'Compliance Documentation – Archive legal or brand-critical pages.',
                                    'Multilingual Testing – Compare screenshots across languages or locales.',
                                ],
                            },
                            getStarted: {
                                label: 'Start Capturing Smarter',
                                description: 'Set up your first screenshot project in minutes. No manual captures. No missed details.',
                            },
                        },
                        groups: {
                            automated: {
                                label: 'AI-Powered Screenshot Capture',
                                options: {
                                    ai: {
                                        label: 'AI-captured Screenshots',
                                        description: 'Leverage AI to describe what should be captured in natural language. Our AI identifies the right elements and captures them.',
                                        disclaimer: {
                                            label: 'Disclaimer',
                                            description: 'AI predictions are based on machine learning and might not always match human judgment. For full precision, use the Manual Mode option of the Custom Screenshot Capture.',
                                        },
                                        expectationExamples: [
                                            'Capture all products on the page marked with "Sale"',
                                            'Take screenshots of all elements on the page with the title "Bestseller"',
                                            'etc...',
                                        ],
                                        maintainInstruction: {
                                            label: 'Maintain Instruction',
                                            description: 'Should the AI instruction be re-evaluated on every run or only during the first execution?',
                                            options: {
                                                reuse: 'Reuse screenshots from first execution',
                                                reEvaluate: 'Re-evaluate instruction on every build',
                                            },
                                        },
                                        confidenceThreshold: {
                                            label: 'Confidence Threshold',
                                            description: 'Minimum confidence (%) the AI must have to capture an element',
                                        },
                                        confidenceLevel: {
                                            label: 'Confidence Level',
                                            description: 'This sets how sure the AI must be before it takes a screenshot of something. Lower values are more forgiving, higher values are more cautious.',
                                            interpretation: {
                                                low: 'Value {{value}} is very low. The AI might capture things that don’t really match your request (false positives). Recommended only if you’re okay with over-capturing.',
                                                medium: 'Value {{value}} is balanced. Good choice if you want accurate results without missing important elements.',
                                                high: 'Value {{value}} is quite strict. The AI might miss some valid elements (false negatives), but what it captures is likely very accurate.',
                                            },
                                        },
                                        cachedSelectors: {
                                            label: 'Cache Selectors',
                                            description: 'Reuse selectors from previous runs to speed up the process.',
                                        },
                                        title: 'Formulate a simple instruction for the AI, describing what you want to screenshot on the current page.',
                                    },
                                },
                            },
                            custom: {
                                label: 'Custom Screenshot Capture',
                                options: {
                                    manual: {
                                        label: 'Manually configured Screenshots',
                                        description: 'Select specific DOM elements, viewport, or full page to capture using selectors or position rules.',
                                    },
                                },
                            },
                        },
                        selectOption: 'Select an option',
                    },
                    dataExtractions: {
                        label: 'Data-Extraction',
                        summary: 'Quickly and accurately extract structured data.',
                        newItem: 'Add Data Extraction',
                        title: 'Extract data from your website.',
                        solutionError: 'This data extraction was skipped due to a runtime error.',
                        teaser: {
                            descriptions: [
                                'Easily extract and organize the data you need from any website. Our tool enables fast and precise data extraction that takes your web analysis to the next level.',
                                'Extract information about network requests, HTML document elements, or custom empirical data.',
                            ],
                        },
                        items: {
                            title: 'Configure Data Extraction',
                            subTitle: 'Group related data extractions together in blocks.',
                            historyLabel: 'Data Extraction History',
                            id: 'THE-ID',
                            build: 'Project-Run',
                            verdict: 'Verdict',
                            loadHistory: 'Load History',
                            showResult: 'Show Result',
                        },
                        failStopLabel: 'Abort data extraction if this step fails.',
                        unavailable: 'This data extraction is no longer available.',
                        itemName: 'Extract',
                        selectItem: 'Extract Data',
                        data: 'Extracted Data',
                        dataEmptyXHRResult: 'No data was captured in this Project-Run because the setting does not match any network URL.',
                        category: 'Category',
                        context: 'Context',
                        preview: 'Show Configuration',
                        search: 'Filter Data Extraction',
                        selectOption: 'Please select the extraction type',
                        captured: '{{count}} Data Points captured',
                        captured_zero: 'No Data captured.',
                        captured_one: '1 Data Point captured',
                        renameGroup: 'Rename Block',
                        deleteGroup: 'Delete Block',
                        multi: {
                            label: 'Multi-Extraction',
                            description: 'This is a multi-extraction as data is extracted separately for all matching XHR requests.',
                        },
                        groups: {
                            label: 'Group',
                            custom: {
                                label: 'Browser',
                                options: {
                                    js: {
                                        label: 'JavaScript Expression',
                                        description: 'Extract data from custom JavaScript code.',
                                        fields: {
                                            reducer: 'Reducer Function',
                                        },
                                    },
                                },
                            },
                            automated: {
                                label: 'AI-Powered Data Extraction',
                                options: {
                                    ai: {
                                        label: 'AI Data-Extraction',
                                        title: 'Formulate a simple instruction for the AI, describing what data you want to extract on the current page.',
                                        description: 'Extract data with the help of an AI assistant. You describe your requirements.',
                                        disclaimer: {
                                            label: 'Disclaimer',
                                            description: 'AI-assisted data extraction is based on machine learning and may not always meet expectations. If you need specific data that cannot be provided by AI, please use alternative solutions.',
                                        },
                                        confidenceLevel: {
                                            label: 'Confidence Level',
                                            description: 'Sets how confident the AI needs to be before extracting any data.',
                                            interpretation: {
                                                low: 'A value of {{value}} means the AI is more lenient and will extract data even if it’s not very confident, possibly leading to false positives.',
                                                medium: 'A value of {{value}} is well-balanced and the AI will extract data when it’s reasonably confident, balancing between false positives and false negatives.',
                                                high: 'A value of {{value}} means the AI will be more strict and will only extract data when it’s very confident, potentially leading to false negatives.',
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        notifications: {
                            mandatoryField: 'Please enter a name',
                            duplicateGroupName: 'A block with this name already exists',
                            deleteSuccess: 'Data extraction configuration has been deleted',
                            editSuccess: 'Data extraction configuration has been successfully edited',
                            saveSuccess: 'Data extraction configuration has been successfully saved',
                            copySuccess: 'Data extraction configuration has been successfully copied',
                        },
                        label_one: 'Data-Extraction',
                        reviewer: {
                            label: 'Data-Extraction Reviewer',
                            cta: 'Open Data-Extraction Reviewer',
                        },
                        viewExtractedData: 'View Extracted Data',
                        extractedData: '{{label}}: Extracted Data',
                        content: {
                            intro: {
                                label: 'Extract Data from Any Website—Fast, Accurate, and Code-Free',
                                title: 'Automated Data Collection for Smarter Web Analysis',
                                description: 'Unlock the full potential of your web research. With webautomate.app’s Data-Extraction solution, you can extract structured information from any website using powerful AI or custom logic—no coding required.',
                            },
                            definition: {
                                label: 'What Is Data-Extraction?',
                                description: 'The Data-Extraction solution lets you automatically collect content such as product details, prices, links, articles, or metadata from one or many web pages. Whether you’re tracking competitors, monitoring listings, or compiling research, we make it scalable and effortless.',
                            },
                            benefits: {
                                label: 'Why Use Data-Extraction?',
                                items: [
                                    'Scrape content across multiple URLs in minutes',
                                    'Extract data using natural language or JavaScript',
                                    'Works with dynamic content and JavaScript-heavy websites',
                                    'Automate recurring tasks with schedulers and notifications',
                                ],
                                note: 'From SEO data to product pricing, if it’s on the page—you can extract it.',
                            },
                            methods: {
                                label: 'Two Ways to Extract Data',
                                ai: {
                                    label: 'AI-Powered Extraction',
                                    description: 'Just describe what you want. Our AI will scan the page and extract relevant data using context awareness and layout analysis.',
                                    example: 'Extract all product names, prices, and ratings on the page.',
                                },
                                custom: {
                                    label: 'JavaScript-Based Extraction',
                                    description: 'For advanced use cases, write custom JS to extract specific elements, attributes, or structured data exactly how you need it.',
                                },
                            },
                            reporting: {
                                label: 'Real-Time Build Reports',
                                items: [
                                    'Number of data points attempted vs. successfully extracted',
                                    'Page load information and screenshots',
                                    'Downloadable results in CSV, JSON, or Excel',
                                    'Error tracking and retry mechanisms',
                                ],
                                note: 'Perfect for developers, analysts, marketers, and QA teams.',
                            },
                            useCases: {
                                label: 'Use Cases',
                                items: [
                                    'Market Research: Track competitor pricing and promotions',
                                    'SEO Monitoring: Extract meta titles, headings, and links',
                                    'Product Feeds: Build or enrich e-commerce databases',
                                    'Content Aggregation: Collect articles, job listings, or reviews',
                                    'QA Automation: Monitor front-end data correctness across builds',
                                ],
                            },
                            getStarted: {
                                label: 'Start Extracting Smarter',
                                description: 'Set up your first data extraction project in minutes. No scraper setup. No manual parsing. Just results.',
                            },
                        },
                        noMatch: 'No matches found',
                    },
                    screenVideos: {
                        label: 'Video Documentation',
                        summary: 'Document your website through video recordings.',
                        title: 'Create a video recording of the website.',
                        captured: '{{count}} videos captured.',
                        captured_zero: 'No videos recorded.',
                        captured_one: '1 video captured.',
                        openVideo: 'Open Video',
                        solutionError: 'This video recording was skipped due to a runtime error.',
                        failStopLabel: 'Abort video recording if this step fails.',
                        teaser: {
                            descriptions: [
                                'Document the state of your website with video recordings. A powerful tool for analyzing user behavior and troubleshooting issues.',
                                'The video recording starts immediately when the page loads. Add a sequence of steps to be executed during the video recording. A few seconds after the last action, the recording stops and the video is saved for you.',
                            ],
                        },
                        label_one: 'Video-Documentation',
                        reviewer: {
                            label: 'Video Reviewer',
                            cta: 'Open Video Reviewer',
                        },
                        content: {
                            intro: {
                                label: 'Record How Your Website Behaves—In Real Time',
                                title: 'Automated Video Captures for UX Reviews, QA, and Performance Proof',
                                description: 'With webautomate.app’s Video Documentation solution, you can automatically record how your web pages load and behave in real time—across browsers, devices, and user conditions. Ideal for testing animations, verifying behavior, or creating visual proofs for developers and stakeholders.',
                            },
                            definition: {
                                label: 'What Is Video Documentation?',
                                description: 'The Video Documentation solution allows you to record a full session of your website loading or interacting, exactly as a user would experience it. This includes content rendering, animations, loading indicators, dynamic elements, and scroll behavior.',
                            },
                            benefits: {
                                label: 'Why Use Video Documentation?',
                                items: [
                                    'Capture full page load behavior visually, in real time',
                                    'Record animations, transitions, and lazy-loaded content',
                                    'Share UX recordings with clients or developers',
                                    'Spot layout shifts, flickers, or rendering bugs',
                                    'Add timed steps or pauses to simulate interaction flows',
                                ],
                                note: 'Video says more than screenshots—especially when timing and behavior matter.',
                            },
                            capabilities: {
                                label: 'Record Simple or Dynamic Pages',
                                description: 'You can create straightforward recordings, or configure conditional steps to simulate user flows, such as:',
                                items: [
                                    'Scroll to a section',
                                    'Wait for a modal to appear',
                                    'Pause for animation',
                                    'Trigger dynamic content',
                                ],
                                note: 'These steps are executed before and during recording to capture meaningful moments.',
                            },
                            reporting: {
                                label: 'See Everything, Frame by Frame',
                                items: [
                                    'Video playback of the session',
                                    'Metadata (browser, device, timestamp, load method)',
                                    'Page load behavior and performance visibility',
                                    'Downloadable video files for sharing and archiving',
                                ],
                            },
                            useCases: {
                                label: 'Use Cases',
                                items: [
                                    'UX QA: Catch layout shifts, flickers, and animation issues',
                                    'Client Demos: Share what users really experience',
                                    'Performance Analysis: Visualize delays and render timing',
                                    'Automated UX Monitoring: See how things change over time',
                                ],
                            },
                            getStarted: {
                                label: 'Start Capturing Experiences',
                                description: 'Set up your first video project in just minutes. Real browsers. Real results.',
                            },
                        },
                    },
                },
            },
            summary: {
                label: 'Completion',
                previewLabel: 'Completion',
                description: '',
                fields: {
                    notifications: {
                        label: 'Notifications',
                        description: 'Receive notifications via email, SMS, or webhooks after the Project-Run of this project.',
                    },
                    subscription: {
                        label: 'Project Subscription',
                        description: 'This project will run with the following subscription:',
                    },
                    scheduler: {
                        label: 'Automated Builds',
                        activate: 'I want the project to be regularly automated.',
                        deactivated: 'Project is not automated.',
                        validity: 'Validity',
                        buildTime: 'Project-Run Times',
                        startDate: 'Start Date',
                        endDate: 'End Date',
                        configurator: {
                            types: {
                                label: 'Schedule',
                                options: {
                                    random: {
                                        cta: 'Manual setting',
                                        label: 'Generate Random Time',
                                        description: 'Randomly {{frequency}} times per {{frequencyQualifier}}',
                                        question: 'When do you receive the most traffic on your website?',
                                        questionInfo: 'We generate a random time each time outside of the specified period to avoid disruptions and to not overload your server.',
                                        frequency: {
                                            label: 'Frequency',
                                            qualifierLabel: 'Per',
                                            qualifierOptions: {
                                                daily: 'Day',
                                                weekly: 'Week',
                                                monthly: 'Month',
                                            },
                                        },
                                    },
                                    cron: {
                                        description: '{{cronPrompt}}',
                                        question: 'Set when you would like the project to run regularly.',
                                        cta: 'AI-Based setting',
                                        nextBuild: 'next {{pageSize}} Project-Run times',
                                        nextBuildMore: 'Show more',
                                        cronDescriptionInputNotice: 'Describe when and how often the project should run',
                                        cronDescriptionPlaceholder: 'Execute the project every monday, wednesday and friday at 4 am.',
                                        validationLabel: 'Validate description',
                                    },
                                },
                            },
                            notifications: {
                                label: 'You have activated automated builds, please complete the configuration of the Project-Run times.',
                            },
                        },
                    },
                    logs: {
                        label: 'Event Log',
                        description: 'Determine which events should be logged.',
                    },
                },
                notifications: {
                    updateSuccess: 'Project "{{name}}" has been successfully updated.',
                    updateSuccessWithSubscriptionInfo: 'Project "{{name}}" has been successfully updated and is now running with the subscription "{{}}".',
                    saveSuccess: 'Project "{{name}}" has been successfully created.',
                },
            },
        },
        subscription: {
            current: 'Current subscription',
            changed: {
                success: 'Project subscription updated successfully',
                failure: 'Project subscription update failed',
            },
        },
        stateNotification: 'Project needs to be active to start with scheduler or manually.',
        sync: 'Project Sync',
    },
    projects: {
        label: 'Projects',
        pageTitle: 'My Projects',
        list: 'Projects',
        summary: {
            label: 'You have {{count}} active projects',
            label_one: 'You have one active project',
        },
        goto: 'Goto my projects',
    },
    readMore: 'Read More',
    register: {
        label: 'Create Account',
        pageTitle: 'Create Account',
        accountQuestion: 'Do you already have an account?',
        registerHere: 'Create account here',
        teaser: 'Create an account with your email address.',
        perInvite: {
            label: 'Accept Invitation',
            teaser: 'Welcome to {{app}}! Please create your password to activate your account and join the {{organisation}} organization.',
        },
        declaration: 'By registering, I agree to the {{privacyPolicy}} and {{termsAndConditions}} of {{app}}.',
        validation: {
            error: 'Registration failed, please try again.',
            methodError: 'Signing up with {{method}} is currently not possible, please try another method.',
            registrationError: 'Registration is not possible, please try to log in if the email address is already registered.',
            accountCreateSuccess: 'Congratulations, you have successfully logged into your new {{app}} account.',
        },
    },
    requestDemo: 'Request Demo',
    reset: 'Reset',
    resetFilters: 'Reset All Filters',
    resetJS: 'Reset Code',
    resources: {
        label: 'Resources',
        groups: {
            documentation: {
                label: 'Documentation',
            },
            legal: {
                label: 'Legal',
            },
            shortcuts: {
                label: 'Shortcuts',
            },
        },
    },
    result: 'Result',
    roundUpInfo: 'The value has been rounded up to the nearest {{step}}.',
    ruleIndex: 'Rule {{index}}: {{type}}',
    running: 'Running',
    save: 'Save',
    saveAndContinue: 'Save & Continue',
    saveAndContinueToProject: 'Save & to Project',
    saved: 'Saved!',
    scroll: {
        top: 'Upwards',
        bottom: 'Downwards',
    },
    seconds: 'Seconds',
    security: {
        label: 'Security',
    },
    selectAll: 'Select All',
    selectedCount: '{{numSelected}} selected',
    selection: {
        label: 'Selection',
        options: {
            selectAll: 'Select all',
            deselectAll: 'Deselect all',
        },
    },
    selector: {
        label: 'Selector',
        invalid: 'Please provide a valid selector.',
    },
    settings: {
        title: 'Settings',
        language: 'My Preferred Language',
        appearance: 'Appearance',
        direction: 'Direction',
        accent: 'Accent color',
        fullScreen: {
            enter: 'Enter fullscreen mode',
            exit: 'Exit fullscreen mode',
        },
        fontSize: {
            label: 'Font size',
        },
    },
    sessionId: 'Session',
    solution: 'Solution',
    workflowSteps: {
        startAddingSteps: 'Define workflow steps',
        addStep: 'New Step',
        delay: 'Delay between steps',
        addStepBefore: 'New step before',
        addStepAfter: 'New step after',
        dynamicLabel: 'Determined at runtime!',
        state: {
            label: 'Status',
            description: 'This step is: {{state}}',
            active: 'Active',
            inactive: 'Inactive',
            notifications: {
                active: 'The step is active and will be executed during the build.',
                inactive: 'The step is not active and will not be executed during the next build.',
            },
            cta: {
                activate: 'Activate step',
                deactivate: 'Deactivate step',
            },
        },
        options: {
            action: {
                label: 'Simulate user interaction',
                ctaLabel: 'Add actions or modify settings',
            },
            condition: {
                label: 'Conditional steps',
                ctaLabel: 'Add a conditional sequence of steps',
                subStepLabel_one: 'Based on this condition, 1 more step will be executed.',
                subStepLabel: 'Based on this condition, {{count}} more steps will be executed.',
                saveNotification: 'You must add at least one step before the block can be saved.',
                labels: {
                    conditionConfig: 'Condition',
                    conditionSettings: 'Settings',
                    conditionSteps: 'Steps',
                    abortion: 'Abort conditional block if a step fails.',
                    ignore: 'Ignore errors in this step.',
                },
                notifications: {
                    willBeIgnored: 'Errors in this step will be ignored (see block settings).',
                },
            },
            loop: {
                label: 'Steps in loops',
                ctaLabel: 'Add loop with a sequence of steps',
                subStepLabel_one: '1 step will be executed in this loop.',
                subStepLabel: '{{count}} steps will be executed in this loop.',
                saveNotification: 'You must add at least one step before the block can be saved.',
                labels: {
                    loopConfig: 'Configure loop',
                    loopSettings: 'Settings',
                    loopSteps: 'Steps',
                    abortion: 'Abort loop block if a step fails.',
                    ignore: 'Ignore errors in this step.',
                },
                notifications: {
                    indexReference: {
                        description: 'A reference to the current element is created and made available in any JS code through the following parameter:',
                        reload: 'Warning! References may be lost if the current page is reloaded or if navigating away from the current URL.',
                    },
                    willBeIgnored: 'Errors in this step will be ignored (see block settings).',
                },
                breakCondition: {
                    label: 'Break condition',
                    description: {
                        ctaPre: 'In each iteration, all steps will be executed. Click on ',
                        ctaPost: ' to break the loop based on the return value of the function.',
                    },
                },
            },
        },
        label: 'Workflow steps',
        solutionsOnly: 'Show only {{solution}} steps',
        view: 'View Workflow Step',
        subItems: {
            label: 'Sub-Steps',
            label_one: 'Sub-Step',
            view: 'View Sub-Steps',
            view_one: 'View Sub-Step',
        },
        showInactiveItems: 'Show inactive',
        step: 'Step',
    },
    solutions: {
        label: 'Solutions',
    },
    solutionsLabel: 'Key Features',
    solutionsUsage: {
        automatizeIt: 'Automate these processes with {{app}}!',
        executionAnytimeAnyBrowserAnyPlace: 'You can run the automated processes anytime, with different browsers - {{browsers}}.',
        examples: {
            auditingAndApprovingComments: {
                title: 'User Comments',
                label: 'Imagine automating these steps to review and approve user comments on your website:',
                items: [
                    'Open laptop and launch browser.',
                    'Enter your website admin panel address (e.g. https://www.yourwebsite.com/admin).',
                    'Click the login link.',
                    'Click \'Login\'.',
                    'Enter username.',
                    'Enter password.',
                    'Wait until login is completed and page is reloaded.',
                    'Navigate to the comment moderation area.',
                    'View new user comments and review the content.',
                    'Click \'Approve\' for acceptable comments.',
                    'Click \'Reject\' or \'Mark for Review\' for problematic comments.',
                    'Write responses to approved comments and click \'Send\' if required.',
                    'Repeat the last 3 steps for other comments.',
                    'Click \'Logout\'.',
                ],
                timeSpent: 'Up to {{durationInMins}} minutes may be needed for this task multiple times a day.',
            },
            managingSocialMedia: {
                title: 'Social Media',
                label: 'Imagine automating these steps on your social media accounts:',
                items: [
                    'Open laptop and launch browser.',
                    'Enter the social media platform address (e.g. https://www.facebook.com).',
                    'Click the login link.',
                    'Click \'Login\'.',
                    'Enter username.',
                    'Enter password.',
                    'Wait until login is completed and page is reloaded.',
                    'Navigate to the post creation area.',
                    'Write a new post and upload a picture/video.',
                    'Click \'Post\'.',
                    'Click on notifications to check comments and likes.',
                    'Write responses to comments and click \'Send\'.',
                    'Repeat the last 3 steps for other posts and platforms.',
                    'Click \'Logout\'.',
                ],
                timeSpent: 'Up to {{durationInMins}} minutes may be needed for this task multiple times a day.',
            },
            managingCustomerSupport: {
                title: 'Customer Support',
                label: 'Imagine automating these steps in your customer support:',
                items: [
                    'Open laptop and launch browser.',
                    'Enter your support system address (e.g. https://www.yoursupport.com).',
                    'Click the login link.',
                    'Click \'Login\'.',
                    'Enter username.',
                    'Enter password.',
                    'Wait until login is completed and page is reloaded.',
                    'Navigate to the ticket management area.',
                    'View new support tickets and read details.',
                    'Write responses to tickets and click \'Send\'.',
                    'Sort and assign tickets by priority.',
                    'Repeat the last 3 steps for other tickets.',
                    'Click \'Logout\'.',
                ],
                timeSpent: 'Up to {{durationInMins}} minutes may be needed for this task multiple times a day.',
            },
        },
        label: 'What people use {{app}} for',
        options: {
            label: 'Examples',
            items: {
                linkValidation: {
                    label: 'Link Validation',
                    description: 'Automatic verification and validation of all links on a website to ensure they are functioning correctly.',
                },
                endToEndVisualTesting: {
                    label: 'E2E Visual Testing',
                    description: 'Comparison of visual elements across different pages and sessions to ensure consistency and detect issues early.',
                },
                screenshotCapturing: {
                    label: 'Screenshot Documentation',
                    description: 'Automatically capturing screenshots of web pages for documentation, troubleshooting, or design versioning.',
                },
                dataExtraction: {
                    label: 'Data Extraction',
                    description: 'Automating the extraction of required data from web pages for analysis or reporting.',
                },
                screenVideoRecording: {
                    label: 'Video Documentation',
                    description: 'Recording interactions on a website to better understand user behavior or diagnose issues.',
                },
                automatedFormFilling: {
                    label: 'Automated Form Filling',
                    description: 'Automatically filling out web forms for testing purposes or data entry.',
                },
                contentMonitoring: {
                    label: 'Content Monitoring',
                    description: 'Regularly checking web pages for content changes or updates.',
                },
                seoAudits: {
                    label: 'SEO Audits',
                    description: 'Conducting automated SEO audits to optimize website visibility and search engine ranking.',
                },
                performanceMonitoring: {
                    label: 'Performance Monitoring',
                    description: 'Continuous monitoring of website performance metrics.',
                },
                userBehaviorSimulation: {
                    label: 'User Behavior Simulation',
                    description: 'Simulating various user interactions to test the responsiveness and functionality of the website.',
                },
                accessibilityTesting: {
                    label: 'Accessibility Testing',
                    description: 'Ensuring that websites are accessible to all users, including those with disabilities.',
                },
                securityTesting: {
                    label: 'Security Testing',
                    description: 'Checking for vulnerabilities and potential security threats on websites.',
                },
                competitorAnalysis: {
                    label: 'Competitor Analysis',
                    description: 'Automatically collecting data from competitor websites for market analysis.',
                },
                eCommerceTesting: {
                    label: 'E-Commerce Testing',
                    description: 'Testing e-commerce platforms for functionality, user experience, and transaction processes.',
                },
                apiTesting: {
                    label: 'API Testing',
                    description: 'Automating the testing of web APIs for functionality, reliability, performance, and security.',
                },
                responsiveDesignTesting: {
                    label: 'Responsive Design Testing',
                    description: 'Ensuring that websites display correctly on different devices and screen sizes.',
                },
                contentScraping: {
                    label: 'Content Scraping',
                    description: 'Extracting specific content from web pages for research purposes or data compilation.',
                },
                automatedReporting: {
                    label: 'Automated Reporting',
                    description: 'Generating regular reports on website metrics, performance, or other important data points.',
                },
            },
        },
    },
    state: 'State',
    status: 'Status',
    subPage: 'Subpage',
    submit: {
        label: 'Submit',
    },
    subscribe: 'Subscribe Now',
    transactions: {
        label: 'Transactions',
        pageTitle: 'My Transactions',
        userItems: 'My Transactions',
        createdOn: 'Created',
        license: 'License',
        validity: 'Validity',
        status: 'Status',
        automationCreditsConsumption: {
            label: 'Automation-Credits Consumption',
            fields: {
                billableAction: 'Billable Action',
                billingCycle: 'Billing Cycle',
                context: 'Context',
                quantity: 'Quantity',
                reference: 'Reference',
                credits: 'Credits',
                timestamp: 'Timestamp',
                billableActions: 'Billable Actions',
                consumption: 'Consumption',
            },
            empty: {
                label: 'No Automation-Credits consumption found',
            },
        },
        automationCreditsTopUp: {
            label: 'Automation-Credits Top Up',
            fields: {
                billingCycle: 'Billing Cycle',
                credits: 'Credits',
                date: 'Date',
                expirationDate: 'Expiration Date',
                reference: 'Reference',
            },
            empty: {
                label: 'No Automation-Credits Top Up found',
            },
        },
        missingItem: 'Are you looking for an item?',
        billingCycle: {
            label: 'Billing Cycle',
            current: 'Current Billing Cycle',
            cycle: 'Billing Cycle',
            selection: 'Select Billing Cycle',
        },
        orderHistory: {
            label: 'Billing',
            fields: {
                id: 'ID',
                created: 'Created',
                status: 'Status',
                amount: 'Amount',
                invoice: 'Invoice',
                email: 'Email',
                context: 'Action',
                billingAction: {
                    CHECKOUT: 'Checkout',
                    AUTOMATION_CREDITS_AUTO_RENEW: 'Auto Reload',
                },
            },
            viewDetails: 'View Billing',
        },
        payment: {
            status: {
                label: 'Payment Status',
                options: {
                    draft: {
                        label: 'Draft',
                        description: 'The invoice has been created but not sent to the customer yet.',
                    },
                    open: {
                        label: 'Open',
                        description: 'The invoice has been sent to the customer but the payment has not been received yet.',
                    },
                    paid: {
                        label: 'Paid',
                        description: 'The invoice has been paid.',
                    },
                    uncollectible: {
                        label: 'Uncollectible',
                        description: 'The payment for the invoice has not been received and is considered uncollectible.',
                    },
                    void: {
                        label: 'Void',
                        description: 'The invoice has been voided.',
                    },
                },
            },
            trigger: {
                label: 'Pay now',
            },
        },
        creditEvents: {
            contexts: {
                event: 'Event',
                options: {
                    DEMO: 'Free Credit for Demo',
                    PROJECT_ASSISTANT: 'Project Setup',
                    PROJECT_BUILD: 'Project Run',
                    CHECKOUT: 'Shop Checkout',
                    PROJECT_STORAGE: 'Storage Space',
                    EXPIRATION: 'Expiration',
                    MONTHLY_SUBSCRIPTION_SETTLEMENT: 'Monthly Subscription Settlement',
                    SUBSCRIPTION: 'Subscription Purchase',
                },
            },
            balanceBefore: 'Balance Before',
            balanceAfter: 'Balance After',
        },
        subscriptions: {
            label: 'Subscriptions',
            active: 'This package is active for you',
            change: {
                label: 'Change subscription',
                title: 'Attention required regarding your subscription',
            },
            cycle: 'Subscription cycle',
            inActive: 'This package is not included in your subscription',
            isCancelled: 'This subscription is already canceled and will be deactivated on {{endDate}}, you can reactivate it again in the subscription management.',
            items: {
                notFound: 'The subscription with the invoice number {{invoiceNumber}} could not be found.',
                rest: 'Remaining',
                restTooltip: 'Still {{valueWithUnit}} ({{percentageValue}}) left',
                usedUp: 'Consumed',
                usedUpTooltip: 'Already {{valueWithUnit}} ({{percentageValue}}) used',
                halfUsedUp: 'You have used up 50% so far.',
                states: {
                    active: 'Active',
                    inActive: 'Inactive',
                },
            },
            missingPackageLabel: 'Function cannot be activated.',
            multipleActiveLabel: 'Specify subscription',
            noActiveLabel: 'No active subscriptions found.',
            openInvoice: 'View invoice details',
            paidOn: 'Paid on {{paymentDate}}',
            packageRecommendations: {
                label: 'Recommended packages',
                notifications: {
                    success: 'Package has been added to your cart.',
                },
            },
            selectLabel: 'Select',
            states: {
                active: 'Active',
                inActive: 'Inactive',
            },
            tabs: {
                details: 'Overview',
                usage: 'Quotas / Consumption',
            },
            validSince: 'Valid since',
            validTill: 'Valid until',
            viewItem: 'View subscription',
            userItems: 'My subscriptions',
            activeLabel: 'You have {{count}} active subscriptions',
            usageReset: {
                label: 'Next monthly reset',
                remainingDays: 'In {{days}} days',
            },
            pageTitle: 'Transactions & Subscriptions',
            showOnlyActiveItems: 'Show only active items',
            isPastDue: 'Your payment for this subscription is past due and could not be processed. Please update your payment details in your customer portal to continue the service.',
            hasEnteredOverage: {
                label: 'Heads up! Some of your resources have exceeded the plan limit. No worries—you can continue with overage charges or explore an upgrade!',
            },
        },
        automationCredits: {
            label: 'Automation-Credits',
            autoReload: 'Auto Reload',
            autoReloadDescription: 'If enabled, your account will automatically reload when your balance falls below the threshold you set.',
            autoReloadActive: 'Auto Reload Active',
            autoReloadActiveDescription: 'If your Automation-Credits balance falls below {{threshold}}, your account will reloaded with {{amount}} Automation-Credits.',
            autoReloadInActive: 'Auto Reload Inactive',
            autoReloadInActiveDescription: 'Enable auto-reload to activate overages for your project builds and avoid interruptions.',
            productItems: 'Product Items to be automatically purchased.',
            selectItem: 'Select Product Item',
            threshold: 'Threshold',
            balanceAndExpiryInfo: 'Your Automation-Credits balance is {{balance}} and will expire on {{expirationDate}}.',
        },
    },
    summary: 'Summary',
    templates: {
        label: 'Templates',
        pageTitle: 'My Templates',
        actions: {
            label: 'Actions',
            options: {
                view: 'View',
                download: 'Download',
                delete: 'Delete',
                edit: 'Edit',
            },
        },
        files: {
            label: 'Files',
            upload: {
                label: 'Upload File',
            },
            edit: {
                label: 'Edit Uploaded File',
                description: 'You are editing the file: "{{name}}".',
            },
            delete: {
                label: 'Delete Uploaded File',
                description: 'Are you sure you want to delete the file: "{{name}}"? This action cannot be undone.',
            },
            empty: 'You have not uploaded any files yet.',
            info: 'You can upload files up to {{fileSize}}.',
            description: 'Choose a file to upload.',
            multipleInfo: 'Please note: If the form on your page does not support uploading multiple files, only the first selected file will be uploaded. For forms that accept multiple files, all selected files will be uploaded.',
            name: 'Filename',
            hint: 'Hint',
            uploadedAt: 'Uploaded on',
            uploadedBy: 'Uploaded by',
            mimeType: 'MIME-Type',
            size: 'Size',
            dropOrSelect: {
                preview: 'Preview',
                label: 'Select or Drop',
                description: 'Drag and drop files here or click here to load files from your filesystem.',
            },
            notification: {
                fileNotFound: 'The file with ID: "{{id}}" is no longer available.',
                uploaded: 'The file {{name}} was successfully saved.',
                deleted: 'The file {{name}} was successfully deleted.',
                updated: 'The file {{name}} was successfully updated.',
                fileTooBig: 'The file {{name}} is {{fileSize}} in size. The maximum file size is {{maxFileSize}}.',
                fileInvalid: 'The file {{name}} is invalid!',
            },
            tags: {
                label: 'File Tags',
                description: 'To organize and manage your files, add any tags separated by commas ",".',
            },
        },
    },
    termsAndConditions: {
        label: 'Terms and Conditions',
        pageTitle: 'Terms and Conditions',
    },
    testLink: 'Test URL',
    themes: {
        light: 'Light',
        dark: 'Dark',
    },
    timestamp: 'Timestamp',
    unchangeable: 'The value "{{reference}}" can no longer be changed.',
    upgradePlan: 'Upgrade Now',
    url: 'URLs',
    url_one: 'URL',
    urlCapture: {
        title: 'Test here whether URLs would meet the condition.',
        label: 'URL Condition',
        helperText: 'Only matching URLs will be considered.',
        criteria: 'Matching criteria',
        criteriaAll: 'All rules must match',
        criteriaAny: 'At least one rule matches',
        moreLabel: 'Add rule',
        description: 'You can specify whether certain strings appear in the URLs "Substring", whether the URLs have an exact structure "Exact", or match a regular expression "Regex".',
        testUrlSuccess: 'The URL matches.',
        testUrlFailure: 'The URL does not match.',
        testUrlFailureGlobal: 'This URL was globally excluded.',
        gotoGlobalSetting: 'Go to global filter setting',
        valueRequired: 'Please enter a value',
        options: {
            exact: {
                label: 'Exact',
                validationError: 'Please enter a valid URL string',
            },
            substring: {
                label: 'Substring',
                validationError: 'Please enter a valid URL string',
            },
            regex: {
                label: 'Regex',
                validationError: 'Please enter a valid regex',
            },
        },
        externals: {
            url: 'URL',
            authorization: {
                label: 'Authorization',
                options: {
                    username: 'Username',
                    password: 'Password',
                },
            },
            headers: {
                label: 'Headers',
            },
            duplicateErrorMessage: 'This URL has already been recorded',
        },
    },
    urlRequest: {
        context: {
            currentPage: 'Entry Page',
            xhr: 'Web resource',
        },
        url: {
            label: 'URL',
            notification: {
                empty: 'Please enter a valid URL.',
            },
        },
        method: {
            label: 'Method',
        },
    },
    urls: 'URLs',
    usageRestrictionPolicy: {
        label: 'Usage Limitation Policy',
        pageTitle: 'Usage Limitation Policy',
    },
    used: 'Used',
    users: {
        label: 'Collaborators',
        profile: {
            label: 'My Profile',
            overview: 'Overview',
            changePassword: 'Change Password',
            languageChange: {
                label: 'Update Preferred Language?',
                description: 'You have changed the UI language. Would you like to also set this as your preferred language for receiving notifications and other communications?',
            },
        },
    },
    validate: 'Validate',
    validateCode: {
        label: 'Code validieren',
        isRequiredForSave: 'Code muss valid sein, bevor er gespeichert werden kann.',
    },
    value: 'Value',
    verifyUser: {
        label: 'Confirm Account',
        pageTitle: 'Confirm Account',
        loginRequired: 'You must log in first to confirm your account.',
    },
    void: '',
    webAutomate: {
        description: 'Configure, plan, and automate activities on your website with {{app}}',
    },
    webAutomateUtils: 'Helper Functions',
    yes: 'Yes',
    yourOptions: 'Your Options',
    codeVerification: {
        sendCode: 'Send code',
        enterCode: 'Enter the code you received',
        noCodeReceived: 'Didn’t receive a code?',
    },
    clickHere: 'Click here',
    accountSecurity: {
        label: 'Account security',
    },
    validateAiText: {
        label: 'Validate AI Text',
        isRequiredForSave: 'The AI has to understand the text, before you can save it.',
    },
    overages: {
        label: 'Overages',
        description: 'Overages occur when you exceed the resources included in your subscription.',
    },
    ai: {
        confidence: {
            label: 'AI-Score',
            description: 'The AI-Agent is {{value}}% confident about the result.',
        },
        step: {
            label: 'AI-Powered Step',
            description: 'This is a step that is powered by AI.',
        },
    },
    view: {
        error: 'View Error',
        aiDialog: 'AI Dialog',
    },
    caching: {
        label: 'Caching',
        inUse: 'Cache used',
        notInUse: 'Cache not used',
        view: 'View Cache',
        clear: 'Clear Cache',
        cachedTime: 'Cached at',
        notifications: {
            cacheCleared: 'Cache cleared successfully.',
        },
    },
    charCount: 'Character count',
    byteCount: 'File size',
    changes: 'Changes',
    changes_one: 'Change',
};
