export const rawProducts = {
    object: 'list',
    data: [
        {
            id: 'prod_RqVoe3nCEgPEDL',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592367,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Automation Credits - 200k',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592367,
            url: null,
        },
        {
            id: 'prod_RqVoqhefFX5CNR',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592367,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Automation Credits - 75k',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592367,
            url: null,
        },
        {
            id: 'prod_RqVoqyOGwGor6T',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592366,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Automation Credits - 12k',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592366,
            url: null,
        },
        {
            id: 'prod_RqVoQNdzE5F6KY',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592366,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Automation Credits - 1k',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592366,
            url: null,
        },
        {
            id: 'prod_RqVn29570ld0Pt',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592322,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Collaborators - 25',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592322,
            url: null,
        },
        {
            id: 'prod_RqVnDZuXWqJEZx',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592321,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Collaborators - 10',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592321,
            url: null,
        },
        {
            id: 'prod_RqVnfVKxMrpQe2',
            object: 'product',
            active: true,
            attributes: [],
            created: 1740592321,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Collaborators - 5',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: 1740592321,
            url: null,
        },
        {
            id: 'prod_RqVn2fOV7MgqRp',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: null,
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Collaborators - 2',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
        {
            id: 'prod_RqVlmnR4fuPltV',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: 'Subscription for Premium - Page-License',
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Premium - Page-License',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
        {
            id: 'prod_RqVl3hTvbrWIJt',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: 'Subscription for Premium - Link mode',
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Premium - Link mode',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
        {
            id: 'prod_RqVlxNEZQGhXTf',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: 'Subscription for Advanced - Page-License',
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Advanced - Page-License',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
        {
            id: 'prod_RqVl7olWf51lgc',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: 'Subscription for Advanced - Link mode',
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Advanced - Link mode',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
        {
            id: 'prod_RqVlagQSHKmR1s',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: 'Subscription for Starter - Page-License',
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Starter - Page-License',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
        {
            id: 'prod_RqVlYHxjRVD69C',
            object: 'product',
            active: true,
            attributes: [],
            created: **********,
            default_price: null,
            description: 'Subscription for Starter - Link mode',
            images: [],
            livemode: false,
            marketing_features: [],
            metadata: {},
            name: 'Starter - Link mode',
            package_dimensions: null,
            shippable: null,
            statement_descriptor: null,
            tax_code: null,
            type: 'service',
            unit_label: null,
            updated: **********,
            url: null,
        },
    ],
    has_more: false,
    url: '/v1/products',
};

export const rawPrices = {
    object: 'list',
    data: [
        {
            id: 'price_1QwompQmpu7UZVla4iCEeDou',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592367,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVoe3nCEgPEDL',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 75000,
            unit_amount_decimal: '75000',
        },
        {
            id: 'price_1QwompQmpu7UZVlaKLarkTQa',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592367,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVoqhefFX5CNR',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 30000,
            unit_amount_decimal: '30000',
        },
        {
            id: 'price_1QwomoQmpu7UZVlaFYGKwJSS',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592366,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVoqyOGwGor6T',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 5000,
            unit_amount_decimal: '5000',
        },
        {
            id: 'price_1QwomoQmpu7UZVlaLE4b70Nq',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592366,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVoQNdzE5F6KY',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 2500,
            unit_amount_decimal: '2500',
        },
        {
            id: 'price_1Qwom6Qmpu7UZVlaUAXU1QPc',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592322,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVn29570ld0Pt',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 50000,
            unit_amount_decimal: '50000',
        },
        {
            id: 'price_1Qwom6Qmpu7UZVlaFVehrGCg',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592322,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVnDZuXWqJEZx',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 30000,
            unit_amount_decimal: '30000',
        },
        {
            id: 'price_1Qwom5Qmpu7UZVlaqvBAGoj2',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592321,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVnfVKxMrpQe2',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 20000,
            unit_amount_decimal: '20000',
        },
        {
            id: 'price_1Qwom5Qmpu7UZVlayXZ81IAv',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: 1740592321,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVn2fOV7MgqRp',
            recurring: null,
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'one_time',
            unit_amount: 10000,
            unit_amount_decimal: '10000',
        },
        {
            id: 'price_1QwokCQmpu7UZVla9cogTAYw',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlmnR4fuPltV',
            recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 689000,
            unit_amount_decimal: '689000',
        },
        {
            id: 'price_1QwokCQmpu7UZVlamDMg53Kc',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlmnR4fuPltV',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 6,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 359000,
            unit_amount_decimal: '359000',
        },
        {
            id: 'price_1QwokBQmpu7UZVla43wJWs5a',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlmnR4fuPltV',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 3,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 199000,
            unit_amount_decimal: '199000',
        },
        {
            id: 'price_1QwokBQmpu7UZVlaWQoDZr9E',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlmnR4fuPltV',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 79900,
            unit_amount_decimal: '79900',
        },
        {
            id: 'price_1QwokBQmpu7UZVla04dBOnju',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl3hTvbrWIJt',
            recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 599000,
            unit_amount_decimal: '599000',
        },
        {
            id: 'price_1QwokAQmpu7UZVlasRrUS0Y1',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl3hTvbrWIJt',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 6,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 319000,
            unit_amount_decimal: '319000',
        },
        {
            id: 'price_1QwokAQmpu7UZVla9d7HV2iq',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl3hTvbrWIJt',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 3,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 179000,
            unit_amount_decimal: '179000',
        },
        {
            id: 'price_1QwokAQmpu7UZVlaGoufUMzz',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl3hTvbrWIJt',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 69000,
            unit_amount_decimal: '69000',
        },
        {
            id: 'price_1Qwok9Qmpu7UZVla8oiR4klN',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlxNEZQGhXTf',
            recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 189000,
            unit_amount_decimal: '189000',
        },
        {
            id: 'price_1Qwok9Qmpu7UZVla4t6znPLz',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlxNEZQGhXTf',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 6,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 97900,
            unit_amount_decimal: '97900',
        },
        {
            id: 'price_1Qwok9Qmpu7UZVlaU6rcLxhw',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlxNEZQGhXTf',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 3,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 52900,
            unit_amount_decimal: '52900',
        },
        {
            id: 'price_1Qwok8Qmpu7UZVlaQW0Rs2pd',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlxNEZQGhXTf',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 19900,
            unit_amount_decimal: '19900',
        },
        {
            id: 'price_1Qwok8Qmpu7UZVlaqiklnh7X',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl7olWf51lgc',
            recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 159000,
            unit_amount_decimal: '159000',
        },
        {
            id: 'price_1Qwok8Qmpu7UZVlalLVPIv1E',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl7olWf51lgc',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 6,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 84900,
            unit_amount_decimal: '84900',
        },
        {
            id: 'price_1Qwok7Qmpu7UZVlawM7yL4XW',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl7olWf51lgc',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 3,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 45900,
            unit_amount_decimal: '45900',
        },
        {
            id: 'price_1Qwok7Qmpu7UZVlaKSmdLBPV',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVl7olWf51lgc',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 16900,
            unit_amount_decimal: '16900',
        },
        {
            id: 'price_1Qwok6Qmpu7UZVla7s19ldFz',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlagQSHKmR1s',
            recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 31900,
            unit_amount_decimal: '31900',
        },
        {
            id: 'price_1Qwok6Qmpu7UZVlalg3vv7Jk',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlagQSHKmR1s',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 6,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 17900,
            unit_amount_decimal: '17900',
        },
        {
            id: 'price_1Qwok6Qmpu7UZVlaSmgjtOG9',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlagQSHKmR1s',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 3,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 8900,
            unit_amount_decimal: '8900',
        },
        {
            id: 'price_1Qwok6Qmpu7UZVlaBgPyFRBP',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlagQSHKmR1s',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 2900,
            unit_amount_decimal: '2900',
        },
        {
            id: 'price_1Qwok5Qmpu7UZVlazhctfyj8',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlYHxjRVD69C',
            recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 27900,
            unit_amount_decimal: '27900',
        },
        {
            id: 'price_1Qwok5Qmpu7UZVlaBkoGTeH8',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlYHxjRVD69C',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 6,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 15900,
            unit_amount_decimal: '15900',
        },
        {
            id: 'price_1Qwok5Qmpu7UZVlaaGTBvurM',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlYHxjRVD69C',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 3,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 7900,
            unit_amount_decimal: '7900',
        },
        {
            id: 'price_1Qwok4Qmpu7UZVlaQi8yVnmG',
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'chf',
            custom_unit_amount: null,
            livemode: false,
            lookup_key: null,
            metadata: {},
            nickname: null,
            product: 'prod_RqVlYHxjRVD69C',
            recurring: {
                aggregate_usage: null,
                interval: 'month',
                interval_count: 1,
                meter: null,
                trial_period_days: null,
                usage_type: 'licensed',
            },
            tax_behavior: 'unspecified',
            tiers_mode: null,
            transform_quantity: null,
            type: 'recurring',
            unit_amount: 2900,
            unit_amount_decimal: '2900',
        },
    ],
    has_more: false,
    url: '/v1/prices',
};
