import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PubSub from 'pubsub-js';
import {Link as RouterLink} from 'react-router-dom';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {globalConfig} from '../../config/setup';
import useLocales from '../../hooks/useLocales';

const {appName} = solutions.screenshots;

const Screenshots = () => {
    const {translate} = useLocales();
    const solution = translate(`project.wizardSteps.solutionsConfigs.groups.${appName}.label`);

    return (
        <Stack spacing={6}>
            <Stack spacing={2}>
                <Typography variant="h4">
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.intro.label')}
                </Typography>
                <Typography>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.intro.description')}
                </Typography>
            </Stack>

            <Divider sx={{my: 3}} />

            <Stack spacing={2}>
                <Typography variant="h4">
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.definition.label')}
                </Typography>
                <Typography>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.definition.description')}
                </Typography>
            </Stack>

            <Divider sx={{my: 3}} />

            <Stack spacing={3} sx={{pl: 6}}>
                <Typography variant="h4" sx={{ml: -6}}>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.benefits.label')}
                </Typography>
                {
                    translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.benefits.items', {
                        returnObjects: true,
                    }).map((item) => (
                        <Stack key={item} direction="row" sx={{ml: (theme) => `-${theme.spacing(4)} !important`}}>
                            <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                            <Typography>{item}</Typography>
                        </Stack>
                    ))
                }
            </Stack>
            <Alert variant="filled" severity="info">
                {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.benefits.note')}
            </Alert>

            <Divider sx={{my: 3}} />

            <Stack spacing={3} sx={{pl: 6}}>
                <Typography variant="h4" sx={{ml: -6}}>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.label')}
                </Typography>

                <Stack direction="row" sx={{ml: (theme) => `-${theme.spacing(4)} !important`}}>
                    <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                    <Typography variant="subtitle1">
                        {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.ai.label')}
                    </Typography>
                </Stack>
                <Typography>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.ai.description')}
                </Typography>
                <Box component="i" sx={{fontSize: '1rem', color: 'text.secondary', pl: 4}}>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.ai.example')}
                </Box>

                <Stack direction="row" sx={{ml: (theme) => `-${theme.spacing(4)} !important`}}>
                    <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                    <Typography variant="subtitle1">
                        {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.custom.label')}
                    </Typography>
                </Stack>
                <Typography>
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.custom.description')}
                </Typography>
                {
                    translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.modes.custom.items', {
                        returnObjects: true,
                    }).map((item) => (
                        <Stack key={item} direction="row">
                            <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                            <Typography>{item}</Typography>
                        </Stack>
                    ))
                }
            </Stack>
            <Divider sx={{my: 3}} />

            <Stack spacing={3}>
                <Typography variant="h4">
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.reporting.label')}
                </Typography>
                {
                    translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.reporting.items', {
                        returnObjects: true,
                    }).map((item) => (
                        <Stack key={item} direction="row" sx={{ml: (theme) => `${theme.spacing(3)} !important`}}>
                            <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                            <Typography>{item}</Typography>
                        </Stack>
                    ))
                }
            </Stack>
            <Alert variant="filled" severity="info">
                {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.reporting.note')}
            </Alert>

            <Divider sx={{my: 3}} />

            <Stack spacing={3}>
                <Typography variant="h4">
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.useCases.label')}
                </Typography>
                {
                    translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.useCases.items', {
                        returnObjects: true,
                    }).map((item) => (
                        <Stack key={item} direction="row" sx={{ml: (theme) => `${theme.spacing(3)} !important`}}>
                            <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                            <Typography>{item}</Typography>
                        </Stack>
                    ))
                }
            </Stack>

            <Divider sx={{my: 3}} />

            <Typography variant="h6">
                {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.getStarted.description')}
            </Typography>

            <Stack
                direction={{xs: 'column', md: 'row'}}
                justifyContent="flex-end"
                alignItems="center"
                spacing={3}
            >
                <Button
                    variant="outlined"
                    startIcon={<Icon icon="eva:message-square-outline" width={40} height={40}/>}
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'aiAssistant',
                            initialPrompt: translate('assistant.prompts.exploreSpecificFeature.description', {
                                app: globalConfig.domain,
                                solution,
                            }),
                        });
                    }}
                >
                    {translate('assistant.prompts.exploreSpecificFeature.label', {
                        app: globalConfig.domain,
                        solution,
                    })}
                </Button>

                <Button
                    variant="contained"
                    size="large"
                    to={PATH_PAGE.projectWizard}
                    sx={{color: 'common.white'}}
                    component={RouterLink}
                    endIcon={<Icon icon="eva:round-arrow-right-alt" width={40} height={40}/>}
                >
                    {translate('project.wizardSteps.solutionsConfigs.groups.screenshots.content.getStarted.label')}
                </Button>
            </Stack>
        </Stack>
    );
};

export default Screenshots;
