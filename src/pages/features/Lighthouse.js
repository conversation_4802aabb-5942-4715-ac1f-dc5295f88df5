import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PubSub from 'pubsub-js';
import {Link as RouterLink} from 'react-router-dom';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {globalConfig} from '../../config/setup';
import useLocales from '../../hooks/useLocales';

const {appName} = solutions.lighthouse;

const Lighthouse = () => {
    const {translate} = useLocales();
    const solution = translate(`project.wizardSteps.solutionsConfigs.groups.${appName}.label`);

    const t = (key, options) =>
        translate(`project.wizardSteps.solutionsConfigs.groups.${appName}.content.${key}`, options);

    const renderList = (path) =>
        t(path, { returnObjects: true })?.map((item) => (
            <Stack
                key={item}
                direction="row"
                sx={{ ml: (theme) => `-${theme.spacing(4)} !important` }}
            >
                <Icon icon="eva:chevron-right-outline" width={40} height={40} />
                <Typography>{item}</Typography>
            </Stack>
        ));

    return (
        <Stack spacing={6}>
            {/* Intro */}
            <Stack spacing={2}>
                <Typography variant="h4">{t('intro.label')}</Typography>
                <Typography>{t('intro.description')}</Typography>
            </Stack>

            <Divider sx={{ my: 3 }} />

            {/* Definition */}
            <Stack spacing={2}>
                <Typography variant="h4">{t('definition.label')}</Typography>
                <Typography>{t('definition.description')}</Typography>
            </Stack>

            <Divider sx={{ my: 3 }} />

            {/* Benefits */}
            <Stack spacing={3} sx={{ pl: 6 }}>
                <Typography variant="h4" sx={{ ml: -6 }}>{t('benefits.label')}</Typography>
                {renderList('benefits.items')}
            </Stack>
            <Alert variant="filled" severity="info">
                {t('benefits.note')}
            </Alert>

            <Divider sx={{ my: 3 }} />

            {/* Metrics */}
            <Stack spacing={3} sx={{ pl: 6 }}>
                <Typography variant="h4" sx={{ ml: -6 }}>{t('metrics.label')}</Typography>
                {renderList('metrics.items')}
            </Stack>
            <Alert variant="filled" severity="info">
                {t('metrics.note')}
            </Alert>

            <Divider sx={{ my: 3 }} />

            {/* Reporting */}
            <Stack spacing={3} sx={{ pl: 6 }}>
                <Typography variant="h4" sx={{ ml: -6 }}>{t('reporting.label')}</Typography>
                {renderList('reporting.items')}
            </Stack>
            <Alert variant="filled" severity="info">
                {t('reporting.note')}
            </Alert>

            <Divider sx={{ my: 3 }} />

            {/* Use Cases */}
            <Stack spacing={3} sx={{ pl: 6 }}>
                <Typography variant="h4" sx={{ ml: -6 }}>{t('useCases.label')}</Typography>
                {renderList('useCases.items')}
            </Stack>

            <Divider sx={{ my: 3 }} />

            {/* CTA */}
            <Typography variant="h6">{t('getStarted.description')}</Typography>

            <Stack
                direction={{ xs: 'column', md: 'row' }}
                justifyContent="flex-end"
                alignItems="center"
                spacing={3}
            >
                <Button
                    variant="outlined"
                    startIcon={<Icon icon="eva:message-square-outline" width={40} height={40} />}
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'aiAssistant',
                            initialPrompt: translate('assistant.prompts.exploreSpecificFeature.description', {
                                app: globalConfig.domain,
                                solution,
                            }),
                        });
                    }}
                >
                    {translate('assistant.prompts.exploreSpecificFeature.label', {
                        app: globalConfig.domain,
                        solution,
                    })}
                </Button>

                <Button
                    variant="contained"
                    size="large"
                    to={PATH_PAGE.projectWizard}
                    sx={{ color: 'common.white' }}
                    component={RouterLink}
                    endIcon={<Icon icon="eva:round-arrow-right-alt" width={40} height={40} />}
                >
                    {t('getStarted.label')}
                </Button>
            </Stack>
        </Stack>
    );
};

export default Lighthouse;
