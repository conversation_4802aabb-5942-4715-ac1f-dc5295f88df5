import {Link as RouterLink} from 'react-router-dom';
import {Box, Button, Container, Typography} from '@material-ui/core';
import {PATH_HOME} from '@w7-3/webeagle-resources/dist/config/paths';
import {SeverErrorIllustration} from '../assets';
import PageWrapper from '../custom/components/PageWrapper';
import useLocales from '../hooks/useLocales';

const Page500 = () => {
    const {translate} = useLocales();

    return (
        <PageWrapper
            title={translate('directives.notifications.genericError')}
            sx={{my: 10, mx: 0, p: 0, minWidth: 360}}
            maxWidth="xl">
            <Container>
                <Box sx={{
                    maxWidth: 768,
                    margin: 'auto',
                    textAlign: 'center',
                }}>
                    <Typography variant="h3" paragraph>
                        {translate('directives.notifications.genericError')}
                    </Typography>
                    <SeverErrorIllustration sx={{
                        height: 260,
                        my: {
                            xs: 5,
                            sm: 10,
                        },
                    }}/>
                    <Button to={PATH_HOME} size="large" variant="contained" component={RouterLink}>
                        {translate('page404.backHome')}
                    </Button>
                </Box>
            </Container>
        </PageWrapper>
    );
};

export default Page500;
