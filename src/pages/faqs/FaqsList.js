import {useState} from 'react';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import {Accordion, AccordionDetails, AccordionSummary, Button, Divider, Stack, Typography} from '@material-ui/core';
import chevronRightOutline from '@iconify/icons-eva/chevron-right-outline';
import {MotionInView, varFadeIn} from '../../components/animate';
import useLocales from '../../hooks/useLocales';
import SearchStyle from '../../custom/components/utils/SearchStyle';
import {globalConfig} from '../../config/setup';

const highlightPattern = (text, pattern) => {
    const regex = new RegExp(pattern, 'gi');

    return text.replace(regex, (match) => `<mark>${match}</mark>`);
}

export default function FaqsList() {
    const [term, setTerm] = useState('');
    const {translate} = useLocales();
    const i18nData = translate('faqs', {
        returnObjects: true,
        app: globalConfig.domain,
    });
    const filteredItems = Object.values(i18nData.items).filter(({label, description}) => {
        return label.toLowerCase().includes(term.toLowerCase()) ||
            description.toLowerCase().includes(term.toLowerCase());
    });

    return (
        <MotionInView variants={varFadeIn}>
            <SearchStyle
                placeholder={i18nData.title}
                value={term}
                onChange={(e) => {
                    setTerm(e.target.value);
                }}
            />
            <Divider sx={{my: 5}} />
            {
                filteredItems.length === 0 && (
                    <Stack spacing={1} direction="row" alignItems="center">
                        <Typography
                            component="p"
                            variant="subtitle1"
                            sx={{color: 'text.secondary'}}>
                            {i18nData.noResults}
                        </Typography>
                        <Button
                            variant="outlined"
                            startIcon={<Icon icon={chevronRightOutline} width={40} height={40}/>}
                            onClick={() => {
                                PubSub.publish('SHOW.DIALOG', {
                                    type: 'aiAssistant',
                                });
                            }}
                            sx={{
                                ml: 'auto',
                            }}
                        >
                            {translate('assistant.ask')}
                        </Button>
                    </Stack>
                )
            }
            {
                filteredItems.map(({label, description}, index) => {
                    return (
                        <Accordion
                            defaultExpanded={index === 0}
                            key={`${label}-${index}`}
                            sx={{
                                mark: {
                                    backgroundColor: 'transparent',
                                    color: 'primary.main',
                                    fontWeight: 600,
                                },
                                my: 3,
                                borderRadius: 1,
                                '&::before': {
                                    display: 'none'
                                },
                                borderBottom: (theme) => `solid 1px ${theme.palette.grey[500_8]}`,
                            }}
                        >
                            <AccordionSummary
                                expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                                <Typography
                                    component="p"
                                    variant="subtitle1"
                                    sx={{color: 'text.secondary'}}
                                    dangerouslySetInnerHTML={{__html: highlightPattern(label, term)}}
                                />
                            </AccordionSummary>
                            <AccordionDetails>
                                <Typography
                                    component="p"
                                    variant="body2"
                                    dangerouslySetInnerHTML={{__html: highlightPattern(description, term)}}
                                />
                            </AccordionDetails>
                        </Accordion>
                    );
                })
            }
        </MotionInView>
    );
}
