import {useEffect, useState} from 'react';
import PubSub from 'pubsub-js';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import PageWrapper from '../../custom/components/PageWrapper';
import useLocales from '../../hooks/useLocales';
import Markdown from '../../components/Markdown';

export const logGlobalError = (error) => {
    PubSub.publish(
        'LOG.EVENT',
        {
            uiData: {
                type: INFO_CODE_LEVELS.ERROR,
                message: 'Error rendering UsageRestrictionPolicy',
                data: {
                    error: {
                        message: error.message,
                        stack: error.stack,
                    },
                }
            },
        });
};

const UsageRestrictionPolicy = () => {
    const {translate, currentLang} = useLocales();
    const [md, setMd] = useState('');

    useEffect(() => {
        import(`@w7-3/webeagle-resources/dist/webautomate/usage-restriction-policy/i18n/${currentLang.value}.md`)
            .then(res => {
                fetch(res.default)
                    .then(res => res.text())
                    .then(res => setMd(res))
                    .catch(logGlobalError);
            })
            .catch(logGlobalError);
    }, [currentLang.value]);

    return (
        <PageWrapper
            title={translate('usageRestrictionPolicy.pageTitle')}
            sx={{my: 10, mx: 0, p: 0, minWidth: 360}}
            maxWidth="xl">
            <Markdown>{md}</Markdown>
        </PageWrapper>
    );
};

export default UsageRestrictionPolicy;

