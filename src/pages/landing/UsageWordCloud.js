import ReactWordCloud from 'react-d3-cloud';
import {Box, Button, Stack, Typography} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {useTheme} from '@material-ui/core/styles';
import {Icon} from '@iconify/react';
import useLocales from '../../hooks/useLocales';
import {globalConfig} from '../../config/setup';

const UsageWordCloud = () => {
    const {translate} = useLocales();
    const theme = useTheme();
    const data = [
        ['linkValidation', 100],
        ['endToEndVisualTesting', 100],
        ['screenshotCapturing', 100],
        ['dataExtraction', 100],
        ['screenVideoRecording', 100],
        ['automatedFormFilling', 70],
        ['contentMonitoring', 100],
        ['seoAudits', 90],
        ['performanceMonitoring', 80],
        ['userBehaviorSimulation', 100],
        ['accessibilityTesting', 80],
        ['securityTesting', 100],
        ['competitorAnalysis', 100],
        ['eCommerceTesting', 100],
        ['apiTesting', 100],
        ['responsiveDesignTesting', 100],
        ['contentScraping', 100],
        ['automatedReporting', 100],
    ].map(([item, value]) => ({
        text: translate(`solutionsUsage.options.items.${item}.label`),
        info: translate(`solutionsUsage.options.items.${item}.description`),
        value,
    }));

    return (
        <Box sx={{
            '* svg text': {
                cursor: 'pointer',
            },
        }}>
            <ReactWordCloud
                data={data}
                fontSize={15}
                padding={6}
                font="Space Mono, monospace"
                spiral="rectangular"
                fontWeight="bold"
                fill={theme.palette.text.primary}
                onWordClick={(_, word) => {
                    PubSub.publish('SHOW.NOTIFICATION', {
                        message: (
                            <Stack spacing={3}>
                                <Typography>
                                    {word.info}
                                </Typography>
                                <Stack direction="row" justifyContent="flex-end" alignItems="center">
                                    <Button
                                        size="large"
                                        variant="outlined"
                                        startIcon={<Icon icon="eva:message-square-outline" width={40} height={40}/>}
                                        onClick={() => {
                                            PubSub.publish('HIDE.NOTIFICATIONS.ALL');
                                            PubSub.publish('SHOW.DIALOG', {
                                                type: 'aiAssistant',
                                                initialPrompt: translate('assistant.prompts.learnAboutFeature.description', {
                                                    app: globalConfig.domain,
                                                    problem: word.info,
                                                }),
                                            });
                                        }}
                                    >
                                        {translate('assistant.prompts.learnAboutFeature.label', {
                                            app: globalConfig.domain,
                                            problem: word.info,
                                        })}
                                    </Button>
                                </Stack>
                            </Stack>
                        ),
                        variant: 'info',
                        anchorOrigin: {
                            vertical: 'bottom',
                            horizontal: 'right',
                        },
                    });
                }}
            />
        </Box>
    );
};

export default UsageWordCloud;
