import {useEffect, useState} from 'react';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import PubSub from 'pubsub-js';
import PageWrapper from '../../custom/components/PageWrapper';
import useLocales from '../../hooks/useLocales';
import Markdown from '../../components/Markdown';

export const logGlobalError = (error) => {
    PubSub.publish(
        'LOG.EVENT',
        {
            uiData: {
                type: INFO_CODE_LEVELS.ERROR,
                message: 'Error rendering TermsAndConditions',
                data: {
                    error: {
                        message: error.message,
                        stack: error.stack,
                    },
                }
            },
        });
};

const TermsAndConditions = () => {
    const {translate, currentLang} = useLocales();
    const [md, setMd] = useState('');

    useEffect(() => {
        import(`@w7-3/webeagle-resources/dist/webautomate/terms-and-conditions/i18n/${currentLang.value}.md`)
            .then(res => {
                fetch(res.default)
                    .then(res => res.text())
                    .then(res => setMd(res))
                    .catch(logGlobalError);
            })
            .catch(logGlobalError);
    }, [currentLang.value]);

    return (
        <PageWrapper
            title={translate('termsAndConditions.pageTitle')}
            sx={{my: 10, mx: 0, p: 0, minWidth: 360}}
            maxWidth="xl">
            <Markdown>{md}</Markdown>
        </PageWrapper>
    );
};

export default TermsAndConditions;

