# Terms of Service for WEBAUTOMATE.APP

Last Updated: March 31, 2024
______________________________

Welcome to WEBAUTOMATE.APP, a service provided by F1 Software GmbH ("Company", "we", "us", or "our"), accessible via https://webautomate.app ("Website"). These Terms of Service ("Terms") govern your access to and use of our services, features, and functionalities (collectively, the "Services"). By accessing or using the Services, you agree to these Terms. If you do not agree to these Terms, you may not use the Services.

## Acceptance of Terms
1. **Agreement to Terms**: By using the Services, you agree to be bound by these Terms, [our Privacy Policy](/privacy-policy) and [our Usage Restriction Policy](/usage-restriction-policy). If you are using the Services on behalf of an organization, you represent that you have the authority to bind that organization to these Terms.
2. **Modification of Terms**: We reserve the right to modify these Terms at any time. Your continued use of the Services after such modification constitutes your acceptance of the modified Terms.

## Service Description

WEBAUTOMATE.APP offers a suite of tools designed for automating actions on websites, including link validation, performance audits, web tests, visual consistency checks, data extraction, and more. Each project is a configuration of settings and actions executed either manually or via scheduler. Our services include but are not limited to:

* Link Validator: Checks the integrity of links on your website.
* Google Lighthouse Audits: Perform performance, accessibility, best practices, SEO, and PWA audits.
* Web Tests: Ensures smooth operation through comprehensive testing.
* E2E Visual Tests: Assures visual consistency across pages and sessions.
* Screenshot Capture: Documents the current state of web pages.
* Data Extraction: Extracts and organizes data from web pages.
* Screen Video Recording: Records interactions for UX testing and troubleshooting.

All these tools come together in project form. Each project within WEBAUTOMATE.APP is a configuration of page and browser settings, actions, steps, automated execution settings, and report settings that can be executed manually or via scheduler.

## Account Page Terms


Before you can use any of our services, you must create an account. When creating an account, you must provide accurate and complete information and keep this information up to date. If you suspect any unauthorized use of your account or a security breach, you must immediately [contact our Support Team](/contact-us).

1. You are responsible for maintaining the security of your account and password and for ensuring that any of your collaborators do the same. The Company cannot and will not be liable for any loss or damage from your failure to comply with this security obligation.
2. You may not sublicense, rent, lease, or otherwise transfer your rights to the Service.
3. You may not use the Services for any purpose outlined in our [Usage Restriction Policy](/usage-restriction-policy), and you may not permit any of your collaborators to do so.
4. You are responsible for all activity, especially running automated process on any website, that occurs under your account, including the activities of any collaborators in your account. This includes any activity by anyone who: (a) have access to your login credentials; or (b) have access to the logins of collaborators under your account.
5. You must be a human. Accounts registered by "bots" or other automated methods are not permitted.

## Privacy Terms

Please see our [Privacy Policy](/privacy-policy) for information about how we collect, use, and disclose information about users of the Site and the Services. By using the Site and the Services, you consent to our collection, use, and disclosure of information as set forth in our Privacy Policy, as we may update that policy from time to time.

## Changes to Terms or Services

We may modify these Terms and our Services at any time. We will provide notice of any significant changes and your continued use of the Services after such notice constitutes your agreement to the changes.

## Use of the Services

Please read our [Usage Restriction Policy](/usage-restriction-policy) to get information on how you are allowed to use the services.


1. Subject to your continued compliance with these Terms, you are granted a limited, non-exclusive, revocable license to access and use the Services for your internal business purposes.
2. You may not misuse the Services, including but not limited to, engaging in illegal activities, infringing upon intellectual property, or disrupting the Services or other users.

## Content and Data

When you upload data, images, code, or any kind of content ("Content") through WEBAUTOMATE.APP, you are solely responsible for it and any consequences that may arise from it. This includes any form of content, regardless of whether you are the original creator. It is your duty to ensure all Content on your account, even if uploaded by others, adheres to the following standards:

1. Intellectual Property Compliance: Ensure all uploaded content does not infringe on any third-party intellectual property rights, holding necessary permissions for its use and distribution.
2. Lawful Use: Guarantee that your content's utilization by WEBAUTOMATE.APP aligns with legal standards and does not violate any intellectual property laws.
3. Safety and Security: Maintain content free from malicious codes, including viruses and malware, safeguarding the platform's integrity.
4. Privacy Respect: Avoid including sensitive personal data or any information breaching privacy laws, ensuring user and data protection.
5. Ethical Content: Refrain from uploading spam, manipulative SEO content, or any material promoting unethical practices.
6. Non-offensive Material: Ensure content is respectful, devoid of violence, privacy infringement, or any offensive material.
7. Proper Representation: Accurately represent and categorize content, especially computer code, avoiding misleading names or associations.
8. Grant of Rights: Acknowledge that by uploading content, you grant WEBAUTOMATE.APP a royalty-free, non-exclusive right to use, reproduce, and modify the content for service provision, including creating public summaries or aggregations without revealing specific identities or content details.

We reserve the right to manage content that breaches these terms or poses a risk to the platform or its users, with measures including content removal without prior notice.

## Package TransactionPage, PaymentPage, Renewal, and Cancellation

1. After creating an account, you have the option to request a demo package to explore our services or purchase a paid package. The demo package may be provided free of charge or, in certain instances, a nominal fee of 1CHF might be applied, at our sole discretion. The demo package is available for use for a maximum period of 30 days. Upon completion of the demo period, you will be prompted to purchase one of our paid package.
2. If you are on a demo package, we treat you just like customers who pay for our Services.
3. Fees for our Services are detailed in your selected package and must be paid in advance.
4. Subscriptions automatically renew unless you cancel before the renewal date.
5. Sometimes we change the pricing structure for our products. When we do that, we tend to exempt existing customers from those changes. However, we may choose to change the prices for existing customers. If we do so, we will give at least 30 days notice and will notify you via the email address on record. We may also post a notice about changes on our websites or the affected Services themselves.
6. If you cancel a subscription before the end of your current period, your cancellation will take effect immediately, and you will not be charged again. We do not automatically prorate unused contingents in the last billing cycle.
7. Cancellations can be made via your account settings; no refunds are provided for unused portions of the Services.

## Conditions for maintaining your account

To ensure continued use of our services, it is required that all users have an active subscription or at least an active demo package. The following terms and conditions describe the requirements and procedures for maintaining account health and the consequences for failure to comply:

1. Active TransactionPage Required: Users must have a valid subscription at all times to access the full functionality of our Service. This includes both paid subscriptions and demo packages.
2. Exception to Additional Licenses: Purchasing “Employee Additional Licenses,” which increases the number of employees who can access an account, does not affect the requirement for an active subscription. An account will be closed if there is no active service subscription, regardless of the number of additional licenses purchased.
3. Grace period in case of inactivity: If an account no longer has an active subscription or demo package, it automatically enters a grace period of 30 days. During this time, no automations will be executed, but access to the account and data will be retained.
4. AccountPage closure: If the account remains without an active subscription even after the grace period has expired, it will be closed. This means that access to the account and all related services will be completely revoked.
5. Data deletion: After account closure, all data and content stored in the account will be retained for an additional 30 days. After this period, the data will be permanently deleted. Restoring this data is no longer possible after deletion.
6. AccountPage Restoration: To request account restoration before permanent deletion, the user must purchase a new subscription or reactivate the account within the 30-day retention period. The ability to restore is available only during this period and is subject to review and approval by [our support team](/contact-us).

By complying with these Terms, you will ensure that your account and information remain secure and accessible. If you have any questions about your subscription status or account recovery, please [our support team](/contact-us).

## Uptime, Modifications to the Service, Security, and Privacy

1. We make a promise to our customers to support our Services until the end of the Internet. That means when it comes to security, privacy, and customer support, we will continue to maintain any legacy Services. Sometimes it becomes technically impossible to continue a feature, or we redesign a part of our Services because we think it could be better, or we decide to close new signups of a product. We reserve the right at any time to modify or discontinue, temporarily or permanently, any part of our Services with or without notice.
2. Your use of the Services is at your sole risk. We provide these Services on an "as is" and "as available" basis. We do not offer service-level agreements for all of our Services.
3. We reserve the right – at any time, and without notice or liability to you – to modify the Site or the Services, or any part of them, temporarily or permanently. We may modify the Services for a variety of reasons, including, without limitation, for the purpose of providing new features, implementing new protocols, maintaining compatibility with emerging standards, or complying with regulatory requirements.
4. We take many measures to protect and secure your data through backups, redundancies, and encryption. We enforce encryption for data transmission from the public Internet. There are some edge cases where we may send your data through our network unencrypted. Please [contact our Support Team](/contact-us) for how to report a security incident or threat.
5. When you use our Services, you entrust us with your data. We take that trust to heart. You agree that WEBAUTOMATE.APP may process your data as described in our [Privacy Policy](/privacy-policy) and for no other purpose. We as humans can access your data for the following reasons:
    - **To help you with support requests you make.** We’ll ask for express consent before accessing your account.
    - **On the rare occasions when an error occurs that stops an automated process partway through.** We get automated alerts when such errors occur. When we can fix the issue and restart automated processing without looking at any personal data, we do. In rare cases, we have to look at a minimum amount of personal data to fix the issue. In these rare cases, we aim to fix the root cause to prevent the errors from recurring.
    - **To safeguard WEBAUTOMATE.APP.** We’ll look at logs and metadata as part of our work to ensure the security of your data and the Services as a whole. If necessary, we may also access accounts as part of an abuse report investigation.
    - **To the extent required by applicable law.** As a Swiss company with all data infrastructure located in the Switzerland, we only preserve or share customer data if compelled by the swiss government authority with a legally binding order or proper request under the Stored Communications Act, or in limited circumstances in the event of an emergency request. If a non-swiss authority approaches WEBAUTOMATE.APP for assistance, our default stance is to refuse unless the order has been approved by the swiss government, which compels us to comply through procedures outlined in an established mutual legal assistance treaty or agreement mechanism. If WEBAUTOMATE.APP is audited by a tax authority, we only share the bare minimum billing information needed to complete the audit.
6. We use third party vendors and hosting partners to provide the necessary hardware, software, networking, storage, and related technology required to run the Services.
7. Under the swiss law, WEBAUTOMATE.APP is a "service provider", not a "business" or "third party", with respect to your use of the Services. That means we process any data you share with us only for the purpose you signed up for and as described in this policy. We do not retain, use, disclose, or sell any of that information for any other commercial purposes unless we have your explicit permission. And on the flip-side, you agree to comply with your requirements under the swiss laws and not use WEBAUTOMATE.APP's Services in a way that violates the regulations.

## Blocking of Websites for Violations and Protection

To ensure the security and reliability of WEBAUTOMATE.APP, WEBAUTOMATE.APP reserves the right to impose access restrictions or blockages to certain websites. This may include prohibiting the creation or execution of projects based on these websites as well as deactivating already active projects under the following circumstances:

1. **Violations of Terms of Use:** This includes, but is not limited to, illegal activities, malware distribution, spamming, privacy violations, unauthorized security testing, fraudulent actions, or any use of our services that conflicts with WEBAUTOMATE.APP's established terms of use.
2. **Protection Against Threats:** Taking preventive measures to protect website operators and their users from potential threats or abuse, including ensuring security and privacy.
3. **Complaints from Website Operators:** In cases of complaints from website owners regarding unauthorized or abusive use of our platform in connection with their websites.
4. **Prevention of Abuse for DDOS Attacks:** To prevent our platform from being misused for Distributed Denial of Service (DDOS) attacks aimed at making websites or network services inaccessible.

Upon identifying such violations or to implement protective measures, WEBAUTOMATE.APP will take appropriate steps to inform the affected account holder and, if possible, provide instructions for rectifying the issues. We are committed to creating a safe, trustworthy, and legally compliant environment for all users while protecting the integrity and reliability of our services.

The decision to block or restrict will be made after thorough review, always with the aim of protecting the community and the integrity of our service. Affected users are encouraged to [contact our support](/contact-us) to obtain additional information and assistance in resolving any issues.

## FeaturesPage and Bugs

We design our Services with care, based on our own experience and the experiences of customers who share their time and feedback. However, there is no such thing as a service that pleases everybody. We make no guarantees that our Services will meet your specific requirements or expectations.

We also test all of our features extensively before shipping them. As with any software, our Services inevitably have some bugs. We track the bugs reported to us and work through priority ones, especially any related to security or privacy. Not all reported bugs will get fixed and we don’t guarantee completely error-free Services.

## Indemnification

You agree to indemnify and hold harmless WEBAUTOMATE.APP and its affiliates from any claims, damages, or expenses arising from your use of the Services or violation of these Terms.

## Limitation of Liability

We mention liability throughout these Terms but to put it all in one section:

***You expressly understand and agree that we, along with our affiliates, licensors, service providers, employees, agents, officers, and directors, shall not be liable for any damages arising from your use or inability to use the Website and its services. This includes, but is not limited to, direct, indirect, incidental, special, consequential, or punitive damages. Such damages may encompass personal injury, emotional distress, loss of profits, revenue, business, savings, data, goodwill, or other intangible losses. This limitation applies even if WEBAUTOMATE.APP has been advised of the possibility of such damages and regardless of whether they result from the use or misuse of the service, inability to access the service, the cost of obtaining substitute goods or services, unauthorized access to or alteration of your data, or any other related issue. This limitation applies to all claims, regardless of the legal theory under which they are brought.***

## Governing Law and Jurisdiction

These conditions are subject to Swiss law, and any disputes arising from these conditions or your use of the services shall exclusively be governed by Swiss law. The exclusive jurisdiction shall be the Bezirksgericht Bremgarten.

## General Provisions

These Terms constitute the entire agreement between you and WEBAUTOMATE.APP regarding the Services. If any provision of these Terms is held to be invalid or unenforceable, the remaining provisions will remain in full effect.

## Changes to the Terms of Service

We reserve the right to modify these Terms of Service ("Terms") at our discretion. Significant updates to our policies will prompt us to update the date at the top of this page and notify account holders through appropriate means, which may include emailing or posting a notice on our website and Services. We recommend regularly reviewing these Terms to stay informed.

Changes to these Terms are effective immediately upon posting and apply to all subsequent access and use of the Website. By continuing to use the Website after we post revised Terms, you accept and agree to the updates. It's important to frequently check this page for any changes, as they are binding on you.

## ContactPage Information

For questions about these Terms, please [contact us via our support channel](/contact-us).

