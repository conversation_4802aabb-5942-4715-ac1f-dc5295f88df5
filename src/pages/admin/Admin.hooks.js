/* eslint-disable react-hooks/exhaustive-deps */
import {useEffect, useState} from 'react';
import {Icon} from '@iconify/react';
import copyFill from '@iconify/icons-eva/copy-fill';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import {Box, IconButton, Stack, Tooltip, Typography} from '@material-ui/core';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyArray, isNonEmptyObject, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useApiCaller from '../../custom/hooks/useApiCaller';
import useNotification from '../../custom/hooks/useNotification';
import {getAdminAPIPath} from '../../custom/utils/getPath';
import {JOBS} from './Admin.constants';

export const useCallbacks = () => {
    const notify = useNotification();
    const [notifications, setNotifications] = useState([]);
    const alwaysCallback = (responseData) => {
        if (!window.webAutomate.showAdminNotifications) {
            return;
        }

        let variant = 'error';
        let message = 'Failure';

        if (responseData?.data?.success) {
            variant = 'success';
            message = 'Success';
        }

        const dataString = JSON.stringify(responseData, null, 2);

        setNotifications([
            ...notifications,
            notify({
                message: (
                    <Stack>
                        <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="space-between">
                            <Typography variant="subtitle1">{message}</Typography>
                            <CopyToClipboard text={dataString}>
                                <Tooltip title="Copy">
                                    <IconButton
                                        color="primary"
                                    >
                                        <Icon icon={copyFill} width={40} height={40}/>
                                    </IconButton>
                                </Tooltip>
                            </CopyToClipboard>
                        </Stack>
                        <Box
                            component="pre"
                            sx={{
                                display: 'block',
                                maxWidth: '50vw',
                                maxHeight: '10vh',
                                overflow: 'scroll',
                            }}>{dataString}</Box>
                    </Stack>
                ),
                variant,
                autoHideDuration: 5000,
                anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'right'
                }
            }),
        ]);
    };

    return {
        alwaysCallback,
    };
};

export const useUpdateSubscriptions = () => {
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const handleUpdateSubscriptions = () => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.updateSubscriptions),
            alwaysCallback,
        });
    };

    return {
        handleUpdateSubscriptions,
    };
};

export const useProcessProjectDeleteQueue = () => {
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const handleProjectDeleteQueue = () => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.processProjectDeleteQueue),
            alwaysCallback,
        });
    };

    return {
        handleProjectDeleteQueue,
    };
};

export const useProcessRequest = ({job}) => {
    const [requests, setRequests] = useState([]);
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const updateList = () => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.fetchRequestList),
            successCallback: ({data}) => {
                setRequests(data?.requests);
            },
            alwaysCallback,
        });
    };
    const replyDemoRequest = (data) => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.replyDemo),
            data,
            successCallback: () => {
                updateList();
            },
            alwaysCallback,
        });
    };
    const processRequest = (data) => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.processRequest),
            data,
            successCallback: () => {
                updateList();
            },
            alwaysCallback,
        });
    };

    useEffect(() => {
        if (job !== JOBS.processRequests.key || requests.length > 0) {
            return;
        }

        updateList();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [job]);

    return {
        requests,
        replyDemoRequest,
        processRequest,
    };
};

export const useAccountData = ({job}) => {
    const [accountList, setAccountList] = useState([]);
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();

    useEffect(() => {
        if (!job || accountList.length > 0) {
            return;
        }

        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.fetchAccountList),
            successCallback: ({data}) => {
                if (!isNonEmptyArray(data?.accountList)) {
                    return;
                }

                setAccountList(data.accountList);
            },
            alwaysCallback,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [job]);

    return {
        accountList,
    };
};

export const useBuildProject = ({
    job,
    accountId,
}) => {
    const [projectList, setProjectList] = useState([]);
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const handleBuildProject = (data) => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.buildProject),
            data,
            alwaysCallback,
        });
    };
    const {
        accountList,
    } = useAccountData({job});

    useEffect(() => {
        setProjectList([]);
    }, [accountList]);

    useEffect(() => {
        if (!accountId || job !== JOBS.buildProject.key) {
            return;
        }

        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.fetchProjectList),
            data: {
                accountId,
            },
            successCallback: ({data}) => {
                if (!isNonEmptyArray(data?.projectList)) {
                    return;
                }

                setProjectList(data.projectList);
            },
            alwaysCallback,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [job, accountId]);

    return {
        projectList,
        accountList,
        handleBuildProject,
    };
};

export const useBroadcastAccountNotification = () => {
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const [isSuccess, setIsSuccess] = useState(false);

    const broadcastAccountNotification = (data) => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.broadcastAccountNotification),
            data,
            successCallback: () => {
                setIsSuccess(true);
            },
            alwaysCallback,
        });
    };

    return {
        isSuccess,
        broadcastAccountNotification,
    };
}

export const useBlockDomain = () => {
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const [blockDomainData, setBlockDomainData] = useState();
    const handleUnblockDomain = (data) => {
        setBlockDomainData(null);
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.blacklistDomainRemove),
            data,
            successCallback: () => {
                setBlockDomainData({
                    isFree: true,
                });
            },
            alwaysCallback,
        });
    };
    const handleBlockDomain = (data) => {
        setBlockDomainData(null);
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.blacklistDomain),
            data,
            successCallback: () => {
                setBlockDomainData({
                    isBlocked: true,
                    isJustBlocked: true,
                });
            },
            alwaysCallback,
        });
    };
    const handleCheckDomainStatus = (data) => {
        setBlockDomainData(null);
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.blacklistDomainStatus),
            data,
            successCallback: ({data}) => {
                if (isNonEmptyString(data?.domainData?.domain)) {
                    setBlockDomainData({
                        isBlocked: true,
                    });
                    return;
                }

                setBlockDomainData({
                    isFree: true,
                });
            },
            alwaysCallback,
        });
    };

    return {
        blockDomainData,
        setBlockDomainData,
        handleUnblockDomain,
        handleBlockDomain,
        handleCheckDomainStatus,
    };
};

export const useProcessAdminLogs = () => {
    const [logItems, setLogItems] = useState({});
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const updateLogs = () => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.fetchAdminLogs),
            successCallback: ({data}) => {
                if (!isNonEmptyObject(data?.logItems)) {
                    return;
                }

                setLogItems(data?.logItems);
            },
            alwaysCallback,
        });
    }
    const processLogItem = ({date, id, action}) => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.processAdminLogs),
            data: {date, id, action},
            successCallback: () => {
                updateLogs();
            },
            alwaysCallback,
        });
    };

    useEffect(() => {
        updateLogs();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return {
        logItems,
        processLogItem,
    };
};

export const useAccountState = ({
    job,
    accountId,
}) => {
    const apiCaller = useApiCaller();
    const {alwaysCallback} = useCallbacks();
    const {
        accountList,
    } = useAccountData({job});

    const updateAccountState = (fields) => {
        apiCaller({
            uri: getAdminAPIPath(serverlessAPI.adminApi.uris.updateAccount),
            data: {
                accountId,
                fields,
            },
            alwaysCallback,
        });
    };

    return {
        accountList,
        updateAccountState,
    };
};
