import {useState, Fragment} from 'react';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary, Box,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import {useProcessAdminLogs} from '../Admin.hooks';
import LabelStyle from '../../../custom/components/utils/LabelStyle';
import InfoCode from '../../../custom/components/utils/InfoCode';

const ProcessAdminLogs = () => {
    const [showDetails, setShowDetails] = useState({});
    const {
        logItems,
        processLogItem,
    } = useProcessAdminLogs();

    const logDateList = Object.keys(logItems).sort((a, b) => {
        return new Date(b).getTime() - new Date(a).getTime();
    });

    return (
        <>
            {
                logDateList.map((date) => {
                    return (
                        <Accordion key={date}>
                            <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                                <LabelStyle>
                                    {date}
                                </LabelStyle>
                            </AccordionSummary>
                            <AccordionDetails>
                                <TableContainer sx={{minWidth: 500}}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>
                                                    Timestamp
                                                </TableCell>
                                                <TableCell>
                                                    Server
                                                </TableCell>
                                                <TableCell>
                                                    Severity
                                                </TableCell>
                                                <TableCell sx={{width: 150, p: 1}} />
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {logItems[date].length === 0 && (
                                                <TableRow>
                                                    <TableCell colSpan={4}>
                                                        <Typography align="center" variant="body2">
                                                            No Logs!
                                                        </Typography>
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                            {logItems[date].map(({id, data}) => (
                                                <Fragment key={id}>
                                                    <TableRow>
                                                        <TableCell>
                                                            {data?.timestamp}
                                                        </TableCell>
                                                        <TableCell>
                                                            {data?.serverId}
                                                        </TableCell>
                                                        <TableCell>
                                                            <InfoCode
                                                                infoCode={data?.infoCode}
                                                                isDismissible={false}
                                                                showDetails={false}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Button
                                                                fullWidth
                                                                onClick={() => setShowDetails({
                                                                    ...showDetails,
                                                                    [id]: !showDetails[id],
                                                                })}>
                                                                Details
                                                            </Button>
                                                        </TableCell>
                                                    </TableRow>
                                                    {
                                                        showDetails[id] && (
                                                            <TableRow>
                                                                <TableCell colSpan={4}>
                                                                    <Box component="pre" sx={{
                                                                        backgroundColor: 'rgba(0,0,0,0.1)',
                                                                    }}>
                                                                        {JSON.stringify(data, null, 2)}
                                                                    </Box>
                                                                    <Button
                                                                        size="large"
                                                                        variant="contained"
                                                                        onClick={() => processLogItem({date, id, action: 'delete'})}
                                                                        fullWidth
                                                                        sx={{my: 3}}
                                                                    >
                                                                        Delete
                                                                    </Button>
                                                                </TableCell>
                                                            </TableRow>
                                                        )
                                                    }
                                                </Fragment>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </AccordionDetails>
                        </Accordion>
                    );
                })
            }
        </>
    );
};


export default ProcessAdminLogs;
