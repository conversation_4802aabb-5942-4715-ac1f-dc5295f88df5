import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    FormControlLabel,
    Stack,
    Switch,
    TextField,
    ToggleButton,
    ToggleButtonGroup, Typography,
} from '@material-ui/core';
import isURL from 'validator/es/lib/isURL';
import {Controller, useForm} from 'react-hook-form';
import {useState} from 'react';
import PropTypes from 'prop-types';
import {useAccountData, useBlockDomain} from '../Admin.hooks';
import SelectAccount from './utils/SelectAccount';

const MODES = {
    BLOCK: 'block',
    UNBLOCK: 'unblock',
    STATUS: 'status',
};
const FORM_FIELDS = {
    url: 'url',
    global: 'global',
    accountId: 'accountId',
    reason: 'reason',
};

const propTypes = {
    job: PropTypes.string,
};

const DomainManagement = ({job}) => {
    const [mode, setMode] = useState(MODES.STATUS);
    const {
        watch,
        control,
        setError,
        clearErrors,
        setValue,
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            [FORM_FIELDS.global]: false,
            [FORM_FIELDS.url]: '',
            [FORM_FIELDS.accountId]: '',
            [FORM_FIELDS.reason]: '',
        },
    });
    const {
        blockDomainData,
        setBlockDomainData,
        handleUnblockDomain,
        handleBlockDomain,
        handleCheckDomainStatus,
    } = useBlockDomain();
    const {
        accountList,
    } = useAccountData({job});
    const handleData = ({callback}) => {
        const data = watch();
        const isValid = isURL(data.url);
        if (!isValid) {
            setError(FORM_FIELDS.url, {
                type: 'manual',
                message: 'Invalid URL',
            });
            return;
        }
        clearErrors(FORM_FIELDS.url);
        callback(data);
    };

    return (
        <Stack spacing={3}>
            <ToggleButtonGroup
                fullWidth
                exclusive
                value={mode}
                onChange={(_, value) => {
                    setMode(value);
                    setBlockDomainData(null);
                }}>
                {
                    Object.values(MODES).map((value) => (
                        <ToggleButton
                            key={value}
                            value={value}
                            sx={{textTransform: 'none'}}>
                            {value.toUpperCase()}
                        </ToggleButton>
                    ))
                }
            </ToggleButtonGroup>
            <Controller
                name={FORM_FIELDS.url}
                control={control}
                render={({
                    field,
                    fieldState: {error},
                }) => (
                    <Stack spacing={1}>
                        <TextField
                            {...field}
                            fullWidth
                            label="URL"
                            type="url"
                        />
                        {Boolean(error?.message) && (
                            <Typography
                                sx={{color: 'error.main'}}>
                                {error?.message}
                            </Typography>
                        )}
                    </Stack>
                )}
            />
            <Stack>
                <Controller
                    name={FORM_FIELDS.global}
                    control={control}
                    render={({
                        field,
                    }) => (
                        <Stack spacing={3}>
                            <FormControlLabel
                                control={<Switch {...field} size="large" />}
                                label="Global (all accounts)"
                            />
                            {
                                !watch(FORM_FIELDS.global) && (
                                    <SelectAccount
                                        accountList={accountList}
                                        setAccountId={(id) => {
                                            setValue(FORM_FIELDS.accountId, id);
                                        }}
                                    />
                                )
                            }
                            {
                                mode === MODES.BLOCK && (
                                    <Controller
                                        name={FORM_FIELDS.reason}
                                        control={control}
                                        render={({
                                            field,
                                            fieldState: {error},
                                        }) => (
                                            <Stack spacing={1}>
                                                <TextField
                                                    {...field}
                                                    fullWidth
                                                    multiline
                                                    minRows={4}
                                                    maxRows={8}
                                                    label="Reason"
                                                />
                                                {Boolean(error?.message) && (
                                                    <Typography
                                                        sx={{color: 'error.main'}}>
                                                        {error?.message}
                                                    </Typography>
                                                )}
                                            </Stack>
                                        )}
                                    />
                                )
                            }
                        </Stack>
                    )}
                />
            </Stack>
            {
                blockDomainData?.isBlocked && !blockDomainData?.isJustBlocked && (
                    <Alert color="error">
                        The Domain <strong>{blockDomainData.url}</strong> is blocked
                    </Alert>
                )
            }
            {
                blockDomainData?.isBlocked && blockDomainData?.isJustBlocked && (
                    <Alert color="error">
                        The Domain <strong>{blockDomainData.url}</strong> is now also blocked
                    </Alert>
                )
            }
            {
                blockDomainData?.isFree && (
                    <Alert color="success">
                        The Domain <strong>{blockDomainData.url}</strong> is free
                    </Alert>
                )
            }
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                {
                    mode === MODES.BLOCK && (
                        <Button
                            fullWidth
                            size="large"
                            variant="outlined"
                            onClick={() => {
                                handleData({
                                    callback: handleBlockDomain,
                                });
                            }}>
                            Block Domain
                        </Button>
                    )
                }
                {
                    mode === MODES.UNBLOCK && (
                        <Button
                            fullWidth
                            size="large"
                            variant="outlined"
                            onClick={() => {
                                handleData({
                                    callback: handleUnblockDomain,
                                });
                            }}>
                            Unblock Domain
                        </Button>
                    )
                }
                {
                    mode === MODES.STATUS && (
                        <Button
                            fullWidth
                            size="large"
                            variant="outlined"
                            onClick={() => {
                                handleData({
                                    callback: handleCheckDomainStatus,
                                });
                            }}>
                            Check Status
                        </Button>
                    )
                }
            </Stack>
        </Stack>
    );
};

DomainManagement.propTypes = propTypes;

export default DomainManagement;
