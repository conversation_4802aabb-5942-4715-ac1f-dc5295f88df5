import {
    <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Button,
    FormControlLabel,
    MenuItem,
    Radio,
    RadioGroup,
    Stack,
    TextField,
    ToggleButton,
    ToggleButtonGroup,
} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import mapObjIndexed from 'ramda/src/mapObjIndexed';
import {yupResolver} from '@hookform/resolvers/yup';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {DEFAULT_LANGUAGE, LANGUAGES} from '@w7-3/webeagle-resources/dist/config/languages';
import {isNonEmptyString, isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {useAccountData, useBroadcastAccountNotification} from '../Admin.hooks';
import SelectAccount from './utils/SelectAccount';

const FORM_FIELDS = {
    accountIdList: 'accountIdList',
    global: 'global',
    level: 'level',
    messages: 'messages',
    titles: 'titles',
};

const propTypes = {
    job: PropTypes.string,
};

const BroadcastAccountNotification = ({job}) => {
    const {
        watch,
        control,
        formState: {errors},
        trigger,
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            [FORM_FIELDS.global]: 'false',
            [FORM_FIELDS.accountIdList]: [],
            [FORM_FIELDS.titles]: mapObjIndexed(() => '', LANGUAGES),
            [FORM_FIELDS.messages]: mapObjIndexed(() => '', LANGUAGES),
            [FORM_FIELDS.level]: INFO_CODE_LEVELS.INFO,
        },
        resolver: yupResolver(Yup.object().shape({
            [FORM_FIELDS.titles]: Yup.object().shape(mapObjIndexed((lang) => Yup.mixed().test('', `${lang.toUpperCase()} title is required`, (value) => {
                return isNonEmptyString(value);
            }), LANGUAGES)),
            [FORM_FIELDS.messages]: Yup.object().shape(mapObjIndexed((lang) => Yup.mixed().test('', `${lang.toUpperCase()} message is required`, (value) => {
                return isNonEmptyString(value);
            }), LANGUAGES)),
            [FORM_FIELDS.accountIdList]: Yup.array().when(FORM_FIELDS.global, {
                is: (value) => {
                    return value === 'false';
                },
                then: Yup.array().min(1, 'You need to select at least one account'),
                otherwise: Yup.array().notRequired(),
            }),
        })),
    });
    const {
        isSuccess,
        broadcastAccountNotification,
    } = useBroadcastAccountNotification();
    const [i18nTab, setI18nTab] = useState(DEFAULT_LANGUAGE);
    const {
        accountList,
    } = useAccountData({job});
    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (isSuccess) {
        return (
            <Alert color="success">
                <AlertTitle>Success</AlertTitle>
                Notification has been sent successfully
            </Alert>
        );
    }

    return (
        <Stack spacing={3}>
            <Controller
                name={FORM_FIELDS.global}
                control={control}
                render={({
                    field,
                }) => (
                    <>
                        <RadioGroup
                            {...field}
                            onChange={(event, value) => {
                                field.onChange(value);
                                trigger(FORM_FIELDS.accountIdList);
                            }}
                        >
                            <FormControlLabel
                                value="true"
                                label="Send to every account"
                                control={<Radio />} />
                            <FormControlLabel
                                value="false"
                                label="Send only to these accounts"
                                control={<Radio />} />
                        </RadioGroup>
                        {
                            field.value === 'false' && (
                                <Controller
                                    name={FORM_FIELDS.accountIdList}
                                    control={control}
                                    render={({
                                        field,
                                    }) => (
                                        <SelectAccount
                                            accountList={accountList}
                                            multiple
                                            setAccountId={(values) => {
                                                field.onChange(values);
                                                trigger(FORM_FIELDS.accountIdList);
                                            }}
                                            values={field.value}
                                        />
                                    )}
                                />
                            )
                        }
                    </>
                )}
            />
            <ToggleButtonGroup
                exclusive
                fullWidth
                value={i18nTab}
                onChange={(_, value) => setI18nTab(value)}>
                {
                    Object.values(LANGUAGES).map((lang) => (
                        <ToggleButton
                            key={lang}
                            value={lang}
                            sx={{textTransform: 'none'}}>
                            {lang.toUpperCase()}
                        </ToggleButton>
                    ))
                }
            </ToggleButtonGroup>
            {Object.values(LANGUAGES).map((lang) => (
                <Stack
                    spacing={3}
                    key={lang}
                    sx={{
                        mb: 3,
                        display: i18nTab === lang ? 'block' : 'none',
                    }}>
                    <Controller
                        name={`${FORM_FIELDS.titles}.${lang}`}
                        control={control}
                        render={({
                            field,
                        }) => (
                            <TextField
                                {...field}
                                key={lang}
                                fullWidth
                                variant="standard"
                                label={`Title (${lang.toUpperCase()})`}
                            />
                        )}
                    />
                    <Controller
                        name={`${FORM_FIELDS.messages}.${lang}`}
                        control={control}
                        render={({
                            field,
                        }) => (
                            <TextField
                                {...field}
                                key={lang}
                                fullWidth
                                rows={4}
                                maxRows={8}
                                label={`Message (${lang.toUpperCase()})`}
                                sx={{
                                    '& .MuiInputBase-root': {
                                        minHeight: '75px',
                                    },
                                }}
                            />
                        )}
                    />
                </Stack>
            ))}
            <Controller
                name={FORM_FIELDS.level}
                control={control}
                render={({
                    field,
                }) => (
                    <TextField
                        {...field}
                        select
                        fullWidth
                        label="Level">
                        {Object.values(INFO_CODE_LEVELS).map((level) => (
                            <MenuItem key={level} value={level}>
                                {level}
                            </MenuItem>
                        ))}
                    </TextField>
                )}
            />
            {
                isNonEmptyObject(errors) && (
                    <Alert color="error">
                        <AlertTitle>Attention</AlertTitle>
                        {
                            [
                                errors[FORM_FIELDS.accountIdList]?.message,
                                ...Object.values(errors[FORM_FIELDS.titles] || {}).map((error) => error.message),
                                ...Object.values(errors[FORM_FIELDS.messages] || {}).map((error) => error.message),
                            ].map((message) => (
                                <Box key={message}>
                                    {message}
                                </Box>
                            ))
                        }
                    </Alert>
                )
            }
            <Button
                size="large"
                variant="contained"
                onClick={() => broadcastAccountNotification(watch())}
                sx={{
                    ml: 'auto',
                }}
                disabled={isNonEmptyObject(errors)}
            >
                SEND
            </Button>
        </Stack>
    );
};

BroadcastAccountNotification.propTypes = propTypes;

export default BroadcastAccountNotification;
