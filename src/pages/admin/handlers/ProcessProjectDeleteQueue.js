import {Button} from '@material-ui/core';
import {JOBS} from '../Admin.constants';
import {useProcessProjectDeleteQueue} from '../Admin.hooks';

const ProcessProjectDeleteQueue = () => {
    const {
        handleProjectDeleteQueue,
    } = useProcessProjectDeleteQueue();

    return (
        <Button
            fullWidth
            size="large"
            variant="contained"
            onClick={handleProjectDeleteQueue}>
            {JOBS.processProjectDeleteQueue.label}
        </Button>
    );
};

export default ProcessProjectDeleteQueue;
