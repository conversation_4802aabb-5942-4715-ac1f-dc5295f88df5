import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
// material
import {But<PERSON>, Fab, IconButton} from '@material-ui/core';
// components
import {ButtonAnimate} from '../../../../../components/animate';

// ----------------------------------------------------------------------

export default function MediumClick() {
    return (
        <>
            <ButtonAnimate mediumClick>
                <Button variant="contained" size="large">
                    Button
                </Button>
            </ButtonAnimate>
            <ButtonAnimate mediumClick>
                <Fab>
                    <Icon icon={plusFill} width={40} height={40}/>
                </Fab>
            </ButtonAnimate>
            <ButtonAnimate mediumClick>
                <IconButton color="primary">
                    <Icon icon={plusFill} width={40} height={40}/>
                </IconButton>
            </ButtonAnimate>
        </>
    );
}
