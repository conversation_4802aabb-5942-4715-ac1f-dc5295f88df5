import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
// material
import {Button, Fab, IconButton} from '@material-ui/core';
// components
import {ButtonAnimate} from '../../../../../components/animate';

// ----------------------------------------------------------------------

export default function SmallClick() {
    return (
        <>
            <ButtonAnimate>
                <Button variant="contained">Button</Button>
            </ButtonAnimate>
            <ButtonAnimate>
                <Fab size="large">
                    <Icon icon={plusFill} width={40} height={40}/>
                </Fab>
            </ButtonAnimate>
            <ButtonAnimate>
                <IconButton color="primary">
                    <Icon icon={plusFill} width={40} height={40}/>
                </IconButton>
            </ButtonAnimate>
        </>
    );
}
