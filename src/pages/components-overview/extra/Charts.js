// material
import {styled} from '@material-ui/core/styles';
import {<PERSON>, Card, CardContent, CardHeader, Container, Grid} from '@material-ui/core';
// routes
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
// components
import Page from '../../../components/Page';
import HeaderBreadcrumbs from '../../../components/HeaderBreadcrumbs';
import {
    ChartArea,
    ChartBar,
    ChartColumnMultiple,
    ChartColumnNegative,
    ChartColumnSingle,
    ChartColumnStacked,
    ChartDonut,
    ChartLine,
    ChartMixed,
    ChartPie,
    ChartRadialBar,
    ChartsRadarBar,
} from '../../../components/charts';

// ----------------------------------------------------------------------

const RootStyle = styled(Page)(({theme}) => ({
    paddingTop: theme.spacing(11),
    paddingBottom: theme.spacing(15),
}));

export default function Charts() {
    return (
        <RootStyle title="Components: Charts | Minimal-UI">
            <Box
                sx={{
                    pt: 6,
                    pb: 1,
                    mb: 10,
                    bgcolor: (theme) => (theme.palette.mode === 'light' ? 'grey.200' : 'grey.800'),
                }}
            >
                <Container maxWidth="lg">
                    <HeaderBreadcrumbs
                        heading="Charts"
                        links={[{
                            name: 'Components',
                            href: PATH_PAGE.components,
                        }, {name: 'Charts'}]}
                        moreLink="https://apexcharts.com"
                    />
                </Container>
            </Box>

            <Container maxWidth="lg">
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Area"/>
                            <CardContent>
                                <ChartArea/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Line"/>
                            <CardContent>
                                <ChartLine/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Column Single"/>
                            <CardContent>
                                <ChartColumnSingle/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Column Multiple"/>
                            <CardContent>
                                <ChartColumnMultiple/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Column Stacked"/>
                            <CardContent>
                                <ChartColumnStacked/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Column Negative"/>
                            <CardContent>
                                <ChartColumnNegative/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Bar"/>
                            <CardContent>
                                <ChartBar/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Mixed"/>
                            <CardContent>
                                <ChartMixed/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Pie"/>
                            <CardContent
                                sx={{
                                    height: 420,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                <ChartPie/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Donut"/>
                            <CardContent
                                sx={{
                                    height: 420,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                <ChartDonut/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Radial Bar"/>
                            <CardContent
                                sx={{
                                    height: 420,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                <ChartRadialBar/>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <Card dir="ltr">
                            <CardHeader title="Radar"/>
                            <CardContent
                                sx={{
                                    height: 420,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                <ChartsRadarBar/>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
        </RootStyle>
    );
}
