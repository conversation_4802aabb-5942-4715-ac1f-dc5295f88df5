import {useState} from 'react';
// material
import {Button, Dialog, Dialog<PERSON><PERSON>, DialogContent, DialogContentText, DialogTitle} from '@material-ui/core';

// ----------------------------------------------------------------------

export default function AlertDialog() {
    const [open, setOpen] = useState(false);

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <div>
            <Button color="info" variant="outlined" onClick={handleClickOpen}>
                Open alert dialog
            </Button>

            <Dialog open={open} onClose={handleClose}>
                <DialogTitle>Use Google's location service?</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        Let Google help apps determine location. This means sending anonymous location data to Google,
                        even when no
                        apps are running.
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Disagree</Button>
                    <Button onClick={handleClose} autoFocus>
                        Agree
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
}
