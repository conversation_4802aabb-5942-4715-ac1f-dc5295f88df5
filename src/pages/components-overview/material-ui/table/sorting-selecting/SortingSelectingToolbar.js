import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import trash2Fill from '@iconify/icons-eva/trash-2-fill';
import roundFilterList from '@iconify/icons-ic/round-filter-list';
// material
import {styled, useTheme} from '@material-ui/core/styles';
import {IconButton, Toolbar, Tooltip, Typography} from '@material-ui/core';

// ----------------------------------------------------------------------

const RootStyle = styled(Toolbar)(({theme}) => ({
    height: 96,
    display: 'flex',
    justifyContent: 'space-between',
    padding: theme.spacing(0, 1, 0, 3),
}));

// ----------------------------------------------------------------------

SortingSelectingToolbar.propTypes = {
    numSelected: PropTypes.number.isRequired,
};

export default function SortingSelectingToolbar({numSelected}) {
    const theme = useTheme();
    const isLight = theme.palette.mode === 'light';

    return (
        <RootStyle
            sx={{
                ...(numSelected > 0 && {
                    color: isLight ? 'primary.main' : 'text.primary',
                    bgcolor: isLight ? 'primary.lighter' : 'primary.dark',
                }),
            }}
        >
            {numSelected > 0 ? (
                <Typography color="inherit" variant="subtitle1" component="div">
                    {numSelected} selected
                </Typography>
            ) : (
                <Typography variant="h6" id="tableTitle" component="div">
                    Sorting & Selecting
                </Typography>
            )}

            {numSelected > 0 ? (
                <Tooltip title="Delete">
                    <IconButton
                        color="primary"
                    >
                        <Icon icon={trash2Fill}/>
                    </IconButton>
                </Tooltip>
            ) : (
                <Tooltip title="Filter list">
                    <IconButton>
                        <Icon icon={roundFilterList}/>
                    </IconButton>
                </Tooltip>
            )}
        </RootStyle>
    );
}
