import {Link as RouterLink} from 'react-router-dom';
import {styled} from '@material-ui/core/styles';
import {<PERSON><PERSON>, Container, <PERSON>, Stack, Typography} from '@material-ui/core';
import ReactDOMServer from 'react-dom/server';
import {PATH_AUTH, PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import isEmail from 'validator/es/lib/isEmail';
import {MHidden} from '../../components/@material-extend';
import {RegisterForm} from '../../components/authentication/register';
import AuthFirebaseSocials from '../../components/authentication/AuthFirebaseSocial';
import useLocales from '../../hooks/useLocales';
import {globalConfig} from '../../config/setup';
import PageWrapper from '../../custom/components/PageWrapper';

const ContentStyle = styled('div')(({theme}) => ({
    maxWidth: 768,
    margin: 'auto',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    padding: theme.spacing(12, 0),
}));

const RegisterPage = () => {
    const {translate} = useLocales();
    const privacyPolicy = ReactDOMServer.renderToStaticMarkup(
        <a href={PATH_PAGE.privacyPolicy}
           data-static-link=""
           target="_blank"
           rel="nofollow noreferrer noopener">
            {translate('privacyPolicy.label')}
        </a>,
    );
    const termsAndConditions = ReactDOMServer.renderToStaticMarkup(
        <a href={PATH_PAGE.termsAndConditions}
           data-static-link=""
           target="_blank"
           rel="nofollow noreferrer noopener">
            {translate('termsAndConditions.label')}
        </a>,
    );
    const app = ReactDOMServer.renderToStaticMarkup(
        <Typography variant="body" sx={{color: 'primary.main'}}>
            {globalConfig.domain}
        </Typography>,
    );
    const registerContent = (
        <Stack
            direction={{
                xs: 'column',
                sm: 'row',
            }}
            alignItems="center"
            justifyContent="flex-end"
            sx={{pt: 3}}
        >
            <Typography variant="body2">
                {translate('register.accountQuestion')}
            </Typography>
            <Link
                underline="none"
                variant="subtitle2"
                component={RouterLink}
                to={PATH_AUTH.login}>
                {translate('login.loginHere')}
            </Link>
        </Stack>
    );
    let guest = '';
    let organisation = '';
    window.location.search.substr(1)
        .split('&')
        .forEach((keyValue) => {
            const [key, value] = keyValue.split('=');

            if (key === urlParameters.account.guest) {
                guest = decodeURI(value);
            }

            if (key === urlParameters.account.organisation) {
                organisation = decodeURI(value);
            }
        });
    const isPerInvite = isNonEmptyString(organisation) && isNonEmptyString(guest) && isEmail(guest);
    const i18nContext = isPerInvite ? 'register.perInvite' : 'register';

    return (
        <PageWrapper title={translate('register.pageTitle')}>
            <MHidden width="smDown">
                {registerContent}
            </MHidden>
            <Container>
                <ContentStyle>
                    <Stack spacing={3} sx={{mb: 5}}>
                        <Typography variant="h4" gutterBottom>
                            {translate(`${i18nContext}.label`)}
                        </Typography>
                        <Alert severity="info">
                            {translate(`${i18nContext}.teaser`, {
                                app: globalConfig.domain,
                                organisation,
                            })}
                        </Alert>
                    </Stack>
                    <AuthFirebaseSocials/>
                    <RegisterForm
                        email={guest}
                        organisation={organisation}
                        isPerInvite={isPerInvite}
                    />
                    <Typography variant="body2" align="center" sx={{
                        color: 'text.secondary',
                        mt: 3,
                    }}>
                        <span dangerouslySetInnerHTML={{
                            __html: translate('register.declaration', {
                                privacyPolicy,
                                termsAndConditions,
                                app,
                            }),
                        }}/>
                    </Typography>
                    <MHidden width="smUp">
                        {registerContent}
                    </MHidden>
                </ContentStyle>
            </Container>
        </PageWrapper>
    );
};

export default RegisterPage;
