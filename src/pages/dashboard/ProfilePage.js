import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Button,
    Card,
    Divider,
    Grid,
    Stack,
    Typography,
} from '@material-ui/core';
import {STATES as userStates} from '@w7-3/webeagle-resources/dist/config/users';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import HeaderBreadcrumbs from '../../components/HeaderBreadcrumbs';
import useLocales from '../../hooks/useLocales';
import useAuth from '../../hooks/useAuth';
import {fDateTimeHumanReadable} from '../../utils/formatTime';
import Label from '../../components/Label';
import {accountStateLabelColors} from '../../config/account';
import ModalContainer from '../../custom/components/utils/ModalContainer';
import useNotification from '../../custom/hooks/useNotification';
import PageWrapper from '../../custom/components/PageWrapper';
import DefinitionList from '../../custom/components/utils/DefinitionList';

const propTypes = {
    accountHolder: PropTypes.string,
};

const mapStateToProps = ({state}) => {
    const {
        accountHolder,
    } = state;

    return {
        accountHolder,
    };
};

const ProfilePage = (props) => {
    const {translate, allLang} = useLocales();
    const {user, resetPassword} = useAuth();
    const invitationProcessItemUnverified = user?.invitationProcess?.find((item) => {
        return item?.state === userStates.UNVERIFIED;
    });
    const invitationProcessItemLatest = user?.invitationProcess?.slice?.(-1)?.[0];
    const notify = useNotification();
    const userLang = allLang.find((lang) => lang.value === user.preferredLanguage);

    return (
        <PageWrapper title={translate('users.profile.label')}>
            <HeaderBreadcrumbs
                heading={translate('users.profile.label')}
                links={[
                    {
                        name: translate('dashboard.label'),
                        href: PATH_DASHBOARD.root,
                    },
                    {
                        name: translate('users.label'),
                        href: PATH_DASHBOARD.management.collaboratorList,
                    },
                    {name: translate('users.profile.label')},
                ]}
            />
            <Card sx={{p: 3}}>
                <Grid container spacing={3}>
                    {
                        [
                            {
                                key: translate('form.email'),
                                node: (
                                    <Typography>{user.email}</Typography>
                                ),
                            },
                            {
                                key: translate('collaborators.role.label'),
                                node: (
                                    <Typography>
                                        {translate(`collaborators.role.options.${user.role}`)}
                                    </Typography>
                                ),
                            },
                            getOptionalMap(Boolean(user.invitedBy), {
                                key: translate('collaborators.invitedBy'),
                                node: (
                                    <Typography>
                                        {user.invitedBy}
                                    </Typography>
                                ),
                            }),
                            getOptionalMap(Boolean(props.accountHolder !== user.email), {
                                key: translate('account.holder'),
                                node: (
                                    <Typography>
                                        {props.accountHolder}
                                    </Typography>
                                ),
                            }),
                            getOptionalMap(Boolean(invitationProcessItemUnverified?.ts), {
                                key: translate('collaborators.created'),
                                node: (
                                    <Typography>
                                        {fDateTimeHumanReadable(invitationProcessItemUnverified?.ts)}
                                    </Typography>
                                ),
                            }),
                            {
                                key: translate('collaborators.state.label'),
                                node: (
                                    <Label color={accountStateLabelColors[invitationProcessItemLatest.state]}>
                                        {translate(`collaborators.state.options.${invitationProcessItemLatest.state}.label`)}
                                    </Label>
                                ),
                            },
                            {
                                key: translate('account.settings.language.label'),
                                node: (
                                    <Stack direction="row" spacing={1}>
                                        <Box component="img" alt={userLang.label} src={userLang.icon}/>
                                            <Typography variant="body2" sx={{mr: 1}}>
                                                {userLang.label}
                                            </Typography>
                                    </Stack>
                                ),
                            },
                        ].map(({key, node}) => {
                            if (!key || !node) {
                                return null;
                            }

                            return (
                                <Grid key={key} item xs={12} sm={6} md={4}>
                                    <Typography sx={{color: 'text.secondary'}}>{key}</Typography>
                                    {node}
                                </Grid>
                            );
                        })
                    }
                </Grid>
            </Card>
            <Divider sx={{my: 3}} />
            <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                    {translate('accountSecurity.label')}
                </AccordionSummary>
                <AccordionDetails>
                    <DefinitionList
                        dataList={[
                            {
                                key: translate('users.profile.changePassword'),
                                node: (
                                    <ModalContainer
                                        triggerLabel={translate('passwordReset.label')}
                                        title={translate('passwordReset.label')}
                                        buttonProps={{
                                            startIcon: null,
                                            variant: 'contained',
                                        }}
                                        CtaButton={({handleClose}) => {
                                            return (
                                                <Button
                                                    variant="outlined"
                                                    sx={{textTransform: 'none'}}
                                                    onClick={async () => {
                                                        handleClose();
                                                        try {
                                                            await resetPassword(user.email);
                                                            notify({
                                                                message: translate('passwordReset.wasResent', {
                                                                    email: user.email,
                                                                }),
                                                                variant: 'success',
                                                            });
                                                        } catch (e) {
                                                            notify({
                                                                message: translate(`directives.notifications.${NOTIFICATIONS.genericErrorWithRetry}`),
                                                                variant: 'error',
                                                            });
                                                        }
                                                    }}>
                                                    {translate('form.send')}
                                                </Button>
                                            );
                                        }}>
                                        <Typography>
                                            {translate('passwordReset.willBeSent', {
                                                email: user.email,
                                            })}
                                        </Typography>
                                    </ModalContainer>
                                ),
                            },
                        ]}
                    />
                </AccordionDetails>
            </Accordion>
        </PageWrapper>
    );
};

ProfilePage.propTypes = propTypes;

export default connect(mapStateToProps)(ProfilePage);
