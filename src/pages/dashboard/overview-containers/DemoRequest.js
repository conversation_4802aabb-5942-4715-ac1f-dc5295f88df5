import {useState} from 'react';
import PubSub from 'pubsub-js';
import {connect} from 'react-redux';
import {LoadingButton} from '@material-ui/lab';
import {<PERSON><PERSON>, <PERSON><PERSON>, Stack} from '@material-ui/core';
import {requestOptions} from '@w7-3/webeagle-resources/dist/config/contactFormRequests';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {INFO_CODE_LEVELS, NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';
import DefinitionList from '../../../custom/components/utils/DefinitionList';
import Label from '../../../components/Label';
import {fDateTime} from '../../../utils/formatTime';
import useApiCaller from '../../../custom/hooks/useApiCaller';
import {getAPIPath} from '../../../custom/utils/getPath';
import {DemoRequestType} from '../../../config/prop-types/Account';

export const Flags = {
    [requestOptions.demo.states.PENDING]: {
        color: 'warning',
    },
    [requestOptions.demo.states.APPROVED]: {
        color: 'success',
    },
    [requestOptions.demo.states.ACTIVATED]: {
        color: 'info',
    },
    [requestOptions.demo.states.COMPLETED]: {
        color: 'secondary',
    },
    [requestOptions.demo.states.REJECTED]: {
        color: 'error',
    },
};

const propTypes = {
    demoRequest: DemoRequestType,
};

const mapStateToProps = ({state}) => {
    const {demoRequest} = state;

    return {
        demoRequest
    };
};

const DemoRequest = ({
    demoRequest,
}) => {
    const {translate} = useLocales();
    const apiCaller = useApiCaller();
    const [isLoading, setIsLoading] = useState(false);
    const handleDemoRequest = async () => {
        setIsLoading(true);
        apiCaller({
            uri: getAPIPath(api.demo),
            data: {
                id: demoRequest?.id,
            },
            successCallback: () => {
                PubSub.publish('UPDATE.ACCOUNT.RELATED.DATA', {
                    callbacks: {
                        success: (data) => {
                            PubSub.publish('SHOW.NOTIFICATION', {
                                message: translate(`directives.notifications.${NOTIFICATIONS.automationCreditsToppedUpForDemo}`, {
                                    automationCredits: data?.automationCredits,
                                }),
                                variant: INFO_CODE_LEVELS.SUCCESS,
                            });
                        },
                    },
                });
            },
            failureCallback: () => {
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate(`directives.notifications.${NOTIFICATIONS.genericErrorWithRetry}`),
                    variant: 'error',
                });
                setIsLoading(false);
            },
        });
    };

    if (!demoRequest?.state || [
        requestOptions.demo.states.EXPIRED,
    ].includes(demoRequest?.state)) {
        return null;
    }

    const content = (
        <>
            <DefinitionList
                dataList={[
                    {
                        label: translate('demo.application.state.label'),
                        node: (
                            <div>
                                <Label color={Flags?.[demoRequest?.state]?.color} sx={{p: 1.5}}>
                                    {translate(`demo.application.state.options.${demoRequest?.state}`)}
                                </Label>
                            </div>
                        ),
                    },
                    ...demoRequest?.automationCredits ?  [{
                        label: translate('pricing.otp.automationCredits.label'),
                        node: (
                            demoRequest?.automationCredits
                        ),
                    }] : [],
                    {
                        label: translate('demo.application.applicant'),
                        node: (
                            demoRequest?.applicant
                        ),
                    },
                    {
                        label: translate('demo.application.created'),
                        node: (
                            fDateTime(demoRequest?.ts, false)
                        ),
                    },
                    ...demoRequest?.state === requestOptions.demo.states.APPROVED ? [
                        {
                            label: translate('demo.application.approved'),
                            node: (
                                fDateTime(demoRequest?.approvedTs, false)
                            ),
                        },
                    ] : [],
                    ...demoRequest?.state === requestOptions.demo.states.ACTIVATED ? [
                        {
                            label: translate('demo.application.activated'),
                            node: (
                                fDateTime(demoRequest?.approvedTs, false)
                            ),
                        },
                    ] : [],
                    ...demoRequest?.state === requestOptions.demo.states.COMPLETED ? [
                        {
                            label: translate('demo.application.completed'),
                            node: (
                                fDateTime(demoRequest?.completedTs, false)
                            ),
                        },
                    ] : [],
                ]}
            />
            {
                demoRequest?.message && (
                    <Alert severity="info" sx={{my: 3}}>
                        {demoRequest?.message}
                    </Alert>
                )
            }
            {
                demoRequest?.state === requestOptions.demo.states.APPROVED && (
                    <Stack direction="row" justifyContent="flex-end" alignItems="center" sx={{mt: 1}}>
                        <LoadingButton
                            loading={isLoading}
                            type="submit"
                            variant="contained"
                            onClick={handleDemoRequest}
                            sx={{textTransform: 'none'}}>
                            {translate('demo.application.activate')}
                        </LoadingButton>
                    </Stack>
                )
            }
        </>
    );

    return (
        <>
            <DefinitionList
                dataList={[
                    {
                        label: translate('demo.application.state.label'),
                        node: (
                            <div>
                                <Label color={Flags?.[demoRequest?.state]?.color} sx={{p: 1.5}}>
                                    {translate(`demo.application.state.options.${demoRequest?.state}`)}
                                </Label>
                            </div>
                        ),
                    },
                ]}
                variant={DefinitionList.VARIANTS.horizontal}
            />
            <Stack
                flexDirection="row"
                alignItems="center"
                justifyContent="flex-end"
            >
                <Button
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'custom',
                            dialogProps: {
                                title: translate('demo.application.label'),
                            },
                            children: content,
                        });
                    }}
                    sx={{
                        ml: 'auto !important',
                    }}
                >
                    {translate('details')}
                </Button>
            </Stack>
        </>
    );
};

DemoRequest.propTypes = propTypes;

export default connect(mapStateToProps)(DemoRequest);
