import {createSlice} from '@reduxjs/toolkit';
import {plans} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';

const initialState = {
    interval: plans.monthlyX12.appName,
    cartItemCount: 0,
    subscriptionsCartItemList: [],
    collaboratorSeats: [],
    automationCredits: [],
};

const slice = createSlice({
    name: 'pricing',
    initialState,
    reducers: {
        updateInterval(state, action) {
            state.interval = action.payload;
        },

        updatePrices(state, action) {
            state.prices = action.payload;
        },

        updateProducts(state, action) {
            state.products = action.payload;
        },

        updateCartItem(state, action) {
            const {listType, id, values} = action.payload;
            state[listType].forEach((item, index) => {
                if (item.id === id) {
                    state[listType][index] = {
                        ...item,
                        ...values,
                    }
                }
            });
        },

        addToCart(state, action) {
            const {cartItem, listType} = action.payload;
            let isUpdated = false;
            state[listType] = state[listType].map((item) => {
                if (item.priceData.id === cartItem.priceData.id) {
                    item.quantity += cartItem.quantity;
                    isUpdated = true;
                }
                return item;
            });

            if (!isUpdated) {
                state[listType] = [
                    ...state[listType],
                    cartItem,
                ];
            }

            state.cartItemCount = state.subscriptionsCartItemList.length + state.collaboratorSeats.length + state.automationCredits.length;
        },

        deleteFromCart(state, action) {
            const {priceId, listType} = action.payload;

            (listType ? [
                listType
            ] :
                [
                'subscriptionsCartItemList',
                'collaboratorSeats',
            ]).forEach((listType) => {
                state[listType] = state[listType].filter((item) => {
                    return priceId !== item.priceData.id;
                });
            });

            state.cartItemCount = state.subscriptionsCartItemList.length + state.collaboratorSeats.length + state.automationCredits.length;
        },

        resetPricing(state) {
            Object.keys(initialState).forEach((key) => {
                state[key] = initialState[key];
            });
        },

        setDomains(state, action) {
            state.domains = action.payload;
        },
    },
});

export default slice.reducer;

export const {actions} = slice;

export const updateInterval = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.updateInterval(data));
    };
};

export const updatePrices = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.updatePrices(data));
    };
};

export const updateProducts = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.updateProducts(data));
    };
};

export const updateCartItem = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.updateCartItem(data));
    };
};

export const addToCart = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.addToCart(data));
    };
};

export const deleteFromCart = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.deleteFromCart(data));
    };
};

export const resetPricing = () => {
    return async (dispatch) => {
        dispatch(slice.actions.resetPricing());
    };
};

export const setDomains = (data) => {
    return async (dispatch) => {
        dispatch(slice.actions.setDomains(data));
    };
};

