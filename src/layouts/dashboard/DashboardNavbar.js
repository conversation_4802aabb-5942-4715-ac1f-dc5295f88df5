import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {alpha, styled} from '@material-ui/core/styles';
import {AppBar, Box, Container, IconButton, Stack, Toolbar} from '@material-ui/core';
import useCollapseDrawer from '../../hooks/useCollapseDrawer';
import AccountPopover from './AccountPopover';
import LanguagePopover from './LanguagePopover';
import NotificationsPopover from './NotificationsPopover';

const APPBAR_MOBILE = 64;
const APPBAR_DESKTOP = 92;

const RootStyle = styled(AppBar)(({theme}) => ({
    boxShadow: 'none',
    backdropFilter: 'blur(6px)',
    WebkitBackdropFilter: 'blur(6px)',
    backgroundColor: alpha(theme.palette.background.default, 0.72),
}));

const ToolbarStyle = styled(Toolbar)(({theme}) => ({
    width: '100%',
    margin: 'auto',
    minHeight: APPBAR_MOBILE,
    [theme.breakpoints.up('lg')]: {
        minHeight: APPBAR_DESKTOP,
    },
}));

const DashboardNavbar = ({onOpenSidebar}) => {
    const {isCollapse} = useCollapseDrawer();

    return (
        <RootStyle
            sx={{
                ...(isCollapse && {
                    width: '100%',
                }),
            }}
        >
            <ToolbarStyle>
                <Container
                    maxWidth="xl"
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                    }}>
                    <IconButton
                        onClick={onOpenSidebar}
                        color="primary"
                    >
                        <Icon
                            icon="eva:menu-2-fill"
                            width={40}
                            height={40}
                        />
                    </IconButton>
                    <Box sx={{flexGrow: 1}}/>
                    <Stack
                        direction="row"
                        alignItems="center"
                        spacing={{
                            xs: 0.5,
                            sm: 1.5,
                        }}>
                        <LanguagePopover />
                        <NotificationsPopover />
                        <AccountPopover />
                    </Stack>
                </Container>
            </ToolbarStyle>
        </RootStyle>
    );
};

DashboardNavbar.propTypes = {
    onOpenSidebar: PropTypes.func,
};

export default DashboardNavbar;
