import PropTypes from 'prop-types';
import {Link as RouterLink} from 'react-router-dom';
import {Icon} from '@iconify/react';
import {Box, ListItemButton, ListItemText, Typography} from '@material-ui/core';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import doneAllFill from '@iconify/icons-eva/done-all-fill';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import useLocales from '../../../hooks/useLocales';
import {globalConfig} from '../../../config/setup';

const NotificationItem = ({notification, handleClose}) => {
    const {translate} = useLocales();

    return (
        <ListItemButton
            disableGutters
            to={`${PATH_DASHBOARD.management.notificationList}/${notification.id}`}
            onClick={handleClose}
            component={RouterLink}
            sx={{
                py: 1.5,
                px: 2.5,
                mt: '1px',
                ...(notification.isNew && {
                    bgcolor: 'action.selected',
                }),
            }}
        >
            <ListItemText
                primary={translate(`accountNotifications.types.${notification.type}`, {
                    app: globalConfig.domain,
                })}
                secondary={
                    <Typography
                        variant="caption"
                        sx={{
                            mt: 0.5,
                            color: notification.isNew ? 'inherit' : 'text.disabled',
                            display: 'flex',
                            alignItems: 'center',
                        }}
                    >
                        {fDateTimeHumanReadable(notification.ts)}
                        {
                            !notification.isNew &&
                            <Box component="span" sx={{m: 'auto'}}>
                                <Icon icon={doneAllFill} width={40} height={40} />
                            </Box>
                        }
                    </Typography>
                }
            />
        </ListItemButton>
    );
};

NotificationItem.propTypes = {
    notification: PropTypes.object.isRequired,
    handleClose: PropTypes.func,
};

export default NotificationItem;
