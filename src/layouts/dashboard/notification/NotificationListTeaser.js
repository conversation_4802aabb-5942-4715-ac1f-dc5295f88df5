import PropTypes from 'prop-types';
import {Link as RouterLink} from 'react-router-dom';
import {Box, Button, Divider, List, ListSubheader, Stack, Typography} from '@material-ui/core';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {connect} from 'react-redux';
import Scrollbar from '../../../components/Scrollbar';
import useLocales from '../../../hooks/useLocales';
import {NotificationTypes} from '../../../config/prop-types/NotificationTypes';
import {useAccountNotifications} from '../../../hooks/useAccountNotifications';
import NotificationItem from './NotificationItem';

const propTypes = {
    accountNotificationList: PropTypes.arrayOf(NotificationTypes),
    showOldNotifications: PropTypes.bool,
    handleClose: PropTypes.func,
    isPreview: PropTypes.bool,
};

const defaultProps = {
    showOldNotifications: true,
}

const mapStateToProps = ({state}) => {
    const {
        accountNotificationList,
    } = state;

    return {
        accountNotificationList: isNonEmptyArray(accountNotificationList) ? accountNotificationList : [],
    };
};

const NotificationListTeaser = ({
    accountNotificationList,
    showOldNotifications,
    handleClose,
    isPreview,
}) => {
    const {translate} = useLocales();
    const {
        newNotifications,
        oldNotifications,
        newCount,
        oldCount,
    } = useAccountNotifications(accountNotificationList);

    return (
        <Stack spacing={3}>
            <Box sx={{display: 'flex', alignItems: 'center'}}>
                <Box sx={{flexGrow: 1}}>
                    <Typography variant="subtitle1">
                        {translate('accountNotifications.label')}
                    </Typography>
                    <Typography variant="body2" sx={{color: 'text.secondary', pt: 1}}>
                        {translate('accountNotifications.description', {
                            count: newCount,
                        })}
                    </Typography>
                </Box>
            </Box>
            {
                !isPreview && <>
                    <Scrollbar sx={{
                        height: {
                            xs: 340,
                            sm: 'auto',
                        },
                        maxHeight: {
                            lg: '25vh',
                        }}}>
                        {
                            newCount > 0 && (
                                <>
                                    <Divider/>
                                    <List
                                        disablePadding
                                        subheader={
                                            <ListSubheader disableSticky sx={{py: 1, typography: 'overline'}}>
                                                {translate('accountNotifications.new')}
                                            </ListSubheader>
                                        }
                                    >
                                        {newNotifications.map((notification) => (
                                            <NotificationItem
                                                key={notification.id}
                                                notification={notification}
                                                handleClose={handleClose}
                                            />
                                        ))}
                                    </List>
                                </>
                            )
                        }
                        {
                            showOldNotifications && oldCount > 0 && (
                                <>
                                    <Divider/>
                                    <List
                                        disablePadding
                                        subheader={
                                            <ListSubheader disableSticky sx={{py: 1, typography: 'overline'}}>
                                                {translate('accountNotifications.old')}
                                            </ListSubheader>
                                        }
                                        sx={{
                                            mt: 3,
                                        }}
                                    >
                                        {oldNotifications.map((notification) => (
                                            <NotificationItem
                                                key={notification.id}
                                                notification={notification}
                                                handleClose={handleClose}
                                            />
                                        ))}
                                    </List>
                                </>
                            )
                        }
                    </Scrollbar>
                </>
            }
            {
                newCount > 0 && oldCount > 0 && (
                    <>
                        {!isPreview && <Divider/>}
                        <Stack
                            flexDirection="row"
                            alignItems="center"
                            justifyContent="flex-end"
                        >
                            <Button
                                disableRipple
                                component={RouterLink}
                                sx={{textTransform: 'none'}}
                                onClick={handleClose}
                                to={PATH_DASHBOARD.management.notificationList}>
                                {translate('accountNotifications.viewAll')}
                            </Button>
                        </Stack>
                    </>
                )
            }
        </Stack>
    );
};

NotificationListTeaser.propTypes = propTypes;
NotificationListTeaser.defaultProps = defaultProps;

export default connect(mapStateToProps)(NotificationListTeaser);
