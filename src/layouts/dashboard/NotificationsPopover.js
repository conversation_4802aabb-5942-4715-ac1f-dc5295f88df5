import PropTypes from 'prop-types';
import {useRef} from 'react';
import {Icon} from '@iconify/react';
import {Badge, Box, IconButton} from '@material-ui/core';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {connect} from 'react-redux';
import {Link as RouterLink} from 'react-router-dom';
import {NotificationTypes} from '../../config/prop-types/NotificationTypes';

const propTypes = {
    accountNotificationList: PropTypes.arrayOf(NotificationTypes),
};

const mapStateToProps = ({state}) => {
    const {
        accountNotificationList,
    } = state;

    return {
        accountNotificationList: isNonEmptyArray(accountNotificationList) ? accountNotificationList : [],
    };
};

const NotificationsPopover = ({accountNotificationList}) => {
    const anchorRef = useRef(null);
    const newNotificationList = accountNotificationList.filter((item) => item.isNew);

    return (
        <Box>
            <IconButton
                ref={anchorRef}
                size="large"
                color="primary"
                component={RouterLink}
                to={PATH_DASHBOARD.management.notificationList}>
                <Badge badgeContent={newNotificationList.length} color="error">
                    <Icon
                        icon="eva:bell-fill"
                        width={40}
                        height={40}
                    />
                </Badge>
            </IconButton>
        </Box>
    );
};

NotificationsPopover.propTypes = propTypes;

export default connect(mapStateToProps)(NotificationsPopover);
