import {useEffect, useState} from 'react';
import PubSub from 'pubsub-js';

export default (item) => {
    const [open, setOpen] = useState(false);

    const handleOpen = () => {
        setOpen(true);
        PubSub.publish('NAVIGATION.ITEM.OPENED', {
            item,
        });
    };
    const handleClose = () => {
        setOpen(false);
    };

    useEffect(() => {
        PubSub.subscribe('LOCATION.CHANGED', handleClose);
        PubSub.subscribe('NAVIGATION.ITEM.OPENED', (_, payload) => {
            if (payload?.item?.path === item?.path || payload?.id === item?.id) {
                return;
            }

            handleClose()
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return {
        open,
        handleOpen,
        handleClose,
    };
};
