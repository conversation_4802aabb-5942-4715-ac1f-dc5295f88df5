import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {useEffect, useState} from 'react';
import {NavLink as RouterLink, useLocation} from 'react-router-dom';
import {alpha, styled} from '@material-ui/core/styles';
import {
    Box,
    Button,
    Collapse,
    Drawer,
    IconButton,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
} from '@material-ui/core';
import settingsFill from '@iconify/icons-eva/settings-fill';
import PubSub from 'pubsub-js';
import {getMenuConfig} from './MenuConfig';
import NavSection from '../../components/NavSection';
import Scrollbar from '../../components/Scrollbar';
import useLocales from '../../hooks/useLocales';

const ITEM_SIZE = 48;
const PADDING = 2.5;

const ListItemStyle = styled(ListItemButton)(({theme}) => ({
    ...theme.typography.body2,
    height: ITEM_SIZE,
    textTransform: 'capitalize',
    paddingLeft: theme.spacing(PADDING),
    paddingRight: theme.spacing(2.5),
    color: theme.palette.text.secondary,
}));

// ----------------------------------------------------------------------

MenuMobileItem.propTypes = {
    item: PropTypes.object,
    isOpen: PropTypes.bool,
    isActive: PropTypes.bool,
    onOpen: PropTypes.func,
};

function MenuMobileItem({
    item,
    isOpen,
    isActive,
    onOpen,
}) {
    const {
        title,
        path,
        icon,
        children,
    } = item;

    if (children) {
        return (
            <div key={title}>
                <ListItemStyle onClick={onOpen}>
                    <ListItemIcon>{icon}</ListItemIcon>
                    <ListItemText disableTypography primary={title}/>
                    <Box
                        component={Icon}
                        icon={isOpen ? 'eva:arrow-ios-downward-fill' : 'eva:arrow-ios-forward-fill'}
                        sx={{
                            width: 16,
                            height: 16,
                            ml: 1,
                        }}
                    />
                </ListItemStyle>

                <Collapse in={isOpen} timeout="auto" unmountOnExit>
                    <Box sx={{
                        display: 'flex',
                        flexDirection: 'column-reverse',
                    }}>
                        <NavSection
                            navConfig={children}
                        />
                    </Box>
                </Collapse>
            </div>
        );
    }

    return (
        <ListItemStyle
            to={path}
            component={RouterLink}
            sx={{
                ...(isActive && {
                    color: 'primary.main',
                    fontWeight: 'fontWeightMedium',
                    bgcolor: (theme) => alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
                }),
            }}
        >
            <ListItemIcon>{icon}</ListItemIcon>
            <ListItemText disableTypography primary={title}/>
        </ListItemStyle>
    );
}

MenuMobile.propTypes = {
    isOffset: PropTypes.bool,
    isHome: PropTypes.bool,
};

export default function MenuMobile({
    isOffset,
    isHome,
}) {
    const {pathname} = useLocation();
    const [open, setOpen] = useState(false);
    const [mobileOpen, setMobileOpen] = useState(false);
    const {translate} = useLocales();
    const menuConfig = getMenuConfig(translate);

    useEffect(() => {
        if (mobileOpen) {
            handleDrawerClose();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pathname]);

    useEffect(() => {
        PubSub.subscribe('LOCATION.CHANGED', () => {
            setMobileOpen(false);
        });

        return () => {
            PubSub.unsubscribe('LOCATION.CHANGED');
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleDrawerOpen = () => {
        setMobileOpen(true);
    };

    const handleDrawerClose = () => {
        setMobileOpen(false);
    };

    const handleOpen = () => {
        setOpen(!open);
    };

    return (
        <>
            <IconButton
                onClick={handleDrawerOpen}
                sx={{
                    ml: 1,
                    ...(isHome && {color: 'common.white'}),
                    ...(isOffset && {color: 'text.primary'}),
                }}
            >
                <Icon icon="eva:menu-2-fill"/>
            </IconButton>

            <Drawer
                open={mobileOpen}
                onClose={handleDrawerClose}
                ModalProps={{keepMounted: true}}
                PaperProps={{
                    sx: {
                        pb: 5,
                        width: 320,
                    },
                }}
            >
                <Scrollbar>
                    <List disablePadding>
                        {
                            menuConfig
                                .filter((item) => !item.disableOnMobile)
                                .map((link) => (
                                    <MenuMobileItem
                                        key={link.title}
                                        item={link}
                                        isOpen={open}
                                        onOpen={handleOpen}
                                        isActive={pathname === link.path}
                                    />
                                ))}
                    </List>
                    <List sx={{
                        mx: PADDING,
                    }}>
                        <Button
                            startIcon={<Icon icon={settingsFill} width={40} height={40}/>}
                            onClick={() => {
                                setMobileOpen(false);
                                PubSub.publish('SHOW.DIALOG', {
                                    type: 'uiSettings',
                                });
                            }}>
                            {translate('settings.title')}
                        </Button>
                    </List>
                </Scrollbar>
            </Drawer>
        </>
    );
}
