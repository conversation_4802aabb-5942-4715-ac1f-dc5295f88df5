import {Grid, List, ListItem, ListSubheader} from '@material-ui/core';
import {NavLink as RouterLink} from 'react-router-dom';
import propTypes from './props-types';
import IconBullet from './IconBullet';

const Standard = ({
    item,
    pathname,
    handleClose,
}) => {
    const {
        children,
    } = item;

    return (
        <Grid
            container
            spacing={3}
            onMouseLeave={handleClose}>
            {children.map((list) => {
                const {
                    subheader,
                    items,
                } = list;

                return (
                    <Grid key={subheader} item xs={12} md={4}>
                        <List disablePadding>
                            <ListSubheader
                                disableSticky
                                disableGutters
                                sx={{
                                    display: 'flex',
                                    lineHeight: 'unset',
                                    alignItems: 'center',
                                    color: 'text.primary',
                                    typography: 'overline',
                                }}
                            >
                                <IconBullet type="subheader"/> {subheader}
                            </ListSubheader>

                            {items.map((item) => (
                                <ListItem
                                    key={item.title}
                                    to={item.path}
                                    component={RouterLink}
                                    underline="none"
                                    sx={{
                                        p: 0,
                                        mt: 3,
                                        typography: 'body2',
                                        color: 'text.secondary',
                                        transition: (theme) => theme.transitions.create('color'),
                                        '&:hover': {color: 'text.primary'},
                                        ...(item.path === pathname && {
                                            typography: 'subtitle2',
                                            color: 'text.primary',
                                        }),
                                    }}
                                    onClick={handleClose}
                                >
                                    <IconBullet />
                                    {item.title}
                                </ListItem>
                            ))}
                        </List>
                    </Grid>
                );
            })}
        </Grid>
    );
};

Standard.propTypes = propTypes;

export default Standard;
