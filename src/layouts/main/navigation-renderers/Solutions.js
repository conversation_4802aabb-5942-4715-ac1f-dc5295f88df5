import {useRef, useState} from 'react';
import {Button, Grid, Stack} from '@material-ui/core';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {Link as RouterLink} from 'react-router-dom';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {snakeCase} from 'change-case';
import propTypes from './props-types';
import SolutionTeaser from '../../../custom/components/features/SolutionTeaser';
import useLocales from '../../../hooks/useLocales';

const availableSolutions = Object.values(solutions).filter(({configurable}) => configurable);

availableSolutions.sort((a, b) => {
    return a.position - b.position;
});

const Solution = ({
    handleClose,
}) => {
    const {translate} = useLocales();
    const [selectedAppName, setAppName] = useState(availableSolutions[0].appName);
    const ref = useRef(null);

    return (
        <Grid
            container
            spacing={3}
            onMouseLeave={handleClose}
            ref={ref}
        >
            <Grid item xs={12} md={4}>
                <Stack alignItems="center" spacing={3} sx={{mt: 3}}>
                    {
                        availableSolutions.map((solution) => {
                            const {appName} = solution;
                            return (
                                <Button
                                    key={appName}
                                    fullWidth
                                    variant={selectedAppName === appName ? 'contained' : 'outlined'}
                                    sx={{textTransform: 'none'}}
                                    to={`${PATH_PAGE.features}/${snakeCase(appName)}`}
                                    component={RouterLink}
                                    onMouseOver={() => setAppName(appName)}
                                    onClick={handleClose}
                                >
                                    {translate(`project.wizardSteps.solutionsConfigs.groups.${appName}.label`)}
                                </Button>
                            );
                        })
                    }
                </Stack>
            </Grid>
            <Grid item xs={12} md={8}>
                <SolutionTeaser
                    appName={selectedAppName}
                    isLinked
                />
            </Grid>
        </Grid>
    );
};

Solution.propTypes = propTypes;

export default Solution;
