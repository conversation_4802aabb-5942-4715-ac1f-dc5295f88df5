import {Box} from '@material-ui/core';
import PropTypes from 'prop-types';

const propTypes = {
    type: PropTypes.oneOf(['subheader', 'item']),
};

const IconBullet = ({type = 'item'}) => {
    return (
        <Box sx={{
            width: 24,
            height: 16,
            display: 'flex',
            alignItems: 'center',
        }}>
            <Box
                component="span"
                sx={{
                    ml: '2px',
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    bgcolor: 'currentColor',
                    ...(type !== 'item' && {
                        ml: 0,
                        width: 8,
                        height: 2,
                        borderRadius: 2,
                    }),
                }}
            />
        </Box>
    );
};

IconBullet.propTypes = propTypes;

export default IconBullet;

