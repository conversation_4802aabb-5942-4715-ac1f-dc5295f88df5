import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import Standard from './navigation-renderers/Standard';
import Solutions from './navigation-renderers/Solutions';

export const getMenuConfig = (translate) => {
    return [
        {
            title: translate('solutions.label'),
            DesktopRenderer: Solutions,
            disableOnMobile: true,
        },
        {
            title: translate('solutions.label'),
            path: PATH_PAGE.features,
            disableOnDesktop: true,
        },
        {
            title: translate('project.wizard'),
            path: PATH_PAGE.projectWizard,
        },
        {
            title: translate('resources.label'),
            children: [
                {
                    subheader: translate('resources.groups.documentation.label'),
                    items: [
                        {
                            title: translate('faqs.label'),
                            path: PATH_PAGE.faqs,
                        },
                    ]
                },
                {
                    subheader: translate('resources.groups.shortcuts.label'),
                    items: [
                        {
                            title: translate('pricing.label'),
                            path: PATH_PAGE.pricing,
                        },
                        {
                            title: translate('contactUs.label'),
                            path: PATH_PAGE.contact,
                        },
                    ]
                },
                {
                    subheader: translate('resources.groups.legal.label'),
                    items: [
                        {
                            title: translate('termsAndConditions.label'),
                            path: PATH_PAGE.termsAndConditions,
                        },
                        {
                            title: translate('privacyPolicy.label'),
                            path: PATH_PAGE.privacyPolicy,
                        },
                        {
                            title: translate('usageRestrictionPolicy.pageTitle'),
                            path: PATH_PAGE.usageRestrictionPolicy,
                        },
                    ]
                },
            ],
            DesktopRenderer: Standard,
        },
        {
            title: translate('pricing.label'),
            path: PATH_PAGE.pricing,
        },
    ];
};
