import {Link as ScrollLink} from 'react-scroll';
import {styled} from '@material-ui/core/styles';
import {<PERSON><PERSON>, Container, Divider, Grid, Stack, Typography} from '@material-ui/core';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {Icon} from '@iconify/react';
import PubSub from 'pubsub-js';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import Logo from '../../components/Logo';
import useLocales from '../../hooks/useLocales';
import {globalConfig} from '../../config/setup';
import Link from '../../custom/components/utils/Link';
import LanguageChange from '../../custom/components/LanguageChange';
import {getLocalStorageItem} from '../../custom/utils/storage';

/*
const SOCIALS = [
    {
        name: 'Youtube',
        icon: '/static/assets/svg/youtube.svg',
        href: globalConfig.companyYoutube,
        width: `${24}px !important`,
        height: `${24}px !important`,
    },
    {
        name: 'X',
        icon: '/static/assets/svg/x.svg',
        href: globalConfig.companyX,
        width: `${24}px !important`,
        height: `${24}px !important`,
    },
    {
        name: 'Whatsapp',
        icon: '/static/assets/svg/whatsapp.svg',
        href: globalConfig.companyWhatsapp,
        width: `${24}px !important`,
        height: `${24}px !important`,
    },
];
*/

const getLinks = (translate) => [
    {
        headline: globalConfig.domain,
        children: [
            {name: translate('contactUs.label'), href: PATH_PAGE.contact},
            {name: translate('faqs.label'), href: PATH_PAGE.faqs},
        ],
    },
    {
        headline: translate('resources.groups.legal.label'),
        children: [
            {name: translate('termsAndConditions.label'), href: PATH_PAGE.termsAndConditions},
            {name: translate('privacyPolicy.label'), href: PATH_PAGE.privacyPolicy},
            {name: translate('usageRestrictionPolicy.label'), href: PATH_PAGE.usageRestrictionPolicy},
        ],
    },
    /* @todo: implement developer api
    {
        headline: translate('developers.label'),
        children: [
            {name: 'GITHUB', href: 'https://github.com/w7-3/webautomate'},
            {name: 'WebAutomate API', href: PATH_PAGE.api},
        ],
    },
    */
];

const RootStyle = styled('div')(({theme}) => ({
    position: 'relative',
    backgroundColor: theme.palette.background.default,
}));

const MainFooter = () => {
    const {
        currentLang,
        translate,
        onChangeLang,
    } = useLocales();
    const sessionId = getLocalStorageItem('lsid');

    return (
        <RootStyle>
            <Divider/>
            <Container maxWidth="lg" sx={{pt: 10}}>
                <Grid
                    container
                    justifyContent={{xs: 'center', md: 'space-between'}}
                    sx={{textAlign: {xs: 'center', md: 'left'}}}
                >
                    <Grid item xs={12} sx={{mb: 3}}>
                        <ScrollLink to="move_top" spy smooth>
                            <Logo sx={{mx: {xs: 'auto', md: 'inherit'}, cursor: 'pointer'}}/>
                        </ScrollLink>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Stack spacing={3} direction="column">
                            <Typography variant="body2" sx={{pr: {md: 5}}}>
                                {translate('webAutomate.description', {
                                    app: globalConfig.appName,
                                })}
                            </Typography>
                        </Stack>
                    </Grid>
                    <Grid item xs={12} md={5}>
                        <Stack spacing={7} direction={{xs: 'column', md: 'row'}} justifyContent="flex-end">
                            {getLinks(translate).map((list) => {
                                const {headline, children} = list;
                                return (
                                    <Stack key={headline} spacing={2}>
                                        <Typography component="p" variant="overline">
                                            {headline}
                                        </Typography>
                                        {children.map((link) => {
                                            return (
                                                <Link
                                                    key={link.name}
                                                    {...link}
                                                />
                                            );
                                        })}
                                    </Stack>
                                );
                            })}
                        </Stack>
                    </Grid>
                </Grid>
                <Stack
                    spacing={5}
                    direction={{xs: 'column', md: 'row'}}
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{py: 6}}
                >
                    <LanguageChange
                        language={currentLang.value}
                        onChange={onChangeLang}
                    />
                    <Button
                        sx={{alignSelf: 'flex-end', ml: 'auto'}}
                        startIcon={<Icon icon="eva:settings-fill" width={40} height={40}/>}
                        onClick={() => {
                            PubSub.publish('SHOW.DIALOG', {
                                id: 'uiSettings',
                                type: 'uiSettings',
                                isUnique: true,
                            });
                        }}>
                        {translate('settings.title')}
                    </Button>
                </Stack>
                {/* @todo: implement socials
                <Stack
                    spacing={5}
                    direction="row"
                    alignItems="center"
                    justifyContent="center">
                    {SOCIALS.map((social) => (
                        <Button
                            key={social.name}
                            variant="text"
                            href={social.href}
                            target="_blank"
                            color="inherit"
                            sx={{pr: '0 !important'}}
                        >
                            <SvgIconStyle
                                src={social.icon}
                                color="primary"
                                sx={{
                                    height: social.height,
                                    width: social.width,
                                }}
                            />
                        </Button>
                    ))}
                </Stack>
                */}
                <Stack
                    spacing={5}
                    direction={{xs: 'column', md: 'row'}}
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{py: 6}}
                >
                    <Typography
                        component="p"
                        variant="body2"
                        sx={{
                            fontSize: 13,
                            textAlign: {xs: 'center', md: 'left'},
                        }}
                    >
                        © {translate('copyright', {year: new Date().getFullYear(), app: globalConfig.appName})}
                    </Typography>
                    {
                        sessionId && (
                            <CopyToClipboard text={JSON.stringify({sessionId, version: globalConfig.version})}>
                                <Button
                                    variant="text"
                                    startIcon={<Icon icon="eva:copy-fill" width={40} height={40}/>}
                                    sx={{
                                        mt: 3,
                                        display: 'flex',
                                        textTransform: 'none',
                                    }}
                                >
                                    ({translate(('sessionId'))}: {sessionId}/{globalConfig.version})
                                </Button>
                            </CopyToClipboard>
                        )
                    }
                </Stack>
            </Container>
        </RootStyle>
    );
};

export default MainFooter;
