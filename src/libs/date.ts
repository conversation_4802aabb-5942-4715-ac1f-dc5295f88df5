import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import deLocale from 'dayjs/locale/de';
import frLocale from 'dayjs/locale/fr';
import enLocale from 'dayjs/locale/en';

dayjs.extend(utc);
dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.extend(timezone);

export const getLocale = (locale?: string): any => {
    switch (locale) {
    case 'de':
        return deLocale;
    case 'fr':
        return frLocale;
    default:
        return enLocale;
    }
};

export const humanReadableDateFormats = {
    full: 'DD.MM.YYYY HH:mm:ss',
    fullWithSeconds: 'DD.MM.YYYY HH:mm:ss',
    fullWithMilliseconds: 'DD.MM.YYYY HH:mm:ss.SSS',
    fullWithTimezone: 'DD.MM.YYYY HH:mm:ss.SSS Z',
    label: 'YYYY-MM-DD_HH-mm-ss',
    standard: 'DD.MM.YYYY HH:mm',
    ISO: 'YYYY.MM.DD',
    EU: 'DD.MM.YYYY',
};

export const getHumanReadableTimestamp = ({template}: {template: string} = {template: humanReadableDateFormats.full}): string => {
    return dayjs.utc().local().format(template);
};

export const getDateString = (date: number | string, config: Partial<{
    template: string;
    locale: string;
    timeZone: string;
}>): string => {
    const template = config?.template || humanReadableDateFormats.standard;
    const tz = config?.timeZone;

    dayjs.locale(getLocale(config?.locale));

    return dayjs(new Date(date)).tz(tz).utc().format(template);
};

export const getDuration = (durationInMS = 0): string => {
    if (Number.isNaN(durationInMS)) {
        return 'N/A';
    }

    const timeObject = dayjs.duration(durationInMS, 'milliseconds');

    if (durationInMS <= 1000) {
        return `${timeObject.asMilliseconds().toFixed(0)}ms`;
    }

    if (durationInMS <= 1000 * 60) {
        return `${timeObject.asSeconds().toFixed(1)}s`;
    }

    if (durationInMS <= 1000 * 60 * 60) {
        return `${timeObject.asMinutes().toFixed(1)}m`;
    }

    if (durationInMS <= 1000 * 60 * 60 * 24) {
        return `${timeObject.asHours().toFixed(2)}h`;
    }

    return `${timeObject.asDays().toFixed(0)}d`;
};

export const getI18nDuration = (durationInMS = 0, config?: Partial<{
    locale: string;
    humanize: boolean;
    humanizeWithSuffix: boolean;
}>): string => {
    dayjs.locale(getLocale(config?.locale));
    const durationObject = dayjs.duration(durationInMS, 'milliseconds');

    if (config?.humanize) {
        return durationObject.humanize(config.humanizeWithSuffix);
    }

    return durationObject.format('D [days] H [hours] m [minutes] s [seconds]');
};

export default dayjs;
