import path from 'ramda/src/path';
import {packages} from '../../config/catalog/subscriptions';
import type {CheckoutTransaction, Catalog, SubscriptionSummary} from 'administration';

type SubscriptionConsumptionCatalog = Catalog<{
    active: boolean,
    initialContingent: number,
    usedContingent: number,
    restContingent: number,
    restPercentage: number,
    overage: number,
}>;

const initialSubscriptionItem = {
    active: false,
    initialContingent: 0,
    usedContingent: 0,
    restContingent: 0,
    restPercentage: 0,
    overage: 0,
};

export const getSubscriptionItem = ({
    itemPath,
    subscriptionSummary,
}: {
    itemPath: string[],
    subscriptionSummary: null | Pick<
        SubscriptionSummary, 'initialContingent' | 'restContingent'>
}) => {
    if (!subscriptionSummary?.initialContingent || !subscriptionSummary?.restContingent) {
        return initialSubscriptionItem;
    }

    const initialItem = path(itemPath, subscriptionSummary.initialContingent);

    if (!initialItem.active) {
        return initialSubscriptionItem;
    }

    const restItem = path(itemPath, subscriptionSummary.restContingent);
    const initialContingent = initialItem.count;
    const restContingent = restItem.count;
    const usedContingent = initialContingent - restContingent;
    const restPercentage = restContingent > 0
        ? Math.round(((restContingent / initialContingent) * 100) * 100) / 100
        : 0;
    const overage = usedContingent > initialContingent ? usedContingent - initialContingent : 0;

    return {
        active: true,
        initialContingent,
        usedContingent,
        restContingent,
        restPercentage,
        overage,
    };
};

export const getSubscriptionConsumption = ({
    subscription,
}: {
    subscription: CheckoutTransaction<true>,
}): SubscriptionConsumptionCatalog => {
    const result = {} as SubscriptionConsumptionCatalog;
    const {
        data: {
            subscriptionSummary,
        },
    } = subscription;

    [
        ...Object.values(packages.solutions.items).map(({appName}) => ({
            appName,
            data: getSubscriptionItem({
                itemPath: ['solutions', 'items', appName],
                subscriptionSummary,
            }),
        })),
        ...Object.values(packages.administration.items).map(({appName}) => ({
            appName,
            data: getSubscriptionItem({
                itemPath: ['administration', 'items', appName],
                subscriptionSummary,
            }),
        })),
    ].map(({appName, data}) => {
        result[appName] = data;
    });

    return result;
};
