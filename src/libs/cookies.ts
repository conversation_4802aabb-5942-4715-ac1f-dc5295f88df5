import {isNonEmptyString, isPositiveInt} from './validators';

export type CookieType = {
    name: string;
    value: string;
    domain?: string;
    path?: string;
    expires?: string | Date;
    maxAge?: number;
};

export const getCookieValue = (cname: string): string => {
    const value = new RegExp(`${cname}=([^;]+)`).exec(window.document?.cookie);

    if (!Array.isArray(value) || value.length < 2) {
        return '';
    }

    return window.decodeURIComponent(value[1].replace(/^\s+|\s+$/g, ''));
};

export const setCookie = ({
    name,
    value,
    domain,
    path,
    expires,
    maxAge,
}: CookieType) => {
    if (!name || !value) {
        return;
    }

    const props = [
        `${name}=${value}`,
        `path=${isNonEmptyString(path) ? path : '/'}`,
    ];

    if (domain) {
        props.push(`domain=${domain}`);
    }

    if (isNonEmptyString(expires as string)) {
        props.push(`expires=${expires}`);
    }

    if (isPositiveInt(maxAge as number)) {
        props.push(`max-age=${maxAge}`);
    }

    window.document.cookie = `${props.join('; ')};`;
};

export const hasCookie = (cname: string): boolean => {
    return isNonEmptyString(getCookieValue(cname));
};

export const deleteCookie = (cname: string) => {
    if (!isNonEmptyString(cname)) {
        return;
    }

    window.document.cookie = `${cname}=;expires=Thu, 01 Jan 1970 00:00:00 GMT`;
};
