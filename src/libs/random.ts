const numeric = '0123456789';
const alphaLowerCased = 'abcdefghijklmnopqrstuvwxyz';
const alphaUpperCased = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

export const libraryKeys = {
    numeric: 'numeric',
    alphaLowerCased: 'alphaLowerCased',
    alphaUpperCased: 'alphaUpperCased',
    alphanumeric: 'alphanumeric',
    alphaNumericLowerCased: 'alphaNumericLowerCased',
    alphaNumericUpperCased: 'alphaNumericUpperCased',
};

const libraries = {
    [libraryKeys.numeric]: numeric,
    [libraryKeys.alphaLowerCased]: alphaLowerCased,
    [libraryKeys.alphaUpperCased]: alphaUpperCased,
    [libraryKeys.alphanumeric]: `${alphaUpperCased}${alphaLowerCased}${numeric}`,
    [libraryKeys.alphaNumericLowerCased]: `${alphaLowerCased}${numeric}`,
    [libraryKeys.alphaNumericUpperCased]: `${alphaUpperCased}${numeric}`,
};

export const getRandomString = (rawLength = 8, libraryKey: string = libraryKeys.alphanumeric): string => {
    let result = '';
    const library = libraries[libraryKey] || libraries[libraryKeys.alphanumeric];
    const charactersLength = library.length;
    const length = Number.isInteger(rawLength) ? rawLength : 8;

    for (let i = 0; i < length; i++) {
        result += library.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
};
