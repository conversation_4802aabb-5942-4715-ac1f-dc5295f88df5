import getUrlDetails from './getUrlDetails';

export default ({
    url,
    pageUrl,
}: {
    url: string;
    pageUrl: string;
}): boolean => {
    const urlDetails = getUrlDetails(url);
    const pageUrlDetails = getUrlDetails(pageUrl);

    if (
        (!urlDetails.domain || !pageUrlDetails.domain || !urlDetails.subDomain || !pageUrlDetails.subDomain)
        || (urlDetails.domain !== pageUrlDetails.domain)
        || (urlDetails.subDomain === pageUrlDetails.subDomain)
    ) {
        return false;
    }

    if (pageUrlDetails.subDomain !== 'www') {
        return urlDetails.subDomain.endsWith(`.${pageUrlDetails.subDomain}`);
    }

    return true;
};
