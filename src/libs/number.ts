export const getIsValidNumber = (input: any): boolean => {
    if (isNaN(input)) {
        return false;
    }

    return !(typeof input === 'string' && !/\d/.test(input));
};

export const getPercentageValue = (input: number, fractionDigits = 2): number | string => {
    if (!getIsValidNumber(input)) {
        return input;
    }

    return `${(input * 100).toFixed(fractionDigits)}%`;
};

export const getNumberWithSign = (n: number): string => {
    if (n === 0) {
        return '0';
    }

    if (n > 0) {
        return `+ ${Math.abs(n).toString()}`;
    }

    return `- ${Math.abs(n).toString()}`;
};
