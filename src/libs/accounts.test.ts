import {getAutoRenewCreditsSummary} from './accounts';
import type {AccountData} from '../../types/webautomate/global';

describe('{getAutoRenewCreditsSummary}', () => {
    it('should be ok', () => {
        const accountData = {
            customerData: {
                automationCredits: {
                    alerting: [],
                    triggerAt: 1000,
                    autoRenew: true,
                    items: [
                        {
                            priceId: 'price_1QwompQmpu7UZVla4iCEeDou',
                            quantity: 2,
                        },
                        {
                            priceId: 'price_1QwomoQmpu7UZVlaFYGKwJSS',
                            quantity: 1,
                        },
                        {
                            priceId: 'price_1QwomoQmpu7UZVlaFYGKwJSS',
                            quantity: 1,
                            autoRenew: true,
                        },
                    ],
                },
            },
        } as unknown as AccountData;

        const result = getAutoRenewCreditsSummary({
            accountData,
        });
        expect(result).toEqual({
            isActive: true,
            threshold: 1000,
            isDeactivationAllowed: false,
            amount: 424000,
        });
    });
});
