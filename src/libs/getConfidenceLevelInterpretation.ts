import {confidenceLevelThresholds} from '../config/ai';

export default ({value}: {value: number}) => {
    if (value <= confidenceLevelThresholds.low) {
        return {
            code: 'low',
            severity: 'warning',
        };
    }

    if (value >= confidenceLevelThresholds.high) {
        return {
            code: 'high',
            severity: 'error',
        };
    }

    return {
        code: 'medium',
        severity: 'success',
    };
};
