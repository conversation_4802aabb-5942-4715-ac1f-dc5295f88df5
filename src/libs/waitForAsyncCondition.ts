import {isPositiveInt} from './validators';

export default async ({
    asyncCallback,
    interval,
    timeout,
}: {
    asyncCallback: Function,
    interval: number,
    timeout: number,
}): Promise<boolean> => {
    if (![
        interval,
        timeout,
    ].every((item: number): boolean => isPositiveInt(item))) {
        return false;
    }

    let elapsedTime = 0;
    return new Promise((resolve) => {
        const intervalRef = setInterval(async () => {
            const value = await asyncCallback();
            if (value !== true) {
                if (elapsedTime > timeout) {
                    clearInterval(intervalRef);
                    resolve(false);
                    return;
                }

                elapsedTime += interval;
                return;
            }

            clearInterval(intervalRef);
            resolve(true);
        }, interval);
    });
};
