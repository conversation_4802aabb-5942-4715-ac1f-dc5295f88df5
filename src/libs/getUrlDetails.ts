import urlParse from 'url-parse';
import queryString from 'query-string';
import {getDomain, getSubdomain} from 'tldts';
import type {UrlDetails} from 'webautomate/global';

export default (url: string): UrlDetails => {
    const details = urlParse(url);
    const domain = getDomain(details.hostname);
    const subDomain = getSubdomain(details.hostname) || 'www';

    return {
        ...details,
        protocol: details.protocol.replace(':', ''),
        domain,
        subDomain,
        parsedParams: queryString.parse(details.query),
    };
};
