import {OPTIONS} from '../config/scrapper';
import normalizeUrl from './normalizeUrl';
import pick from 'ramda/src/pick';

export type NormalizedUrlOptions = {
    stripAuthentication: boolean;
    stripHash: boolean;
    stripWWW: boolean;
    removeQueryParameters: boolean;
    removeSingleSlash: boolean;
    removeTrailingSlash: boolean;
};

export default (url: string, inputOptions: object | NormalizedUrlOptions = {}): string => {
    const options = {
        ...pick([
            'stripAuthentication',
            'stripHash',
            'stripWWW',
            'removeQueryParameters',
            'removeTrailingSlash',
        ], OPTIONS),
        ...inputOptions,
    };
    options.removeSingleSlash = options.removeTrailingSlash;

    return normalizeUrl(url, options);
};
