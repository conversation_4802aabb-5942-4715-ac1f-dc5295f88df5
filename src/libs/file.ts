import fs from 'fs';
import {v1 as uuidv1} from 'uuid';
import {isNonEmptyString} from './validators';

export const deleteFile = ({filePath}: {filePath: string}): boolean => {
    if (!isNonEmptyString(filePath)) {
        return false;
    }

    try {
        fs.unlinkSync(filePath);

        return true;
    } catch (e) {
        return false;
    }
};

export const deleteFolder = async ({
    folderPath,
    maxRetries = 3,
    recursive = true,
    retryDelay = 1000,
}: Partial<{
    folderPath: string;
    maxRetries: number;
    recursive: boolean;
    retryDelay: number;
}>): Promise<boolean> => {
    if (!isNonEmptyString(folderPath)) {
        return false;
    }

    try {
        await fs.promises.rm(folderPath, {
            maxRetries,
            recursive,
            retryDelay,
        });

        return true;
    } catch (e) {
        return false;
    }
};

export const getFileSize = async ({filePath}: {filePath: string}): Promise<number> => {
    return new Promise((resolve) => {
        try {
            fs.stat(filePath, (err, stats) => {
                if (err) {
                    resolve(0);
                    return;
                }

                resolve(stats.size);
            });
        } catch (e) {
            resolve(0);
        }
    });
};

export const writeFile = async (config: {
    dir?: string,
    name?: string,
    content?: string,
}): Promise<false | string> => {
    return new Promise((resolve) => {
        if (!config?.dir || !config?.content) {
            return resolve(false);
        }

        if (!fs.existsSync(config.dir)) {
            fs.mkdirSync(config.dir, {recursive: true});
        }

        const name: string = config?.name || uuidv1();
        const path = `${config.dir}/${name}`;
        fs.writeFile(path, config.content, (error) => {
            if (error) {
                return resolve(false);
            }

            resolve(path);
        });
    });
};
