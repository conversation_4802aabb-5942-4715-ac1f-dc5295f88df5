// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`getUrlDetails should return url details with "sub" as subdomain 1`] = `
Object {
  "auth": "",
  "domain": "domain.ch",
  "hash": "",
  "host": "sub.domain.ch",
  "hostname": "sub.domain.ch",
  "href": "https://sub.domain.ch/",
  "origin": "https://sub.domain.ch",
  "parsedParams": Object {},
  "password": "",
  "pathname": "/",
  "port": "",
  "protocol": "https",
  "query": "",
  "slashes": true,
  "subDomain": "sub",
  "username": "",
}
`;

exports[`getUrlDetails should return url details with "www" as subdomain 1`] = `
Object {
  "auth": "",
  "domain": "domain.ch",
  "hash": "",
  "host": "domain.ch",
  "hostname": "domain.ch",
  "href": "https://domain.ch/",
  "origin": "https://domain.ch",
  "parsedParams": Object {},
  "password": "",
  "pathname": "/",
  "port": "",
  "protocol": "https",
  "query": "",
  "slashes": true,
  "subDomain": "www",
  "username": "",
}
`;
