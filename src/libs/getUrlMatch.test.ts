import {URL_MATCHER} from '../config/scrapper';
import getUrlMatch from './getUrlMatch';

describe('getUrlMatch', () => {
    it('should be true', () => {
        expect(getUrlMatch(
            'https://stage.brack.ch/?param-1=value-1',
            [{
                id: 'test',
                matcher: URL_MATCHER.regex,
                value: 'stage.brack.ch',
            }]
        )).toBe(true);

        expect(getUrlMatch(
            '*',
            [{
                id: 'test',
                matcher: URL_MATCHER.exact,
                value: '*',
            }]
        )).toBe(true);
        expect(getUrlMatch(
            '/anything',
            [{
                id: 'test',
                matcher: URL_MATCHER.regex,
                value: '*',
            }]
        )).toBe(true);
    });

    it('should be false', () => {
        expect(getUrlMatch(
            'https://stage.brack.ch/?param-1=value-1',
            [{
                id: 'test',
                matcher: URL_MATCHER.exact,
                value: '*',
            }]
        )).toBe(false);
    });
});
