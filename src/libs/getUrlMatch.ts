import {URL_MATCHER} from '../config/scrapper';
import {URL_MATCHER_REGEX_FLAGS} from '../config/misc';
import {isNonEmptyArray, isNonEmptyString} from './validators';
import type {UrlMatchItem} from 'webautomate/global';

export default (rawUrl: string, list: UrlMatchItem[], anyMatch = true): boolean => {
    if (!isNonEmptyArray(list) || !isNonEmptyString(rawUrl)) {
        return false;
    }

    const validList = list.filter(({value, matcher}) => {
        if (!isNonEmptyString(value) || !URL_MATCHER[matcher]) {
            return false;
        }

        if (matcher !== URL_MATCHER.regex) {
            return true;
        }

        try {
            new RegExp(`/${value}`, URL_MATCHER_REGEX_FLAGS);
            return true;
        } catch (e) {
            return false;
        }
    });

    if (validList.length < 1) {
        return false;
    }

    const url = rawUrl.toLowerCase();
    let matchCount = 0;

    for (let i = 0; i < validList.length; i++) {
        let isAMatch = false;
        const {
            value,
            matcher,
        } = validList[i];

        if (
            (matcher === URL_MATCHER.exact && url === value)
            || (matcher === URL_MATCHER.substring && url.indexOf(value) >= 0)
        ) {
            isAMatch = true;
        } else if (matcher === URL_MATCHER.regex) {
            isAMatch = new RegExp(`/${value}`, URL_MATCHER_REGEX_FLAGS).test(url);
        }

        if (isAMatch && anyMatch) {
            return true;
        }

        matchCount += isAMatch ? 1 : 0;
    }

    return matchCount === validList.length;
};
