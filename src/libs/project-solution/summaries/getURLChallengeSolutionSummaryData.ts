import type {
    ProjectBuildOverviewData,
    SolutionSummaryData,
    SolutionSummaryURLChallenge,
    URLChallengeState,
} from 'solutions';
import translate from '../../../i18n/translate';
import {getDelta} from '../getDeltaData';
import solutions from '../../../config/solutions';
import {getSolutionSummaryDataItems, getSolutionStepsSummaryData} from './utils';
import {urlChallengeData} from '../../../config/urlChallenge';

const appName = solutions.urlChallenge.appName;

export default ({
    language,
    projectBuildOverviewData,
    previousProjectBuildOverviewData,
}: {
    language: string,
    projectBuildOverviewData: ProjectBuildOverviewData,
    previousProjectBuildOverviewData: ProjectBuildOverviewData,
}): SolutionSummaryData['urlChallenge'] => {
    const currentSummary = (projectBuildOverviewData.solutionSummary as SolutionSummaryURLChallenge)?.urlChallenge;
    const previousSummary = (previousProjectBuildOverviewData?.solutionSummary as SolutionSummaryURLChallenge)?.urlChallenge;
    const solution = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.label`);
    const label = translate(language, 'project.build.report.summary.label', {
        solution,
    });
    const items: SolutionSummaryData['urlChallenge']['items'] = getSolutionSummaryDataItems({
        language,
        results: currentSummary?.results,
        previousResults: previousSummary?.results,
    }).items;

    const additionalItems: Partial<SolutionSummaryData['urlChallenge']['additionalItems']> = {};
    [
        {data: urlChallengeData.participants, isPositive: undefined},
        {data: urlChallengeData.totalChallenges, isPositive: undefined},
        {data: urlChallengeData.passedChallenges, isPositive: true},
        {data: urlChallengeData.failedOrVoidChallenges, isPositive: false},
    ].forEach(({data, isPositive}) => {
        const state = data as URLChallengeState;
        const value = currentSummary?.additionalResults?.[state];
        const label = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.labels.${state}.label`);

        additionalItems[state] = {
            label,
            value: String(value),
            delta: getDelta({
                language,
                change: value - previousSummary?.additionalResults?.[state],
                isPositive,
            }),
        };
    });

    const {steps} = getSolutionStepsSummaryData({
        language,
        projectBuildSteps: currentSummary?.steps || {},
        previousProjectBuildSteps: previousSummary?.steps || {},
    });
    const participants = currentSummary?.additionalResults?.participants || 0;
    const description = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.captured`, {
        count: currentSummary?.results?.success || 0,
        participants: `${participants} ${translate(language, 'url', {
            count: currentSummary?.additionalResults?.participants || 0,
        })}`,
    });

    return {
        label,
        description,
        items,
        additionalItems,
        steps,
    } as SolutionSummaryData['urlChallenge'];
};
