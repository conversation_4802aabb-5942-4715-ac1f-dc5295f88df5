import type {
    ProjectBuildOverviewData,
    SolutionSummaryData,
    SolutionSummaryScreenshots,
} from 'solutions';
import translate from '../../../i18n/translate';
import solutions from '../../../config/solutions';
import {getSolutionStepsSummaryData, getSolutionSummaryDataItems} from './utils';

const appName = solutions.screenshots.appName;

export default ({
    language,
    projectBuildOverviewData,
    previousProjectBuildOverviewData,
}: {
    language: string,
    projectBuildOverviewData: ProjectBuildOverviewData,
    previousProjectBuildOverviewData: ProjectBuildOverviewData,
}): SolutionSummaryData['screenshots'] => {
    const currentSummary = (projectBuildOverviewData.solutionSummary as SolutionSummaryScreenshots)?.screenshots;
    const previousSummary = (previousProjectBuildOverviewData?.solutionSummary as SolutionSummaryScreenshots)?.screenshots;
    const solution = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.label`);
    const label = translate(language, 'project.build.report.summary.label', {
        solution,
    });
    const items: SolutionSummaryData['screenshots']['items'] = getSolutionSummaryDataItems({
        language,
        results: currentSummary?.results,
        previousResults: previousSummary?.results,
    }).items;
    const {steps} = getSolutionStepsSummaryData({
        language,
        projectBuildSteps: currentSummary?.steps || {},
        previousProjectBuildSteps: previousSummary?.steps || {},
    });
    const description = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.captured`, {
        count: currentSummary?.results?.success || 0,
    });

    return {
        label,
        description,
        items,
        steps,
    };
};
