import type {
    LighthouseCategory,
    LinkCheckerLighthouseDevice,
    ProjectBuildOverviewData,
    SolutionSummaryData,
    SolutionSummaryLighthouse,
} from 'solutions';
import translate from '../../../i18n/translate';
import solutions from '../../../config/solutions';
import {getDelta} from '../getDeltaData';
import {LIGHTHOUSE_DEVICES} from '../../../config/scrapper';
import {isPositiveInt} from '../../validators';
import {getSolutionSummaryDataItems} from './utils';
import {LIGHTHOUSE_FIELDS} from '../../../config/lighthouse';

const appName = solutions.lighthouse.appName;

export default ({
    language,
    projectBuildOverviewData,
    previousProjectBuildOverviewData,
}: {
    language: string,
    projectBuildOverviewData: ProjectBuildOverviewData,
    previousProjectBuildOverviewData: ProjectBuildOverviewData,
}): SolutionSummaryData['lighthouse'] => {
    const currentSummary = (projectBuildOverviewData.solutionSummary as SolutionSummaryLighthouse)?.lighthouse;
    const previousSummary = (previousProjectBuildOverviewData?.solutionSummary as SolutionSummaryLighthouse)?.lighthouse;
    const solution = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.label`);
    const label = translate(language, 'project.build.report.summary.label', {
        solution,
    });
    const items: SolutionSummaryData['lighthouse']['items'] = getSolutionSummaryDataItems({
        language,
        results: {
            success: (currentSummary?.desktop?.results?.success || 0) + (currentSummary?.mobile?.results?.success || 0),
            failure: (currentSummary?.desktop?.results?.failure || 0) + (currentSummary?.mobile?.results?.failure || 0),
        },
        previousResults: {
            success: (previousSummary?.desktop?.results?.success || 0) + (previousSummary?.mobile?.results?.success || 0),
            failure: (previousSummary?.desktop?.results?.failure || 0) + (previousSummary?.mobile?.results?.failure || 0),
        },
    }).items;

    const additionalItems: Partial<SolutionSummaryData['lighthouse']['additionalItems']> = {};
    let total = 0;
    [
        LIGHTHOUSE_DEVICES.desktop.key,
        LIGHTHOUSE_DEVICES.mobile.key,
    ].forEach((deviceKey: string) => {
        const device = deviceKey as LinkCheckerLighthouseDevice;
        if (!isPositiveInt(currentSummary?.[device]?.results?.success)) {
            return;
        }

        total += currentSummary?.[device]?.results?.success || 0;
        // @ts-expect-error this is a valid key
        additionalItems[device] = {};
        Object.values(LIGHTHOUSE_FIELDS.categories).forEach((categoryKey: string) => {
            const category = categoryKey as LighthouseCategory;
            const value = currentSummary?.[device]?.additionalResults?.[category] || 0;

            // @ts-expect-error this is a valid key
            additionalItems[device][category] = {
                label: translate(language, `project.wizardSteps.solutionsConfigs.groups.lighthouse.categories.fields.${category}.label`),
                value,
                delta: getDelta({
                    language,
                    change: value - previousSummary?.[device]?.additionalResults?.[category],
                    isPositive: true,
                }),
            };
        });
    });

    const description = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.captured`, {
        count: total,
    });

    return {
        label,
        description,
        items,
        additionalLabel: translate(language, 'averageValues'),
        additionalItems,
    } as SolutionSummaryData['lighthouse'];
};
