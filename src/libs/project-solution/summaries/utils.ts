import type {
    BasicSolutionSummaryDataItems,
    SolutionSummaryResults,
    SolutionSummarySteps,
    SolutionSummaryStepsData,
} from 'solutions';
import translate from '../../../i18n/translate';
import {failureColor, ignoreColor, successColor} from '../config';
import {getDelta} from '../getDeltaData';
import {solutionStepTypes} from '../../../config/project';

export const getSolutionSummaryDataItems = ({
    language,
    results,
    previousResults,
}: {
    language: string,
    results: SolutionSummaryResults,
    previousResults: SolutionSummaryResults,
}): {
    items: BasicSolutionSummaryDataItems,
} => {
    const items: Partial<BasicSolutionSummaryDataItems> = {};
    let total = 0;
    let totalPrevious = 0;

    total += results?.success || 0;
    totalPrevious += previousResults?.success || 0;
    items.success = {
        label: translate(language, 'project.build.report.results.success'),
        value: results?.success,
        color: results?.success > 0 ? successColor : ignoreColor,
        delta: getDelta({
            language,
            change: results?.success - previousResults?.success,
            isPositive: true,
        }),
    };


    total += results?.failure || 0;
    totalPrevious += previousResults?.failure || 0;
    items.failure = {
        label: translate(language, 'project.build.report.results.failure'),
        value: results?.failure,
        color: results?.failure > 0 ? failureColor : ignoreColor,
        delta: getDelta({
            language,
            change: results?.failure - previousResults?.failure,
            isPositive: false,
        }),
    };

    items.total = {
        label: translate(language, 'project.build.report.results.total'),
        value: total,
        color: '',
        delta: getDelta({
            language,
            change: total - totalPrevious,
        }),
    };

    return {
        items,
    } as {
        items: BasicSolutionSummaryDataItems,
    };
};

export const getSolutionStepsSummaryData = ({
    language,
    projectBuildSteps,
    previousProjectBuildSteps,
}: {
    language: string,
    projectBuildSteps: SolutionSummarySteps,
    previousProjectBuildSteps: SolutionSummarySteps,
}): {
    steps: SolutionSummaryStepsData,
} => {
    const steps: Partial<SolutionSummaryStepsData> = {};

    [
        solutionStepTypes.action,
        solutionStepTypes.loop,
        solutionStepTypes.condition,
    ].forEach((key: string) => {
        const step = key as keyof SolutionSummarySteps;
        const value = projectBuildSteps[step] || 0;
        const previousValue = previousProjectBuildSteps[step] || 0;
        steps[step as keyof SolutionSummarySteps] = {
            label: translate(language, `workflowSteps.options.${step}.label`),
            value,
            delta: getDelta({
                language,
                change: value - previousValue,
            }),
        };
    });

    return {
        steps,
    } as {
        steps: SolutionSummaryStepsData,
    };
};
