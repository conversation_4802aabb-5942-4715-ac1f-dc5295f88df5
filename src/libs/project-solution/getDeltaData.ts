import getTendencyColorForSuccess from './getTendencyColorForSuccess';
import getTendencyColorForFailure from './getTendencyColorForFailure';
import translate from '../../i18n/translate';
import {
    getIsValidNumber,
    getNumberWithSign,
} from '../number';
import type {SolutionDataItem} from 'webautomate/global';
import {ignoreColor} from './config';

export const getDelta = ({
    language,
    change,
    isPositive,
}: {
    language: string;
    change: number;
    isPositive?: boolean;
}): SolutionDataItem => {
    const changeLabel = translate(language, 'project.build.report.summary.delta');
    const notAvailableLabel = translate(language, 'notAvailable');

    if (!getIsValidNumber(change)) {
        return {
            isInvalid: true,
            label: changeLabel,
            color: ignoreColor,
            value: notAvailableLabel,
        };
    }

    const color = typeof isPositive === 'undefined' ? ignoreColor
        : isPositive ? getTendencyColorForSuccess(change) : getTendencyColorForFailure(change);

    return {
        label: changeLabel,
        color,
        value: getNumberWithSign(change),
    };
};

export default ({language, successChange, failureChange}: {
    language: string;
    successChange: number;
    failureChange: number;
}): {
    success: SolutionDataItem;
    failure: SolutionDataItem;
} => {
    return {
        success: getDelta({
            language,
            change: successChange,
            isPositive: true,
        }),
        failure: getDelta({
            language,
            change: failureChange,
            isPositive: false,
        }),
    };
};
