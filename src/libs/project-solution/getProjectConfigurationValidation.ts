import solutions from '../../config/solutions';
import {isNonEmptyArray, isNonEmptyObject} from '../validators';
import {MAX_PROJECT_NAME_LENGTH, steps} from '../../config/project';
import {PROJECT_VALIDATION} from '../../config/infoCodes';
import type {ProjectConfig} from 'webautomate/global';
import type {
    LighthouseProjectBuildConfig, ProjectBuildStep} from 'project';

export default ({
    item,
    list = [],
}: {
    item: ProjectConfig;
    list?: ProjectConfig[];
}): {
    success: boolean;
    step?: string;
    reason?: typeof PROJECT_VALIDATION[keyof typeof PROJECT_VALIDATION];
    data?: {
        matchingConfig: ProjectConfig;
    };
} => {
    if (!isNonEmptyObject(item?.data)) {
        return {
            success: false,
            step: steps.void.key,
            reason: PROJECT_VALIDATION.INVALID_CONFIG,
        };
    }

    const {
        projectName,
        solution,
        target,
        projectId,
    } = item.data;

    if (!projectName) {
        return {
            success: false,
            step: steps.void.key,
            reason: PROJECT_VALIDATION.PROJECT_NAME_MISSING,
        };
    }

    if (projectName.length > MAX_PROJECT_NAME_LENGTH) {
        return {
            success: false,
            step: steps.void.key,
            reason: PROJECT_VALIDATION.PROJECT_NAME_TOO_LONG,
        };
    }

    if (!solution?.key || !(solution.key in solutions)) {
        return {
            success: false,
            step: steps.solutionSelection.key,
            reason: PROJECT_VALIDATION.PROJECT_SOLUTION_MISSING_MISSING,
        };
    }

    if (!projectId) {
        return {
            success: true,
            step: steps.void.key,
            reason: PROJECT_VALIDATION.PROJECT_SOLUTION_MISSING_MISSING,
        };
    }

    const matchingConfig = list.find((configItem) => configItem?.data?.projectName === projectName);

    if (matchingConfig && matchingConfig?.data?.projectId !== item?.data?.projectId) {
        return {
            success: false,
            step: steps.void.key,
            reason: PROJECT_VALIDATION.PROJECT_NAME_ALREADY_EXISTS,
            data: {
                matchingConfig,
            },
        };
    }

    if (!solution || !isNonEmptyArray(target?.urls)) {
        return {
            success: false,
            step: steps.target.key,
            reason: PROJECT_VALIDATION.URLS_MISSING,
        };
    }

    if (solutions.lighthouse.appName === solution?.key) {
        const solutionConfig = solution?.config as LighthouseProjectBuildConfig;

        return {
            success: isNonEmptyArray(solutionConfig?.categoryList),
            step: steps.solutionsConfigs.key,
            reason: PROJECT_VALIDATION.LIGHT_HOUSE_CATEGORY_LIST_MISSING,
        };
    }

    if (!isNonEmptyObject(target?.device)) {
        return {
            success: false,
            step: steps.target.key,
            reason: PROJECT_VALIDATION.DEVICE_CONFIG_INVALID,
        };
    }

    if ([
        solutions.dataExtractions.key,
        solutions.e2eVisualTests.key,
        solutions.screenVideos.key,
        solutions.screenshots.key,
        // @ts-expect-error this is a valid key
    ].includes(solution?.key)) {
        const solutionConfig = solution?.config as {
            stepList: Array<ProjectBuildStep>;
        };

        return {
            success: isNonEmptyArray(solutionConfig?.stepList),
            step: steps.solutionsConfigs.key,
            reason: PROJECT_VALIDATION.STEPS_MISSING,
        };
    }

    return {
        success: true,
    };
};
