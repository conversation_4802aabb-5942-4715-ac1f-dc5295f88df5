import type {LinkB<PERSON>R<PERSON>ult, LinkCheckerSolutionDetails} from 'solutions';
import translate from '../../../i18n/translate';
import {getDateString, humanReadableDateFormats} from '../../date';

export default ({
    language,
    timeZone,
    buildResult,
}: {
    language: string,
    timeZone: string,
    buildResult: LinkBuildResult,
}): unknown => {
    const responseSummary = buildResult.responseSummary as LinkCheckerSolutionDetails;

    return {
        requestedUrl: {
            label: translate(language, 'project.requestedURL'),
            value: buildResult.requestedUrl,
        },
        resolvedUrl: {
            label: translate(language, 'project.resolvedURL'),
            value: responseSummary.resolvedUrl,
        },
        result: {
            label: translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.result.label'),
            value: responseSummary.success
                ? translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.result.success')
                : translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.result.failure'),
        },
        status: {
            label: translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.status'),
            value: responseSummary.status,
        },
        statusText: {
            label: translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.statusText'),
            value: responseSummary.statusText,
        },
        contentType: {
            label: translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.contentType'),
            value: responseSummary.contentType,
        },
        start: {
            label: translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.start'),
            value: getDateString(responseSummary.startTime, {
                template: humanReadableDateFormats.fullWithMilliseconds,
                locale: language,
                timeZone,
            }),
        },
        end: {
            label: translate(language, 'project.wizardSteps.solutionsConfigs.groups.linkChecker.end'),
            value: getDateString(responseSummary.endTime, {
                template: humanReadableDateFormats.fullWithMilliseconds,
                locale: language,
                timeZone,
            }),
        },
    };
};
