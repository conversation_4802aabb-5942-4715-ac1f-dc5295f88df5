import translate from '../../i18n/translate';
import {getDateString} from '../date';

export default ({language, nextBuildList, timeZone}: {
    language: string;
    nextBuildList: number[];
    timeZone: string;
}): string => {
    const nextExecution = nextBuildList?.[0];
    if (!nextExecution) {
        return '';
    }

    return translate(language, 'project.build.report.nextExecution.label', {
        dateTime: getDateString(nextExecution, {locale: language}),
        timeZone,
    });
};
