import {resultChildCollectionSeparator} from '../config/collections';
import {isNonEmptyArray} from './validators';

export default (pageIdList: Array<string>): string => {
    const {
        prefix,
        separator,
    } = isNonEmptyArray(pageIdList) ? {
        prefix: '/',
        separator: `/${resultChildCollectionSeparator}`,
    } : {
        prefix: '',
        separator: '',
    };

    return `${prefix}${pageIdList.join(`${separator}/`)}${separator}`;
};
