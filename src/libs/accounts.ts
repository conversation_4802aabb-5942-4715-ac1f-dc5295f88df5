import {isNonEmptyArray, isNonEmptyObject} from './validators';
import {
    VERIFICATION_STATE,
} from '../config/domains';
import type {AccountData, DomainVerification} from 'webautomate/global';
import credits from '../config/catalog/credits';

export const getBillingCycleId = ({
    year,
    month,
}: {
    year: number;
    month: number;
}): string => {
    return `${year}-${String(month).padStart(2, '0')}`;
};

export const getPreviousBillingCycleId = (currentData: {
    year: number;
    month: number;
}): string => {
    const month = currentData.month === 1 ? 12 : currentData.month - 1;
    const year = currentData.month === 1 ? currentData.year - 1 : currentData.year;

    return getBillingCycleId({
        year,
        month,
    });
};

export const getVerifiedDomains = ({
    accountData,
}: {
    accountData: AccountData;
}): string[] => {
    if (!isNonEmptyObject(accountData.domainVerifications)) {
        return [];
    }

    return Object.values(accountData.domainVerifications)
        .filter((domainVerification: DomainVerification) => {
            return domainVerification?.state === VERIFICATION_STATE.VERIFIED && domainVerification.expiry! > Date.now();
        })
        .map((domainVerification: DomainVerification) => domainVerification.id);
};

export const getAutoRenewCreditsSummary = ({
    accountData,
}: {
    accountData: AccountData;
}): {
    isActive: boolean;
    isDeactivationAllowed: boolean;
    threshold: number;
    amount: number;
} => {
    const {automationCredits} = accountData?.customerData || {};
    if (!automationCredits?.autoRenew || !isNonEmptyArray(automationCredits?.items)) {
        return {
            isActive: false,
            isDeactivationAllowed: false,
            threshold: 0,
            amount: 0,
        };
    }

    const isDeactivationAllowed = accountData?.lifeTimeAutomationCredits?.value >= 0;
    const amount = automationCredits.items.reduce((acc, item) => {
        const creditItem = Object.values(credits.items).find((i) => i.priceId === item.priceId);

        if (!creditItem) {
            return acc;
        }

        return acc + creditItem.quantity * item.quantity;
    }, 0);

    return {
        isActive: true,
        isDeactivationAllowed,
        threshold: automationCredits.triggerAt,
        amount,
    };
};

