import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import dbInstance from '../setup/setupDB';
import type {ProjectConfig} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async ({
    accountId,
    projectId,
    configDataMap,
}: {
    accountId: string,
    projectId: string,
    configDataMap: Record<string, Record<string, any>>,
}): Promise<Record<string, any>> => {
    if (!accountId) {
        return {};
    }

    const {
        collections: {
            builds,
        },
    } = getPaths({
        accountId,
        projectId,
    });

    const [, resultsMap] = await dao.getAllCollectionData({
        collection: builds,
    });

    if (!isNonEmptyObject(resultsMap)) {
        return {};
    }

    const results = {};
    await Promise.all(Object.keys(resultsMap).map(async (buildId) => {
        const {
            collections,
        } = getPaths({
            accountId,
            projectId,
            buildId: Number(buildId),
        });
        results[buildId] = {
            data: resultsMap[buildId],
            events: {},
            solutionResults: {},
        };

        results[buildId].data.configData = configDataMap[results[buildId].data.configId];
        if (!results[buildId].data.configData) {
            const [, projectConfig] = await dao.getDocumentData<ProjectConfig>({
                path: `${collections.configs}/${results[buildId].data.configId}`,
            });

            results[buildId].data.configData = projectConfig?.data;
        }

        await dao.getAllCollectionData({
            collection: builds,
        });
        const [, buildEvents] = await dao.getAllCollectionData({
            collection: collections.buildEvents,
        });

        results[buildId].events = buildEvents;

        const [, solutionResults] = await dao.getAllCollectionData({
            collection: collections.buildResults,
        });

        results[buildId].solutionResults = solutionResults;
    }));

    return results;
};
