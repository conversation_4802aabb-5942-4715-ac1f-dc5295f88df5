import type OpenAI from 'openai';

export type CreateOpenAIAssistantParams = {
    name: string;
    instructions: string;
    model?: string;
    tools?: OpenAI.Beta.Assistants.AssistantTool[];
    response_format?: OpenAI.Beta.Threads.AssistantResponseFormatOption;
    temperature?: number;
    top_p?: number;
};

export type Contents = {
    filePath: string;
    name: string;
}[];

export type Message = {
    role: string;
    content: string;
};

