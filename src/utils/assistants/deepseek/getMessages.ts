import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getIsValidNumber} from '@w7-3/webeagle-resources/dist/libs/number';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import type OpenAI from 'openai';
import type {Message} from '@w7-3/webeagle-resources/types/assistant';

const timeout = 1000 * 60 * 5;

export type GetMessages = {
    assistantId: string,
    threadId?: string,
    runId?: string,
    content: any, // @todo refine type
    sort?: 'asc' | 'desc',
    limit?: number,
};

export const getMessages = async (
    openai: OpenAI,
    {
        assistantId,
        threadId,
        runId,
        content,
        sort,
        limit,
    }: GetMessages): Promise<{
    success: boolean,
    messages?: Message[],
    error?: Error,
}> => {
    try {
        let thread = threadId ? await openai.beta.threads.retrieve(threadId) : null;

        if (!thread?.id) {
            thread = await openai.beta.threads.create();
        }

        await openai.beta.threads.messages.create(
            thread.id,
            {
                role: 'user',
                content,
            }
        );

        let run = runId ? await openai.beta.threads.runs.retrieve(thread.id, runId) : null;

        if (!run) {
            run = await openai.beta.threads.runs.create(
                thread.id,
                {assistant_id: assistantId}
            );
        }

        if (!run) {
            return {
                success: false,
            };
        }

        let interval: string | number | NodeJS.Timeout | undefined;
        const intervalDuration = 1000;
        const [error] = await awaitOn(new Promise<void>((resolve) => {
            interval = setInterval(async () => {
                run = await openai.beta.threads.runs.retrieve(thread!.id, run!.id);

                if (run!.status !== 'completed') {
                    return;
                }

                clearInterval(interval);
                resolve();
            }, intervalDuration);
        }), {
            timeout,
        });

        if (interval) {
            clearInterval(interval);
        }

        if (error) {
            return {
                success: false,
                error,
            };
        }

        let threadMessages = await openai.beta.threads.messages.list(thread.id);
        if (threadMessages.data.length === 1) {
            await new Promise((resolve) => setTimeout(resolve, intervalDuration * 10));
            threadMessages = await openai.beta.threads.messages.list(thread.id);
        }
        const messages: Message[] = [];

        if (isNonEmptyArray(threadMessages.data)) {
            if (sort === 'asc') {
                threadMessages.data.reverse();
            }
            for (let i = 0; i < threadMessages.data.length; i++) {
                if (getIsValidNumber(limit) && i >= limit!) {
                    break;
                }

                const item = threadMessages.data[i];
                const text = (item.content[0] as any)?.text;
                const content = (text?.value || '').replaceAll(/【.*?】/g, '');

                messages.push({
                    id: item.id,
                    threadId: item?.thread_id,
                    runId: item?.run_id as string,
                    role: item.role,
                    createdAt: item.created_at * 1000,
                    content,
                });
            }
        }

        return {
            success: true,
            messages,
        };
    } catch (e) {
        const error = e as Error;
        return {
            success: false,
            error,
        };
    }
};
