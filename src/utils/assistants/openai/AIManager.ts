import fse from 'fs-extra';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import chillaxe from '@w7-3/webeagle-resources/dist/libs/chillaxe';
import logger from '../../logger';
import type OpenAI from 'openai';
import type {
    Contents,
    CreateOpenAIAssistantParams,
} from './types';

const createTimeout = 120000;
const uploadFilesTimeout = 120000;
const vectorStoreTimeout = 120000;
const vectorStoreInterval = 30000;
const cleanupTimeout = 120000;

export class AIManager {
    openai: OpenAI;
    name!: string;
    assistantId!: string;
    vectorStoreId!: string;
    fileIds: string[] = [];

    constructor(openai: OpenAI) {
        this.openai = openai;
    }

    createAssistant = async ({
        name,
        instructions,
        model = 'gpt-4o',
        tools = [
            {type: 'file_search'},
            {type: 'code_interpreter'},
        ],
        response_format = {
            type: 'json_object',
        },
        temperature = 0.25,
        top_p = 0.5,
    }: CreateOpenAIAssistantParams) => {
        const [error, assistant] = await awaitOn(this.openai.beta.assistants.create({
            name,
            instructions,
            model,
            tools,
            response_format,
            temperature,
            top_p,
        }), {
            timeout: createTimeout,
        });

        if (error) {
            throw error;
        }

        this.name = name;
        this.assistantId = assistant!.id;
    };

    async waitForVectorStoreReady(): Promise<boolean> {
        if (!this.vectorStoreId) {
            return false;
        }

        let res = false;
        const maxAttempts = Math.ceil(vectorStoreTimeout / vectorStoreInterval);

        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const vectorStore = await this.openai.vectorStores.retrieve(this.vectorStoreId);

            if (vectorStore.status === 'completed') {
                const files = await this.openai.vectorStores.files.list(this.vectorStoreId);
                const incomplete = files.data.filter((file) => file.status !== 'completed');

                if (incomplete.length === 0) {
                    res = true;
                    break;
                }
            }

            await chillaxe(vectorStoreInterval);
        }

        return res;
    }

    uploadFilesIntoKnowledgeBase = async (
        contents: Contents,
    ): Promise<{
        success: boolean;
        isVectorStoreReady: boolean;
    }> => {
        await awaitOn(
            Promise.all(
                contents.map(async ({filePath}) => {
                    const file = await this.openai.files.create({
                        file: fse.createReadStream(filePath),
                        purpose: 'assistants',
                    });

                    this.fileIds.push(file.id);
                }),
            ),
            {
                timeout: uploadFilesTimeout,
            }
        );

        if (!isNonEmptyArray(this.fileIds)) {
            return {
                success: false,
                isVectorStoreReady: false,
            };
        }

        const [vectorStoreError, vectorStore] = await awaitOn(this.openai.vectorStores.create({
            name: this.name,
            file_ids: this.fileIds,
            expires_after: {
                anchor: 'last_active_at',
                days: 1,
            },
        }));

        if (vectorStoreError || !vectorStore) {
            return {
                success: false,
                isVectorStoreReady: false,
            };
        }

        this.vectorStoreId = vectorStore.id;
        await this.openai.beta.assistants.update(this.assistantId, {
            tool_resources: {
                file_search: {
                    vector_store_ids: [vectorStore.id],
                },
            },
        });
        const isVectorStoreReady = await this.waitForVectorStoreReady();

        return {
            success: true,
            isVectorStoreReady,
        };
    };

    cleanup = async () => {
        if (!this.assistantId) {
            return;
        }

        const [error] = await awaitOn(
            new Promise(async (resolve) => {
                await this.openai.beta.assistants.del(
                    this.assistantId,
                );

                await this.openai.vectorStores.del(
                    this.vectorStoreId,
                );

                if (isNonEmptyArray(this.fileIds)) {
                    await Promise.all(this.fileIds.map(async (fileId) => {
                        await this.openai.files.del(fileId);
                    }));
                }

                resolve(true);
            }),
            {
                timeout: cleanupTimeout,
            }
        );

        if (!error) {
            return;
        }

        logger.error('Failed to delete assistant', {
            assistantId: this.assistantId,
            vectorStoreId: this.vectorStoreId,
            fileIds: this.fileIds,
            error,
        });
    };
}
