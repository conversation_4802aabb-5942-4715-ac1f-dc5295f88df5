import {
    isNonEmptyArray,
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import getClient from './getClient';
import type {MessagesGetterProps} from './types';

export default async ({
    attachments,
}: {
    attachments: MessagesGetterProps['attachments'];
}): Promise<{
    success: boolean;
    outcomes?: Array<PromiseSettledResult<unknown>>;
}> => {
    if (!isNonEmptyArray(attachments)) {
        return {
            success: false,
        };
    }

    const openai = getClient();

    const outcomes = await Promise.allSettled(attachments.map(async (attachment) => {
        if (!isNonEmptyString(attachment.file_id)) {
            return;
        }

        await openai.files.del(attachment.file_id);
    }));

    const success = outcomes.every((result) => result.status === 'fulfilled');

    return {
        success,
        outcomes,
    };
};
