import fse from 'fs-extra';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import logger from '../../logger';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import getClient from './getClient';

export default async ({
    filePaths,
    assistantId,
}): Promise<{
    success: boolean,
    fileIds?: string[],
    error?: Error | {infoCodes: Array<InfoCode>},
}> => {
    const fileIds: string[] = [];
    if (isNonEmptyArray(filePaths)) {
        await awaitOn(Promise.allSettled(filePaths.map(async (filePath) => {
            if (!fse.existsSync(filePath)) {
                logger.error('Trying to upload a file that does not exist', {
                    filePath,
                    assistantId,
                });
            }

            const uploadedFile = await getClient().files.create({
                file: fse.createReadStream(filePath),
                purpose: 'assistants',
            });

            fileIds.push(uploadedFile.id);
        })));
    }

    return {
        success: true,
        fileIds,
    };
};
