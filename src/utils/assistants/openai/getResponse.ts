import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import generateAutomationFiles from '../../generateAutomationFiles';
import getAssistantResponseAsJSON from '../../getAssistantResponseAsJSON';
import uploadAssistantFiles from './uploadAssistantFiles';
import getAssistantMessages from './getAssistantMessages';
import type {Page} from '@w7-3/webeagle-resources/types/playwright';
import type {
    BuildFileData,
    SolutionHandler,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    PageBuildSession,
} from '@w7-3/webeagle-resources/types/webautomate/state';
import type {ProjectBuildStep} from '@w7-3/webeagle-resources/types/project';
import type {MessagesGetterProps} from './types';

export default async <T>({
    step,
    page,
    handler,
    request,
    prompt,
    context,
    pageBuildSessionId,
    resultId,
    assistantId,
    files,
    clearFiles = true,
}: {
    step: ProjectBuildStep;
    page: Page;
    handler: SolutionHandler;
    request: string;
    prompt: string;
    context?: Record<string, unknown>;
    pageBuildSessionId: PageBuildSession['id'];
    resultId: string;
    assistantId: string;
    files?: {
        currentScreenshotPathName: string;
        baselineScreenshotPathName: string;
        diffScreenshotFilePathName: string;
    },
    clearFiles?: boolean;
}): Promise<{response: T, buildFileData: BuildFileData}> => {
    const [fileGenerationError, fileGenerationResult] = await awaitOn(generateAutomationFiles({
        step,
        page,
        handler,
        request,
        prompt,
        pageBuildSessionId,
        files,
    }));

    if (fileGenerationError || !isNonEmptyObject(fileGenerationResult)) {
        throw fileGenerationError;
    }

    const {
        storageFolder,
        knowledgeFileList,
        imageList,
    } = fileGenerationResult;

    const {fileIds} = await uploadAssistantFiles({
        filePaths: knowledgeFileList.map((file) => file.filePath),
        assistantId,
    });

    handler.updateConsumptionContingent({
        aiRequests: 1,
    });

    const attachments: MessagesGetterProps['attachments'] = [];
    fileIds?.forEach((fileId) => {
        attachments.push({
            file_id: fileId,
            tools: [{type: 'file_search'}],
        });
    });

    const language = handler.storage.accountData?.preferredLanguage;
    const url = page.url();
    const content: MessagesGetterProps['content'] = [{
        type: 'text',
        text: JSON.stringify({
            request,
            prompt,
            context,
            language,
            url,
        }),
    }];

    imageList.forEach((image) => {
        content.push({
            type: 'image_url',
            image_url: {
                url: image.publicUrl,
                detail: 'high',
            },
        });
    });

    const {
        success,
        messages,
        threadId,
        error,
    } = await getAssistantMessages({
        assistantId,
        attachments,
        content,
        limit: 1,
    });

    if (!success) {
        throw error;
    }

    const message = messages?.[0];
    const response = getAssistantResponseAsJSON<T>(message?.content);
    const buildFileData = {
        storageFolder,
        attachments,
        imageList,
        language,
        url,
        createAt: Date.now(),
        expireAt: Date.now(),
        assistantId,
        threadId,
    };

    if (clearFiles && (attachments.length > 0 || imageList.length > 0)) {
        await handler.storage.logBuildFiles({
            id: resultId,
            data: buildFileData,
        });
    }

    return {response, buildFileData};
};
