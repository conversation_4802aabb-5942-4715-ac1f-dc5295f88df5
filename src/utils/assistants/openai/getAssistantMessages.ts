import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getIsValidNumber} from '@w7-3/webeagle-resources/dist/libs/number';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import chillaxe from '@w7-3/webeagle-resources/dist/libs/chillaxe';
import logger from '../../logger';
import {getClient} from './index';
import type {Message} from '@w7-3/webeagle-resources/types/assistant';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {MessagesGetterProps} from './types';

const timeout = 1000 * 60 * 5;
const maximumProcessTimePerKnowledgeFile = 1000 * 60 * 5;
const pollingInterval = 1000;

export default async (
    {
        assistantId,
        threadId,
        runId,
        content,
        attachments,
        sort,
        limit,
    }: MessagesGetterProps): Promise<{
    success: boolean,
    messages?: Message[],
    threadId?: string,
    runId?: string,
    error?: Error | {infoCodes: Array<InfoCode>},
}> => {
    const openai = getClient();
    try {
        let thread = threadId ? await openai.beta.threads.retrieve(threadId) : null;

        if (!thread?.id) {
            thread = await openai.beta.threads.create();
        }

        await openai.beta.threads.messages.create(
            thread.id,
            {
                role: 'user',
                content,
                attachments,
            }
        );

        thread = await openai.beta.threads.retrieve(thread.id);

        if (thread?.id && isNonEmptyArray(attachments)) {
            const [vectorStoreIntegrationError, isVectorStoreIntegrated] = await awaitOn(new Promise(async (resolve) => {
                let isReady = false;

                while (!isReady) {
                    thread = await openai.beta.threads.retrieve(thread!.id);

                    if (!thread?.id) {
                        break;
                    }

                    await chillaxe(pollingInterval * 10);
                    isReady = isNonEmptyArray(thread?.tool_resources?.file_search?.vector_store_ids);
                }

                resolve(isReady);
            }), {
                timeout: maximumProcessTimePerKnowledgeFile * attachments.length,
            });

            if (!isVectorStoreIntegrated || vectorStoreIntegrationError) {
                return {
                    success: false,
                    error: {
                        infoCodes: [{
                            code: EVENTS.AI_SERVICE_FAILED,
                            level: INFO_CODE_LEVELS.WARNING,
                            timestamp: Date.now(),
                            data: {
                                assistantId,
                                threadId: thread.id,
                            },
                        }],
                    },
                };
            }

            const [vectorStoreError, isVectorStoreReady] = await awaitOn(new Promise(async (resolve) => {
                for (const id of thread!.tool_resources!.file_search!.vector_store_ids!) {
                    let storeStatus = '';
                    while (storeStatus !== 'completed') {
                        const store = await openai.vectorStores.retrieve(id);
                        storeStatus = store.status;
                        if (storeStatus === 'failed') {
                            resolve(false);
                        }
                        if (storeStatus !== 'completed') {
                            await chillaxe(pollingInterval);
                        }
                    }

                    resolve(true);
                }
            }), {
                timeout: maximumProcessTimePerKnowledgeFile * attachments.length,
            });

            if (!isVectorStoreReady || vectorStoreError) {
                return {
                    success: false,
                    error: {
                        infoCodes: [{
                            code: EVENTS.AI_SERVICE_FAILED,
                            level: INFO_CODE_LEVELS.WARNING,
                            timestamp: Date.now(),
                            data: {
                                assistantId,
                                threadId: thread.id,
                            },
                        }],
                    },
                };
            }
        }

        let run = runId ? await openai.beta.threads.runs.retrieve(thread.id, runId) : null;

        if (!run) {
            run = await openai.beta.threads.runs.create(
                thread.id,
                {assistant_id: assistantId}
            );
        }

        if (!run) {
            logger.error('Failed to create or retrieve run, ai assistant is probably no longer available');
            return {
                success: false,
            };
        }

        let interval: string | number | NodeJS.Timeout | undefined;
        const [error, status] = await awaitOn(new Promise((resolve) => {
            interval = setInterval(async () => {
                run = await openai.beta.threads.runs.retrieve(thread!.id, run!.id);

                if (['queued', 'in_progress', 'requires_action', 'cancelling'].includes(run.status)) {
                    return;
                }

                clearInterval(interval);
                resolve(run?.status);
            }, pollingInterval);
        }), {
            timeout,
        });

        if (interval) {
            clearInterval(interval);
        }

        if (error) {
            return {
                success: false,
                error,
            };
        }

        if (status !== 'completed') {
            return {
                success: false,
                error: {
                    infoCodes: [{
                        code: EVENTS.AI_SERVICE_FAILED,
                        level: INFO_CODE_LEVELS.WARNING,
                        timestamp: Date.now(),
                        data: {
                            status,
                            assistantId,
                            threadId: thread.id,
                            runId: run.id,
                        },
                    }],
                },
            };
        }

        let threadMessages = await openai.beta.threads.messages.list(thread.id);
        if (threadMessages.data.length === 1) {
            await chillaxe(pollingInterval * 10);
            threadMessages = await openai.beta.threads.messages.list(thread.id);
        }
        const messages: Message[] = [];

        if (isNonEmptyArray(threadMessages.data)) {
            if (sort === 'asc') {
                threadMessages.data.reverse();
            }
            for (let i = 0; i < threadMessages.data.length; i++) {
                if (getIsValidNumber(limit) && i >= limit!) {
                    break;
                }

                const item = threadMessages.data[i];
                const text = (item.content[0] as any)?.text;
                const content = (text?.value || '').replaceAll(/【.*?】/g, '');

                messages.push({
                    id: item.id,
                    threadId: item?.thread_id,
                    runId: item?.run_id as string,
                    role: item.role,
                    createdAt: item.created_at * 1000,
                    content,
                });
            }
        }

        return {
            success: true,
            messages,
            threadId: thread.id,
        };
    } catch (e) {
        const error = e as Error;
        return {
            success: false,
            error,
        };
    }
};
