import os from 'os';
import fs from 'fs';
import pdf from 'pdfjs';
import font from 'pdfjs/font/Helvetica';
import fontBold from 'pdfjs/font/Helvetica-Bold';
import {getRandomString} from '@w7-3/webeagle-resources/dist/libs/random';
import {DATA_TYPES} from '@w7-3/webeagle-resources/dist/config/data';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import type {
    ProjectExportData,
    ProjectExportJSON,
} from '../getProjectExportData';
import daoInstance from '../../../../setup/setupDB';

const dao = daoInstance.getDAO();

export const renderProjectExportData = ({doc, data, cellOptions, labelOptions}: {
    doc: pdf.Document,
    data: ProjectExportData,
    cellOptions: Record<string, any>,
    labelOptions: Record<string, any>,
}) => {
    // @ts-expect-error type is ok
    if ([DATA_TYPES.STRING, DATA_TYPES.NUMBER, DATA_TYPES.DATE].includes(data.type)) {
        doc.cell(cellOptions)
            .text()
            .add(data.label, labelOptions)
            .add(': ')
            .add(`${data.value}`, {
                font,
                fontSize: 12,
            });

        return;
    }

    if (data.type === DATA_TYPES.LINK) {
        doc.cell(cellOptions)
            .text()
            .add(data.label, labelOptions)
            .add(': ')
            .add(`${data.value}`, {
                link: data.value,
                underline: true,
                color: 0x569cd6,
            });

        return;
    }

    if (data.type === DATA_TYPES.OBJECT) {
        doc.cell(cellOptions)
            .text()
            .add(data.label, {
                font: fontBold,
            });

        (Object.values(data.value) as ProjectExportData[]).forEach((item) => {
            renderProjectExportData({
                doc,
                data: item,
                cellOptions: {
                    paddingLeft: (cellOptions?.paddingLeft || 0) + .5 * pdf.cm,
                    paddingBottom: .25 * pdf.cm,
                },
                labelOptions: {},
            });
        });
    }

    if (data.type === DATA_TYPES.ARRAY) {
        doc
            .cell(cellOptions)
            .text()
            .add(data.label, {
                font: fontBold,
            });

        data.value.forEach((item: ProjectExportData) => {
            renderProjectExportData({
                doc,
                data: item,
                cellOptions: {
                    paddingLeft: (cellOptions?.paddingLeft || 0) + .5 * pdf.cm,
                    paddingBottom: .25 * pdf.cm,
                },
                labelOptions: {},
            });
        });
    }

    if (data.type === DATA_TYPES.TABLE) {
        doc
            .cell(cellOptions)
            .text()
            .add(data.label, {
                font: fontBold,
            });
        const table = doc.cell().table({
            widths: Array(data.value.labels.length).fill(null),
            padding: .5 * pdf.cm,
        });
        const tr = table.header({
            font: fontBold,
        });

        data.value.labels.forEach((label: string) => {
            tr.cell(label);
        });
        data.value.items.forEach((itemList: ProjectExportData[]) => {
            const tr = table.row();
            data.value.labels.forEach((_: unknown, index: number) => {
                tr.cell(`${itemList?.[index]}`);
            });
        });
    }
};

export default async ({
    destination,
    exportData,
}: {
    destination: string,
    exportData: ProjectExportJSON,
}): Promise<boolean> => {
    const filePath = `${os.tmpdir()}/${getRandomString(16)}.pdf`;
    const doc = new pdf.Document({
        font,
        padding: pdf.cm,
        fontSize: 12,
    });

    const header = doc
        .header()
        .table({widths: [null, null], paddingBottom: .75 * pdf.cm})
        .row();
    header
        .cell()
        .text({textAlign: 'left'})
        .add(
            `${exportData.meta.timestamp.label}: ${exportData.meta.timestamp.value}`,
            {color: 0xcccccc})
        .br()
        .add(
            `${exportData.meta.timeZone.label}: ${exportData.meta.timeZone.value}`,
            {color: 0xcccccc});

    header
        .cell()
        .text({textAlign: 'right'})
        .add(
            `${exportData.meta.language.label}: ${exportData.meta.language.value}`,
            {color: 0xcccccc})
        .br()
        .add(exportData.meta.website.value, {
            link: exportData.meta.website.value,
            underline: true,
            color: 0x569cd6,
        });
    doc.footer().pageNumber(
        function(curr, total) {
            return curr + ' / ' + total;
        },
        {textAlign: 'center'},
    );
    doc.pipe(fs.createWriteStream(filePath));
    (Object.values(exportData.result) as ProjectExportData[]).forEach((item: ProjectExportData, index: number) => {
        renderProjectExportData({
            doc,
            data: item,
            cellOptions: {
                fontSize: 16,
                paddingBottom: .5 * pdf.cm,
                ...getOptionalMap(index > 0, {
                    paddingTop: .5 * pdf.cm,
                }),
            },
            labelOptions: {
                fontSize: 12,
                font: fontBold,
            },
        });
    });
    await doc.end();

    const {success} = await dao.uploadFile({
        storageFilePath: destination,
        filePath,
        deleteAfterUpload: true,
    });

    return success;
};
