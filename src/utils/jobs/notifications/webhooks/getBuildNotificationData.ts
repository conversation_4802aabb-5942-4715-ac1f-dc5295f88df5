import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    resultChildCollectionSeparator,
} from '@w7-3/webeagle-resources/dist/config/collections';
import dbInstance from '../../../../setup/setupDB';
import logger from '../../../logger';
import type {
    InfoCode,
    ProjectConfig,
    ProjectData,
    WebhookData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    LinkBuildResult,
    ProjectBuildOverviewData,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export const getBuildResults = async <T = Record<string, unknown>>({
    buildResultsCollection,
}: {
    buildResultsCollection: string;
}) => {
    const results: Record<string, T> = {};

    try {
        const [, results] = await dao.getAllCollectionData<LinkBuildResult>({
            collection: buildResultsCollection,
        }) as [Error, Record<string, LinkBuildResult>];

        await Promise.allSettled(Object.keys(results).map(async (resultId) => {
            const [, subResults] = await dao.getAllCollectionData<LinkBuildResult>({
                collection: `${buildResultsCollection}/${resultId}/${resultChildCollectionSeparator}`,
            });

            results[resultId].buildSubResults = subResults;
        }));
    } catch (error) {
        logger.error('Error getting build results', {
            error,
        });
    }

    return results;
};

export const getData = async ({
    projectData,
    buildNotification,
}: {
    projectData: ProjectData,
    buildNotification: ProjectBuildNotification,
}): Promise<WebhookData> => {
    const {
        accountId,
        buildId,
        projectId,
    } = buildNotification;
    const paths = getPaths({
        accountId,
        projectId,
        buildId,
    });

    const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
        collection: paths.collections.configs,
        id: projectData.configId,
    });
    const [, requestData] = await dao.getCollectionDocumentData<ProjectBuildOverviewData>({
        collection: paths.collections.builds,
        id: `${buildId}`,
    });
    const [, buildEvents] = await dao.getAllCollectionData<InfoCode>({
        collection: paths.collections.buildEvents,
    });
    const buildResults = await getBuildResults<ProjectBuildOverviewData>({
        buildResultsCollection: paths.collections.buildResults,
    });

    return {
        projectConfig,
        requestData,
        buildEvents,
        buildResults,
    };
};

export default async (buildNotification: ProjectBuildNotification): Promise<WebhookData> => {
    const {
        accountId,
        buildId,
        projectId,
    } = buildNotification;
    const paths = getPaths({
        accountId,
        projectId,
        buildId,
    });
    const [, projectData] = await dao.getDocumentData<ProjectData>({
        path: paths.collections.project,
    }) as [Error, ProjectData];

    return await getData({
        projectData,
        buildNotification,
    });
};
