import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    ITEM_LABELLING_STRATEGY,
} from '@w7-3/webeagle-resources/dist/config/misc';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import evaluateFunction from './evaluateFunction';
import {getInfoCodeList} from './getInfoCodes';
import type {Page} from '@w7-3/webeagle-resources/types/playwright';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {ProjectBuildStep} from '@w7-3/webeagle-resources/types/project';

export default async ({
    content,
    page,
}: {
    content: ProjectBuildStep,
    page: Page,
}): Promise<{
    label: string,
    infoCodes: InfoCode[],
}> => {
    let error, label;
    const infoCodes: InfoCode[] = [];
    const {
        id,
        stepType,
        label: rawLabel,
        labelScript,
        fallbackLabel,
        labelStrategy,
    } = content;

    if (labelStrategy === ITEM_LABELLING_STRATEGY.JS && isNonEmptyString(labelScript?.value)) {
        ([error, label] = await evaluateFunction({
            page,
            config: {
                paramNameList: [],
                paramValueList: [],
            },
            extractionCode: labelScript?.value as string,
        }) as [Error, string]);

        if (error) {
            infoCodes.push(
                ...getInfoCodeList(error), {
                    code: EVENTS.STEP_LABELLING_FUNCTION_FAILED,
                    level: INFO_CODE_LEVELS.INFO,
                    timestamp: Date.now(),
                    data: {
                        id,
                        stepType,
                    },
                });
        }
    } else {
        label = rawLabel;
    }

    label = label || fallbackLabel;

    return {
        label,
        infoCodes,
    };
};
