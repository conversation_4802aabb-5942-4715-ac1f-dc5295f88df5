import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import conditions from '@w7-3/webeagle-resources/dist/config/conditions';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {CancellationScopes} from '@w7-3/webeagle-resources/dist/config/build';
import {getInfoCodeList} from '../getInfoCodes';
import cookie from './services/conditions/cookie';
import elementPresent from './services/conditions/elementPresent';
import jsCondition from './services/conditions/jsCondition';
import responseHeader from './services/conditions/responseHeader';
import automatedCondition from './services/conditions/automatedCondition';
import {logFromAPI} from '../logger';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    ConditionFuncParams,
    ConditionResult,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildStepResult,
    ProjectSolutionStepResultData,
} from '@w7-3/webeagle-resources/types/project';

const renderers = {
    [conditions.cookie.value]: cookie,
    [conditions.elementPresent.value]: elementPresent,
    [conditions.js.value]: jsCondition,
    [conditions.responseHeader.value]: responseHeader,
    [conditions.ai.value]: automatedCondition,
};

export default async ({
    contextId,
    getStepResultCurry,
    handler,
    labellingData,
    page,
    pageBuildSessionId,
    step,
}: ConditionFuncParams): Promise<Pick<
    ProjectBuildStepResult<'condition'>, 'isAborted' | 'infoCodes' | 'data'>> => {
    const {
        id,
        stepList,
    } = step;
    const callback: (...args) => Promise<ConditionResult> = renderers[step?.type];
    let isAborted = false;

    if (typeof callback !== 'function') {
        await logFromAPI({
            code: EVENTS.INVALID_CONDITION_CONFIGURATION,
            level: INFO_CODE_LEVELS.ERROR,
            timestamp: Date.now(),
            data: {
                id,
                labellingData,
            },
        });

        return {
            isAborted: step.abortOnError,
            infoCodes: [
                {
                    code: EVENTS.INVALID_CONDITION_CONFIGURATION,
                    level: INFO_CODE_LEVELS.ERROR,
                    timestamp: Date.now(),
                    data: {
                        id,
                    },
                },
            ],
            data: {
                success: false,
                result: null,
            },
        };
    }

    const infoCodes: InfoCode[] = [];
    const data: ProjectSolutionStepResultData = {
        success: false,
        result: null,
    };

    try {
        const result = await callback({
            step,
            page,
            pageBuildSessionId,
            handler,
            contextId,
            labellingData,
        });
        data.result = result;
        data.stepListResults = [];

        if (result.passed && isNonEmptyArray(stepList)) {
            for (let i = 0; i < stepList.length; i++) {
                if (isAborted || handler.getCancellationDataScope(CancellationScopes.global)) {
                    break;
                }

                const subStep = stepList[i];
                const stepResult: ProjectBuildStepResult = await getStepResultCurry({
                    step: subStep,
                    parentStep: step,
                    subContextId: `${contextId}_${step.id}`,
                });

                data.stepListResults.push(stepResult);
                isAborted = step.abortOnError && !subStep.ignoreOnError && stepResult.isAborted;
            }
        }

        data.success = true;
    } catch (error) {
        data.success = false;
        infoCodes.push(
            ...getInfoCodeList(error),
        );
    }

    return {
        isAborted,
        infoCodes,
        data,
    };
};
