import {EVENTS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    isPositiveInt,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {timeoutConfig} from '@w7-3/webeagle-resources/dist/config/actions';
import {actions} from '@w7-3/webeagle-resources/dist/config/actions';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import evaluateFunction from '../../../../utils/evaluateFunction';
import type {
    ActionFuncParams,
    ActionConfig,
} from '@w7-3/webeagle-resources/types/solutions';

export default async ({page, step, labellingData}: ActionFuncParams, config: ActionConfig) => {
    const code = step?.value?.jsCode?.value;
    if (!isNonEmptyString(code)) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }
    const defaultTimeout = step?.value?.timeout || config.timeout;
    const timeout = isPositiveInt(defaultTimeout)
        ? Math.max(defaultTimeout, timeoutConfig.maxTimeout) : timeoutConfig.defaultTimeout;
    await awaitOn(new Promise((resolve, reject) => {
        const interval = setInterval(async () => {
            const [error, result] = await evaluateFunction({
                page,
                config: {
                    paramNameList: [],
                    paramValueList: [],
                },
                extractionCode: code,
            });

            if (error) {
                clearInterval(interval);
                reject(error);
                return;
            }

            if (!result) {
                return;
            }

            clearInterval(interval);
            resolve(true);
        }, actions.booleanFunction.interval);
    }), {
        timeout,
        error: {
            infoCodes: [{
                code: EVENTS.ACTIONS_TIMEOUT,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    name: labellingData.label,
                    duration: timeout,
                },
            }],
        },
    });
};
