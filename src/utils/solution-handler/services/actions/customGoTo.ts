import {EVENTS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {REQUEST_WAIT_UNTIL} from '@w7-3/webeagle-resources/dist/config/request';
import loadUrl from '../../../loadUrl';
import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';

export default async ({
    page,
    step,
    handler,
}: ActionFuncParams) => {
    if (!isNonEmptyString(step?.value?.url)) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }

    const {
        requestOptions: {
            browser: {
                linkRequestTimeout,
            },
        },
    } = handler.config;
    const {
        urlRequestState,
    } = await loadUrl({
        urlConfig: {
            url: step.value.url,
        },
        page,
        linkRequestTimeout,
        waitUntil: {
            option: REQUEST_WAIT_UNTIL.domContentLoaded,
        },
    });

    if (urlRequestState.error) {
        throw urlRequestState.error;
    }
};
