import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import {WAIT_FOR_SELECTOR_TIMEOUT} from '@w7-3/webeagle-resources/dist/config/misc';
import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';

export default async ({
    page,
    step,
}: ActionFuncParams) => {
    if (!isNonEmptyString(step?.selector)) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }

    const [error] = await awaitOn(page.waitForSelector(step?.selector, {
        timeout: WAIT_FOR_SELECTOR_TIMEOUT,
    }));

    if (error) {
        throw {
            infoCodes: [{
                code: EVENTS.ELEMENT_NOT_FOUND,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    selector: step?.selector,
                },
            }],
        };
    }

    const {
        text,
    } = step.value;

    const locator = await page.locator(step?.selector);

    if (!locator) {
        throw new Error(EVENTS.ELEMENT_NOT_FOUND);
    }

    await locator.fill(text, {
        force: true,
        timeout: 0,
    });
};
