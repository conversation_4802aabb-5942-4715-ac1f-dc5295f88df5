import os from 'os';
import fse from 'fs-extra';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    isNonEmptyArray,
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getRandomString} from '@w7-3/webeagle-resources/dist/libs/random';
import {deleteFile} from '@w7-3/webeagle-resources/dist/libs/file';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import dbInstance from '../../../../setup/setupDB';
import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {FilesConfig} from '@w7-3/webeagle-resources/types/firebase';

const dao = dbInstance.getDAO();
const destination = `${os.tmpdir()}`;

export const getFiles = async ({
    fileIdList,
    fileCollection,
}: {
    fileIdList: string[];
    fileCollection: string;
}): Promise<string[]> => {
    const [fileDataError, filesData] = await dao.getAllCollectionData({
        collection: fileCollection,
        limit: fileIdList.length,
        condition: {
            field: 'id',
            operator: 'in',
            value: fileIdList,
        },
    });

    if (fileDataError) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }

    const files: string[] = [];
    await Promise.allSettled((Object.values(filesData) as FilesConfig[]).map(async (data: FilesConfig) => {
        if (!data?.uploadData?.success) {
            return;
        }

        const filePath = `${destination}/${data.uploadData.data?.destination}`;

        if (!filePath) {
            return;
        }

        const [fileDownloadError, fileDownloadSuccess] = await dao.downloadFile({
            source: data.uploadData.data?.destination,
            destination: filePath,
        });

        if (fileDownloadError || !fileDownloadSuccess) {
            return;
        }

        files.push(filePath);
    }));

    return files;
};

export default async ({
    page,
    step,
    handler,
}: ActionFuncParams): Promise<{
    infoCodes: InfoCode[];
}> => {
    if (!isNonEmptyString(step?.selector) || !isNonEmptyArray(step?.value?.fileIdList)) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }

    const locator = await page.locator(step?.selector);

    if (!locator) {
        throw new Error(EVENTS.ELEMENT_NOT_FOUND);
    }

    const infoCodes: InfoCode[] = [];
    const subFolder = `${os.tmpdir()}/${getRandomString(16)}`;
    fse.ensureDirSync(subFolder);

    const fileChooserPromise = page.waitForEvent('filechooser');
    await locator.click();
    const fileChooser = await fileChooserPromise;
    const fileIdList: string[] = (fileChooser.isMultiple() ? step.value.fileIdList : [step.value.fileIdList?.[0]]) as string[];

    if (!fileChooser.isMultiple() && step.value.fileIdList.length > 1) {
        infoCodes.push({
            code: EVENTS.ACTION_FILE_UPLOAD_ONLY_SINGLE_FILES,
            level: INFO_CODE_LEVELS.ERROR,
            timestamp: Date.now(),
        });
    }

    const {
        accountId,
        projectId,
        buildId,
    } = handler.storage;
    const paths = getPaths({
        accountId,
        projectId,
        buildId,
    });
    const files = await getFiles({
        fileIdList,
        fileCollection: paths.collections.files,
    });

    if (!isNonEmptyArray(files)) {
        throw new Error(EVENTS.FILE_NOT_FOUND);
    }

    files.forEach((filePath) => {
        handler.cleanupTasks.push(() => deleteFile({filePath}));
    });
    await fileChooser.setFiles(files);

    return {infoCodes};
};
