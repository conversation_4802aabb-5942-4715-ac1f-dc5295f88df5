import getUrlMatch from '@w7-3/webeagle-resources/dist/libs/getUrlMatch';
import {METHODS} from '@w7-3/webeagle-resources/dist/config/request';
import {isPositiveInt} from '@w7-3/webeagle-resources/dist/libs/validators';
import {timeoutConfig} from '@w7-3/webeagle-resources/dist/config/actions';
import {EVENTS, INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import {getW103Global} from '../../../globalHandlers';
import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';

export const getHasRecordedResponse = ({
    page,
    step,
}: Partial<ActionFuncParams>) => {
    const {browserPages, responses} = getW103Global();
    const pageBuildSessionId = browserPages.get(page!);

    if (!pageBuildSessionId) {
        return false;
    }

    for (const [, responseItem] of responses.entries()) {
        const method = step?.value?.method || METHODS.GET;
        const urlList = step?.value?.urlData?.urlMatch || [];

        if (getUrlMatch(responseItem?.url, urlList) && method === responseItem?.method) {
            return true;
        }
    }

    return false;
};

export default async ({
    page,
    step,
    labellingData,
}: ActionFuncParams) => {
    const timeout = isPositiveInt(step?.value?.timeout)
        ? Math.max(step?.value?.timeout, timeoutConfig.maxTimeout) : timeoutConfig.defaultTimeout;
    await awaitOn(
        new Promise((resolve) => {
            const interval = setInterval(() => {
                if (!getHasRecordedResponse({page, step})) {
                    return;
                }
                clearInterval(interval);
                resolve(true);
            }, Math.min(150, timeout / 10));
        }),
        {
            timeout,
            error: {
                infoCodes: [{
                    code: EVENTS.ACTIONS_TIMEOUT,
                    level: INFO_CODE_LEVELS.ERROR,
                    timestamp: Date.now(),
                    data: {
                        name: labellingData.label,
                        duration: timeout,
                    },
                }],
            },
        },
    );
};
