import {elementSelectTypes} from '@w7-3/webeagle-resources/dist/config/actions';
import {EVENTS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';

export default async ({
    page,
    step,
}: ActionFuncParams) => {
    if (!isNonEmptyString(step?.selector)) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }

    const {
        type,
        list,
    } = step.value;

    const options = type === elementSelectTypes.value ? list : list.map(({label}) => ({label}));
    const element = await page.locator(step?.selector);

    if (!element) {
        throw new Error(EVENTS.ELEMENT_NOT_FOUND);
    }

    element.selectOption(options);
};
