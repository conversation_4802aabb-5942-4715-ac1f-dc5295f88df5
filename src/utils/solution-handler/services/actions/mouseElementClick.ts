import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';
import ensureDomElementExists from '../../../ensureDomElementExists';

export default async ({
    page,
    step,
}: ActionFuncParams) => {
    await ensureDomElementExists({
        selector: step?.selector,
        page,
    });

    const {
        buttonType,
        clickCount,
        delay,
    } = step.value;

    await page.locator(step?.selector).click({
        button: buttonType,
        clickCount,
        delay,
        force: true,
    });
};
