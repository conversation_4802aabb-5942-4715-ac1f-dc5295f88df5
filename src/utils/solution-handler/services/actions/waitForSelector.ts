import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {WAIT_FOR_SELECTOR_TIMEOUT} from '@w7-3/webeagle-resources/dist/config/misc';
import type {ActionFuncParams} from '@w7-3/webeagle-resources/types/solutions';

export default async ({
    page,
    step,
}: ActionFuncParams) => {
    if (!isNonEmptyString(step?.selector)) {
        return false;
    }
    const timeout = step?.value?.timeout || WAIT_FOR_SELECTOR_TIMEOUT;
    return await page.waitForSelector(step?.selector, {
        timeout,
    });
};
