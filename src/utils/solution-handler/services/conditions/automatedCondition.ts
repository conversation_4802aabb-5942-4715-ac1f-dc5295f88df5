import {getConfig} from 'app-config';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import type {
    BuildFileData,
    ConditionFuncParams,
    ConditionResult,
} from '@w7-3/webeagle-resources/types/solutions';
import getResponse from '../../../assistants/openai/getResponse';
import getRetryResponse from '../../../assistants/openai/getRetryResponse';
import logger from '../../../logger';
import type {ProjectBuildStep} from '@w7-3/webeagle-resources/types/project';
import type {
    ConditionEvaluationData, DataExtractionData,
} from '../../../assistants/openai/types';

type RetryData = {
    attempt: number;
    isRetry: boolean;
    buildFileData?: BuildFileData;
};

type ResponseData = {
    response: ConditionEvaluationData;
    buildFileData: BuildFileData | null;
};

const request = 'ConditionEvaluation';
const RETRIES = 3;

export const getResponseData = async (
    params: Pick<ConditionFuncParams, 'step' | 'page' | 'pageBuildSessionId' | 'handler'>,
    resultId: string,
    prompt: string,
    retryData: RetryData | null,
): Promise<ResponseData> => {
    const {
        step,
        page,
        pageBuildSessionId,
        handler,
    } = params;
    let response;
    let buildFileData = retryData?.buildFileData || null;
    if (!retryData?.isRetry) {
        ({response, buildFileData} = await getResponse<ConditionEvaluationData>({
            step,
            page,
            handler,
            request,
            prompt,
            pageBuildSessionId,
            resultId,
            assistantId: getConfig().AI.SOLUTION_BUILDER_ASSISTANT_ID,
        }));

        return {response, buildFileData};
    }

    ({response} = await getRetryResponse<DataExtractionData>({
        language: buildFileData!.language,
        url: buildFileData!.url,
        request: 'RETRY',
        assistantId: buildFileData!.assistantId,
        threadId: buildFileData!.threadId!,
        prompt: `This is attempt number: ${retryData!.attempt}.`,
    }));

    return {response, buildFileData};
};

export default async ({
    step,
    page,
    pageBuildSessionId,
    handler,
    contextId,
}: ConditionFuncParams): Promise<ConditionResult> => {
    const resultId = `${contextId}__${(step as ProjectBuildStep).id}`;
    const prompt = step?.value?.code?.value;
    let attempt = 1;
    let isSuccess = false;
    let responseData: ResponseData;
    let retryData: RetryData | null = null;
    while (!isSuccess && attempt <= RETRIES) {
        (responseData = await getResponseData(
            {
                step,
                page,
                pageBuildSessionId,
                handler,
            },
            resultId,
            prompt,
            retryData,
        ));

        attempt += 1;
        retryData = {
            attempt,
            isRetry: true,
            buildFileData: responseData!.buildFileData!,
        };

        if (responseData!.response?.success !== true) {
            continue;
        }

        isSuccess = true;
    }

    if (typeof responseData!.response?.success !== 'boolean') {
        logger.error('Failed to get AI Data', {
            response: responseData!.response,
        });

        throw {
            infoCodes: [{
                code: EVENTS.PROJECT_BUILD_STEP_CONDITION_AI_ERROR,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    message: responseData!.response?.error,
                },
            }],
        };
    }

    const {data, confidence, error} = responseData!.response as ConditionEvaluationData;
    const isPassed = data?.result === 'yes';
    const isThresholdAttained = confidence >= step?.value?.confidenceLevel;

    if (isPassed && !isThresholdAttained) {
        throw {
            infoCodes: [{
                code: EVENTS.AI_THRESHOLD_NOT_MET,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    confidence,
                    confidenceThreshold: step?.value?.confidenceLevel,
                },
            }],
        };
    }

    return {
        passed: isPassed && isThresholdAttained,
        data: {
            confidence,
            confidenceThreshold: step?.value?.confidenceLevel,
            conditionComments: data?.conditionComments,
            error,
        },
    };
};
