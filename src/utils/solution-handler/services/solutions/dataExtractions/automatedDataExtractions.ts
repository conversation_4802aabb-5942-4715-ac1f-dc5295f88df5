import {getConfig} from 'app-config';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import logger from '../../../../logger';
import getResponse from '../../../../assistants/openai/getResponse';
import type {
    BuildFileData,
    DataExtractionParams,
    DataExtractionResult,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildStep,
} from '@w7-3/webeagle-resources/types/project';
import type {
    DataExtractionData,
} from '../../../../assistants/openai/types';
import getRetryResponse from '../../../../assistants/openai/getRetryResponse';

const request = 'DataExtraction';
const RETRIES = 3;

type RetryData = {
    attempt: number;
    isRetry: boolean;
    buildFileData?: BuildFileData;
};

type ResponseData = {
    response: DataExtractionData;
    buildFileData: BuildFileData | null;
};

export const getResponseData = async (
    params: Pick<DataExtractionParams, 'step' | 'page' | 'pageBuildSessionId' | 'handler'>,
    resultId: string,
    prompt: string,
    retryData: RetryData | null,
): Promise<ResponseData> => {
    const {
        step,
        page,
        pageBuildSessionId,
        handler,
    } = params;
    let response;
    let buildFileData = retryData?.buildFileData || null;
    if (!retryData?.isRetry) {
        ({response, buildFileData} = await getResponse<DataExtractionData>({
            step,
            page,
            handler,
            request,
            prompt,
            pageBuildSessionId,
            resultId,
            assistantId: getConfig().AI.SOLUTION_BUILDER_ASSISTANT_ID,
        }));

        return {response, buildFileData};
    }

    ({response} = await getRetryResponse<DataExtractionData>({
        language: buildFileData!.language,
        url: buildFileData!.url,
        request: 'RETRY',
        assistantId: buildFileData!.assistantId,
        threadId: buildFileData!.threadId!,
        prompt: `This is attempt number: ${retryData!.attempt}.`,
    }));

    return {response, buildFileData};
};

export default async ({
    step,
    page,
    pageBuildSessionId,
    handler,
    contextId,
}: DataExtractionParams): Promise<DataExtractionResult> => {
    const resultId = `${contextId}__${(step as ProjectBuildStep).id}`;
    const prompt = step?.value?.code?.value;
    let attempt = 1;
    let isSuccess = false;
    let responseData: ResponseData;
    let retryData: RetryData | null = null;
    while (!isSuccess && attempt <= RETRIES) {
        (responseData = await getResponseData(
            {
                step,
                page,
                pageBuildSessionId,
                handler,
            },
            resultId,
            prompt,
            retryData,
        ));

        attempt += 1;
        retryData = {
            attempt,
            isRetry: true,
            buildFileData: responseData!.buildFileData!,
        };

        if (responseData!.response?.success !== true) {
            continue;
        }

        isSuccess = true;
    }

    if (typeof responseData!.response?.success !== 'boolean') {
        logger.error('Failed to get AI Data', {
            response: responseData!.response,
        });

        throw {
            infoCodes: [{
                code: EVENTS.PROJECT_BUILD_STEP_DATA_EXTRACTION_AI_ERROR,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
            }],
        };
    }

    const value = responseData!.response.data?.result;

    if (!isSuccess) {
        throw {
            infoCodes: [{
                code: EVENTS.PROJECT_BUILD_DATA_EXTRACTION_NO_RESULT,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    message: responseData!.response?.error,
                },
            }],
        };
    }

    return {
        value: responseData!.response.data!.result,
        extractionData: {
            confidence: responseData!.response?.confidence,
            charCount: value.length,
            byteCount: new TextEncoder().encode(value).length,
        },
    };
};
