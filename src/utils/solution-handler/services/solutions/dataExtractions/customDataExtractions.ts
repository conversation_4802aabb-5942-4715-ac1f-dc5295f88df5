import evaluateFunction from '../../../../evaluateFunction';
import getAutomationData from '../../../../getAutomationData';
import type {
    DataExtractionParams,
    DataExtractionResult,
} from '@w7-3/webeagle-resources/types/solutions';

export default async ({
    step,
    page,
    pageBuildSessionId,
}: Pick<DataExtractionParams, 'step' |'page' | 'pageBuildSessionId'>): Promise<DataExtractionResult> => {
    const extractionCode = step?.value?.jsCode?.value;
    const {
        paramNameList,
        paramValueList,
    } = await getAutomationData({
        page,
        handler: step.handler,
        pageBuildSessionId,
        config: {
            automationState: true,
            domSnapshot: false,
            lighthouseDesktop: false,
            lighthouseMobile: false,
        },
    });
    const [error, value] = await evaluateFunction({
        page,
        extractionCode,
        config: {
            paramNameList,
            paramValueList,
        },
    });

    if (error) {
        throw error;
    }

    return {value};
};
