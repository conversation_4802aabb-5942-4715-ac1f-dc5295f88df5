import looksSame from 'looks-same';
import {
    visualCompareOptions,
    visualTestStates,
} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import captureScreenshots from '../../../../captureScreenshots';
import type {
    ProjectBuildStep,
} from '@w7-3/webeagle-resources/types/project';
import type {
    ScreenshotCaptureData,
    VisualDiffOptions,
    VisualDiffParams,
    VisualDiffResult,
} from '@w7-3/webeagle-resources/types/solutions';
import type E2EVisualTestsSolutionHandler from '../../../libs/E2EVisualTestsSolutionHandler';

export default async ({
    collection,
    contextId,
    handler,
    labellingData,
    page,
    step,
}: VisualDiffParams, {
    options,
}: {
    options: VisualDiffOptions,
}): Promise<{
    result: VisualDiffResult,
    files: {
        currentScreenshotPathName: string,
        baselineScreenshotPathName: string,
        diffScreenshotFilePathName: string,
    },
}> => {
    const resultId = `${contextId}__${(step as ProjectBuildStep).id}`;
    const localResultDirectory = `${(handler as E2EVisualTestsSolutionHandler).storage.getStorageDirectory()}${collection}/${resultId}`;
    const [screenshotCaptureData] = await captureScreenshots({
        content: step.value,
        page,
        directory: localResultDirectory,
        fileNamePrefix: `${resultId}_${Date.now()}`,
        labellingData,
    }) as [ScreenshotCaptureData];

    if (!screenshotCaptureData?.success) {
        throw screenshotCaptureData;
    }

    const currentScreenshotPathName = screenshotCaptureData.data?.path;
    const currentScreenshotFileName = screenshotCaptureData.data?.name;
    const currentScreenshotFileExtension = screenshotCaptureData.data?.extension;
    const baselineScreenshotFileName = `${currentScreenshotFileName}.prev`;
    const baselineScreenshotFileNameWithExtension = `${baselineScreenshotFileName}.${currentScreenshotFileExtension}`;
    const baselineScreenshotPathName = `${localResultDirectory}/${baselineScreenshotFileNameWithExtension}`;
    const diffScreenshotFileName = `${currentScreenshotFileName}.diff`;
    const diffScreenshotFileNameWithExtension = `${diffScreenshotFileName}.${currentScreenshotFileExtension}`;
    const diffScreenshotFilePathName = `${localResultDirectory}/${diffScreenshotFileNameWithExtension}`;
    const ts = Date.now();
    const uploadResult = await handler.storage.uploadFile(screenshotCaptureData?.data, false);
    const currentImage= uploadResult?.data?.destination as string;

    if (!uploadResult?.success) {
        throw uploadResult;
    }

    let buildIndex = handler.storage.buildId - 1;
    const baselineRecords = await handler.storage.getVisualDiffBaseline({
        id: resultId,
    });
    let baseline: VisualDiffResult['baseline'] = null;

    if (baselineRecords?.success) {
        while (buildIndex > -1 && !baseline) {
            if (baselineRecords?.data?.[buildIndex]?.data?.state === visualTestStates.accepted) {
                baseline = baselineRecords?.data?.[buildIndex];
            }
            buildIndex -= 1;
        }
    }

    let diffImage = currentImage;
    if (!baseline?.data?.currentImage) {
        return {
            result: {
                state: visualTestStates.new,
                initialState: visualTestStates.new,
                success: true,
                passed: true,
                currentImage,
                diffImage: currentImage,
                ts,
                id: resultId,
                baseline,
                reviewData: null,
            },
            files: {
                currentScreenshotPathName,
                baselineScreenshotPathName,
                diffScreenshotFilePathName,
            },
        };
    }

    const {
        target: {
            device: {
                viewport: {
                    deviceScaleFactor,
                },
            },
        },
    } = handler.config;

    const looksSameOptions = {
        ...visualCompareOptions.strategy[step.value.strategy],
        highlightColor: options.highlightColor || visualCompareOptions.highlightColor,
        clustersSize: options.clustersSize || visualCompareOptions.clustersSize,
        pixelRatio: deviceScaleFactor,
        shouldCluster: true,
    };

    const downloadResult = await handler.storage.downloadFile({
        source: baseline!.data.currentImage,
        destination: baselineScreenshotPathName,
    });

    if (!downloadResult.success) {
        throw downloadResult;
    }

    const looksSameResult = await new Promise(async (resolve, reject) => {
        try {
            const data = await looksSame(
                baselineScreenshotPathName,
                currentScreenshotPathName,
                {
                    ...looksSameOptions,
                    shouldCluster: true,
                    createDiffImage: true,
                },
            ) as looksSame.LooksSameResult<true>;
            resolve(data);
        } catch (e) {
            const error = e as Error;
            reject(error);
        }
    }) as looksSame.LooksSameResult<true>;

    if (!looksSameResult.equal) {
        await looksSameResult.diffImage.save(diffScreenshotFilePathName);
        const diffImageTransferData = await handler.storage.uploadFile({
            directory: localResultDirectory,
            extension: currentScreenshotFileExtension,
            name: diffScreenshotFileName,
        }, false);

        if (!diffImageTransferData.success) {
            throw {infoCodes: diffImageTransferData.infoCodes};
        }

        diffImage = diffImageTransferData.data!.destination as string;
    }

    const state = looksSameResult.equal ? visualTestStates.accepted : visualTestStates.changed;

    return {
        result: {
            state,
            initialState: state,
            success: true,
            passed: looksSameResult.equal,
            currentImage,
            diffImage,
            ts,
            id: resultId,
            differentPixels: looksSameResult.differentPixels,
            totalPixels: looksSameResult.totalPixels,
            diffBounds: looksSameResult.diffBounds,
            diffClusters: looksSameResult.diffClusters,
            deviceScaleFactor,
            baseline,
            reviewData: null,
        },
        files: {
            currentScreenshotPathName,
            baselineScreenshotPathName,
            diffScreenshotFilePathName,
        },
    };
};
