import {getConfig} from 'app-config';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import getResponse from '../../../../assistants/openai/getResponse';
import type {
    VisualDiffEvaluationData,
} from '../../../../assistants/openai/types';
import type {
    VisualDiffParams,
    VisualDiffResult,
} from '@w7-3/webeagle-resources/types/solutions';
import type {ProjectBuildStep} from '@w7-3/webeagle-resources/types/project';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import logger from '../../../../logger';

export default async ({
    step,
    page,
    handler,
    contextId,
    labellingData,
    pageBuildSessionId,
}: VisualDiffParams, {
    files,
    context,
}: {
    files: {
        currentScreenshotPathName: string;
        baselineScreenshotPathName: string;
        diffScreenshotFilePathName: string;
    },
    context: Pick<VisualDiffResult, 'differentPixels' | 'totalPixels' | 'deviceScaleFactor'>;
}): Promise<{
    success: false;
    infoCodes: Array<InfoCode>;
} | {
    success: true;
    passed: VisualDiffResult['passed'],
    reviewData: VisualDiffResult['reviewData'],
}> => {
    const prompt = step.value.ai.instructions.value;
    const resultId = `${contextId}__${(step as ProjectBuildStep).id}`;
    const {response} = await getResponse<VisualDiffEvaluationData>({
        step,
        page,
        handler,
        request: 'VisualDiffEvaluation',
        context,
        prompt,
        pageBuildSessionId,
        resultId,
        assistantId: getConfig().AI.SOLUTION_BUILDER_ASSISTANT_ID,
        files,
    });

    const infoCodes: Array<InfoCode> = [];

    if (typeof response?.success !== 'boolean') {
        logger.error('Failed to get AI Data', {
            response,
        });

        infoCodes.push({
            code: EVENTS.PROJECT_BUILD_STEP_E2E_VISUAL_TEST_AI_ERROR,
            level: INFO_CODE_LEVELS.ERROR,
            timestamp: Date.now(),
        });
    }

    if (!response?.success) {
        return {
            success: false,
            infoCodes: [
                ...infoCodes,
                {
                    code: EVENTS.PROJECT_BUILD_STEP_E2E_VISUAL_TEST_NO_REVIEW,
                    level: INFO_CODE_LEVELS.WARNING,
                    timestamp: Date.now(),
                    data: {
                        message: response?.error,
                    },
                },
            ],
        };
    }

    const {data, confidence} = response as VisualDiffEvaluationData;
    const isPassed = data?.result === 'yes';
    const isThresholdAttained = confidence >= step.value.ai.confidenceLevel;
    const passed = isPassed && isThresholdAttained;

    return {
        success: true,
        passed,
        reviewData: {
            verdict: data.result!,
            confidence: confidence,
            reviewComments: data.reviewComments!,
        },
    };
};
