import {getConfig} from 'app-config';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {CancellationScopes} from '@w7-3/webeagle-resources/dist/config/build';
import {logFromAPI} from '../../../logger';
import {getInfoCodeList} from '../../../getInfoCodes';
import evaluateFunction from '../../../../utils/evaluateFunction';

import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {LoopFuncParams} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildStepResult,
    ProjectSolutionStepResultData,
} from '@w7-3/webeagle-resources/types/project';

export default async ({
    handler,
    step,
    page,
    labellingData,
    executeStepList,
}: Pick<LoopFuncParams, 'handler' | 'step' | 'page' | 'executeStepList' | 'labellingData'>): Promise<Pick<ProjectBuildStepResult, 'isAborted' | 'infoCodes' | 'data'>> => {
    const {
        APP_DATA: {
            MAX_LOOPS_ITERATIONS,
        },
    } = getConfig();
    const {
        id,
    } = step;
    const infoCodes: InfoCode[] = [];
    if (!isNonEmptyString(step?.value?.jsCode?.value)) {
        const infoCode = {
            code: EVENTS.INVALID_LOOP_CONFIGURATION,
            level: INFO_CODE_LEVELS.ERROR,
            timestamp: Date.now(),
            data: {
                id,
            },
        };
        await logFromAPI(infoCode);
        infoCodes.push(infoCode);

        return {
            isAborted: step.abortOnError,
            infoCodes,
            data: {
                success: false,
            },
        };
    }

    try {
        const data: ProjectSolutionStepResultData = {
            success: true,
            stepListResults: [],
        };
        let isAborted: boolean = false;
        let index = 0;

        while (index < MAX_LOOPS_ITERATIONS) {
            const [error, canContinue] = await evaluateFunction({
                page,
                config: {
                    paramNameList: ['index'],
                    paramValueList: [index],
                },
                extractionCode: step?.value?.jsCode?.value,
            }) as [Error, boolean];

            if (error) {
                infoCodes.push(...getInfoCodeList(error));
                break;
            }

            if (!canContinue || isAborted || handler.getCancellationDataScope(CancellationScopes.global)) {
                break;
            }

            const [referenceError] = await evaluateFunction({
                page,
                config: {
                    paramNameList: [],
                    paramValueList: [],
                },
                extractionCode: `window.${step.indexReference} = ${index};`,
            });

            if (referenceError) {
                infoCodes.push(...getInfoCodeList(referenceError));
                break;
            }

            const {
                isStepListInLoopAborted,
                subStep,
                stepListResults,
                isBroken,
                infoCodes: executeStepListInfoCodes,
            } = await executeStepList({
                index,
            });

            if (isBroken) {
                infoCodes.push(...executeStepListInfoCodes);
                break;
            }

            isAborted = step.abortOnError && isStepListInLoopAborted;
            if (isAborted) {
                infoCodes.push({
                    code: EVENTS.STEP_ABORTED,
                    level: INFO_CODE_LEVELS.ERROR,
                    timestamp: Date.now(),
                    data: {
                        id,
                        index,
                        subData: {
                            id: subStep?.id,
                        },
                    },
                });
            }

            data.stepListResults!.push({
                data: {
                    success: true,
                    stepListResults,
                },
                step,
                id: `${index}`,
                infoCodes: executeStepListInfoCodes,
                isAborted,
                labellingData,
                stepType: solutionStepTypes.loopIteration,
                ts: Date.now(),
            });

            index += 1;
        }

        return {
            isAborted,
            infoCodes,
            data,
        };
    } catch (error) {
        return {
            isAborted: step.abortOnError,
            infoCodes: getInfoCodeList(error),
            data: {
                success: false,
            },
        };
    }
};
