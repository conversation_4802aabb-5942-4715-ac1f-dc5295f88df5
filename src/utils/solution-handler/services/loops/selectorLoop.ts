import {getConfig} from 'app-config';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {
    isNonEmptyString,
    isPositiveInt,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {CancellationScopes} from '@w7-3/webeagle-resources/dist/config/build';
import {logFromAPI} from '../../../logger';
import {getInfoCodeList} from '../../../getInfoCodes';
import evaluateFunction from '../../../../utils/evaluateFunction';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {LoopFuncParams} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildStepResult,
    ProjectSolutionStepResultData,
} from '@w7-3/webeagle-resources/types/project';

export default async ({
    handler,
    step,
    page,
    labellingData,
    executeStepList,
}: Pick<LoopFuncParams, 'handler' | 'step' | 'page' | 'executeStepList' | 'labellingData'>): Promise<Pick<ProjectBuildStepResult, 'isAborted' | 'infoCodes' | 'data'>> => {
    const {
        APP_DATA: {
            MAX_LOOPS_ITERATIONS,
        },
    } = getConfig();
    const {
        id,
    } = step;
    const infoCodes: InfoCode[] = [];
    if (!isNonEmptyString(step?.selector)) {
        const infoCode = {
            code: EVENTS.INVALID_LOOP_CONFIGURATION,
            level: INFO_CODE_LEVELS.ERROR,
            timestamp: Date.now(),
            data: {
                id,
            },
        };
        await logFromAPI(infoCode);

        return {
            isAborted: step.abortOnError,
            infoCodes: [infoCode],
            data: {
                success: false,
            },
        };
    }

    try {
        const data: ProjectSolutionStepResultData = {
            success: true,
        };
        const tempListInWindowContext = `window.${step.indexReference}_TEMP`;
        const extractionCode = `
            var nodeList = document.querySelectorAll(${JSON.stringify(step?.selector)});
            ${tempListInWindowContext} = Array.from(nodeList);
            
            return nodeList.length;
        `;
        let isAborted: boolean = false;

        let [error, size] = await evaluateFunction({
            page,
            config: {
                paramNameList: [],
                paramValueList: [],
            },
            extractionCode,
        }) as [Error | null, number];

        if (error) {
            return {
                isAborted: step.abortOnError,
                infoCodes: [
                    ...infoCodes,
                    ...getInfoCodeList(error),
                ],
                data: {
                    success: false,
                },
            };
        }

        if (!isPositiveInt(size)) {
            return {
                isAborted: step.abortOnError,
                infoCodes: [{
                    code: EVENTS.STEP_LOOP_EMPTY,
                    level: INFO_CODE_LEVELS.INFO,
                    timestamp: Date.now(),
                    data: {
                        id,
                    },
                }],
                data: {
                    success: false,
                },
            };
        }

        data.stepListResults = [];
        let index = 0;
        while (index < MAX_LOOPS_ITERATIONS && index < size) {
            if (isAborted || handler.getCancellationDataScope(CancellationScopes.global)) {
                break;
            }

            const [referenceError, referenceExists] = await evaluateFunction({
                page,
                config: {
                    paramNameList: [],
                    paramValueList: [],
                },
                extractionCode: `return Array.isArray(window.${step.indexReference});`,
            }) as [Error, number];

            if (referenceError) {
                infoCodes.push(...getInfoCodeList(referenceError));

                break;
            }

            if (!referenceExists) {
                ([error, size] = await evaluateFunction({
                    page,
                    config: {
                        paramNameList: [],
                        paramValueList: [],
                    },
                    extractionCode,
                }) as [Error, number]);

                if (error) {
                    infoCodes.push(...getInfoCodeList(error));

                    break;
                }

                if (index >= size) {
                    infoCodes.push({
                        code: EVENTS.STEP_REFERENCE_LOST,
                        level: INFO_CODE_LEVELS.INFO,
                        timestamp: Date.now(),
                        data: {
                            id,
                            index,
                        },
                    });

                    break;
                }
            }

            ([error] = await evaluateFunction({
                page,
                config: {
                    paramNameList: [],
                    paramValueList: [],
                },
                extractionCode: `window.${step.indexReference} = ${tempListInWindowContext}[${index}];`,
            }));

            if (error) {
                infoCodes.push(...getInfoCodeList(error));

                break;
            }

            const {
                isStepListInLoopAborted,
                subStep,
                stepListResults,
                isBroken,
                infoCodes: executeStepListInfoCodes,
            } = await executeStepList({
                index,
            });

            if (isBroken) {
                infoCodes.push(...executeStepListInfoCodes);
                break;
            }

            isAborted = step.abortOnError && isStepListInLoopAborted;
            if (isAborted) {
                infoCodes.push({
                    code: EVENTS.STEP_ABORTED,
                    level: INFO_CODE_LEVELS.ERROR,
                    timestamp: Date.now(),
                    data: {
                        id,
                        index,
                        subData: {
                            id: subStep?.id,
                        },
                    },
                });
            }

            data.stepListResults.push({
                data: {
                    success: true,
                    stepListResults,
                },
                step,
                id: `${index}`,
                infoCodes: executeStepListInfoCodes,
                isAborted,
                labellingData,
                stepType: solutionStepTypes.loopIteration,
                ts: Date.now(),
            });

            index += 1;
        }

        return {
            isAborted,
            infoCodes,
            data,
        };
    } catch (error) {
        return {
            isAborted: step.abortOnError,
            infoCodes: getInfoCodeList(error),
            data: {
                success: false,
            },
        };
    }
};
