import e2eVisualTests, {
    visualTestStates,
} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import GlobalLinkHandler from './GlobalLinkHandler';
import {getInfoCodeList} from '../../getInfoCodes';
import manualVisualTests
    from '../services/solutions/e2eVisualTests/customE2EVisualTests';
import automatedE2EVisualTests
    from '../services/solutions/e2eVisualTests/automatedE2EVisualTests';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    LinkBuildResultSolutionData,
    SolutionSummaryE2EVisualTests,
    VisualDiffResult,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildStep,
    ProjectBuildStepLabelling,
} from '@w7-3/webeagle-resources/types/project';
import {getW103Global} from '../../globalHandlers';

export default class E2EVisualTestsSolutionHandler extends GlobalLinkHandler {
    declare solutionSummary: SolutionSummaryE2EVisualTests;

    protected async getItemData({
        step,
        collection,
        pageBuildSessionId,
        options,
        contextId,
        labellingData,
    }: {
        step: ProjectBuildStep;
        collection: string;
        pageBuildSessionId: string;
        options: any; // @todo add type
        contextId: string;
        labellingData: ProjectBuildStepLabelling;
    }): Promise<LinkBuildResultSolutionData> {
        const {
            id,
            type,
            abortOnError,
        } = step;

        this.updateConsumptionContingent({
            e2eVisualTests: 1,
        });

        const handler = this;
        const infoCodes: InfoCode[] = [];
        const isAborted = Boolean(abortOnError);
        const w103Global = getW103Global();
        const page = w103Global.browserPagesReversed.get(pageBuildSessionId);

        try {
            const {
                result,
                files,
            } = await manualVisualTests({
                collection,
                contextId,
                handler,
                labellingData,
                page: page!,
                pageBuildSessionId,
                step,
            }, {options}) as {
                result: VisualDiffResult,
                files: {
                    currentScreenshotPathName: string,
                    baselineScreenshotPathName: string,
                    diffScreenshotFilePathName: string,
                },
            };

            this.solutionSummary.e2eVisualTests.additionalResults[result.state] += 1;
            this.solutionSummary.e2eVisualTests.results.success += 1;
            let passed = result.state === visualTestStates.accepted || (
                !result.baseline && result.state === visualTestStates.new && options?.acceptFirstShot
            );
            let state = passed ? visualTestStates.accepted : undefined;

            if (!passed && type === e2eVisualTests.automated.ai.value) {
                const aiEvaluation = await automatedE2EVisualTests({
                    collection,
                    contextId,
                    handler,
                    labellingData,
                    step,
                    page: page!,
                    pageBuildSessionId,
                }, {
                    files,
                    context: {
                        differentPixels: result?.differentPixels,
                        totalPixels: result?.totalPixels,
                        deviceScaleFactor: result?.deviceScaleFactor,
                    },
                });

                if (!aiEvaluation.success) {
                    this.solutionSummary.e2eVisualTests.results.success -= 1;
                    this.solutionSummary.e2eVisualTests.results.failure += 1;

                    infoCodes.push(...aiEvaluation.infoCodes);
                    passed = false;
                }

                if (aiEvaluation.success) {
                    passed = aiEvaluation.passed;
                    result.reviewData = aiEvaluation.reviewData;
                    // @ts-expect-error type is ok
                    state = passed ? visualTestStates.accepted : visualTestStates.rejected;
                }
            }

            if (state) {
                await handler.storage.setVisualDiffBaseline({
                    id: result.id,
                    state,
                    currentImage: result.currentImage,
                    reviewData: result.reviewData,
                });
            }

            return {
                id,
                stepType: type,
                isAborted: false,
                infoCodes,
                data: {
                    result,
                    success: true,
                },
            };
        } catch (e) {
            const error = e as Error;
            this.solutionSummary.e2eVisualTests.results.failure += 1;

            return {
                id,
                stepType: type,
                isAborted,
                infoCodes: getInfoCodeList(error),
                data: {
                    success: false,
                },
            };
        }
    }
}
