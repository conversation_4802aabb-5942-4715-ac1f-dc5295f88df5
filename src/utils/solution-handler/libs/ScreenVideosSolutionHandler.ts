import path from 'path';
import chillaxe from '@w7-3/webeagle-resources/dist/libs/chillaxe';
import {getFileSize} from '@w7-3/webeagle-resources/dist/libs/file';
import GlobalLinkHandler from './GlobalLinkHandler';
import type {
    GetSolutionDataParams,
    LinkBuildResultSolutionData,
    SolutionSummaryScreenVideos,
} from '@w7-3/webeagle-resources/types/solutions';
import {getW103Global} from '../../globalHandlers';

const MINIMUM_VIDEO_DURATION = 5000;

export default class ScreenVideosSolutionHandler extends GlobalLinkHandler {
    declare solutionSummary: SolutionSummaryScreenVideos;

    private async getVideoFileData(params: GetSolutionDataParams): Promise<{
        success: boolean,
        data: {
            name: string,
            newFileName: string,
            extension: string,
            directory: string,
        },
    }> {
        const {
            collection,
            id,
            pageBuildSessionId,
        } = params;
        const w103Global = getW103Global();
        const page = w103Global.browserPagesReversed.get(pageBuildSessionId);
        await chillaxe(MINIMUM_VIDEO_DURATION);
        await this.closeTab(pageBuildSessionId);
        const newFileName = `video-${id}`;
        const filePath = await page?.video()?.path() as string;
        const fileName = path.basename(filePath);
        const extensionWithDot = path.extname(fileName);
        const extension = extensionWithDot.substring(1);
        const name = path.basename(filePath, extensionWithDot);
        const directory = `${this.storage.getStorageDirectory()}${collection}`;

        let interval: NodeJS.Timeout;
        const success = await Promise.race<boolean>([
            new Promise((resolve) => {
                setTimeout(() => {
                    resolve(false);
                }, 5000);
            }),
            await new Promise((resolve) => {
                interval = setInterval(async () => {
                    await getFileSize({filePath}).then((size) => {
                        if (size > 0) {
                            resolve(true);
                        }
                    });
                }, 1000);
            }),
        ]);

        clearInterval(interval!);

        return {
            success,
            data: {
                name,
                newFileName,
                extension,
                directory,
            },
        };
    }

    protected async getSolutionData(params: GetSolutionDataParams): Promise<LinkBuildResultSolutionData> {
        const result = await super.getSolutionData(params);

        this.updateConsumptionContingent({
            screenVideos: 1,
        });
        const {success, data} = await this.getVideoFileData(params);

        if (!success) {
            this.solutionSummary.screenVideos.results.failure += 1;
            return {
                ...result,
                video: {
                    success,
                },
            };
        }

        const video = await this.storage.uploadFile(data, false);
        this.solutionSummary.screenVideos.results[!result?.isAborted && video?.success ? 'success' : 'failure'] += 1;

        return {
            ...result,
            video,
        };
    }
}
