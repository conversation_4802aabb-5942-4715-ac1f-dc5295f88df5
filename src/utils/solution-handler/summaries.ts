import type {
    BuildSummary,
    SolutionSummarySteps,
    SolutionSummaryLinkChecker,
    SolutionSummaryScreenVideos,
    SolutionSummaryURLChallenge,
    SolutionSummaryE2EVisualTests,
    SolutionSummaryScreenshots,
    SolutionSummaryLighthouse,
    SolutionSummaryDataExtractions,
} from '@w7-3/webeagle-resources/types/solutions';

export const linkChecker: SolutionSummaryLinkChecker['linkChecker'] = {
    results: {
        ignored: 0,
        invalid: 0,
        success: 0,
        failure: 0,
    },
};

export const workflowSteps: SolutionSummarySteps = {
    action: 0,
    condition: 0,
    loop: 0,
    loopIteration: 0,
    root: 0,
    solution: 0,
};

export const buildSummary: BuildSummary = {
    aiRequests: 0,
    buildSteps: 0,
    builds: 0,
    httpRequests: 0,
    buildRuntimeMinutes: 0,
    dataExtractions: 0,
    e2eVisualTests: 0,
    screenshots: 0,
    screenVideos: 0,
    lighthouse: 0,
    urlChallenge: 0,
};

export const screenVideos: SolutionSummaryScreenVideos['screenVideos'] = {
    results: {
        success: 0,
        failure: 0,
    },
    steps: workflowSteps,
};

export const urlChallenge: SolutionSummaryURLChallenge['urlChallenge'] = {
    results: {
        success: 0,
        failure: 0,
    },
    additionalResults: {
        participants: 0,
        totalChallenges: 0,
        passedChallenges: 0,
        failedOrVoidChallenges: 0,
    },
    steps: workflowSteps,
};

export const e2eVisualTests: SolutionSummaryE2EVisualTests['e2eVisualTests'] = {
    results: {
        success: 0,
        failure: 0,
    },
    additionalResults: {
        new: 0,
        accepted: 0,
        rejected: 0,
        changed: 0,
    },
    steps: workflowSteps,
};

export const screenshots: SolutionSummaryScreenshots['screenshots'] = {
    results: {
        success: 0,
        failure: 0,
    },
    steps: workflowSteps,
};

export const dataExtractions: SolutionSummaryDataExtractions['dataExtractions'] = {
    results: {
        success: 0,
        failure: 0,
    },
    steps: workflowSteps,
};


export const lighthouse: SolutionSummaryLighthouse['lighthouse'] = {
    mobile: {
        results: {
            success: 0,
            failure: 0,
        },
        additionalResults: {
            performance: 0,
            accessibility: 0,
            'best-practices': 0,
            seo: 0,
            pwa: 0,
        },
    },
    desktop: {
        results: {
            success: 0,
            failure: 0,
        },
        additionalResults: {
            performance: 0,
            accessibility: 0,
            'best-practices': 0,
            seo: 0,
            pwa: 0,
        },
    },
};
