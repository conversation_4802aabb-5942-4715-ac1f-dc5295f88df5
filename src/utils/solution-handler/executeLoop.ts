import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import loops from '@w7-3/webeagle-resources/dist/config/loops';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {CancellationScopes} from '@w7-3/webeagle-resources/dist/config/build';
import {logFromAPI} from '../logger';
import customListLoop from './services/loops/customListLoop';
import jsListLoop from './services/loops/jsListLoop';
import jsLoop from './services/loops/jsLoop';
import rangeLoop from './services/loops/rangeLoop';
import selectorLoop from './services/loops/selectorLoop';
import evaluateFunction from '../evaluateFunction';
import {getInfoCodeList} from '../getInfoCodes';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {LoopFuncParams} from '@w7-3/webeagle-resources/types/solutions';
import type {
    ProjectBuildStep,
    ProjectBuildStepResult,
} from '@w7-3/webeagle-resources/types/project';

const handlers = {
    [loops.customList.value]: customListLoop,
    [loops.jsList.value]: jsListLoop,
    [loops.js.value]: jsLoop,
    [loops.range.value]: rangeLoop,
    [loops.selector.value]: selectorLoop,
};

export default async ({
    contextId,
    getStepResultCurry,
    handler,
    labellingData,
    page,
    step,
}: Omit<LoopFuncParams, 'executeStepList'>): Promise<{
    isAborted: boolean,
    infoCodes: Array<InfoCode>,
    data?: any,
}> => {
    const {
        id,
    } = step;
    const callback = handlers[step?.type];

    if (typeof callback !== 'function') {
        await logFromAPI({
            code: EVENTS.INVALID_LOOP_CONFIGURATION,
            level: INFO_CODE_LEVELS.ERROR,
            timestamp: Date.now(),
            data: {
                id,
            },
        });

        return {
            isAborted: step.abortOnError,
            infoCodes: [{
                code: EVENTS.INVALID_LOOP_CONFIGURATION,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    id,
                },
            }],
            data: {
                success: false,
            },
        };
    }

    const executeStepList = async ({
        index,
    }: {
        index: number;
    }): Promise<{
        isStepListInLoopAborted: boolean,
        subStep: null | ProjectBuildStep,
        stepListResults: ProjectBuildStepResult[],
        isBroken: boolean,
        infoCodes: Array<InfoCode>,
    }> => {
        const infoCodes: InfoCode[] = [];
        const stepListResults: ProjectBuildStepResult[] = [];
        let isStepListInLoopAborted = false;
        let subStep: null | ProjectBuildStep = null;
        let stepResult: ProjectBuildStepResult;

        const [error, breakLoop] = isNonEmptyString(step.breakConditionJSCode?.value) ? await evaluateFunction({
            page,
            config: {
                paramNameList: ['index'],
                paramValueList: [index],
            },
            extractionCode: step.breakConditionJSCode.value,
        }) as [Error, boolean]: [null, false];

        const isBroken = breakLoop || Boolean(error);

        if (error) {
            infoCodes.push(...getInfoCodeList(error));
        }

        if (isBroken) {
            infoCodes.push({
                code: EVENTS.STEP_LOOP_BROKEN,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    label: labellingData?.label,
                    position: index + 1,
                },
            });
        }

        if (!isBroken) {
            for (let j = 0; j < step.stepList.length; j++) {
                subStep = step.stepList[j];

                if (isStepListInLoopAborted || handler.getCancellationDataScope(CancellationScopes.global)) {
                    break;
                }

                stepResult = await getStepResultCurry({
                    step: subStep,
                    parentStep: step,
                    subContextId: `${contextId}_${step.id}_${subStep?.originalStepIndex}`,
                });

                stepListResults.push(stepResult);
                isStepListInLoopAborted = step.abortOnError && !subStep?.ignoreOnError && stepResult.isAborted;
            }
        }

        return {
            isStepListInLoopAborted,
            subStep,
            stepListResults,
            isBroken,
            infoCodes,
        };
    };

    return await callback({
        handler,
        step,
        page,
        labellingData,
        executeStepList,
    });
};
