import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {isPositiveInt} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    projectStates,
    vendors,
} from '@w7-3/webeagle-resources/dist/config/project';
import dbInstance from '../setup/setupDB';
import type {
    ProjectConfig,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import getNextBuildListFromCron from './getNextBuildListFromCron';
import projectQueueForBuild from './projects/projectQueueForBuild';
import type {ProjectBuildQueueItem} from '@w7-3/webeagle-resources/types/project';

const dao = dbInstance.getDAO();

export default async ({
    accountId,
    projectId,
    triggeredBy,
}: {
    accountId: string;
    projectId: string;
    triggeredBy: ProjectBuildQueueItem['triggeredBy'];
}) => {
    const paths = getPaths({
        accountId,
        projectId,
    });

    const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
        collection: paths.collections.projects,
        id: projectId,
    });

    if (!projectData?.configId || projectData.state !== projectStates.active) {
        return;
    }

    const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
        id: projectData.configId,
        collection: paths.collections.configs,
    });

    const projectConfigData = projectConfig?.data;

    if (!projectConfigData?.summary?.activateScheduler) {
        return;
    }

    const {
        schedulerData,
        schedulerEndDate,
    } = projectConfigData.summary;

    const nextBuildList = getNextBuildListFromCron({
        schedulerData,
        startDate: Date.now(),
        endDate: schedulerEndDate,
        pageSize: 1,
    });

    const executeAt = nextBuildList?.[0];

    if (!isPositiveInt(executeAt)) {
        return;
    }

    await projectQueueForBuild({
        accountId,
        projectId,
        item: {
            executeAt,
            retries: [],
            vendor: vendors.auto,
            triggeredBy,
        },
    });
};
