import * as starterPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/starterPackage';
import * as advancedPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/advancedPackage';
import * as premiumPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/premiumPackage';
import * as subscriptions
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    CREDIT_EVENT_CONTEXTS,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {
    handleLifeTimeAutomationCredits,
    handleLifeTimeCollaboratorSeats,
} from '../handleOneTimeItems';
import type {
    StripeChargeItem,
    StripeCheckoutLineItem,
    StripeCheckoutSessionData,
    CheckoutTransaction,
    Purchase,
    SubscriptionSummary,
} from '@w7-3/webeagle-resources/types/administration';
import dbInstance from '../../setup/setupDB';
import type Stripe from 'stripe';
import logger from '../logger';
import {handleTopUp} from '../credits';

const dao = dbInstance.getDAO();

const packageList = [
    starterPackage,
    advancedPackage,
    premiumPackage,
];

export const getSubscriptionSummary = (priceId: string): null | SubscriptionSummary => {
    for (let i = 0; i < packageList.length; i++) {
        const pack = packageList[i];
        const buildModeLicenceList = Object.keys(subscriptions.buildModeLicenses);
        for (let j = 0; j < buildModeLicenceList.length; j++) {
            const buildModeLicence = buildModeLicenceList[j];
            const plans = pack?.buildModeLicenses?.[buildModeLicence]?.plans;

            if (!plans) {
                continue;
            }

            const interval = Object.keys(plans).find((item: string): boolean => {
                return plans[item].priceId === priceId;
            });

            if (!interval) {
                continue;
            }

            return {
                hasEnteredOverage: false,
                buildModeLicence,
                initialContingent: pack.packages,
                interval,
                restContingent: pack.packages,
                subscriptionCategory: pack.appName,
            };
        }
    }

    return null;
};

export const getInvoiceData = ({
    checkoutLineItem,
    checkoutData,
}: {
    checkoutLineItem: StripeCheckoutLineItem,
    checkoutData: StripeCheckoutSessionData,
    chargeItem: StripeChargeItem,
}): CheckoutTransaction<true>['data'] => {
    const created = Date.now();
    const invoice = checkoutData.invoice as Stripe.Invoice;
    const invoiceLineItem = invoice?.lines?.data?.find((item): boolean => {
        return checkoutLineItem.price?.id === item.price?.id;
    });
    const subscription = checkoutData.subscription as Stripe.Subscription;
    const id = invoice.id;
    const subscriptionId = subscription.id;
    const periodStart = invoiceLineItem!.period.start * 1000;
    const periodEnd = invoiceLineItem!.period?.end * 1000;
    const cancelAtPeriodEnd = Boolean(subscription?.cancel_at_period_end);
    const subscriptionSummary = getSubscriptionSummary(checkoutLineItem.price!.id);

    if (!subscriptionSummary) {
        logger.error('Subscription summary not found', {
            checkoutLineItem,
            checkoutData,
        });
        throw new Error('Subscription summary not found');
    }

    return {
        cancelAtPeriodEnd,
        created,
        id,
        isCancelled: false,
        periodEnd,
        periodStart,
        subscriptionId,
        subscriptionSummary,
        renewals: {
            count: 0,
            isPastDue: false,
            billingReference: '',
            history: [],
        },
    };
};

export const processLineItem = async ({
    checkoutData,
    accountId,
    chargeItem,
    checkoutLineItem,
}: {
    checkoutData: StripeCheckoutSessionData,
    accountId: string,
    chargeItem: StripeChargeItem,
    checkoutLineItem: StripeCheckoutLineItem,
}): Promise<void> => {
    const {
        id,
        description,
        price,
    } = checkoutLineItem;

    let isLifeTimeCollaboratorSeats = false;
    let isLifeTimeAutomationCredits = false;

    if (price?.type === 'one_time') {
        try {
            isLifeTimeCollaboratorSeats = await handleLifeTimeCollaboratorSeats({
                checkoutLineItem,
                accountId,
            });

            if (!isLifeTimeCollaboratorSeats) {
                isLifeTimeAutomationCredits = await handleLifeTimeAutomationCredits({
                    accountId,
                    checkoutId: checkoutData.id,
                    email: checkoutData.customer_email!,
                    item: {
                        priceId: price.id,
                        quantity: checkoutLineItem.quantity as number,
                    },
                    loginSessionId: checkoutData.id,
                    source: checkoutLineItem.id,
                });
            }
        } catch (error) {
            logger.error('Error while processing one time item', {error});
        }
    }

    const periodEnd = Date.now() + (1000 * 60 * 60 * 24 * 30);
    const isSubscriptionItem = price?.type === 'recurring';
    const paths = getPaths({accountId});

    if (isSubscriptionItem) {
        const invoiceData = getInvoiceData({
            checkoutLineItem,
            checkoutData,
            chargeItem,
        });
        const subscription: Omit<CheckoutTransaction<true>, 'checkout'> = {
            active: price!.active,
            checkoutId: checkoutData?.id,
            currency: checkoutLineItem.currency as 'chf',
            data: invoiceData,
            description: description as string,
            id,
            isSubscriptionItem,
            priceId: price!.id,
            product: price!.product as string,
            quantity: checkoutLineItem.quantity as number,
            type: price.type,
            monthlyReset: {
                count: 0,
                nextResetTimestamp: periodEnd,
                previousResetTimestamp: 0,
            },
        };

        await dao.setCollectionDocumentData<Omit<CheckoutTransaction<true>, 'checkout'>>({
            id: checkoutLineItem.id,
            collection: paths.collections.subscriptions,
            data: subscription,
        });

        if (invoiceData.subscriptionSummary.initialContingent.additional.automationCredits.active) {
            await handleTopUp({
                accountId,
                context: CREDIT_EVENT_CONTEXTS.SUBSCRIPTION,
                quantity: invoiceData.subscriptionSummary.initialContingent.additional.automationCredits.quantity,
                checkoutId: checkoutData.id,
                email: checkoutData.customer_email!,
                source: checkoutLineItem.id,
                loginSessionId: checkoutData.id,
                expiry: subscription.data.periodEnd,
            });
        }
    }

    if (isLifeTimeCollaboratorSeats || isLifeTimeAutomationCredits) {
        const purchase: Omit<Purchase, 'checkout'> = {
            checkoutId: checkoutData?.id,
            currency: checkoutLineItem.currency as 'chf',
            description: description as string,
            id,
            priceId: price!.id,
            product: price!.product as string,
            quantity: checkoutLineItem.quantity as number,
            isLifeTimeCollaboratorSeats,
            isLifeTimeAutomationCredits,
            type: price?.type as 'one_time',
        };

        await dao.setCollectionDocumentData<Omit<Purchase, 'checkout'>>({
            id: checkoutLineItem.id,
            collection: paths.collections.subscriptions,
            data: purchase,
        });
    }
};
