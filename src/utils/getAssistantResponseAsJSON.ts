import getParsedJSON from '@w7-3/webeagle-resources/dist/libs/getParsedJSON';

export default <T>(content: unknown): T => {
    if (!content || typeof content !== 'string') {
        return {} as T;
    }

    const jsonMatch = (content as string).match(/```json[\r\n]+([\s\S]*?)\r?\n?```/);
    const jsonString = jsonMatch && jsonMatch[1] ? jsonMatch[1] : content;

    return getParsedJSON(jsonString as string) as T;
};
