import type {Page} from '@w7-3/webeagle-resources/types/playwright';
import chillaxe from '@w7-3/webeagle-resources/dist/libs/chillaxe';
import getListOfTagsWithLinks from './getListOfTagsWithLinks';
import {getInfoCodeList} from './getInfoCodes';
import evaluateFunction from './evaluateFunction';
import type {InfoCode, LinkOptionsSelection} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {LinkContextData} from '@w7-3/webeagle-resources/types/solutions';

type InputParams = {
    page: Page,
    selection: LinkOptionsSelection,
};
type ResponseParams = Promise<{
    children: Array<LinkContextData>,
    infoCodes: Array<InfoCode>,
}>;

const attempts = 3;

export const getLinksOnPage = async ({
    page,
    selection,
}: InputParams): ResponseParams => {
    try {
        const query = getListOfTagsWithLinks({
            selection,
        });
        const [error, children] = await evaluateFunction({
            page,
            config: {
                paramNameList: [],
                paramValueList: [],
            },
            extractionCode: query,
        }) as [Error, Array<LinkContextData>];

        if (error) {
            return {
                children: [],
                infoCodes: getInfoCodeList(error),
            };
        }

        return {
            children,
            infoCodes: [],
        };
    } catch (error) {
        return {
            children: [],
            infoCodes: getInfoCodeList(error),
        };
    }
};

export default async (params: InputParams): ResponseParams => {
    let children;
    let infoCodes;
    await chillaxe(1000);
    for (let i = 0; i < attempts; i++) {
        ({
            infoCodes,
            children,
        } = await getLinksOnPage(params));

        if (infoCodes.length < 1) {
            break;
        }

        await chillaxe(500);
    }

    return {
        infoCodes,
        children,
    };
};

