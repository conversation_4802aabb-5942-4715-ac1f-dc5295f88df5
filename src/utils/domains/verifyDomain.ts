import axios from 'axios';
import robotsParser from 'robots-parser';
import {
    VERIFICATION_SNIPPETS,
    VERIFICATION_TYPE,
    PLACEHOLDER,
} from '@w7-3/webeagle-resources/dist/config/domains';
import {WEB_AUTOMATE_CRAWLER_NAME} from '@w7-3/webeagle-resources/dist/config/w73';
import type {
    UserData,
    DomainVerification,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import getCollaboratorList from '../getCollaboratorList';
import {parse} from 'tldts';

export default async ({
    url,
    accountId,
    method,
}: {
    url: string;
    accountId: string;
    method: string;
}): Promise<{
    success: boolean;
    data: {canCrawl: boolean, crawlDelay: number},
    verifiedDomain?: DomainVerification,
}> => {
    let success = false;
    let data = {canCrawl: false, crawlDelay: 0};

    if (method === VERIFICATION_TYPE.ROBOTS_TXT) {
        try {
            const robotsTxtUrl = `${new URL(url).origin}/robots.txt`;
            const content = await axios.get(robotsTxtUrl);
            const robots = robotsParser(robotsTxtUrl, content.data);
            const canCrawl = !robots.isDisallowed(url, WEB_AUTOMATE_CRAWLER_NAME);
            const crawlDelay = robots.getCrawlDelay(WEB_AUTOMATE_CRAWLER_NAME) || 0;

            success = canCrawl;
            data = {canCrawl, crawlDelay};
        } catch (e) {
            success = false;
        }
    }

    if (method === VERIFICATION_TYPE.COLLABORATOR) {
        const domain = parse(url).domain as string;
        const collaboratorList: UserData[] = await getCollaboratorList(accountId);
        success = collaboratorList.some((collaborator) => {
            const domainFromURL = domain.split('.').slice(-2);
            const domainFromEmail = collaborator.email.split('@')[1].split('.')?.slice(-2);

            return domainFromURL.join('.') === domainFromEmail.join('.');
        });

        data = {canCrawl: success, crawlDelay: 0};
    }

    if (method === VERIFICATION_TYPE.META_TAG) {
        try {
            const content = await axios.get(url);
            const snippet = VERIFICATION_SNIPPETS[VERIFICATION_TYPE.META_TAG].snippet.replace(PLACEHOLDER, accountId);

            success = content.data.includes(snippet);
            data = {canCrawl: success, crawlDelay: 0};
        } catch (e) {
            success = false;
        }
    }

    return {
        success,
        data,
    };
};
