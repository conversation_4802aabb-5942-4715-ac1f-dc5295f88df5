import {format, getTime, formatDistanceToNow, intervalToDuration} from 'date-fns';
// import {enGB, fr, de} from 'date-fns/locale'

// ----------------------------------------------------------------------

export const padNumber = (number) => {
    if (number < 10) {
        return `0${number}`;
    }

    return number;
};

export const fPercentageValue = (number) => {
    if (Number.isNaN(number)) {
        return number;
    }

    return (Number.parseInt(number * 10000, 10)) / 100;
};

export const fDuration = (startDate, endDate = Date.now()) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const duration = intervalToDuration({start: start.getTime(), end: end.getTime()});

    return `${padNumber(duration.hours)}h ${padNumber(duration.minutes)}m ${padNumber(duration.seconds)}s`;
};

export function fDate(input, isUnix = true) {
    const date = typeof input === 'number' ?
        input * (isUnix ? 1000 : 1) :
        input;
    return format(new Date(date), 'dd MMMM yyyy');
}

export function fDateTime(input, isUnix = true) {
    try {
        const date = typeof input === 'number' ?
            input * (isUnix ? 1000 : 1) :
            input;
        return format(new Date(date), 'dd MMM yyyy HH:mm');
    } catch (e) {
        return undefined;
    }
}

export function fTimestamp(input, isUnix = true) {
    const date = typeof input === 'number' ?
        input * (isUnix ? 1000 : 1) :
        input;
    return getTime(new Date(date));
}

export function fDateTimeHumanReadable(input, isUnix = false) {
    try {
        const date = typeof input === 'number' ?
            input * (isUnix ? 1000 : 1) :
            input;
        return format(new Date(date), 'dd.MM.yyyy, HH:mm:ss');
    } catch (error) {
        return null;
    }
}

export function fToNow(input, isUnix = true) {
    try {
        const date = typeof input === 'number' ?
            input * (isUnix ? 1000 : 1) :
            input;
        return formatDistanceToNow(new Date(date), {
            addSuffix: true,
        });
    } catch (e) {
        return undefined;
    }
}
