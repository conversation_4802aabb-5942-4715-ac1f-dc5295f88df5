import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import chillaxe from '@w7-3/webeagle-resources/dist/libs/chillaxe';
import {REQUEST_WAIT_UNTIL} from '@w7-3/webeagle-resources/dist/config/request';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {EVENTS, INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getInfoCodeList} from './getInfoCodes';
import evaluateFunction from './evaluateFunction';
import type {LoadURLParams, URLRequestState} from '@w7-3/webeagle-resources/types/solutions';
import type {Response} from '@w7-3/webeagle-resources/types/playwright';

const attempts = 3;

const attemptLoadUrl = async ({
    urlConfig,
    page,
    linkRequestTimeout,
    waitUntil,
}: LoadURLParams): Promise<{urlRequestState: URLRequestState; response: null | Response}> => {
    const timeout = linkRequestTimeout || 0;
    const options = {
        timeout,
        waitUntil: waitUntil?.option,
    };

    if (!waitUntil?.option || waitUntil?.option === REQUEST_WAIT_UNTIL.js) {
        options.waitUntil = REQUEST_WAIT_UNTIL.domContentLoaded;
    }

    const [error, response = null] = await awaitOn<null | Response>(page.goto(urlConfig.url, options));

    if (!error && response && waitUntil?.option === REQUEST_WAIT_UNTIL.js) {
        const [error, result] = await evaluateFunction({
            page,
            extractionCode: waitUntil.jsCode?.value,
            config: {
                paramNameList: [],
                paramValueList: [],
            },
        });

        if (error) {
            return {
                urlRequestState: {
                    error,
                    isPageLoadComplete: false,
                    infoCodes: [{
                        code: EVENTS.PAGE_CUSTOM_TIMEOUT_JS_ERROR,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                    }],
                },
                response,
            };
        }

        if (!result) {
            return {
                urlRequestState: {
                    error,
                    isPageLoadComplete: false,
                    infoCodes: [{
                        code: EVENTS.PAGE_CUSTOM_TIMEOUT,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                        data: {
                            url: urlConfig?.url,
                            linkRequestTimeout,
                        },
                    }],
                },
                response,
            };
        }
    }

    return {
        urlRequestState: {
            error,
            isPageLoadComplete: !error && Boolean(response),
        },
        response,
    };
};

export default async (
    params: LoadURLParams,
): Promise<{urlRequestState: URLRequestState; response: Response}> => {
    let urlRequestState;
    let response;
    for (let i = 0; i < attempts; i++) {
        ({
            urlRequestState,
            response,
        } = await attemptLoadUrl(params));

        if (!urlRequestState.error && response) {
            break;
        }

        await chillaxe(250);
    }

    if (!isNonEmptyArray(urlRequestState.infoCodes)) {
        urlRequestState.infoCodes = [];
    }

    if ((urlRequestState.error || !response) && !isNonEmptyArray(urlRequestState.infoCodes)) {
        const infoCodeList = getInfoCodeList(urlRequestState.error, {
            url: params?.urlConfig?.url,
            linkRequestTimeout: params?.linkRequestTimeout,
        });

        urlRequestState.infoCodes.push(...infoCodeList);
    }

    return {
        urlRequestState,
        response,
    };
};
