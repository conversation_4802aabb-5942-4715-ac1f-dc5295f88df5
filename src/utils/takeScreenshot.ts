import type {Page, ElementHandle} from '@w7-3/webeagle-resources/types/playwright';
import chillaxe from '@w7-3/webeagle-resources/dist/libs/chillaxe';
import {getInfoCodeList} from './getInfoCodes';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';

type InputParams = {
    node: Page | ElementHandle,
    path: string,
    fullPage?: boolean,
    url: string,
};
type ResponseParams = Promise<{
    success?: boolean,
    infoCodes?: Array<InfoCode>,
}>;

const attempts = 3;

export const takeScreenshot = async ({
    node,
    path,
    fullPage,
    url,
}: InputParams): ResponseParams => {
    try {
        await node.screenshot({
            path,
            fullPage,
        });

        return {
            success: true,
        };
    } catch (error) {
        return {
            success: false,
            infoCodes: getInfoCodeList(error, {url}),
        };
    }
};

export default async (params: InputParams): ResponseParams => {
    let infoCodes;
    await chillaxe(1000);
    for (let i = 0; i < attempts; i++) {
        ({
            infoCodes,
        } = await takeScreenshot(params));

        if (infoCodes.length < 1) {
            break;
        }

        await chillaxe(500);
    }

    return {
        infoCodes,
    };
};

