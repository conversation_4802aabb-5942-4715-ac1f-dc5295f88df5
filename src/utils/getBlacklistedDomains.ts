import collections from '@w7-3/webeagle-resources/dist/config/collections';
import dbInstance from '../setup/setupDB';
import type {
    AccountData,
    BlacklistedDomain,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async ({
    accountData,
}: {
    accountData: AccountData,
}): Promise<{
    success: boolean;
    data: {
        globallyBlacklistedDomainList: BlacklistedDomain[];
    },
}> => {
    const [
        globallyBlacklistedDomainError,
        globallyBlacklistedDomains,
    ] = await dao.getAllCollectionData<BlacklistedDomain>({
        collection: collections.blacklistedDomains,
    });

    if (globallyBlacklistedDomainError) {
        return {
            success: false,
            data: {
                globallyBlacklistedDomainList: [],
            },
        };
    }

    const domainVerifications = accountData.domainVerifications || {};
    const globallyBlacklistedDomainList: BlacklistedDomain[] = [];

    Object.values(globallyBlacklistedDomains).forEach((item) => {
        if (domainVerifications[item.domain]) {
            return;
        }

        globallyBlacklistedDomainList.push(item);
    });

    return {
        success: true,
        data: {
            globallyBlacklistedDomainList,
        },
    };
};
