import {
    getHumanReadableTimestamp,
    humanReadableDateFormats,
} from '@w7-3/webeagle-resources/dist/libs/date';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import daoInstance from '../setup/setupDB';
import {getConfig} from 'app-config';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    UILog,
} from '@w7-3/webeagle-resources/types/ui';

const dao = daoInstance.getDAO();

export const logUIEvent = async (infoCode: InfoCode, data: UILog['data']): Promise<boolean> => {
    if (!infoCode?.level) {
        return false;
    }

    try {
        const timestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.fullWithMilliseconds});
        const isoTimestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.ISO});
        const config = getConfig();
        await dao.setCollectionDocumentData<UILog>({
            id: `${infoCode?.level}: ${timestamp}`,
            collection: `${collections.admin}/${collections.logsUI}/${isoTimestamp}`,
            data: {
                serverId: config.SERVER_ID as string,
                serverEnv: config.ENV,
                ts: Date.now(),
                infoCode,
                data,
            },
        });

        return true;
    } catch (e) {
        return false;
    }
};

export const logFromAPI = async (infoCode: InfoCode, data: UILog['data'] = {}) => {
    if (!infoCode?.level) {
        return;
    }

    try {
        const timestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.fullWithMilliseconds});
        const isoTimestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.ISO});
        const config = getConfig();
        await dao.setCollectionDocumentData<UILog>({
            id: `${infoCode?.level}: ${timestamp}`,
            collection: `${collections.admin}/${collections.logs}/${isoTimestamp}`,
            data: {
                serverId: config.SERVER_ID as string,
                serverEnv: config.ENV,
                ts: Date.now(),
                infoCode,
                data,
            },
        });
    } catch (error) {
        console.log('Cannot write admin log', {error});
    }
};

export const logIntoDB = async (collection: string, data: InfoCode): Promise<void> => {
    if (!data?.level) {
        return;
    }

    try {
        await dao.setCollectionDocumentData<InfoCode>({
            id: getHumanReadableTimestamp({template: humanReadableDateFormats.fullWithMilliseconds}),
            collection,
            data,
        });
    } catch (error) {
        console.log('Cannot log project event', {error});
    }
};

export default {
    debug: (message: string, config: Record<string, unknown> = {}) => {
        console.log(JSON.stringify({
            message,
            config,
            type: 'debug',
        }, null, 4));
        if (config?.error) {
            console.log('Error details:', config.error);
        }
    },
    error: (message: string, config: Record<string, unknown> = {}) => {
        console.log(JSON.stringify({
            message,
            config,
            type: 'error',
        }, null, 4));
        if (config?.error) {
            console.log('Error details:', config.error);
        }
    },
    fatal: (message: string, config: Record<string, unknown> = {}) => {
        console.log(JSON.stringify({
            message,
            config,
            type: 'fatal',
        }, null, 4));
        if (config?.error) {
            console.log('Error details:', config.error);
        }
    },
    info: (message: string, config: Record<string, unknown> = {}) => {
        console.log(JSON.stringify({
            message,
            config,
            type: 'info',
        }, null, 4));
        if (config?.error) {
            console.log('Error details:', config.error);
        }
    },
    warn: (message: string, config: Record<string, unknown> = {}) => {
        console.log(JSON.stringify({
            message,
            config,
            type: 'warn',
        }, null, 4));
        if (config?.error) {
            console.log('Error details:', config.error);
        }
    },
    trace: (message: string, config: Record<string, unknown> = {}) => {
        console.log(JSON.stringify({
            message,
            config,
            type: 'trace',
        }, null, 4));
        if (config?.error) {
            console.log('Error details:', config.error);
        }
    },
};
