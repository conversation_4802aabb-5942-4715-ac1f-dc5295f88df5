import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import {WAIT_FOR_SELECTOR_TIMEOUT} from '@w7-3/webeagle-resources/dist/config/misc';
import type {Page} from '@w7-3/webeagle-resources/types/playwright';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';

export default async ({selector, page}: {
    selector: string,
    page: Page,
}) => {
    if (!isNonEmptyString(selector)) {
        throw new Error(EVENTS.INVALID_ACTION_CONFIGURATION);
    }

    const [error] = await awaitOn(page.waitForSelector(selector, {
        timeout: WAIT_FOR_SELECTOR_TIMEOUT,
    }));

    if (error) {
        throw {
            infoCodes: [{
                code: EVENTS.ELEMENT_NOT_FOUND,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    selector,
                },
            }],
        };
    }
};
