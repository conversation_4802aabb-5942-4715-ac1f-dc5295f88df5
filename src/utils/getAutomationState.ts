import logger from './logger';
import type {
    BrowserContextSession,
    AutomationState,
} from '@w7-3/webeagle-resources/types/webautomate/state';
import type {Page} from '@w7-3/webeagle-resources/types/playwright';
import {getW103Global} from './globalHandlers';
import {getPageBuildRuntimeData} from './getPageBuildSessionData';

export default async ({
    page,
}: {
    page: Page;
}): Promise<{
    success: boolean;
    automationState?: AutomationState;
}> => {
    try {
        const {
            browserContexts,
            buildId,
            pageSessions,
            projectId,
            sessionStart,
            vendor,
        } = getW103Global();

        if (pageSessions.size === 0) {
            return {
                success: false,
            };
        }

        const pageBuildSessionList: AutomationState['pageBuildSessionList'] = [];
        for (const [, pageBuildSession] of pageSessions.entries()) {
            const runtime = await getPageBuildRuntimeData({
                page,
            });
            pageBuildSessionList.push({
                ...pageBuildSession,
                pageBuild: {
                    ...pageBuildSession.pageBuild,
                    runtime,
                },
            });
        }

        pageBuildSessionList.sort((a, b) => {
            return a.index - b.index;
        });

        const browserContextList: BrowserContextSession[] = Array.from(browserContexts.values());

        return {
            success: true,
            automationState: {
                browserContextList,
                buildId,
                currentUrl: page.url(),
                pageBuildSessionList,
                projectId,
                sessionStart,
                vendor,
            },
        };
    } catch (error) {
        logger.error('Failed to get AI Data', {
            error,
        });
        return {
            success: false,
        };
    }
};
