import * as starterPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/starterPackage';
import * as advancedPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/advancedPackage';
import * as premiumPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/premiumPackage';
import * as prePaidPackage
    from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/prePaidPackage';
import {priorityLevels} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import {PREPAID_SUBSCRIPTION_KEY} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import type {
    SubscriptionCategory,
    SubscriptionPackage,
} from '@w7-3/webeagle-resources/types/administration';

const packages: {
    packages: SubscriptionPackage;
    appName: string;
}[] = [
    starterPackage,
    advancedPackage,
    premiumPackage,
    prePaidPackage,
];
export const getPriorityLevel = (params: {
    subscriptionCategory?: SubscriptionCategory;
    subscriptionId: string;
}): SubscriptionPackage['priorityLevel'] => {
    if (params.subscriptionId === PREPAID_SUBSCRIPTION_KEY) {
        return prePaidPackage.packages.priorityLevel;
    }

    const item = packages.find((item) => item.appName === params.subscriptionCategory);

    if (!item) {
        return priorityLevels.STARTER;
    }

    return item.packages.priorityLevel;
};
