import {METHODS} from '@w7-3/webeagle-resources/dist/config/request';
import getRequestData from './getRequestData';
import type {Request} from '@w7-3/webeagle-resources/types/playwright';

describe('getRequestData', () => {
    it('should be ok', () => {
        const request = {
            method: jest.fn(),
            url: jest.fn(),
            postData: jest.fn(),
        };
        request.method.mockReturnValue(METHODS.GET);
        request.url.mockReturnValue('/current-page?x=1&y=2');

        expect(getRequestData(request as unknown as Request)).toMatchSnapshot();
    });
});
