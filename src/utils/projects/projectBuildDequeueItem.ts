import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    PROJECT_DEQUEUE_REASONS,
} from '@w7-3/webeagle-resources/dist/config/project';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {
    getHumanReadableTimestamp,
    humanReadableDateFormats,
} from '@w7-3/webeagle-resources/dist/libs/date';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    accountNotificationTypes,
} from '@w7-3/webeagle-resources/dist/config/account';
import {
    NOTIFICATION_METHODS,
    NOTIFICATION_TYPES,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import dbInstance from '../../setup/setupDB';
import type {
    ProjectBuildQueueItemVendor,
} from '@w7-3/webeagle-resources/types/project';
import type {
    AccountNotification,
    EmailNotification,
    ProjectNotificationPayload,
    ProjectSubscriptionProblemNotificationPayload,
} from '@w7-3/webeagle-resources/types/notifications';
import logger from '../logger';

const dao = dbInstance.getDAO();

export default async ({
    accountId,
    id,
    projectId,
    reason,
    vendor,
    data,
}: {
    accountId: string;
    projectId: string;
    id: string;
    reason: ProjectSubscriptionProblemNotificationPayload['reason'];
    vendor: ProjectBuildQueueItemVendor;
    data: ProjectNotificationPayload & {
        email: string;
        language: string;
    },
}): Promise<{
    success: boolean,
    directives: {
        notification: {
            message: string,
            data?: {
                projectName: string,
            },
        },
    },
}> => {
    const paths = getPaths({
        accountId,
        projectId,
    });

    try {
        await dao.setDocumentData({
            path: paths.collections.project,
            data: {
                buildQueue: {
                    [vendor]: dao.FieldValue.delete(),
                },
            },
        });

        await dao.deleteCollectionData({
            id,
            collection: collections.buildQueue,
        });
    } catch (error) {
        logger.error('Failed to dequeue project build', {error});
    }

    const isoTimestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.ISO});
    if (reason === PROJECT_DEQUEUE_REASONS.accountProblem) {
        const id = getRandomString(32, libraryKeys.alphaNumericLowerCased);
        await dao.setCollectionDocumentData<ProjectSubscriptionProblemNotificationPayload>({
            id,
            collection: `${collections.notifications}/${collections.buildQueue}/${isoTimestamp}`,
            data: {
                id,
                reason,
                projectId,
                accountId,
                ts: Date.now(),
            },
        });
    }

    if (reason === PROJECT_DEQUEUE_REASONS.subscriptionProblem) {
        const id = getRandomString(32, libraryKeys.alphaNumericLowerCased);
        await dao.setCollectionDocumentData<ProjectSubscriptionProblemNotificationPayload>({
            id,
            collection: `${collections.notifications}/${collections.buildQueue}/${isoTimestamp}`,
            data: {
                id,
                reason,
                projectId,
                accountId,
                ts: Date.now(),
            },
        });
    }

    if (reason === PROJECT_DEQUEUE_REASONS.overageProblem) {
        const paths = getPaths({
            accountId,
        });

        await dao.insertCollectionData<EmailNotification<ProjectNotificationPayload>>({
            collection: collections.notifications,
            data: {
                accountId,
                createdAt: Date.now(),
                data: {
                    email: data.email,
                    language: data.language,
                    payload: {
                        projectId,
                        projectName: data.projectName,
                        blacklistedDomainListInTarget: [],
                    },
                },
                method: NOTIFICATION_METHODS.EMAIL,
                sendAt: Date.now(),
                type: NOTIFICATION_TYPES.PROJECT_BUILD_UPDATES.type,
                subType: NOTIFICATION_TYPES.PROJECT_BUILD_UPDATES.subTypes.BUILD_SKIPPED_DUE_TO_OVERAGE,
            },
        });

        const notificationId = getRandomString(32, libraryKeys.alphaNumericLowerCased);
        await dao.setCollectionDocumentData<AccountNotification>({
            id: notificationId,
            collection: paths.collections.accountNotifications,
            data: {
                id: notificationId,
                type: accountNotificationTypes.buildNotification,
                ts: Date.now(),
                isNew: true,
                infoCodes: [{
                    code: EVENTS.PROJECT_BUILD_SKIPPED_DUE_TO_OVERAGE,
                    level: INFO_CODE_LEVELS.INFO,
                    timestamp: Date.now(),
                    data,
                }],
            },
        });
    }

    return {
        success: true,
        directives: {
            notification: {
                message: NOTIFICATIONS.projectBuildDequeued,
            },
        },
    };
};
