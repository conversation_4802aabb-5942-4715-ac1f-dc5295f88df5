import {parse} from 'tldts';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    EVENTS,
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    accountNotificationTypes,
} from '@w7-3/webeagle-resources/dist/config/account';
import dbInstance from '../../setup/setupDB';
import type {ProjectConfigData} from '@w7-3/webeagle-resources/types/project';
import type {
    AccountData,
    APIDirectives,
    BlacklistedDomain,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export const getUrlBlacklistData = async ({url, accountData}): Promise<{
    domain: string,
    isBlacklistedForAccount: boolean,
    isGloballyBlacklisted: boolean,
    domainData?: BlacklistedDomain,
}> => {
    const domain = parse(url).domain as string;

    if (accountData.domainVerifications?.[domain]) {
        return {
            domain,
            isBlacklistedForAccount: false,
            isGloballyBlacklisted: false,
        };
    }

    if (accountData?.blacklistedDomains?.[domain]) {
        return {
            domain,
            domainData: accountData.blacklistedDomains[domain],
            isBlacklistedForAccount: true,
            isGloballyBlacklisted: false,
        };
    }

    const [globallyBlockedDomainDataError, domainData] = await dao.getCollectionDocumentData<BlacklistedDomain>({
        collection: collections.blacklistedDomains,
        id: domain,
    });

    if (globallyBlockedDomainDataError || !domainData) {
        return {
            domain,
            isBlacklistedForAccount: false,
            isGloballyBlacklisted: false,
        };
    }

    return {
        domain,
        isBlacklistedForAccount: false,
        isGloballyBlacklisted: true,
        domainData,
    };
};

export default async ({
    accountId,
    urls,
}: {
    accountId: string,
    urls: ProjectConfigData['target']['urls'],
}): Promise<{
    success: boolean;
    data?: {
        directives: APIDirectives,
    },
}> => {
    if (!isNonEmptyArray(urls)) {
        return {
            success: true,
        };
    }

    const [accountDataError, accountData] = await dao.getCollectionDocumentData<AccountData>({
        collection: collections.accounts,
        id: accountId,
    });

    if (accountDataError || !accountData) {
        return {
            success: false,
            data: {
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            },
        };
    }

    const targetDomainList: string[] = [];
    const processedDomainList = Object.keys(accountData.domainBuilds);
    const blacklistedDomainListInTarget: string[] = [];
    const blacklistedDomainData: Record<string, BlacklistedDomain> = {};

    await Promise.allSettled(urls.map(async ({url}: {url: string}) => {
        const {
            domain,
            isBlacklistedForAccount,
            isGloballyBlacklisted,
            domainData,
        } = await getUrlBlacklistData({url, accountData});

        targetDomainList.push(domain);

        if (isBlacklistedForAccount) {
            blacklistedDomainListInTarget.push(domain);
            blacklistedDomainData[domain] = accountData.blacklistedDomains[domain];
            return;
        }

        if (isGloballyBlacklisted) {
            blacklistedDomainListInTarget.push(domain);
            blacklistedDomainData[domain] = domainData!;
        }
    }));

    if (blacklistedDomainListInTarget.length > 0) {
        return {
            success: false,
            data: {
                directives: {
                    custom: {
                        type: accountNotificationTypes.blacklistedDomainsInProject,
                        data: {
                            targetDomainList,
                            processedDomainList,
                            blacklistedDomainData,
                            blacklistedDomainListInTarget,
                        },
                        infoCodes: [{
                            code: EVENTS.BLOCKED_DOMAINS,
                            level: INFO_CODE_LEVELS.ERROR,
                            timestamp: Date.now(),
                            data: {
                                domainList: blacklistedDomainListInTarget,
                                dateTime: Date.now(),
                            },
                        }],
                    },
                },
            },
        };
    }

    return {
        success: true,
    };
};
