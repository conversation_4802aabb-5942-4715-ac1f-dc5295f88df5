import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import dbInstance from '../../setup/setupDB';
import type {
    InfoCode,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {ProjectBuildQueueItem} from '@w7-3/webeagle-resources/types/project';

const dao = dbInstance.getDAO();

export default async ({
    projectData,
    projectId,
    vendor,
}: {
    projectData: ProjectData,
    projectId: string,
    vendor: string,
}): Promise<{
    success: boolean,
    data?: {
        directives?: {
            infoCodes?: InfoCode[],
        },
    },
    infoCodes?: InfoCode[],
}> => {
    const {
        state,
        latestBuild,
    } = projectData;

    if (state !== projectStates.active) {
        return {
            success: false,
            data: {
                directives: {
                    infoCodes: [{
                        code: EVENTS.CANNOT_EXECUTE_INACTIVE_PROJECT,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                    }],
                },
            },
        };
    }

    if (latestBuild?.state === BUILD_STATES.running || latestBuild?.cancellation?.isInProgress) {
        return {
            success: false,
            data: {
                directives: {
                    infoCodes: [{
                        code: EVENTS.DUPLICATE_PROJECT_RUNNING,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                    }],
                },
            },
        };
    }

    const [, projectBuildQueue] = await dao.getAllCollectionDataAsList<ProjectBuildQueueItem>({
        collection: collections.buildQueue,
        condition: {
            fieldPath: 'projectId',
            opStr: operators.EQUAL,
            value: projectId,
        },
    });

    const projectBuildQueueItem = projectBuildQueue?.find((item) => {
        if (item.projectId !== projectId || item.isAssigned || item.isCompleted) {
            return false;
        }

        return item.vendor === vendor;
    });

    if (projectBuildQueueItem) {
        return {
            success: false,
            data: {
                directives: {
                    infoCodes: [{
                        code: EVENTS.DUPLICATE_PROJECT_QUEUED,
                        level: INFO_CODE_LEVELS.WARNING,
                        timestamp: Date.now(),
                    }],
                },
            },
        };
    }

    return {
        success: true,
    };
};
