import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import dbInstance from '../../setup/setupDB';
import type {
    APIDirectives,
    ProjectConfig,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import checkSubscriptionOk from './checkSubscriptionOk';
import type {
    ProjectSubscriptionData,
    ProjectSubscriptionSummary,
} from '@w7-3/webeagle-resources/types/administration';
import checkDomainsPreBuild from './checkDomainsPreBuild';

const dao = dbInstance.getDAO();

export default async ({
    accountId,
    projectId,
    checkSubscription,
}: {
    accountId: string,
    projectId: string,
    checkSubscription?: boolean,
}): Promise<{
    success: boolean,
    projectData?: ProjectData,
    projectConfig?: ProjectConfig,
    directives?: APIDirectives,
    subscription?: {
        selectedSubscription: ProjectSubscriptionData;
        projectSubscriptionSummary: ProjectSubscriptionSummary;
        matchingSubscriptionDataList: ProjectSubscriptionData[];
    },
}> => {
    const paths = getPaths({
        accountId,
        projectId,
    });

    const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
        collection: paths.collections.projects,
        id: projectId,
    });

    if (!projectData) {
        return {
            success: false,
            directives: {
                notification: {
                    message: NOTIFICATIONS.projectNotFound,
                },
            },
        };
    }

    const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
        collection: paths.collections.configs,
        id: projectData?.configId,
    });

    if (!projectConfig) {
        return {
            success: false,
            projectData,
            directives: {
                notification: {
                    message: NOTIFICATIONS.genericError,
                },
            },
        };
    }

    const projectConfigData = projectConfig?.data;

    if (projectData?.latestBuild?.state === BUILD_STATES.running) {
        return {
            success: false,
            projectData,
            projectConfig,
            directives: {
                notification: {
                    message: NOTIFICATIONS.cannotUpdateProjectWhenBuilding,
                    config: {
                        variant: INFO_CODE_LEVELS.WARNING,
                    },
                    data: {
                        projectName: projectConfigData?.projectName,
                    },
                },
            },
        };
    }

    const domainCheck = await checkDomainsPreBuild({
        accountId,
        urls: projectConfigData.target.urls,
    });

    if (!domainCheck.success) {
        return {
            success: false,
            projectData,
            projectConfig,
            directives: domainCheck.data?.directives,
        };
    }

    if (checkSubscription) {
        const subscriptionCheck = await checkSubscriptionOk({
            accountId,
            projectId,
            projectConfigData,
        });

        if (!subscriptionCheck.success) {
            return {
                success: false,
                subscription: subscriptionCheck.subscription?.data,
                directives: subscriptionCheck.directives,
                projectData,
                projectConfig,
            };
        }
    }

    return {
        success: true,
        projectData,
        projectConfig,
    };
};
