import {isJSONContentType} from './getContentType';
import type {
    Response,
} from '@w7-3/webeagle-resources/types/playwright';

export default async (response: Response) => {
    if (!response) {
        return null;
    }

    const headers = response.headers();
    const contentType = headers?.['content-type'];
    const isJSONResponse = isJSONContentType(contentType);

    if (isJSONResponse) {
        return await response.json();
    }

    return await response.text();
};
