import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import getProjectResults from './getProjectResults';
import dbInstance from '../setup/setupDB';
import type {
    ProjectConfig,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    ProjectConfigData,
    ProjectState,
} from '@w7-3/webeagle-resources/types/project';

const dao = dbInstance.getDAO();

export default async ({
    accountId,
    params,
}: {
    accountId: string,
    params: {
        filterIdList: string[],
        includeDetails: boolean,
        filterStateList: ProjectState[],
    },
}): Promise<{projects: Record<ProjectData['id'], ProjectData>}> => {
    const {
        filterIdList,
        includeDetails,
        filterStateList = [],
    } = params;
    const paths = getPaths({accountId});
    const projectConfigDataMap = {};

    const [, projects] = await dao.getAllCollectionData<ProjectData>({
        collection: paths.collections.projects,
        ...getOptionalMap(isNonEmptyArray(filterIdList), {
            condition: {
                fieldPath: 'id',
                opStr: operators.IN,
                value: filterIdList,
            },
        }),
    });

    await Promise.allSettled(Object.keys(projects || {}).map(async (projectId) => {
        if (projects?.[projectId]?.isDeleted || !filterStateList.includes(projects[projectId].state)) {
            delete projects?.[projectId];
            return;
        }

        const {
            collections,
        } = getPaths({
            accountId,
            projectId,
        });

        const [, projectConfig] = await dao.getDocumentData<ProjectConfig>({
            path: `${collections.configs}/${projects[projectId].configId}`,
        });

        if (!projectConfig?.data) {
            return;
        }

        projectConfigDataMap[projectId] = {
            [projects[projectId].configId]: {
                ...projectConfig.data,
            },
        };

        projects[projectId].configData = projectConfig.data;
    }));

    if (includeDetails && Object.keys(projects).length > 0) {
        await Promise.allSettled(Object.keys(projects).map(async (projectId) => {
            if (projects[projectId]?.latestBuild?.state) {
                projects[projectId].results = await getProjectResults({
                    accountId,
                    projectId,
                    configDataMap: projectConfigDataMap[projectId],
                });
            }
        }));
    }

    return {projects};
};
