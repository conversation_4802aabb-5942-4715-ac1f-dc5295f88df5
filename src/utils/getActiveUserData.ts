import collections from '@w7-3/webeagle-resources/dist/config/collections';
import dbInstance from '../setup/setupDB';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: any): Promise<null | UserData> => {
    const [error, loginData] = await dao.getLoginData({
        idToken: String(req.headers.token),
    });

    if (error || !loginData) {
        return null;
    }

    const [error2, userData] = await dao.getCollectionDocumentData<UserData>({
        id: loginData?.email,
        collection: collections.users,
    });

    if (error2 || !userData) {
        return null;
    }

    return userData;
};
