import deepmerge from 'deepmerge';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import logger from './logger';
import getLinkId from './getLinkId';
import type {
    BrowserContextSession,
    PageBuildSession,
    RequestItem,
    ResponseItem,
    W103Global,
    WebAutomateDocumentRequest,
} from '@w7-3/webeagle-resources/types/webautomate/state';
import type {
    LighthouseResult,
    RequestData,
} from '@w7-3/webeagle-resources/types/webautomate/request';
import type {
    LinkCheckDataReference,
    URLRequestState,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    BrowserContext,
    Frame,
    Page,
    Request,
    Response,
} from '@w7-3/webeagle-resources/types/playwright';
import type {DeepPartial} from '@w7-3/webeagle-resources/types/misc';

export const getCacheKey = ({
    pageBuildSessionId,
    url,
    suffix = '',
}: {
    pageBuildSessionId: string;
    url: string;
    suffix?: string;
}): string => {
    return `${pageBuildSessionId}_${getLinkId({url})}${isNonEmptyString(suffix) ? `_${suffix}` : ''}`;
};

export const initializeWebAutomateBuildSession = (
    {
        buildId,
        projectId,
        vendor,
    }: Pick<W103Global, 'buildId' | 'projectId' | 'vendor'>,
) => {
    (global.w103Global as W103Global) = {
        browserContexts: new Map<BrowserContext, BrowserContextSession>(),
        buildId,
        frames: new Map<Frame, string>(),
        lighthouse: {},
        linkCheck: {},
        miscPageData: {},
        pageSessions: new Map<PageBuildSession['id'], PageBuildSession>(),
        browserPages: new Map<Page, PageBuildSession['id']>,
        browserPagesReversed: new Map<PageBuildSession['id'], Page>(),
        projectId,
        requests: new Map<Request, RequestItem>(),
        responses: new Map<Response, ResponseItem>(),
        sessionStart: Date.now(),
        vendor,
    };
};

export const getW103Global = (): W103Global => {
    return global.w103Global;
};

export const registerRequest = ({
    request,
}: {
    request: Request;
}) => {
    if (getW103Global().requests.has(request)) {
        logger.error('Request already registered, this should not be happening');
        return;
    }

    getW103Global().requests.set(request, {
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType(),
    });
};

export const registerResponse = ({
    response,
}: {
    response: Response;
}) => {
    if (getW103Global().responses.has(response)) {
        logger.error('Response already registered, this should not be happening');
        return;
    }

    getW103Global().responses.set(response, {
        url: response.url(),
        method: response.request()?.method?.(),
        status: response.status(),
    });
};

export const setMiscPageData = ({
    pageBuildSessionId,
    data,
}: {
    pageBuildSessionId: PageBuildSession['id'];
    data: W103Global['miscPageData'][string];
}) => {
    getW103Global().miscPageData[pageBuildSessionId] = deepmerge<
        W103Global['miscPageData'][string],
        W103Global['miscPageData'][string]
    >(
        getW103Global().miscPageData[pageBuildSessionId] || {},
        data,
    );
};

export const getActiveDocumentRequestItem = ({
    pageBuildSessionId,
    frame,
}: {
    pageBuildSessionId: PageBuildSession['id'];
    frame: Frame;
}): null | {
    index: number;
    item: WebAutomateDocumentRequest;
} => {
    const frameId = getW103Global().frames.get(frame);

    if (!frameId) {
        return null;
    }

    const pageBuildSession = getW103Global().pageSessions.get(pageBuildSessionId);

    if (!pageBuildSession) {
        return null;
    }

    for (let index = pageBuildSession.documentRequestList.length - 1; index >= 0; index--) {
        const item = pageBuildSession.documentRequestList[index];
        if (item?.frameId !== frameId) {
            continue;
        }

        return {
            index,
            item,
        };
    }

    return null;
};

export const registerBrowserContextSession = ({
    browserContext,
}: {
    browserContext: BrowserContext;
}) => {
    if (getW103Global().browserContexts.has(browserContext)) {
        logger.error('Browser context already registered, this should not be happening');
        return;
    }

    const browserContextSessionId = getRandomString(16, libraryKeys.alphaNumericLowerCased);

    getW103Global().browserContexts.set(
        browserContext,
        {
            id: browserContextSessionId,
            createdAt: Date.now(),
        },
    );
};

export const registerPageBuildSession = ({
    page,
    pageBuildSession,
}: {
    page: Page;
    pageBuildSession: {
        id: PageBuildSession['id'];
        pageBuild: PageBuildSession['pageBuild'];
    };
}) => {
    const browserContext = getW103Global().browserContexts.get(page.context());

    if (!browserContext) {
        logger.error('Browser context not found in cache');
        return;
    }

    if (getW103Global().browserPages.has(page)) {
        logger.error('Page session already registered, this should not be happening');
        return;
    }

    getW103Global().pageSessions.set(
        pageBuildSession.id,
        {
            ...pageBuildSession,
            browserContextSessionId: browserContext.id,
            index: getW103Global().pageSessions.size,
            documentRequestList: [],
        },
    );

    getW103Global().browserPages.set(
        page,
        pageBuildSession.id,
    );

    getW103Global().browserPagesReversed.set(
        pageBuildSession.id,
        page,
    );
};

export const updatePageSession = ({
    id,
    pageBuild,
}: {
    id: PageBuildSession['id'];
    pageBuild: DeepPartial<PageBuildSession['pageBuild']>;
}) => {
    const pageBuildSession = getW103Global().pageSessions.get(id);

    if (!pageBuildSession) {
        logger.error('Page session not found in cache');
        return;
    }

    pageBuildSession.pageBuild = deepmerge<
        PageBuildSession['pageBuild'], DeepPartial<PageBuildSession['pageBuild']>
    >(pageBuildSession.pageBuild, pageBuild);

    getW103Global().pageSessions.set(id, pageBuildSession);
};

export const updateDocumentRequestData = ({
    id,
    data,
    frame,
}: {
    id: PageBuildSession['id'];
    data: DeepPartial<WebAutomateDocumentRequest>,
    frame: Frame;
}) => {
    const pageBuildSession = getW103Global().pageSessions.get(id);
    const activeDocumentRequestItem = getActiveDocumentRequestItem({
        pageBuildSessionId: id,
        frame,
    });

    if (!activeDocumentRequestItem || !pageBuildSession) {
        logger.error('Active document request item not found in cache');
        return;
    }

    Object.entries(data || {}).forEach(([key, value]) => {
        if (!value) return;

        switch (key) {
        case 'networkActivityDataList':
        case 'requestData':
        case 'requestDataList':
        case 'urlRequestState':
            activeDocumentRequestItem.item[key] = deepmerge(activeDocumentRequestItem.item[key], value);
            return;
        }

        logger.error('Attempting to set an invalid document request data', {
            key, value,
        });
    });

    pageBuildSession.documentRequestList[activeDocumentRequestItem.index] = activeDocumentRequestItem.item;
    getW103Global().pageSessions.set(id, pageBuildSession);
};

export const getParentData = ({
    frame,
    pageBuildSessionId,
}: {
    frame: Frame;
    pageBuildSessionId: PageBuildSession['id'];
}): {
    parentDocumentRequest: WebAutomateDocumentRequest | null;
} => {
    const parentFrame = frame.parentFrame();
    if (!parentFrame) {
        return {
            parentDocumentRequest: null,
        };
    }

    const parentFrameId = getW103Global().frames.get(parentFrame);

    if (!parentFrameId) {
        logger.error('Parent frame not found in cache');
        return {
            parentDocumentRequest: null,
        };
    }

    const pageBuildSession = getW103Global().pageSessions.get(pageBuildSessionId);
    if (!pageBuildSession) {
        logger.error('Page session not found in cache');
        return {
            parentDocumentRequest: null,
        };
    }

    const parentDocumentRequest = pageBuildSession.documentRequestList
        .find((item) => item.frameId === parentFrameId);

    if (!parentDocumentRequest) {
        logger.error('Parent document request not found in cache');
        return {
            parentDocumentRequest: null,
        };
    }

    return {
        parentDocumentRequest,
    };
};

export const registerNetworkActivity = ({
    requestData,
    frame,
    urlRequestState,
    id,
    resourceType,
}: {
    requestData: null | RequestData;
    frame: Frame;
    urlRequestState: null | URLRequestState;
    id: PageBuildSession['id'] | Page;
    resourceType: string;
}) => {
    const w103Global = getW103Global();
    const pageBuildSessionId = typeof id === 'string' ? id : w103Global.browserPages.get(id);

    if (!pageBuildSessionId) {
        logger.error('Page session ID not found in cache');
        return;
    }

    const pageBuildSession = w103Global.pageSessions.get(pageBuildSessionId);

    if (!pageBuildSession) {
        logger.error('Page session not found in cache');
        return;
    }

    const page = w103Global.browserPagesReversed.get(pageBuildSessionId);

    if (!page) {
        logger.error('Page not found in cache');
        return;
    }

    const mainFrame = page.mainFrame();
    const isTopLevel = frame === mainFrame;
    const isDocumentRequest = resourceType === 'document';

    /*
    if (!isTopLevel && !isDocumentRequest) { // if a request is made to the main frame without already having a page view, then this is an error!
        logger.error('Attempting to register network activity without an active page view');

        return;
    }

    if (!isTopLevel && isDocumentRequest && !parentId) { // if a request is made to the main frame, but there is no parent ID, then this is an error!
        logger.error('Attempting to register document request without a parent ID');
        return;
    }

    if (!isTopLevel && isDocumentRequest) {
        console.log({
            frameUrl: frame.url(),
            parentId,
            pageBuildSessionId,
            resourceType,
        });
    }
    */

    const {
        parentDocumentRequest,
    } = getParentData({
        frame,
        pageBuildSessionId,
    });

    if (isDocumentRequest) {
        let frameId = w103Global.frames.get(frame);

        if (!frameId) {
            frameId = getRandomString(16, libraryKeys.alphaNumericLowerCased);
            w103Global.frames.set(frame, frameId);
        }

        pageBuildSession.documentRequestList.push({
            id: getRandomString(16, libraryKeys.alphaNumericLowerCased),
            index: pageBuildSession.documentRequestList.length,
            frameId,
            parentDocumentRequestId: parentDocumentRequest?.id || null,
            pageBuildSessionId,
            networkActivityDataList: [],
            requestData: requestData || null,
            serviceWorkers: [],
            urlRequestState: urlRequestState || null,
        });

        w103Global.pageSessions.set(pageBuildSessionId, pageBuildSession);
        return;
    }

    if (!isDocumentRequest && requestData) { // if a request is made to a frame, register it as a network activity
        let documentRequestItem = getActiveDocumentRequestItem({
            pageBuildSessionId,
            frame,
        });

        if (!documentRequestItem?.item && parentDocumentRequest) {
            documentRequestItem = {
                index: -1,
                item: parentDocumentRequest,
            };
        }

        if (documentRequestItem) {
            documentRequestItem.item.networkActivityDataList.push(requestData);
            pageBuildSession.documentRequestList[documentRequestItem.index] = documentRequestItem.item;

            w103Global.pageSessions.set(pageBuildSessionId, pageBuildSession);
            return;
        }
    }

    logger.error('Untracked network activity', {
        frame,
        pageBuildSessionId,
        parentDocumentRequest,
        requestData,
        resourceType,
        isDocumentRequest,
        isTopLevel,
    });
};

export const getLinkCheckDataFromCache = ({
    pageBuildSessionId,
    url,
}: {
    pageBuildSessionId: string;
    url: string;
}) => {
    return getW103Global()
        .linkCheck[getCacheKey({pageBuildSessionId, url})];
};

export const setLinkCheckDataToCache = ({
    pageBuildSessionId,
    url,
    data,
}: {
    pageBuildSessionId: string;
    url: string;
    data: LinkCheckDataReference;
}) => {
    getW103Global()
        .linkCheck[getCacheKey({pageBuildSessionId, url})] = data;
};

export const getLighthouseDataFromCache = ({
    pageBuildSessionId,
    url,
    device,
}: {
    pageBuildSessionId: string;
    url: string;
    device: string;
}): LighthouseResult => {
    return getW103Global()
        .lighthouse[getCacheKey({pageBuildSessionId, url, suffix: device})] || {};
};

export const setLighthouseDataFromCache = ({
    pageBuildSessionId,
    url,
    data,
    device,
}: {
    pageBuildSessionId: string;
    url: string;
    data: LighthouseResult;
    device: string;
}) => {
    getW103Global()
        .lighthouse[getCacheKey({pageBuildSessionId, url, suffix: device})] = data;
};
