import {
    isNonEmptyArray,
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import type {UrlConfigItem} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {LinkHandler} from '@w7-3/webeagle-resources/types/solutions';

export const getBrowserContextConfig = ({
    urlConfig,
}: {
    urlConfig: UrlConfigItem,
}): {
    config: LinkHandler,
} => {
    const config: LinkHandler = {
        extraHTTPHeaders: undefined,
    };

    if (urlConfig?.auth?.active || urlConfig?.headers?.active) {
        config.httpCredentials = urlConfig?.auth?.active ? urlConfig?.auth : undefined;
        config.extraHTTPHeaders = {};

        if (urlConfig?.headers?.active && isNonEmptyArray(urlConfig?.headers?.items)) {
            urlConfig.headers.items.forEach(({name, value}) => {
                if (isNonEmptyString(name) && isNonEmptyString(value)) {
                    config.extraHTTPHeaders![name] = value;
                }
            });
        }
    }

    return {config};
};

export default async ({
    urlConfig,
    browserContextCreator,
}: {
    urlConfig: UrlConfigItem,
    browserContextCreator: Function,
}): Promise<{
    error: Error | null,
}> => {
    const {config} = getBrowserContextConfig({urlConfig});
    const [error] = await awaitOn(browserContextCreator(config));

    return {error};
};
