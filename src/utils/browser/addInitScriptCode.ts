import {type URL_MATCHER} from '@w7-3/webeagle-resources/dist/config/scrapper';
import type {
    RequestOptionsResources,
} from '@w7-3/webeagle-resources/types/webautomate/global';

export type LocalResourceInjection<HTML_ONLY extends boolean = false> = {
    injectionLocation: string;
    isStart: boolean;
    contentConfig: HTML_ONLY extends true ? {
        html: string;
    } : {
        tagName: string;
        attributeList: {key: string, value: string}[];
        innerHTML: string;
    };
    item: RequestOptionsResources['resourcesInjection'][number];
};

export default ({
    resourcesInjectionList,
    url,
    URL_MATCHER_REGEX_FLAGS,
    urlMatcher,
}: {
    resourcesInjectionList: LocalResourceInjection[];
    url: string;
    URL_MATCHER_REGEX_FLAGS: string;
    urlMatcher: typeof URL_MATCHER;
}) => {
    const isSameDomain = new URL(url).hostname === new URL(window.location.href).hostname;

    if (!isSameDomain) {
        return;
    }

    const insertIntoDOM = () => {
        try {
            if (!window.document.head || !window.document.body) {
                window.setTimeout(insertIntoDOM, 100);
                return;
            }

            resourcesInjectionList.forEach(({
                injectionLocation,
                isStart,
                contentConfig,
                item,
            }: LocalResourceInjection) => {
                const {value, matcher} = item.urlData.urlMatch?.[0] || {};
                if (!(()=> {
                    let isAMatch = false;

                    if (
                        (matcher === urlMatcher.exact && url === value)
                        || (matcher === urlMatcher.substring && url.indexOf(value) >= 0)
                    ) {
                        isAMatch = true;
                    } else if (matcher === urlMatcher.regex) {
                        isAMatch = new RegExp(`/${value}`, URL_MATCHER_REGEX_FLAGS).test(url);
                    }

                    if (isAMatch) {
                        return true;
                    }
                })()) {
                    return;
                }

                let tempElement: Element | null = null;
                const targetElement = window.document[injectionLocation];

                if (!targetElement) {
                    return;
                }

                if (contentConfig?.tagName) {
                    tempElement = window.document.createElement(contentConfig.tagName);
                }

                if (tempElement && Array.isArray(contentConfig.attributeList)) {
                    contentConfig.attributeList.forEach(({key, value}) => {
                        tempElement!.setAttribute(key, value);
                    });
                }

                if (tempElement && contentConfig.innerHTML) {
                    tempElement.innerHTML = contentConfig.innerHTML;
                }

                const {html} = contentConfig as unknown as LocalResourceInjection<true>['contentConfig'];

                if (html) {
                    const range = window.document.createRange();
                    range.selectNode(targetElement);
                    tempElement = range.createContextualFragment(html) as unknown as Element;
                }

                if (!tempElement) {
                    return;
                }

                const firstChild = targetElement.firstChild;
                if (isStart && firstChild) {
                    targetElement.insertBefore(tempElement, firstChild);
                    return;
                }

                targetElement.appendChild(tempElement);
            });
        } catch (e) {

        }
    };

    insertIntoDOM();
};
