import {
    HTML_RESOURCE_LOCATION,
    HTML_RESOURCE_POSITION,
    RESOURCE_CONTENT_TYPES,
    RESOURCE_TYPES,
} from '@w7-3/webeagle-resources/dist/config/request';
import getUrlMatch from '@w7-3/webeagle-resources/dist/libs/getUrlMatch';
import {
    isNonEmptyObject,
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {OVERRIDES} from '@w7-3/webeagle-resources/dist/config/code';
import {URL_MATCHER_REGEX_FLAGS} from '@w7-3/webeagle-resources/dist/config/misc';
import {URL_MATCHER} from '@w7-3/webeagle-resources/dist/config/scrapper';
import getRequestData from '../playwright/getRequestData';
import {isJSONContentType} from '../getContentType';
import evaluateFunction from '../evaluateFunction';
import {getInfoCodeList} from '../getInfoCodes';
import addInitScriptCode from './addInitScriptCode';
import logger from '../logger';
import type {SolutionHandler} from '@w7-3/webeagle-resources/types/solutions';
import type {
    InfoCode,
    RequestOptionsResources,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    APIResponse,
    Page,
} from '@w7-3/webeagle-resources/types/playwright';
import {type LocalResourceInjection} from './addInitScriptCode';
import getNetworkData from './getNetworkData';
import urlParse from 'url-parse';

export const applyResourceInjection = async ({
    handler,
    resources,
    page,
    url,
}: {
    handler: SolutionHandler;
    resources: RequestOptionsResources,
    page: Page;
    url: string;
}) => {
    const infoCodes: InfoCode[] = [];
    const {
        resourcesInjection,
    } = resources;
    const resourcesInjectionList: Array<LocalResourceInjection> = [];

    if (!page) {
        logger.error('Attempting to apply resource injection without browser context');
        return;
    }

    resourcesInjection.forEach((item) => {
        try {
            const {content, contentType, injectionType, source} = item;
            if (![
                RESOURCE_CONTENT_TYPES.css.key,
                RESOURCE_CONTENT_TYPES.js.key,
                RESOURCE_CONTENT_TYPES.html.key,
                // @ts-expect-error type is ok
            ].includes(contentType)) {
                infoCodes.push({
                    code: EVENTS.WEB_RESOURCE_INJECTION_FAILED,
                    level: INFO_CODE_LEVELS.ERROR,
                    timestamp: Date.now(),
                    data: {
                        ...item,
                        message: `Unknown content type (${JSON.stringify({contentType})}) specified`,
                    },
                });

                return;
            }

            const {
                injectionLocation = HTML_RESOURCE_LOCATION.body.key,
                injectionPosition = HTML_RESOURCE_POSITION.start.key,
            } = item;
            const isStart = injectionPosition === HTML_RESOURCE_POSITION.start.key;
            let contentConfig;

            if (contentType === RESOURCE_CONTENT_TYPES.html.key) {
                contentConfig = {
                    html: content.value,
                };
            }

            if (contentType === RESOURCE_CONTENT_TYPES.css.key && injectionType === RESOURCE_TYPES.customContent.key) {
                contentConfig = {
                    tagName: 'style',
                    attributeList: [],
                    innerHTML: content.value,
                };
            }

            if (contentType === RESOURCE_CONTENT_TYPES.css.key && injectionType === RESOURCE_TYPES.url.key && isNonEmptyString(source)) {
                contentConfig = {
                    tagName: 'link',
                    attributeList: [
                        {key: 'href', value: source},
                        {key: 'rel', value: 'stylesheet'},
                    ],
                };
            }

            if (contentType === RESOURCE_CONTENT_TYPES.js.key && injectionType === RESOURCE_TYPES.customContent.key) {
                contentConfig = {
                    tagName: 'script',
                    attributeList: [
                        {key: 'type', value: 'text/javascript'},
                        {key: 'id', value: 'vamos'},
                    ],
                    innerHTML: content.value,
                };
            }

            if (contentType === RESOURCE_CONTENT_TYPES.js.key && injectionType === RESOURCE_TYPES.url.key && isNonEmptyString(source)) {
                contentConfig = {
                    tagName: 'script',
                    attributeList: [
                        {key: 'src', value: source},
                        {key: 'type', value: 'text/javascript'},
                    ],
                };
            }

            resourcesInjectionList.push({
                injectionLocation,
                isStart,
                contentConfig,
                item,
            });

            infoCodes.push({
                code: EVENTS.WEB_RESOURCE_INJECTION_COMPLETED,
                level: INFO_CODE_LEVELS.SUCCESS,
                timestamp: Date.now(),
                data: item,
            });
        } catch (e) {
            const error = e as Error;
            infoCodes.push({
                code: EVENTS.WEB_RESOURCE_INJECTION_FAILED,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    ...item,
                    message: error.message,
                },
            });
        }
    });

    await handler.processInfoCodes({infoCodes});

    await page.addInitScript(addInitScriptCode, {
        resourcesInjectionList,
        url,
        URL_MATCHER_REGEX_FLAGS,
        urlMatcher: URL_MATCHER,
    });
};

export const configureNetworkOverrides = async ({page, resources, handler}: {
    page: Page,
    resources: RequestOptionsResources,
    handler: SolutionHandler,
}) => {
    const {
        requestBlockers,
        requestMocks,
        requestOverrides,
        responseOverrides,
    } = resources;
    const infoCodes: InfoCode[] = [];
    await page.route('**/*', async (route) => {
        const requestOptions: Partial<{
            url: string,
            method: string,
            headers: Record<string, string>,
            postData: null | string,
            postDataJSON: null | JSON,
        }> = {};
        const request = route.request();
        const method = request.method();
        const url = request.url();
        const urlParseResult = handler.getParsedUrl({url});
        const requestBlocker = requestBlockers.find(({urlData}) => {
            return getUrlMatch(url, urlData.urlMatch) && `${urlData.method}`.toLowerCase() === `${method}`.toLowerCase();
        });
        const {
            extraHTTPHeaders,
            queryMap,
        } = getNetworkData({
            url,
            resources,
        });
        const parsed = urlParse(url);
        const urlQueryMap = {
            ...queryMap,
            ...Object.fromEntries(new URLSearchParams(parsed.query)),
        };

        parsed.set('query', urlQueryMap);

        requestOptions.url = parsed.toString();
        requestOptions.headers = {
            ...request.headers(),
            ...extraHTTPHeaders,
        };

        if (requestBlocker) {
            const {
                errorCode,
            } = requestBlocker;
            infoCodes.push({
                code: EVENTS.REQUEST_BLOCKED,
                level: INFO_CODE_LEVELS.WARNING,
                timestamp: Date.now(),
                data: {
                    id: requestBlocker.id,
                    url,
                    method,
                },
            });

            await handler.processInfoCodes({infoCodes});

            return route.abort(errorCode || 'aborted');
        }

        const requestMock = requestMocks.find(({urlData}) => {
            return getUrlMatch(url, urlData.urlMatch) && `${urlData.method}`.toLowerCase() === `${method}`.toLowerCase();
        });

        if (requestMock) {
            const {
                contentType,
                status,
                json,
                body,
            } = requestMock;
            const options = {
                status,
                contentType,
                ...isJSONContentType(requestMock?.contentType) ? {
                    json: json.value,
                } : {
                    body: body.value,
                },
            };

            await route.fulfill(options);
            infoCodes.push({
                code: EVENTS.REQUEST_MOCK_COMPLETED,
                level: INFO_CODE_LEVELS.SUCCESS,
                timestamp: Date.now(),
                data: {
                    id: requestMock.id,
                    url,
                    method,
                },
            });

            return;
        }

        if (!urlParseResult.isValid) {
            return route.continue(requestOptions);
        }

        const [
            requestOverride,
            responseOverride,
        ] = [{
            list: requestOverrides,
            code: EVENTS.REQUEST_MODIFICATION_JS_CODE_INVALID,
        }, {
            list: responseOverrides,
            code: EVENTS.RESPONSE_MODIFICATION_JS_CODE_INVALID,
        }].map(({list, code}) => {
            return list.find((item) => {
                const {id, jsCode, urlData} = item;

                if (!jsCode.isValid) {
                    infoCodes.push({
                        code,
                        level: INFO_CODE_LEVELS.WARNING,
                        timestamp: Date.now(),
                        data: {
                            id,
                            url,
                            item,
                        },
                    });

                    return false;
                }

                return getUrlMatch(url, urlData.urlMatch) && `${urlData.method}`.toLowerCase() === `${method}`.toLowerCase();
            });
        });

        await handler.processInfoCodes({infoCodes});

        if (!requestOverride && !responseOverride) {
            return route.continue(requestOptions);
        }

        if (requestOverride) {
            try {
                const {
                    parameters,
                    postData,
                } = getRequestData(request);
                const headers = request.headers();
                const [error, reducedData] = await evaluateFunction({
                    page,
                    config: {
                        paramValueList: [
                            headers,
                            method,
                            postData,
                            parameters,
                            url,
                        ],
                        paramNameList: [...(Object.values(OVERRIDES.request.be) as string[])],
                    },
                    extractionCode: requestOverride.jsCode.value,
                }) as [Error, {
                    url: string,
                    method: string,
                    headers: Record<string, string>,
                    postData: string,
                }];

                if (error) {
                    throw error;
                }

                if (isNonEmptyString(reducedData?.url)) {
                    requestOptions.url = reducedData?.url;
                }

                if (isNonEmptyString(reducedData?.method)) {
                    requestOptions.method = reducedData?.method;
                }

                if (isNonEmptyObject(reducedData?.headers)) {
                    requestOptions.headers = reducedData?.headers;
                }

                if (reducedData?.postData) {
                    requestOptions.postData = reducedData?.postData;
                }

                infoCodes.push({
                    code: EVENTS.REQUEST_MODIFICATION_COMPLETED,
                    level: INFO_CODE_LEVELS.INFO,
                    timestamp: Date.now(),
                    data: {
                        id: requestOverride.id,
                        url,
                        method,
                        reducedData,
                    },
                });
            } catch (e) {
                const error = e as Error;
                infoCodes.push(
                    ...getInfoCodeList(error, {url, method}), {
                        code: EVENTS.REQUEST_MODIFICATION_FAILED,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                        data: {
                            id: requestOverride.id,
                            url,
                            method,
                        },
                    });
            }
        }

        if (!responseOverride) {
            await handler.processInfoCodes({infoCodes});
            return route.continue(requestOptions);
        }

        const response = await route.fetch(requestOptions);
        const responseHeaders = response.headers();
        const contentType = responseHeaders?.['content-type'];
        let isJSONResponse = isJSONContentType(contentType);
        const json = isJSONResponse ? await response.json() : null;
        const body = await response.text();
        const responseOptions: Partial<{
            response: APIResponse,
            headers: Record<string, string>,
            body: string,
            json: JSON,
            contentType: string,
            path?: string,
        }> = {
            headers: responseHeaders,
            body,
            json,
            contentType: responseHeaders?.['content-type'],
        };

        try {
            const [error, reducedData] = await evaluateFunction({
                page,
                config: {
                    paramNameList: [...(Object.values(OVERRIDES.response.be) as string[])],
                    paramValueList: [
                        responseOptions.headers,
                        responseOptions.body,
                        url,
                    ],
                },
                extractionCode: responseOverride.jsCode.value,
            }) as [Error, {
                contentType: string,
                json: JSON,
                headers: Record<string, string>,
                body: string,
            }];

            if (error) {
                throw error;
            }

            isJSONResponse = isJSONContentType(reducedData?.contentType, isJSONResponse);

            if (isJSONResponse) {
                responseOptions.json = isNonEmptyObject(reducedData?.json) ? reducedData.json : json;
            }

            if (!isJSONResponse) {
                responseOptions.body = isNonEmptyString(reducedData?.body) ? reducedData?.body : body;
            }

            if (isNonEmptyObject(reducedData?.headers)) {
                responseOptions.headers = {
                    ...responseOptions.headers,
                    ...reducedData?.headers,
                };
            }

            infoCodes.push({
                code: EVENTS.RESPONSE_MODIFICATION_COMPLETED,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    id: responseOverride.id,
                    url,
                    method,
                    reducedData,
                },
            });
        } catch (e) {
            infoCodes.push({
                code: EVENTS.RESPONSE_MODIFICATION_FAILED,
                level: INFO_CODE_LEVELS.ERROR,
                timestamp: Date.now(),
                data: {
                    id: responseOverride.id,
                    url,
                    method,
                },
            });
        }

        if ((isJSONResponse && isNonEmptyObject(responseOptions.json))) {
            delete responseOptions.body;
        }

        await route.fulfill(responseOptions);
        await handler.processInfoCodes({infoCodes});
        return;
    });
};

