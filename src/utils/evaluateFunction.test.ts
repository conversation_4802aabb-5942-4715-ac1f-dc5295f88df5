import os from 'os';
import {OVERRIDES} from '@w7-3/webeagle-resources/dist/config/code';
import type {Page} from '@w7-3/webeagle-resources/types/playwright';
import evaluateFunction from './evaluateFunction';

test('Test 1', () => {
    console.log(os.tmpdir());
    expect(true).toBe(true);
});

describe('evaluateFunction', () => {
    const page = {
        evaluate: jest.fn(),
        waitForFunction: jest.fn(),
    } as any as Page;
    const evaluate = page.evaluate as jest.Mock;

    it('should evaluate function correctly', async () => {
        const [error] = await evaluateFunction({
            page,
            config: {
                paramValueList: [
                    {
                        'content-type': 'application/json',
                    },
                    'POST',
                    {
                        json: {
                            a: 1,
                        },
                    },
                    {
                        a: 1,
                        b: 2,
                    },
                    '/test-url',
                ],
                paramNameList: [...(Object.values(OVERRIDES.request.be) as string[]), 'url'],
            },
            extractionCode: 'return {a: 1};',
        });

        expect(error).toBeNull();
        expect(evaluate.mock.calls).toMatchSnapshot();
    });
});
