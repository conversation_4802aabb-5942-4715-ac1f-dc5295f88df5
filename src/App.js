import '@w7-3/webeagle-resources/dist/activate';
import {useEffect} from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import Router from './routes';
import ThemeConfig from './theme';
import useAuth from './hooks/useAuth';
import RtlLayout from './components/RtlLayout';
import ScrollToTop from './components/ScrollToTop';
import LoadingScreen from './components/LoadingScreen';
import GoogleAnalytics from './components/GoogleAnalytics';
import NotistackProvider from './components/NotistackProvider';
import ThemePrimaryColor from './components/ThemePrimaryColor';
import ThemeLocalization from './components/ThemeLocalization';
import ProjectSyncHandler from './custom/components/handlers/ProjectSyncHandler';
import ImageLightboxHandler from './custom/components/handlers/ImageLightboxHandler';
import DialogHandler from './custom/components/handlers/DialogHandler';
import ScrollHandler from './custom/components/handlers/ScrollHandler';
import LogHandler from './custom/components/handlers/LogHandler';
import DeferredTasksHandler from './custom/components/handlers/DeferredTasksHandler';
import CartHandler from './custom/components/handlers/CartHandler';
import ProjectBuildHandler from './custom/components/handlers/ProjectBuildHandler';
import NotificationHandler from './custom/components/handlers/NotificationHandler';
import IdleTimeHandler from './custom/components/handlers/IdleTimeHandler';
import FileUploadHandler from './custom/components/handlers/FileUploadHandler';
import Consent from './custom/components/utils/Consent';
import ModalHandler from './custom/components/handlers/ModalHandler';
import AccountStateHandler from './custom/components/handlers/AccountStateHandler';
import LanguageChangeHandler from './custom/components/handlers/LanguageChangeHandler';
import BlacklistedDomainsHandler from './custom/components/handlers/directives/custom/BlacklistedDomainsHandler';
import BusyHandler from './custom/components/handlers/BusyHandler';
import ProjectBuildReviewerE2EVisualTests from './custom/components/handlers/ProjectBuildReviewerE2EVisualTests';
import ProjectBuildReviewerScreenshots from './custom/components/handlers/ProjectBuildReviewerScreenshots';
import ProjectBuildReviewerURLChallenge from './custom/components/handlers/ProjectBuildReviewerURLChallenge';
import ProjectBuildReviewerLighthouse from './custom/components/handlers/ProjectBuildReviewerLighthouse';
import DomainVerificationHandler from './custom/components/handlers/DomainVerificationHandler';
import ProjectAssistantHandler from './custom/components/handlers/ProjectAssistantHandler';
import ProjectBuildReviewerDataExtraction from './custom/components/handlers/ProjectBuildReviewerDataExtraction';
import ProjectBuildReviewerScreenVideos from './custom/components/handlers/ProjectBuildReviewerScreenVideos';
import useLocales from './hooks/useLocales';
import {setGlobalValue} from './custom/utils/globals';
import useRecentLoginHandler from './hooks/useRecentLoginHandler';
import ProjectStepViewer from './custom/components/handlers/ProjectStepViewer';

const mapStateToProps = ({state}) => {
    const {
        isStateLoaded,
    } = state;

    return {
        isStateLoaded,
    };
};

const App = ({isStateLoaded}) => {
    const {isInitialized, isAuthenticated} = useAuth();
    const {translate, currentLang} = useLocales();
    useRecentLoginHandler();
    useEffect(() => {
        setGlobalValue(['i18n'], {
            t: translate,
            changeLanguage: () => {},
        });
    }, [translate, currentLang?.value]);

    return (
        <ThemeConfig>
            <ThemePrimaryColor>
                <ThemeLocalization>
                    <RtlLayout>
                        <NotistackProvider>
                            <ScrollToTop/>
                            <GoogleAnalytics/>
                            <ImageLightboxHandler/>
                            <DialogHandler/>
                            <ModalHandler/>
                            <ScrollHandler/>
                            <LogHandler/>
                            <DeferredTasksHandler/>
                            <CartHandler/>
                            <NotificationHandler/>
                            <IdleTimeHandler/>
                            <BusyHandler/>
                            <FileUploadHandler/>
                            {isInitialized ?
                                <>
                                    <ProjectSyncHandler/>
                                    <ProjectBuildReviewerE2EVisualTests/>
                                    <ProjectBuildReviewerScreenshots/>
                                    <ProjectBuildReviewerURLChallenge/>
                                    <ProjectBuildReviewerLighthouse/>
                                    <ProjectBuildReviewerDataExtraction/>
                                    <ProjectBuildReviewerScreenVideos/>
                                    <BlacklistedDomainsHandler/>
                                    <ProjectBuildHandler/>
                                    <DomainVerificationHandler/>
                                    <ProjectStepViewer/>
                                    <ProjectAssistantHandler/>
                                    <Router/>
                                    {
                                        isStateLoaded && (
                                            <AccountStateHandler/>
                                        )
                                    }
                                    {
                                        isAuthenticated && (
                                            <LanguageChangeHandler/>
                                        )
                                    }
                                </> :
                                <LoadingScreen
                                    sx={{
                                        py: 'auto',
                                    }}
                                />
                            }
                            <Consent/>
                        </NotistackProvider>
                    </RtlLayout>
                </ThemeLocalization>
            </ThemePrimaryColor>
        </ThemeConfig>
    );
};

App.propTypes = {
    isStateLoaded: PropTypes.bool,
};

export default connect(mapStateToProps)(App);
