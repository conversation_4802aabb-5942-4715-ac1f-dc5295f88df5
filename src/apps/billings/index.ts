import process from 'process';
import '@w7-3/webeagle-setup';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {
    getBillingCycleId,
    getPreviousBillingCycleId,
} from '@w7-3/webeagle-resources/dist/libs/accounts';
import {
    getHumanReadableTimestamp,
    humanReadableDateFormats,
} from '@w7-3/webeagle-resources/dist/libs/date';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    CREDIT_EVENT_CONTEXTS,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {
    BILLING_REQUEST_TYPES,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {EMAILS} from '@w7-3/webeagle-resources/dist/config/w73';
import logger from '../../utils/logger';
import dbInstance from '../../setup/setupDB';
import getStorageOverageBillingData from './utils/getStorageOverageBillingData';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';
import type {
    BillingRequest,
    CheckoutTransaction,
} from '@w7-3/webeagle-resources/types/administration';
import {handleConsumption} from '../../utils/credits';

const dao = dbInstance.getDAO();

(async () => {
    const isoTimestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.ISO});
    const [year, month] = isoTimestamp.split('.').map(Number);
    const billingCycle = getPreviousBillingCycleId({
        year,
        month,
    });
    const [accountListError, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
        collection: collections.accounts,
        condition: {
            fieldPath: 'administration.billing.cycle',
            opStr: operators.EQUAL,
            value: billingCycle,
        },
    });

    if (accountListError) {
        logger.error('Failed to fetch account list', {
            error: accountListError,
        });

        process.exit(0);
    }

    if (!isNonEmptyArray(accountList)) {
        process.exit(0);
    }

    await Promise.allSettled(accountList.map(async (accountData) => {
        try {
            const [
                projectBuildQueueError,
                projectBuildQueue,
            ] = await dao.getAllCollectionDataAsList<ProjectBuildQueueItem>({
                collection: collections.buildQueue,
                condition: {
                    fieldPath: 'isAssigned',
                    opStr: operators.EQUAL,
                    value: true,
                },
            });

            if (projectBuildQueueError) {
                logger.error('Failed to fetch project build queue', {
                    error: projectBuildQueueError,
                    accountId: accountData.id,
                });
                return;
            }

            if (isNonEmptyArray(projectBuildQueue)) {
                return;
            }

            const accountId = accountData.id;
            const paths = getPaths({
                accountId,
                billingCycle,
            });

            const [
                subscriptionsError,
                subscriptions,
            ] = await dao.getAllCollectionDataAsList<CheckoutTransaction<true>>({
                collection: paths.collections.subscriptions,
                condition: {
                    fieldPath: 'isSubscriptionItem',
                    opStr: operators.EQUAL,
                    value: true,
                },
            });

            if (subscriptionsError) {
                logger.error('Failed to fetch subscription data', {
                    error: subscriptionsError,
                    accountId: accountData.id,
                });
                return;
            }

            const storageBillingItem = await getStorageOverageBillingData({
                accountData,
                subscriptions,
            });

            if (!storageBillingItem.success) {
                logger.error('Failed to fetch storage billing item', {
                    accountId,
                });
                return;
            }

            const billableActions = storageBillingItem.items.filter((item) => item.quantity > 0);

            if (isNonEmptyArray(billableActions)) {
                const [billingRequestCreationError, billingRequestId] = await dao.insertCollectionData<BillingRequest>({
                    collection: paths.collections.billingRequests,
                    data: {
                        type: BILLING_REQUEST_TYPES.MONTHLY_SETTLEMENT,
                        accountId,
                        created: Date.now(),
                        billingCycle: accountData.administration.billing.cycle,
                        items: billableActions,
                    },
                });

                if (billingRequestCreationError) {
                    logger.error('Failed to create billing request', {
                        error: billingRequestCreationError,
                        accountId,
                    });

                    return;
                }

                const auditTs = Date.now();
                await handleConsumption({
                    accountId,
                    billableActions,
                    buildId: undefined,
                    context: CREDIT_EVENT_CONTEXTS.MONTHLY_SUBSCRIPTION_SETTLEMENT,
                    email: EMAILS.NO_REPLY,
                    loginSessionId: JSON.stringify({auditTs, billingRequestId}),
                    projectId: undefined,
                    source: 'monthly billing',
                });
            }

            await dao.setCollectionDocumentData<{
                administration: {
                    billing: {
                        cycle: AccountData['administration']['billing']['cycle'];
                        history: AccountData['administration']['billing']['history'];
                    };
                },
            }>({
                id: accountData.id,
                collection: collections.accounts,
                data: {
                    administration: {
                        billing: {
                            cycle: getBillingCycleId({
                                year,
                                month,
                            }),
                            history: [{
                                cycle: billingCycle,
                            }],
                        },
                    },
                },
            });
        } catch (error) {
            logger.error('Failed to bill account', {
                error,
            });
        }
    }));
})();

