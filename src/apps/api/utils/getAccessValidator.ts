import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import getActiveUserData from '../../../utils/getActiveUserData';
import dbInstance from '../../../setup/setupDB';
import {getResponseData} from '../../../utils/response';
import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response, NextFunction} from 'express';

const dao = dbInstance.getDAO();

export default ({
    apiWhiteListAllUsers,
    apiWhiteListForUnverifiedUsers,
    roleGuardsForAuthenticatedUsers,
}: {
    apiWhiteListAllUsers: string[];
    apiWhiteListForUnverifiedUsers: string[];
    roleGuardsForAuthenticatedUsers: {
        [key: string]: string[];
    };
}) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        if (apiWhiteListAllUsers.includes(req.url)) {
            return next();
        }

        const [error, loginData] = await dao.getLoginData({
            idToken: String(req.headers.token),
        });

        if (error) {
            return res.send(getResponseData({
                error,
                directives: {
                    idleTime: true,
                },
            }, false));
        }

        if (!loginData?.email || !loginData?.uid) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.loginRequired,
                        config: {
                            variant: INFO_CODE_LEVELS.WARNING,
                        },
                    },
                },
            }, false));
        }

        if (!loginData?.emailVerified && !apiWhiteListForUnverifiedUsers.includes(req.url)) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.unverified,
                        config: {
                            variant: INFO_CODE_LEVELS.WARNING,
                        },
                        data: {
                            email: loginData?.email,
                        },
                    },
                },
            }, false));
        }

        if (roleGuardsForAuthenticatedUsers[req.originalUrl]) {
            const {accountId, role} = await getActiveUserData(req) as UserData;
            if (!roleGuardsForAuthenticatedUsers[req.originalUrl].includes(role)) {
                const [, accountData] = await dao.getCollectionDocumentData<AccountData>({
                    id: accountId,
                    collection: collections.accounts,
                });

                return res.send(getResponseData({
                    directives: {
                        redirect: '/',
                        notification: {
                            message: NOTIFICATIONS.noRights,
                            config: {
                                variant: INFO_CODE_LEVELS.ERROR,
                            },
                            data: {
                                email: accountData?.accountHolder,
                            },
                        },
                    },
                }, false));
            }
        }

        next();
    };
};
