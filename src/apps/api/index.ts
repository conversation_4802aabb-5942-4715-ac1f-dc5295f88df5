/** Start: these modules must be at the top of the file */
import '@w7-3/webeagle-resources/dist/i18n/server-setup';
import {getConfig, setWebAutomateGlobals, updateConfig} from 'app-config';
import process from 'process';
import express from 'express';
import cookieParser from 'cookie-parser';
import bodyParser from 'body-parser';
import {parse} from 'tldts';
import cors from 'cors';
import api, {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import {
    getIsBuildTimeConfigForAPIOk,
} from 'app-config/utils/checkConfigs';
import checkoutApi from './route-handlers/checkout-api';
import adminApi from './route-handlers/admin-api';
import managementApi from './route-handlers/management-api';
import assistantApi from './route-handlers/assistant-api';
import openApi from './route-handlers/open-api';
import protectedApi from './route-handlers/protected-api';
import cdnApi from './route-handlers/cdn-api';
import testApi from './route-handlers/test-api';
import handleError from './utils/handleError';
import getHasAccess from './utils/getHasAccess';
import handleWebhook from './route-handlers/checkout-api/handleWebhook';

import type {NextFunction, Request, Response} from 'express';
import type {BuildTimeConfigForAPI} from 'app-types/config';
import logger from '../../utils/logger';

const buildTimeConfigForAPI = {
    WEBSITE: process.env.WEB_AUTOMATE_WEBSITE,
    TIMEZONE: process.env.WEB_AUTOMATE_TIMEZONE,
    ALLOWED_ORIGINS: `${process.env.WEB_AUTOMATE_ALLOWED_ORIGINS}`
        .split(',')
        .map((item) => item.trim())
        .filter((item) => item.length > 0),
    DEACTIVATE_ACCESS_CHECK: [
        'true',
        '1',
    ].includes(`${process.env.WEB_AUTOMATE_DEACTIVATE_ACCESS_CHECK}`.toLowerCase()),
    ACCESS_CODE_KEY: process.env.WEB_AUTOMATE_ACCESS_CODE_KEY,
    ACCESS_CODE_VALUE: process.env.WEB_AUTOMATE_ACCESS_CODE_VALUE,
    SESSION_COOKIE_NAME: process.env.WEB_AUTOMATE_SESSION_COOKIE_NAME,
    PORT: process.env.WEB_AUTOMATE_APP_PORT,
} as BuildTimeConfigForAPI;

const configValidationResult = getIsBuildTimeConfigForAPIOk(buildTimeConfigForAPI);

if (!configValidationResult?.success) {
    throw new Error(`A required runtime environment is missing. Reason: ${configValidationResult?.reason}`);
}

updateConfig(buildTimeConfigForAPI);
setWebAutomateGlobals({
    appOrigin: buildTimeConfigForAPI.WEBSITE,
    timeZone: buildTimeConfigForAPI.TIMEZONE,
});
const app = express();
const config = getConfig();
const PORT = config.PORT;

process.on('uncaughtException', function(error) {
    logger.error('Uncaught error: ', {error});
});

process.on('unhandledRejection', function(reason) {
    logger.error('Unhandle promise rejection: ', {reason});
});

const routers = {
    cdnApi: `${serverlessAPI.cdnApi.root}`,
    checkoutApi: `${serverlessAPI.checkoutApi.root}${api.version}`,
    adminApi: `${serverlessAPI.adminApi.root}`,
    managementApi: `${serverlessAPI.managementApi.root}${api.version}`,
    assistantApi: `${serverlessAPI.assistantApi.root}${api.version}`,
    openApi: `${serverlessAPI.api.root}${api.versionOpen}`,
    protectedApi: `${serverlessAPI.api.root}${api.version}`,
};
const bodyParserBlackList = [
    `${routers.checkoutApi}${api.webhook}`,
];
const accessCheckWhitelist = [
    `${routers.openApi}${api.checkApiAccess}`,
    `${routers.checkoutApi}${api.webhook}`,
];
const customHeaders = {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'X-Powered-By': '',
};
const corsOptions = {
    origin: (origin: string, callback: Function) => {
        if (!origin || getConfig().IS_DEV) {
            callback(null, true);
            return;
        }

        const domainData = parse(origin);

        if (config.ALLOWED_ORIGINS!.includes(domainData.hostname as string)) {
            callback(null, true);
            return;
        }

        callback(new Error('Not allowed by CORS'));
    },
    methods: ['GET', 'POST'],
    optionsSuccessStatus: 200,
    credentials: true,
};

app.post(
    `${routers.checkoutApi}${api.webhook}`,
    express.raw({type: 'application/json'}),
    handleWebhook,
);
app.use((_: Request, res: Response, next: NextFunction) => {
    Object.entries(customHeaders).forEach(([key, value]) => {
        res.setHeader(key, value);
    });

    next();
});
app.use(cookieParser());
app.use(cors(corsOptions));
app.options('*', cors(corsOptions));
app.use((
    req: Request,
    res: Response,
    next: NextFunction,
): void => {
    if (bodyParserBlackList.includes(req.originalUrl)) {
        return next();
    }

    bodyParser.urlencoded({extended: true})(req, res, next);
});
app.use((
    req: Request,
    res: Response,
    next: NextFunction,
): void => {
    if (bodyParserBlackList.includes(req.originalUrl)) {
        return next();
    }

    bodyParser.json()(req, res, next);
});
app.use(handleError);
app.all('*', async (req, res, next) => {
    if (accessCheckWhitelist.includes(req.originalUrl)) {
        return next();
    }

    const hasAccess = getHasAccess(req);

    if (!hasAccess) {
        return res.sendStatus(403);
    }

    next();
});
app.use(routers.cdnApi, cdnApi);
app.use(routers.checkoutApi, checkoutApi);
app.use(routers.adminApi, adminApi);
app.use(routers.managementApi, managementApi);
app.use(routers.assistantApi, assistantApi);
app.use(routers.openApi, openApi);
app.use(routers.protectedApi, protectedApi);
app.use('/test-api', testApi);
app.all('*', async (_: Request, res: Response) => {
    res.sendStatus(403);
});

app.listen(PORT).setTimeout(300000, (socket: any) => {
    socket.destroy();
});

console.log(`Api server running on port ${PORT}`);
