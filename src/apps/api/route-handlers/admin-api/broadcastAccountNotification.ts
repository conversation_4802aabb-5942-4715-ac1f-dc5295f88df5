import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    accountNotificationTypes,
} from '@w7-3/webeagle-resources/dist/config/account';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {AccountNotification} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            accountIdList,
            global,
            level,
            messages,
            titles,
        } = req.body?.data || {};
        const [error, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
            collection: collections.accounts,
            ...global === 'true' ? {} : {
                condition: {
                    fieldPath: 'id',
                    opStr: operators.IN,
                    value: accountIdList,
                },
                limit: accountIdList.length,
            },
        });

        if (error || !isNonEmptyArray(accountList)) {
            return res.send(getResponseData({}, false));
        }

        await Promise.allSettled(accountList.map(async (account) => {
            const notificationId = getRandomString(32, libraryKeys.alphaNumericLowerCased);
            const paths = getPaths({
                accountId: account.id,
            });
            await dao.setCollectionDocumentData<AccountNotification>({
                id: notificationId,
                collection: paths.collections.accountNotifications,
                data: {
                    id: notificationId,
                    type: accountNotificationTypes.custom,
                    ts: Date.now(),
                    isNew: true,
                    i18nInfos: [{
                        titles,
                        messages,
                        level,
                        timestamp: Date.now(),
                    }],
                },
            });
        }));

        return res.send(getResponseData({}, true));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};
