import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {
    UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import {
    type AccountData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';
const dao = dbInstance.getDAO();

type UpdatableAccountDataFields = Pick<AccountData, 'accountHolder' | 'state'>;

export default async (req: Request, res: Response) => {
    try {
        const {
            accountId,
            fields,
        } = req.body?.data || {};

        if (accountId) {
            const [error, accountData] = await dao.getCollectionDocumentData<AccountData>({
                id: accountId,
                collection: collections.accounts,
            });

            if (error || !accountData) {
                return res.send(getResponseData({
                    directives: {
                        notification: {
                            message: 'Account not found',
                        },
                    },
                }, false));
            }

            const newAccountData = {} as UpdatableAccountDataFields;

            if (fields?.accountHolder) {
                const [, userData] = await dao.getCollectionDocumentData<UserData>({
                    id: accountData?.accountHolder,
                    collection: collections.users,
                });

                if (userData?.accountId !== accountId) {
                    return res.send(getResponseData({
                        directives: {
                            notification: {
                                message: 'User not found',
                            },
                        },
                    }, false));
                }

                const [, accountList] = await dao.getAllCollectionDataAsList({
                    collection: collections.accounts,
                    condition: {
                        fieldPath: 'accountHolder',
                        opStr: '==',
                        value: fields.accountHolder,
                    },
                    limit: 1,
                });

                if (accountList.length > 0) {
                    return res.send(getResponseData({
                        directives: {
                            notification: {
                                message: `Account already exists for ${fields.accountHolder} and more than one account leads to data inconsistency.`,
                            },
                        },
                    }, false));
                }

                newAccountData.accountHolder = fields.accountHolder;

                const [error] = await dao.setCollectionDocumentData<Pick<UserData, 'role'>>({
                    id: userData!.email,
                    collection: collections.users,
                    data: {
                        role: userRoles.ADMIN.key,
                    },
                });

                if (error) {
                    return res.send(getResponseData({
                        directives: {
                            notification: {
                                message: 'Failed to update user role',
                            },
                        },
                    }, false));
                }
            }

            if (fields?.state) {
                newAccountData.state = fields?.state;
            }

            if (isNonEmptyObject(newAccountData)) {
                await dao.setCollectionDocumentData<UpdatableAccountDataFields>({
                    id: accountId,
                    collection: collections.accounts,
                    data: newAccountData,
                });
            }
        }

        return res.send(getResponseData({}, true));
    } catch (error) {
        console.error({error});
    }

    res.send(getResponseData({}, false));
};
