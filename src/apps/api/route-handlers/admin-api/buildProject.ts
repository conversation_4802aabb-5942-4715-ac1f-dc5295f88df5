import {vendors} from '@w7-3/webeagle-resources/dist/config/project';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getResponseData} from '../../../../utils/response';
import projectQueueForBuild
    from '../../../../utils/projects/projectQueueForBuild';
import type {Request, Response} from 'express';

export default async (req: Request, res: Response) => {
    const {
        accountId,
        projectId,
    } = req.body?.data || {};

    try {
        const buildResult = await projectQueueForBuild({
            accountId,
            projectId,
            infoCode: {
                code: EVENTS.PROJECT_BUILD_BY_AUTO,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
            },
            item: {
                executeAt: Date.now(),
                retries: [],
                vendor: vendors.auto,
                triggeredBy: {
                    type: 'adminApi',
                },
            },
        });

        return res.send(getResponseData({buildResult}, buildResult?.data?.success));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};

