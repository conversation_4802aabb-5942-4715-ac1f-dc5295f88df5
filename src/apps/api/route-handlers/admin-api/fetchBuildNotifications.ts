import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (_: Request, res: Response) => {
    const notifications = {};
    try {
        await Promise.allSettled(['builds'].map(async (collection) => {
            const [error, dateList] = await dao.getSubCollections({
                documentPath: `${collections.notifications}/${collections.builds}`,
            });

            if (error || !dateList) {
                return;
            }

            notifications[collection] = {};

            await Promise.allSettled(dateList.map(async (date: string) => {
                const [notificationsError, result] = await dao.getAllCollectionData({
                    collection: `${collections.notifications}/${collections.builds}/${date}`,
                });

                if (notificationsError || !notifications) {
                    return;
                }

                notifications[collection][date] = result;
            }));
        }));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({notifications}, true));
};
