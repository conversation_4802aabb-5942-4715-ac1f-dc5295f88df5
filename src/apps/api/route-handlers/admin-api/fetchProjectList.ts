import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {
    ProjectConfig,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const projectList: any[] = [];
    try {
        const {
            accountId,
        } = req.body?.data || {};
        let paths = getPaths({
            accountId,
            projectId: '',
        });
        const [error, projectDataList] = await dao.getAllCollectionDataAsList<ProjectData>({
            collection: paths.collections.projects,
            condition: {
                fieldPath: 'state',
                opStr: operators.EQUAL,
                value: projectStates.active,
            },
        });

        if (!error && isNonEmptyArray(projectDataList)) {
            await Promise.allSettled(projectDataList.map(async (projectData: ProjectData) => {
                paths = getPaths({
                    accountId,
                    projectId: projectData.id,
                });

                const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
                    id: projectData?.configId,
                    collection: paths.collections.configs,
                });

                projectList.push({
                    ...projectData,
                    projectName: projectConfig?.data?.projectName,
                });
            }));
        }
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({projectList}, true));
};
