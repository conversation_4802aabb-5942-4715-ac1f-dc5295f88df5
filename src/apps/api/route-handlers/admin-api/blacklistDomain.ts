import {parse} from 'tldts';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    NOTIFICATION_TYPES,
    NOTIFICATION_METHODS,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {
    AccountData,
    BlacklistedDomain,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            accountId,
            global,
            reason,
            url,
        } = req.body?.data || {};

        if (!isNonEmptyString(url)) {
            return res.send(getResponseData({
                message: `The url: ${url} is empty.`,
            }, false));
        }

        const domain = parse(url).domain as string;
        const [, loginData] = await dao.getLoginData({
            idToken: String(req.headers.token),
        }) as [Error, {
            email: string;
        }];
        const domainData = {
            domain,
            reason,
            ts: Date.now(),
            admin: {
                email: loginData.email,
            },
        };

        if (global) {
            await dao.setCollectionDocumentData<BlacklistedDomain>({
                id: domain,
                collection: collections.blacklistedDomains,
                data: domainData,
            });

            return res.send(getResponseData({domainData}, true));
        }

        if (!isNonEmptyString(accountId)) {
            return res.send(getResponseData({
                message: `The accountId: ${accountId} is empty.`,
            }, false));
        }

        const [accountDataError, accountData] = await dao.getCollectionDocumentData<AccountData>({
            id: accountId,
            collection: collections.accounts,
        });

        if (accountDataError || !accountData) {
            return res.send(getResponseData({
                error: accountDataError,
                message: `The accountData cannot be found.`,
                directives: {
                    notification: {
                        message: 'Account not found',
                    },
                },
            }, false));
        }

        if (accountData?.blacklistedDomains?.[domain]) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: 'Domain already blacklisted',
                    },
                },
            }, false));
        }

        const [error] = await dao.setCollectionDocumentData<Pick<AccountData, 'blacklistedDomains'>>({
            id: accountId,
            collection: collections.accounts,
            data: {
                blacklistedDomains: {
                    [domain]: domainData,
                },
            },
        });

        if (error) {
            return res.send(getResponseData({}, false));
        }

        await dao.insertCollectionData<EmailNotification<{
            domain: string;
        }>>({
            collection: collections.notifications,
            data: {
                accountId: accountData.id,
                createdAt: Date.now(),
                data: {
                    email: accountData.accountHolder,
                    language: accountData.preferredLanguage,
                    payload: {
                        domain,
                    },
                },
                method: NOTIFICATION_METHODS.EMAIL,
                sendAt: Date.now(),
                type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
                subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.DOMAIN_BLACKLISTED,
            },
        });

        return res.send(getResponseData({domainData}, true));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};
