import collections from '@w7-3/webeagle-resources/dist/config/collections';
import dbInstance from '../../../../setup/setupDB';
import {getResponseData} from '../../../../utils/response';
import type {Request, Response} from 'express';
const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const {
        date,
        id,
        action,
    } = req.body?.data || {};

    try {
        if (action === 'delete') {
            await dao.deleteCollectionData({
                id,
                collection: `${collections.admin}/${collections.logs}/${date}`,
            });
        }

        return res.send(getResponseData({}, true));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};

