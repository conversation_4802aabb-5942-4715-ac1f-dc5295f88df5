import {getResponseData} from '../../../../utils/response';
import processProjectDeleteQueue
    from '../../../../utils/jobs/processProjectDeleteQueue';
import type {Request, Response} from 'express';
export default async (req: Request, res: Response) => {
    try {
        await processProjectDeleteQueue();

        return res.send(getResponseData({}, true));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};
