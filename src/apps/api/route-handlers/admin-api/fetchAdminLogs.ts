import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const logItems: Object = {};
    try {
        const [error, logList] = await dao.getSubCollections({
            documentPath: `${collections.admin}/${collections.logs}`,
        });

        if (error || !logList) {
            return res.send(getResponseData({}, false));
        }

        await Promise.allSettled(logList.map(async (collection: string) => {
            logItems[collection] = [];
            const [, collectionLogs] = await dao.getAllCollectionData({
                collection: `${collections.admin}/${collections.logs}/${collection}`,
            });

            if (!collectionLogs) {
                return;
            }

            Object.keys(collectionLogs).forEach((id) => {
                logItems[collection].push({
                    id,
                    data: collectionLogs[id],
                });
            });
        }));

        return res.send(getResponseData({logItems}, true));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};
