import {parse} from 'tldts';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    NOTIFICATION_TYPES,
    NOTIFICATION_METHODS,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            accountId,
            global,
            url,
        } = req.body?.data || {};

        if (!isNonEmptyString(url)) {
            return res.send(getResponseData({

            }, false));
        }

        const domain = parse(url).domain as string;

        if (global) {
            const [error] = await dao.deleteCollectionData({
                id: domain,
                collection: collections.blacklistedDomains,
            });

            if (error) {
                return res.send(getResponseData({}, false));
            }

            return res.send(getResponseData({url}, true));
        }

        if (!isNonEmptyString(accountId)) {
            return res.send(getResponseData({}, false));
        }

        const [accountDataError, accountData] = await dao.getCollectionDocumentData<AccountData>({
            id: accountId,
            collection: collections.accounts,
        });

        if (accountDataError || !accountData) {
            return res.send(getResponseData({
                error: accountDataError,
                directives: {
                    notification: {
                        message: 'Account not found',
                    },
                },
            }, false));
        }

        if (!accountData?.blacklistedDomains?.[domain]) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: 'Domain is not blacklisted',
                    },
                },
            }, false));
        }

        const [error] = await dao.deleteCollectionDataField({
            id: accountId,
            collection: collections.accounts,
            propPath: ['blacklistedDomains', domain],
        });

        if (error) {
            return res.send(getResponseData({}, false));
        }

        await dao.insertCollectionData<EmailNotification<{
            domain: string;
        }>>({
            collection: collections.notifications,
            data: {
                accountId,
                createdAt: Date.now(),
                data: {
                    email: accountData.accountHolder,
                    language: accountData.preferredLanguage,
                    payload: {
                        domain,
                    },
                },
                method: NOTIFICATION_METHODS.EMAIL,
                sendAt: Date.now(),
                type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
                subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.DOMAIN_WHITELISTED,
            },
        });

        return res.send(getResponseData({}, true));
    } catch (error) {
        console.error({error});
    }

    return res.send(getResponseData({}, false));
};
