import express from 'express';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import accessValidator from './utils/accessValidator';
import handleProjectList from './handleProjectList';
import handleProjectBuildQueue from './handleProjectBuildQueue';
import handleProjectBuildDequeue from './handleProjectBuildDequeue';
import handleProjectStateChange from './handleProjectStateChange';
import handleProjectConfig from './handleProjectConfig';
import handleProjectLogs from './handleProjectLogs';
import handleProjectResult from './handleProjectResult';
import handleError from '../../utils/handleError';
import type {Router} from 'express';

const app: Router = express.Router();

app.use(handleError);

app.all('*', accessValidator);

app.post(`${serverlessAPI.managementApi.uris.projectList}/:accountId`, handleProjectList);

app.post(`${serverlessAPI.managementApi.uris.projectConfig}/:accountId`, handleProjectConfig);

app.post(`${serverlessAPI.managementApi.uris.projectBuildQueue}/:accountId`, handleProjectBuildQueue);

app.post(`${serverlessAPI.managementApi.uris.projectBuildDequeue}/:accountId`, handleProjectBuildDequeue);

app.post(`${serverlessAPI.managementApi.uris.projectStateChange}/:accountId`, handleProjectStateChange);

app.post(`${serverlessAPI.managementApi.uris.projectLogs}/:accountId`, handleProjectLogs);

app.post(`${serverlessAPI.managementApi.uris.projectResult}/:accountId`, handleProjectResult);

export default app;
