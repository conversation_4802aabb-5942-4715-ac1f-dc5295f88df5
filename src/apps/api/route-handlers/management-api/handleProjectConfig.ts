import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import dbInstance from '../../../../setup/setupDB';
import {getErrorData} from '../../../../utils/response';
import type {Request, Response} from 'express';
import type {
    ProjectConfig,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const {projectId} = req.body;

    if (!projectId) {
        return res.status(401).send({
            error: {
                message: 'Parameter "projectId" is required',
            },
        });
    }

    try {
        const accountId = req.params.accountId;
        const paths = getPaths({
            accountId,
            projectId,
        });
        const [, projectData] = await dao.getDocumentData<ProjectData>({
            path: paths.collections.project,
        });

        const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
            id: projectData!.configId,
            collection: paths.collections.configs,
        });

        return res.status(200).send({
            success: true,
            data: projectConfig?.data,
        });
    } catch (e) {
        const error = e as Error;
        return res.status(500).send({
            success: false,
            error: getErrorData(error),
        });
    }
};
