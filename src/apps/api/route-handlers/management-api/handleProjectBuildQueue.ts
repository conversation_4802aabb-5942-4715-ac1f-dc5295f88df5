import omit from 'ramda/src/omit';
import {vendors} from '@w7-3/webeagle-resources/dist/config/project';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {EMAILS} from '@w7-3/webeagle-resources/dist/config/w73';
import projectQueueForBuild
    from '../../../../utils/projects/projectQueueForBuild';
import {getErrorData} from '../../../../utils/response';
import type {Request, Response} from 'express';

export default async (req: Request, res: Response) => {
    const {projectId} = req.body;

    if (!projectId) {
        return res.status(401).send({
            error: {
                message: 'Parameter "projectId" is required',
            },
        });
    }

    try {
        const accountId = req.params.accountId;
        const data = await projectQueueForBuild({
            accountId,
            projectId,
            infoCode: {
                code: EVENTS.PROJECT_BUILD_BY_MANAGEMENT_API,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    author: {
                        email: EMAILS.AI,
                    },
                },
            },
            item: {
                executeAt: Date.now(),
                retries: [],
                vendor: vendors.manager,
                triggeredBy: {
                    type: 'managementApi',
                },
            },
        });

        return res.status(200).send({
            success: data?.data?.success,
            data: omit(['success'], data),
        });
    } catch (e) {
        const error = e as Error;
        return res.status(500).send({
            success: false,
            error: getErrorData(error),
        });
    }
};
