import {
    PROJECT_DEQUEUE_REASONS,
} from '@w7-3/webeagle-resources/dist/config/project';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import projectBuildDequeueItem from '../../../../utils/projects/projectBuildDequeueItem';
import {getErrorData} from '../../../../utils/response';
import type {Request, Response} from 'express';
import type {
    AccountData,
    ProjectConfig,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const {
        id,
        projectId,
        vendor,
    } = req.body;

    if (!id) {
        return res.status(401).send({
            error: {
                message: 'Parameter "projectId" is required',
            },
        });
    }

    try {
        const accountId = req.params.accountId;
        const paths = getPaths({
            accountId,
            projectId,
        });
        const [projectConfigError, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
            collection: paths.collections.configs,
            id: 'current',
        });

        if (projectConfigError || !projectConfig) {
            return res.status(500).send({
                success: false,
                error: getErrorData(projectConfigError as Error),
            });
        }

        const [accountDataError, accountData] = await dao.getDocumentData<AccountData>({
            path: paths.collections.account,
        });

        if (accountDataError || !accountData) {
            return res.status(500).send({
                success: false,
                error: getErrorData(accountDataError as Error),
            });
        }

        const {success} = await projectBuildDequeueItem({
            accountId,
            id,
            projectId,
            reason: PROJECT_DEQUEUE_REASONS.managementApiDequeue,
            vendor,
            data: {
                projectId,
                projectName: projectConfig?.data.projectName,
                blacklistedDomainListInTarget: [],
                email: accountData.accountHolder,
                language: accountData.preferredLanguage,
            },
        });
        const data: {
            reason?: string,
        } = {};

        if (!success) {
            return res.status(401).send({
                error: {
                    message: 'Project is not in queue',
                },
            });
        }

        return res.status(200).send({
            success,
            data,
        });
    } catch (e) {
        const error = e as Error;
        return res.status(500).send({
            success: false,
            error: getErrorData(error),
        });
    }
};
