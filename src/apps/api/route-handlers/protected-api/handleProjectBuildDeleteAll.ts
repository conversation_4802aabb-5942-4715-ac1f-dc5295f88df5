import {
    EVENTS,
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {
    isPositiveInt,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {logIntoDB} from '../../../../utils/logger';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {
    ProjectData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {projectId} = req.body?.data || {};
        const {accountId, email} = await getActiveUserData(req) as UserData;
        let paths = getPaths({accountId, projectId});
        const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
            collection: paths.collections.projects,
            id: projectId,
        });

        if (!projectData) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.projectNotFound,
                    },
                },
            }, false));

            return;
        }

        const hasBeenExecuted = isPositiveInt(projectData?.latestBuild?.index, true);

        if (!hasBeenExecuted) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                        config: {
                            variant: INFO_CODE_LEVELS.ERROR,
                        },
                    },
                },
            }, false));
        }

        if (projectData?.latestBuild?.state === BUILD_STATES.running) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.cannotUpdateProjectWhenBuilding,
                    },
                },
            }, false));
        }

        for (let index = projectData.latestBuild.index!; index > 0; index--) {
            paths = getPaths({accountId, projectId, buildId: index});
            await dao.setDocumentData({
                path: paths.collections.project,
                data: {
                    latestBuild: dao.FieldValue.delete(),
                },
            });

            await dao.recursiveDeleteDocuments({
                path: paths.collections.projectBuild,
            });

            await dao.recursiveDeleteStorageDirectory({
                path: paths.collections.projectBuild,
            });
        }

        const loginSessionId = req.headers['browser-session'] as string;
        await logIntoDB(paths.collections.projectEvents, {
            code: EVENTS.PROJECT_BUILD_DELETED_ALL,
            level: INFO_CODE_LEVELS.INFO,
            timestamp: Date.now(),
            data: {
                projectId,
                loginSessionId,
                deletedOn: Date.now(),
                deletedBy: email,
            },
        });

        res.send(getResponseData({}, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
