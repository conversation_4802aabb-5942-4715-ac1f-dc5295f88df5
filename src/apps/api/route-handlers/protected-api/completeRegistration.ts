import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    ACCOUNT_AUDIT_PERIOD_NEW,
    accountStates,
    THREASHOLDS,
} from '@w7-3/webeagle-resources/dist/config/account';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    ROLES as userRoles,
    STATES as userStates,
} from '@w7-3/webeagle-resources/dist/config/users';
import {
    projectBuildTimeoutHandling,
} from '@w7-3/webeagle-resources/dist/config/project';
import {
    NOTIFICATION_METHODS,
    NOTIFICATION_TYPES,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import {
    getHumanReadableTimestamp,
    humanReadableDateFormats,
} from '@w7-3/webeagle-resources/dist/libs/date';
import {getBillingCycleId} from '@w7-3/webeagle-resources/dist/libs/accounts';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {logIntoDB} from '../../../../utils/logger';
import dbInstance from '../../../../setup/setupDB';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {Request, Response} from 'express';
import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';
import {getStripe} from '../../../../utils/stripe';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            timeZone,
        } = req.body?.data || {};
        const data = await getActiveUserData(req);
        const [, loginData] = await dao.getLoginData({
            idToken: String(req.headers.token),
        });

        if (!data?.email || !loginData?.emailVerified) {
            res.send(getResponseData({}, false));
            return;
        }

        const {
            accountId,
            email,
            state,
            role,
            invitationProcess = [],
            preferredLanguage,
        } = data;

        const isNewlyVerified = !accountId;

        if (state === userStates.VERIFIED && !isNewlyVerified) {
            res.send(getResponseData({data}, true));

            return;
        }

        if (state !== userStates.VERIFIED) {
            data.state = userStates.VERIFIED;
            data.invitationProcess = [
                ...invitationProcess,
                {
                    state: userStates.VERIFIED,
                    ts: Date.now(),
                }];
        }

        const isNewAccount = role === userRoles.ACCOUNT_HOLDER.key && isNewlyVerified;

        if (isNewAccount) {
            const organisation = data.organisation as string;
            const [, accountList] = await dao.getAllCollectionDataAsList({
                collection: collections.accounts,
                condition: {
                    fieldPath: 'accountHolder',
                    opStr: '==',
                    value: email,
                },
                limit: 1,
            });

            if (accountList.length > 0) {
                return sendErrorResponse(res);
            }

            data.accountId = getRandomString(32, libraryKeys.alphaNumericLowerCased);
            const isoTimestamp = getHumanReadableTimestamp({template: humanReadableDateFormats.ISO});
            const [year, month] = isoTimestamp.split('.').map(Number);
            const customer = await getStripe().customers.create({
                name: organisation,
                email,
            });
            const accountData: AccountData = {
                accountHolder: email,
                blacklistedDomains: {},
                created: Date.now(),
                customerData: {
                    id: customer.id,
                    automationCredits: {
                        items: [],
                        autoRenew: false,
                        alerting: [],
                        triggerAt: THREASHOLDS.AUTOMATION_CREDITS_AUTO_TOP_UP_VALUE,
                    },
                },
                domainBuilds: {},
                id: data.accountId,
                lifeTimeAutomationCredits: {
                    value: 0,
                    expiry: 0,
                },
                lifeTimeCollaboratorSeats: {
                    value: 1,
                    records: [{
                        quantity: 1,
                        id: 'setup',
                    }],
                },
                organisation,
                preferredLanguage,
                state: accountStates.new,
                timeZone,
                timeoutHandling: projectBuildTimeoutHandling.noop,
                domainVerifications: {},
                administration: {
                    audit: {
                        active: false,
                        current: Date.now(),
                        period: ACCOUNT_AUDIT_PERIOD_NEW,
                        history: [],
                    },
                    billing: {
                        cycle: getBillingCycleId({
                            year,
                            month,
                        }),
                        history: [],
                    },
                },
            };

            await dao.insertCollectionData<EmailNotification<{
                organisation: AccountData['organisation'];
            }>>({
                collection: collections.notifications,
                data: {
                    accountId: data.accountId,
                    createdAt: Date.now(),
                    data: {
                        email,
                        language: preferredLanguage,
                        payload: {
                            organisation,
                        },
                    },
                    method: NOTIFICATION_METHODS.EMAIL,
                    sendAt: Date.now(),
                    type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
                    subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.REGISTRATION_COMPLETED,
                },
            });

            try {
                Intl.DateTimeFormat(undefined, {timeZone});

                accountData.timeZone = timeZone;
            } catch (ex) {}

            await dao.setCollectionDocumentData<AccountData>({
                id: data.accountId,
                collection: collections.accounts,
                data: accountData,
            });
        }

        const paths = getPaths({accountId: data.accountId});

        if (role === userRoles.ACCOUNT_HOLDER.key) {
            await logIntoDB(paths.collections.accountEvents, {
                code: EVENTS.ACCOUNT_CREATED,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
            });
        }

        await logIntoDB(paths.collections.accountEvents, {
            code: EVENTS.ACCOUNT_NEW_COLLABORATOR_CONFIRMS_ACCOUNT,
            level: INFO_CODE_LEVELS.INFO,
            timestamp: Date.now(),
            data: {
                email,
            },
        });

        if (isNewAccount) {
            await logIntoDB(paths.collections.accountEvents, {
                code: EVENTS.ACCOUNT_PRE_PAID_SUBSCRIPTION_CREATED,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
            });
        }

        // @ts-expect-error - firestore delete field is out of scope
        data.organisation = dao.FieldValue.delete();

        await dao.setCollectionDocumentData<UserData>({
            id: email,
            collection: collections.users,
            data,
        });

        res.send(getResponseData({data, isNewlyVerified}, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
