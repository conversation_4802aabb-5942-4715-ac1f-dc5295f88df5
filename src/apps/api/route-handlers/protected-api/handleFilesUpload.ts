import multer from 'multer';
import os from 'os';
import * as changeCase from 'change-case';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import * as filesResources from '@w7-3/webeagle-resources/dist/config/files';
import getParsedJSON from '@w7-3/webeagle-resources/dist/libs/getParsedJSON';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {FileTransferData} from '@w7-3/webeagle-resources/types/solutions';
import type {Response} from 'express';

export const uploadFileName = 'uploadFile';
const upload = multer({
    dest: `${os.tmpdir()}`,
    limits: {
        fieldNameSize: 128,
        fileSize: filesResources.config.maxFileSize,
        files: 1,
    },
}).single(uploadFileName);
const dao = dbInstance.getDAO();

export default async (req: any, res: Response) => {
    try {
        upload(req, res, async (err: {code: string}) => {
            if (err) {
                if (err instanceof multer.MulterError && err.code === 'LIMIT_FILE_SIZE') {
                    return res.send(getResponseData({
                        directives: {
                            notification: {
                                message: NOTIFICATIONS.fileTooBig,
                                data: {
                                    maxFileSize: `${filesResources.config.maxFileSize / (1024 * 1024)}MB`,
                                },
                            },
                        },
                    }, false));
                }
                return res.send(getResponseData({
                    directives: {
                        notification: {
                            message: NOTIFICATIONS.genericError,
                        },
                    },
                }, false));
            }

            try {
                const {accountId, email} = await getActiveUserData(req) as UserData;
                const paths = getPaths({accountId});
                const {
                    name,
                    extension,
                    isImage,
                    tags: rawTags,
                } = req.body || {};
                const tags = getParsedJSON(rawTags);
                const id = getRandomString(32, libraryKeys.alphaNumericLowerCased);
                const uploadData: FileTransferData = await dao.uploadFile({
                    storageFilePath: `${paths.collections.files}/${id}.${changeCase.snakeCase(extension)}`,
                    filePath: req.file.path,
                    deleteAfterUpload: true,
                    contentType: req.file.mimetype,
                });
                const data = {
                    ...isNonEmptyArray(tags) && {
                        tags,
                    },
                    id,
                    uploadData,
                    lastEditedAt: Date.now(),
                    history: [{
                        uploadedBy: email,
                        uploadedAt: Date.now(),
                        name: name || req.file.originalname,
                        extension,
                        isImage,
                        size: req.file.size,
                        mimeType: req.file.mimetype,
                    }],
                };

                dao.setCollectionDocumentData({
                    id,
                    collection: paths.collections.files,
                    data,
                });

                res.send(getResponseData({data}, true));
            } catch (e) {
                res.send(getResponseData({}, false));
            }
        });
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
