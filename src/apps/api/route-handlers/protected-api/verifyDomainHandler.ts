import {parse} from 'tldts';
import {
    VERIFICATION_TYPE,
    VERIFICATION_ACTION,
    VERIFICATION_STATE,
} from '@w7-3/webeagle-resources/dist/config/domains';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {
    UserData,
    DomainVerification,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';
import verifyDomain from '../../../../utils/domains/verifyDomain';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            url,
            method,
            action,
        } = req.body?.data || {};
        const domain = parse(url).domain as string;

        if (!isNonEmptyString(domain) || !VERIFICATION_TYPE[method] || !VERIFICATION_ACTION[action]) {
            return res.send(getResponseData({}, false));
        }

        const {accountId, email} = await getActiveUserData(req) as UserData;

        if (action === VERIFICATION_ACTION.REVOKE) {
            const [error] = await dao.deleteCollectionDataField({
                id: accountId,
                collection: collections.accounts,
                propPath: ['domainVerifications', domain],
            });

            if (error) {
                return res.send(getResponseData({}, false));
            }

            return res.send(getResponseData({
                state: VERIFICATION_STATE.REVOKED,
            }, true));
        }

        if (action === VERIFICATION_ACTION.VERIFY) {
            const {
                success,
                data,
            } = await verifyDomain({
                url,
                accountId,
                method,
            });
            const domainVerificationData: Partial<DomainVerification> = {
                id: domain,
                ts: Date.now(),
                state: VERIFICATION_STATE.PENDING,
                method,
            };

            if (success && data.canCrawl) {
                domainVerificationData.crawlDelay = data.crawlDelay;
                domainVerificationData.state = VERIFICATION_STATE.VERIFIED;
                domainVerificationData.expiry = Date.now() + 1000 * 60 * 60 * 24 * 365;
                domainVerificationData.author = {
                    email,
                };
            }

            const [error] = await dao.setCollectionDocumentData<{
                domainVerifications: {
                    [domain: string]: Partial<DomainVerification>,
                },
                blacklistedDomains: {
                    [domain: string]: unknown,
                },
            }>({
                id: accountId,
                collection: collections.accounts,
                data: {
                    domainVerifications: {
                        [domain]: domainVerificationData,
                    },
                    blacklistedDomains: {
                        [domain]: dao.FieldValue.delete(),
                    },
                },
            });

            if (error) {
                return res.send(getResponseData({}, false));
            }

            return res.send(getResponseData({
                state: domainVerificationData.state,
            }, true));
        }

        return res.send(getResponseData({}, false));
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
