import multer from 'multer';
import os from 'os';
import * as changeCase from 'change-case';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import * as filesResources from '@w7-3/webeagle-resources/dist/config/files';
import {isNonEmptyString, isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import getParsedJSON from '@w7-3/webeagle-resources/dist/libs/getParsedJSON';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData} from '../../../../utils/response';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';
import type {Response} from 'express';
import type {FilesConfig} from '@w7-3/webeagle-resources/types/firebase';

export const uploadFileName = 'uploadFile';
const upload = multer({dest: `${os.tmpdir()}`}).single(uploadFileName);
const dao = dbInstance.getDAO();

export default async (req: any, res: Response) => {
    try {
        upload(req, res, async (err) => {
            const {
                id,
                name,
                tags: rawTags,
            } = req.body || {};
            const tags = getParsedJSON(rawTags);

            if (err || !isNonEmptyString(id) || !isNonEmptyString(name)) {
                return res.send(getResponseData({
                    directives: {
                        notification: {
                            message: NOTIFICATIONS.genericError,
                            x: 1,
                        },
                    },
                }, false));
            }

            const {accountId, email} = await getActiveUserData(req) as UserData;
            const paths = getPaths({accountId});
            const [, fileData] = await dao.getCollectionDocumentData<FilesConfig>({
                id,
                collection: paths.collections.files,
            });

            if (!fileData?.uploadData?.data?.destination) {
                return res.send(getResponseData({
                    directives: {
                        notification: {
                            message: NOTIFICATIONS.genericError,
                            x: 2,
                        },
                    },
                }, false));
            }

            let uploadData = fileData.uploadData;
            const hasFileBinary = req.file && req.file.size < filesResources.config.maxFileSize;
            try {
                if (hasFileBinary) {
                    const extension = req.file.originalname.split('.').pop();
                    const [oldFileDeleteError] = await dao.deleteStorageFile({
                        storageFilePath: fileData.uploadData.data.destination,
                    });

                    if (oldFileDeleteError) {
                        return res.send(getResponseData({
                            directives: {
                                notification: {
                                    message: NOTIFICATIONS.genericError,
                                },
                            },
                        }, false));
                    }

                    const storageFilePath = `${paths.collections.files}/${id}.${changeCase.snakeCase(extension as string)}`;
                    uploadData = await dao.uploadFile({
                        storageFilePath,
                        filePath: req.file.path,
                        deleteAfterUpload: true,
                        contentType: req.file.mimetype,
                    });
                }

                const previousHistoryItem = fileData.history.at(-1);
                const data = {
                    ...isNonEmptyArray(tags) && {
                        tags,
                    },
                    id,
                    history: [
                        ...fileData.history,
                        {
                            ...previousHistoryItem,
                            uploadedBy: email,
                            uploadedAt: Date.now(),
                            name: name || req.file.originalname,
                            ...hasFileBinary && {
                                size: req?.file?.size,
                                mimeType: req?.file?.mimetype,
                            },
                        },
                    ],
                    uploadData,
                    lastEditedAt: Date.now(),
                };
                const [fileDataInsertError] = await dao.setCollectionDocumentData<FilesConfig>({
                    id,
                    collection: paths.collections.files,
                    data,
                });

                if (fileDataInsertError) {
                    throw fileDataInsertError;
                }

                return res.send(getResponseData({data}, true));
            } catch (e) {
                res.send(getResponseData({}, false));
            }
        });
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
