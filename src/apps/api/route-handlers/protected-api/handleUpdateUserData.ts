import type {Request, Response} from 'express';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            preferredLanguage,
        } = req.body?.data || {};
        const {email} = await getActiveUserData(req) as UserData;

        if (isNonEmptyString(preferredLanguage)) {
            const [error] = await dao.setCollectionDocumentData<Pick<UserData, 'preferredLanguage'>>({
                id: email,
                collection: collections.users,
                data: {
                    preferredLanguage,
                },
            });

            return res.send(getResponseData({preferredLanguage}, !error));
        }

        return res.send(getResponseData({}, false));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
