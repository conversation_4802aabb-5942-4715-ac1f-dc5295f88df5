import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    PREPAID_SUBSCRIPTION_KEY,
} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isPositiveInt} from '@w7-3/webeagle-resources/dist/libs/validators';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import scheduleNextAutoRun from '../../../../utils/scheduleNextAutoRun';
import {logIntoDB} from '../../../../utils/logger';
import type {Request, Response} from 'express';
import type {
    AccountData,
    ProjectConfig,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import projectUpdateAllowed
    from '../../../../utils/projects/checkProjectUpdateAllowed';
import dbInstance from '../../../../setup/setupDB';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId, email} = await getActiveUserData(req) as UserData;
        const {
            projectId,
            subscriptionId,
        } = req.body?.data || {};

        const projectUpdateData = await projectUpdateAllowed({
            accountId,
            projectId,
        });

        if (!projectUpdateData.success) {
            return res.send(getResponseData(projectUpdateData, false));
        }

        if (subscriptionId === PREPAID_SUBSCRIPTION_KEY) {
            const [, accountData] = await dao.getCollectionDocumentData<AccountData>({
                id: accountId,
                collection: collections.accounts,
            });

            if (!isPositiveInt(accountData?.lifeTimeAutomationCredits?.value)) {
                return res.send(getResponseData({
                    directives: {
                        infoCodes: [{
                            code: EVENTS.AUTOMATION_CREDITS_REQUIRED,
                            level: INFO_CODE_LEVELS.ERROR,
                            timestamp: Date.now(),
                        }],
                    },
                }, false));
            }
        }

        await scheduleNextAutoRun({
            accountId,
            projectId,
            triggeredBy: {
                type: 'subscriptionUpdate',
            },
        });

        const paths = getPaths({
            accountId,
            projectId,
        });

        await dao.setCollectionDocumentData<{
            data: {
                subscriptionId: ProjectConfig['data']['subscriptionId'];
            };
        }>({
            id: projectUpdateData.projectConfig!.id,
            collection: paths.collections.configs,
            data: {
                data: {subscriptionId},
            },
        });

        await logIntoDB(paths.collections.projectEvents, {
            code: EVENTS.PROJECT_SUBSCRIPTION_CHANGED,
            level: INFO_CODE_LEVELS.INFO,
            timestamp: Date.now(),
            data: {
                author: {
                    email,
                },
                old: projectUpdateData.projectConfig?.data?.subscriptionId || '""',
                new: subscriptionId,
            },
        });

        return res.send(getResponseData({subscriptionId}, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
