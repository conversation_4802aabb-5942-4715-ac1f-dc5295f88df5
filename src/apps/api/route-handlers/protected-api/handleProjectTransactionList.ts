import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {getProjectSubscriptionData} from '../../../../utils/subscriptionData';
import type {Request, Response} from 'express';
import type {
    ProjectConfig,
    ProjectData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {projectId} = req.body?.data || {};
        const {accountId} = await getActiveUserData(req) as UserData;
        const paths = getPaths({
            accountId,
            projectId,
        });
        const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
            collection: paths.collections.projects,
            id: projectId,
        });

        if (!projectData) {
            return sendErrorResponse(res);
        }

        const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
            collection: paths.collections.configs,
            id: projectData?.configId,
        });

        if (!projectConfig) {
            return sendErrorResponse(res);
        }

        const projectConfigData = projectConfig?.data;
        const {selectedSubscription, matchingSubscriptionDataList} = await getProjectSubscriptionData({
            projectConfigData,
            accountId,
        });

        res.send(getResponseData({
            subscriptionData: selectedSubscription,
            matchingSubscriptionDataList,
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
