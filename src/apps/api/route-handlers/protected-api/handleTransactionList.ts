import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    Checkout,
    CheckoutTransaction,
} from '@w7-3/webeagle-resources/types/administration';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId} = await getActiveUserData(req) as UserData;
        const paths = getPaths({
            accountId,
            projectId: '',
        });
        const [, transactionList] = await dao.getAllCollectionDataAsList<CheckoutTransaction>({
            collection: paths.collections.subscriptions,
            orders: [{
                fieldPath: 'data.created',
                directionStr: 'desc',
            }],
        });
        const [, checkoutsList] = await dao.getAllCollectionDataAsList<Checkout>({
            collection: paths.collections.checkouts,
            orders: [{
                fieldPath: 'created',
                directionStr: 'desc',
            }],
        });

        res.send(getResponseData({
            transactionList,
            checkoutsList,
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
