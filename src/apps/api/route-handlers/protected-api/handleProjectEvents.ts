import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import type {Request, Response} from 'express';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {projectId} = req.body?.data || {};
        const {accountId} = await getActiveUserData(req) as UserData;
        const paths = getPaths({
            accountId,
            projectId,
        });
        const [, projectEvents] = await dao.getAllCollectionData({
            collection: paths.collections.projectEvents,
        });

        res.send(getResponseData({
            projectEventList: Object.values(projectEvents || {}),
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
