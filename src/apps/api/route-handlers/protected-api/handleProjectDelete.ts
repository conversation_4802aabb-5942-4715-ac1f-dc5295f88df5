import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {
    ProjectData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {projectId} = req.body?.data || {};
        const {accountId, email} = await getActiveUserData(req) as UserData;
        const paths = getPaths({accountId, projectId});
        const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
            collection: paths.collections.projects,
            id: projectId,
        });

        if (!projectData) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.projectNotFound,
                    },
                },
            }, false));

            return;
        }

        if (projectData?.latestBuild?.state === BUILD_STATES.running) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.cannotUpdateProjectWhenBuilding,
                    },
                },
            }, false));

            return;
        }

        await dao.setDocumentData({
            path: paths.collections.project,
            data: {
                isDeleted: true,
                state: projectStates.archive,
            },
        });
        await dao.insertCollectionData({
            collection: collections.projectDeleteQueue,
            data: {
                accountId,
                projectId,
                deletedOn: Date.now(),
                deletedBy: email,
            },
        });

        res.send(getResponseData({}, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
