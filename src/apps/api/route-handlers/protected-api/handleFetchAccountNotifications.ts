import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {getResponseData} from '../../../../utils/response';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId} = await getActiveUserData(req) as UserData;
        const paths = getPaths({accountId});
        const [, accountNotificationList] = await dao.getAllCollectionDataAsList({
            collection: paths.collections.accountNotifications,
        });

        res.send(getResponseData({data: {
            accountNotificationList,
        }}, true));
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
