import type {Request, Response} from 'express';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {
    EVENTS,
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {logIntoDB} from '../../../../utils/logger';
import dbInstance from '../../../../setup/setupDB';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {
    ProjectConfig,
    ProjectData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId, email} = await getActiveUserData(req) as UserData;
        const {
            projectId,
            buildId,
        } = req.body?.data || {};

        if (!accountId || !projectId || !buildId) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.unauthenticated,
                    },
                },
            }, false));

            return;
        }

        const paths = getPaths({
            accountId,
            projectId,
            buildId,
        });

        const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
            collection: paths.collections.projects,
            id: projectId,
        });

        if (projectData?.latestBuild?.state !== BUILD_STATES.running) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, true));
        }
        const cancellation = {
            requestTime: Date.now(),
            requestedBy: {email},
            isInProgress: true,
        };
        await dao.setDocumentData({
            path: paths.collections.projectBuild,
            data: {
                cancellation,
            },
        });
        await dao.setDocumentData({
            path: paths.collections.project,
            data: {
                latestBuild: {
                    state: BUILD_STATES.cancel,
                    cancellation,
                },
            },
        });

        await logIntoDB(paths.collections.buildEvents, {
            code: EVENTS.SESSION_CANCELLED_MANUALLY,
            level: INFO_CODE_LEVELS.INFO,
            timestamp: Date.now(),
        });

        await logIntoDB(paths.collections.projectEvents, {
            code: EVENTS.PROJECT_BUILD_CANCEL,
            level: INFO_CODE_LEVELS.INFO,
            timestamp: Date.now(),
            data: {
                author: {
                    email,
                },
            },
        });

        const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
            collection: paths.collections.configs,
            id: projectData?.configId,
        });

        return res.send(getResponseData({
            directives: {
                notification: {
                    message: NOTIFICATIONS.projectBuildCanceled,
                    data: {
                        projectName: projectConfig?.data?.projectName,
                    },
                },
            },
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
