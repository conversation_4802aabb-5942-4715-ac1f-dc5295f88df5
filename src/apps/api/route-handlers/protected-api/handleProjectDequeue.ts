import {
    PROJECT_DEQUEUE_REASONS,
} from '@w7-3/webeagle-resources/dist/config/project';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {vendors} from '@w7-3/webeagle-resources/dist/config/project';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {
    getResponseData,
    sendErrorResponse,
} from '../../../../utils/response';
import projectBuildDequeueItem from '../../../../utils/projects/projectBuildDequeueItem';
import type {
    AccountData,
    ProjectConfig,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';
import dbInstance from '../../../../setup/setupDB';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId} = await getActiveUserData(req) as UserData;
        const {
            id,
            projectId,
        } = req.body?.data || {};

        if (!accountId || !projectId) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.unauthenticated,
                    },
                },
            }, false));

            return;
        }

        const paths = getPaths({
            accountId,
            projectId,
        });
        const [projectConfigError, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
            collection: paths.collections.configs,
            id: 'current',
        });

        if (projectConfigError || !projectConfig) {
            return sendErrorResponse(res);
        }

        const [accountDataError, accountData] = await dao.getDocumentData<AccountData>({
            path: paths.collections.account,
        });

        if (accountDataError || !accountData) {
            return sendErrorResponse(res);
        }

        const {success, directives} = await projectBuildDequeueItem({
            accountId,
            projectId,
            id,
            reason: PROJECT_DEQUEUE_REASONS.noProblem,
            vendor: vendors.manual,
            data: {
                projectId,
                projectName: projectConfig?.data.projectName,
                blacklistedDomainListInTarget: [],
                email: accountData.accountHolder,
                language: accountData.preferredLanguage,
            },
        });

        return res.send(getResponseData({directives}, success));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
