import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import api from '@w7-3/webeagle-resources/dist/config/api';

export const apiWhiteListAllUsers = [
    `${api.user}`,
    `${api.createUser}`,
];

export const apiWhiteListForUnverifiedUsers = [];

export const roleGuardsForAuthenticatedUsers = {
    [`${api.projectCreate}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
    ],
    [`${api.projectUpdate}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
        userRoles.EDITOR.key,
    ],
    [`${api.projectTransactionList}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
        userRoles.EDITOR.key,
    ],
    [`${api.collaborator}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
    ],
    [`${api.transactionList}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
    ],
    [`${api.collaboratorList}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
        userRoles.EDITOR.key,
    ],
    [`${api.updateVisualTestState}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
        userRoles.EDITOR.key,
    ],
    [`${api.updateAccountData}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
    ],
    [`${api.projectDelete}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
        userRoles.EDITOR.key,
    ],
    [`${api.projectBuildDelete}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
        userRoles.EDITOR.key,
    ],
};
