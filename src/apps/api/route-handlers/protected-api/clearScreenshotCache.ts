import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {
    ProjectData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId} = await getActiveUserData(req) as UserData;
        const {
            id,
            projectId,
            buildId,
        } = req.body?.data || {};

        const paths = getPaths({
            accountId,
            projectId,
            buildId,
        });

        const [, projectData] = await dao.getDocumentData<ProjectData>({
            path: paths.collections.project,
        });

        if (!projectData?.cache?.domSelectors?.[id] || buildId !== projectData?.latestBuild?.index) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));

            return;
        }

        await dao.setDocumentData({
            path: paths.collections.project,
            data: {
                cache: {
                    domSelectors: {
                        [id]: dao.FieldValue.delete(),
                    },
                },
            },
        });

        res.send(getResponseData({}, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
