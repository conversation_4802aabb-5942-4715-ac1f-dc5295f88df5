import type {Request, Response} from 'express';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const [, loginData] = await dao.getLoginData({
            idToken: String(req.headers.token),
        }) as [Error, {email: string}];

        if (!loginData) {
            return sendErrorResponse(res);
        }

        const [, userData] = await dao.getCollectionDocumentData<UserData>({
            id: loginData.email,
            collection: collections.users,
        });

        res.send(getResponseData({
            userData,
            loginData,
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
