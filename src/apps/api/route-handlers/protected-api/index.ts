import * as express from 'express';
import api from '@w7-3/webeagle-resources/dist/config/api';
import handleProjectUpdate from './handleProjectUpdate';
import handleProjectCreate from './handleProjectCreate';
import handleProjectBuild from './handleProjectBuild';
import handleFilesUpload from './handleFilesUpload';
import handleFilesFetch from './handleFilesFetch';
import handleFilesUpdate from './handleFilesUpdate';
import handleFilesDelete from './handleFilesDelete';
import handleCollaborator from './handleCollaborator';
import completeRegistration from './completeRegistration';
import handleAccountNotifications from './handleAccountNotifications';
import handleFetchAccountNotifications from './handleFetchAccountNotifications';
import handleProjectDownload from './handleProjectDownload';
import updateVisualTestState from './updateVisualTestState';
import handleProjectTransactionUpdate from './handleProjectTransactionUpdate';
import handleDemo from './handleDemo';
import verifyDomainHandler from './verifyDomainHandler';
import handleProjectSubscriptionUpdate from './handleProjectSubscriptionUpdate';
import handleProjectStateChange from './handleProjectStateChange';
import handleProjectDequeue from './handleProjectDequeue';
import handleFetchBillingCycle from './handleFetchBillingCycle';
import handleUser from './handleUser';
import handleUpdateAccountData from './handleUpdateAccountData';
import handleUpdateUserData from './handleUpdateUserData';
import handleProjectBuildCancel from './handleProjectBuildCancel';
import handleProjects from './handleProjects';
import handleTransactionList from './handleTransactionList';
import handleCollaboratorList from './handleCollaboratorList';
import handleProjectBuildResult from './handleProjectBuildResult';
import handleProjectBuildSubPageResultAll
    from './handleProjectBuildSubPageResultAll';
import handleProjectBuildItemHistory from './handleProjectBuildItemHistory';
import handleProjectBuildDelete from './handleProjectBuildDelete';
import handleProjectBuildDeleteAll from './handleProjectBuildDeleteAll';
import handleProjectDelete from './handleProjectDelete';
import handleProjectTransactionList from './handleProjectTransactionList';
import handleProjectEvents from './handleProjectEvents';
import handleAccountEvents from './handleAccountEvents';
import clearScreenshotCache from './clearScreenshotCache';
import getAccessValidator from '../../utils/getAccessValidator';
import handleError from '../../utils/handleError';
import {
    apiWhiteListAllUsers,
    apiWhiteListForUnverifiedUsers,
    roleGuardsForAuthenticatedUsers,
} from './utils/config';
import type {Router} from 'express';

const app: Router = express.Router();

app.use(handleError);

app.all('*', getAccessValidator({
    apiWhiteListAllUsers,
    apiWhiteListForUnverifiedUsers,
    roleGuardsForAuthenticatedUsers,
}));

app.post(api.user, handleUser);

app.post(api.updateAccountData, handleUpdateAccountData);

app.post(api.updateUserData, handleUpdateUserData);

app.post(api.collaborator, handleCollaborator);

app.post(api.completeRegistration, completeRegistration);

app.post(api.transactionList, handleTransactionList);

app.post(api.collaboratorList, handleCollaboratorList);

app.post(api.projectCreate, handleProjectCreate);

app.post(api.accountNotifications, handleAccountNotifications);

app.post(api.accountNotificationsList, handleFetchAccountNotifications);

app.post(api.projectUpdate, handleProjectUpdate);

app.post(api.projectSubscriptionUpdate, handleProjectSubscriptionUpdate);

app.post(api.projectStateChange, handleProjectStateChange);

app.post(api.projects, handleProjects);

app.post(api.projectDequeue, handleProjectDequeue);

app.post(api.projectBuildCancel, handleProjectBuildCancel);

app.post(api.projectBuild, handleProjectBuild);

app.post(api.projectBuildResult, handleProjectBuildResult);

app.post(api.projectBuildSubPageResultAll, handleProjectBuildSubPageResultAll);

app.post(api.projectBuildItemHistory, handleProjectBuildItemHistory);

app.post(api.projectEvents, handleProjectEvents);

app.post(api.accountEvents, handleAccountEvents);

app.post(api.projectTransactionList, handleProjectTransactionList);

app.post(api.projectTransactionUpdate, handleProjectTransactionUpdate);

app.post(api.updateVisualTestState, updateVisualTestState);

app.post(api.demo, handleDemo);

app.post(api.projectDelete, handleProjectDelete);

app.post(api.projectBuildDelete, handleProjectBuildDelete);

app.post(api.projectBuildDeleteAll, handleProjectBuildDeleteAll);

app.post(api.projectDownload, handleProjectDownload);

app.post(api.filesUpload, handleFilesUpload);

app.post(api.filesFetch, handleFilesFetch);

app.post(api.filesUpdate, handleFilesUpdate);

app.post(api.filesDelete, handleFilesDelete);

app.post(api.verifyDomain, verifyDomainHandler);

app.post(api.billingCycle, handleFetchBillingCycle);

app.post(api.clearScreenshotCache, clearScreenshotCache);

export default app;
