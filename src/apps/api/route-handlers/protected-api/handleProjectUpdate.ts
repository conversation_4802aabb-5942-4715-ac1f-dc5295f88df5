import deepDiff from 'deep-diff';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    isNonEmptyObject,
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import getProjectConfigurationValidation
    from '@w7-3/webeagle-resources/dist/libs/project-solution/getProjectConfigurationValidation';
import {
    EVENTS,
    INFO_CODE_LEVELS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import checkProjectUpdateAllowed
    from '../../../../utils/projects/checkProjectUpdateAllowed';
import {logIntoDB} from '../../../../utils/logger';
import dbInstance from '../../../../setup/setupDB';
import type {
    ProjectConfig,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';
import type {ProjectConfigData} from '@w7-3/webeagle-resources/types/project';
import getProjectConfigs from '../../../../utils/projects/getProjectConfigs';

const dao = dbInstance.getDAO();

export const getNewTargetUrls = ({
    prev,
    next,
}: {
    prev: ProjectConfigData['target']['urls'],
    next: ProjectConfigData['target']['urls'],
}) => {
    const newUrls = next.filter((url) => !prev.find((prevUrl) => prevUrl.url === url.url));
    const updatedUrls = prev.filter((url) => next.find((prevUrl) => prevUrl.url === url.url));
    const deletedUrls = prev.filter((url) => !next.find((nextUrl) => nextUrl.url === url.url));

    return {
        newUrls,
        updatedUrls,
        deletedUrls,
    };
};

export default async (req: Request, res: Response) => {
    try {
        const {accountId, email} = await getActiveUserData(req) as UserData;
        const {
            configData,
            projectId,
        } = (req.body?.data || {}) as {
            configData: ProjectConfigData,
            projectId: string,
        };

        if (!isNonEmptyObject(configData) || !isNonEmptyString(projectId)) {
            return sendErrorResponse(res);
        }

        const projectUpdateData = await checkProjectUpdateAllowed({
            accountId,
            projectId,
        });

        const prevConfigData = projectUpdateData?.projectConfig?.data;
        if (!projectUpdateData.success || !prevConfigData) {
            return res.send(getResponseData({
                directives: projectUpdateData.directives,
            }, false));
        }

        configData.subscriptionId = prevConfigData.subscriptionId;
        configData.projectId = prevConfigData.projectId;

        const diff = deepDiff.diff(prevConfigData, configData);

        if (!diff) {
            return res.send(getResponseData({
                isEdited: false,
            }, true));
        }

        const loginSessionId = req.headers['browser-session'] as string;
        const paths = getPaths({
            accountId,
            projectId,
        });
        const {configs} = await getProjectConfigs({
            accountId,
        });

        if (projectUpdateData?.projectData?.state === projectStates?.active) {
            configData.projectId = projectId;
            const validation = getProjectConfigurationValidation({
                item: {data: configData},
                list: configs,
            });

            if (!validation.success) {
                return res.send(getResponseData({
                    validation,
                }, false));
            }
        }

        const {projectData, projectConfig} = projectUpdateData;
        const projectHasBeenExecuted = Boolean(projectData?.latestBuild?.state);
        const {
            newUrls,
            updatedUrls,
            deletedUrls,
        } = getNewTargetUrls({
            prev: projectConfig!.data.target.urls,
            next: configData.target.urls,
        });

        if (projectHasBeenExecuted) {
            if ((newUrls.length > 0 || deletedUrls.length > 0)) {
                return sendErrorResponse(res);
            }

            for (let i = 0; i < projectConfig!.data.target.urls.length; i++) {
                const urlItem = projectConfig!.data.target.urls[i];
                if (!updatedUrls.find((url) => url.url === urlItem.url)) {
                    continue;
                }

                configData.target.urls[i] = {
                    ...projectConfig!.data.target.urls[i],
                    url: urlItem.url,
                };
            }
        }

        await dao.setCollectionDocumentData<Pick<ProjectConfig, 'data'>>({
            id: projectUpdateData.projectConfig!.id,
            collection: paths.collections.configs,
            data: {
                data: configData,
            },
        });

        await logIntoDB(paths.collections.projectEvents, {
            code: EVENTS.PROJECT_EDITED,
            level: INFO_CODE_LEVELS.INFO,
            timestamp: Date.now(),
            data: {
                email,
                loginSessionId,
            },
        });

        return res.send(getResponseData({
            isEdited: true,
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
