import getActiveUserData from '../../../../utils/getActiveUserData';
import getProjectBuildResult from '../../../../utils/getProjectBuildResult';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import type {Request, Response} from 'express';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';

export default async (req: Request, res: Response) => {
    try {
        const {buildId, projectId, resultIds} = req.body?.data || {};
        const {accountId} = await getActiveUserData(req) as UserData;
        const data = await getProjectBuildResult({
            accountId,
            projectId,
            buildId,
            resultIds,
        });

        res.send(getResponseData({data}, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
