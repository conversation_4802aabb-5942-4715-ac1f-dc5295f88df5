import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    EVENTS,
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    ACTIONS as userInvitationActions,
    ROLES as userRoles,
    STATES as userStates,
} from '@w7-3/webeagle-resources/dist/config/users';
import {
    NOTIFICATION_METHODS,
    NOTIFICATION_TYPES,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {logIntoDB} from '../../../../utils/logger';
import getActiveUserData from '../../../../utils/getActiveUserData';
import getCollaboratorList from '../../../../utils/getCollaboratorList';
import getIsNewCollaboratorAllowed
    from '../../../../utils/getIsNewCollaboratorAllowed';
import dbInstance from '../../../../setup/setupDB';

import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';
import type {
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            action,
            collaboratorData,
        } = req.body?.data || {};

        const activeUser = await getActiveUserData(req) as UserData;

        if ([
            userInvitationActions.invite,
            userInvitationActions.edit,
        ].includes(action)) {
            if (collaboratorData?.role && !userRoles?.[collaboratorData?.role]?.isAssignable) {
                res.send(getResponseData({
                    directives: {
                        notification: {
                            message: NOTIFICATIONS.genericError,
                        },
                    },
                }, false));

                return;
            }
        }


        if (!activeUser?.accountId || ([
            userInvitationActions.invite,
            userInvitationActions.edit,
        ].includes(action)) && !userRoles?.[collaboratorData?.role]?.isAssignable) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        const isNewCollaboratorAllowed = await getIsNewCollaboratorAllowed(activeUser?.accountId as string);

        if (!isNewCollaboratorAllowed) {
            return res.send(getResponseData({
                directives: {
                    modal: {
                        type: NOTIFICATIONS.collaboratorPackageRequired,
                    },
                },
            }, false));
        }

        const [, accountData] = await dao.getCollectionDocumentData<AccountData>({
            id: activeUser?.accountId,
            collection: collections.accounts,
        });

        if (!accountData) {
            return sendErrorResponse(res);
        }

        const paths = getPaths({
            accountId: accountData.id,
            projectId: '',
        });
        const [, collaboratorDataFromDB] = await dao.getCollectionDocumentData<UserData>({
            id: collaboratorData?.email,
            collection: collections.users,
        });

        if (collaboratorDataFromDB?.accountId && collaboratorDataFromDB?.accountId !== activeUser?.accountId) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        if (action === userInvitationActions.invite) {
            if (collaboratorDataFromDB) {
                return res.send(getResponseData({
                    directives: {
                        notification: {
                            message: NOTIFICATIONS.accountExists,
                            data: {
                                email: collaboratorData?.email,
                            },
                        },
                    },
                }, false));
            }

            await dao.setCollectionDocumentData<UserData>({
                id: collaboratorData.email,
                collection: collections.users,
                data: {
                    accountId: activeUser.accountId,
                    email: collaboratorData.email,
                    invitationProcess: [
                        {
                            state: userStates.PENDING,
                            ts: Date.now(),
                        }],
                    invitedBy: activeUser?.email,
                    organisation: accountData.organisation,
                    preferredLanguage: collaboratorData.preferredLanguage,
                    role: collaboratorData.role,
                    state: userStates.PENDING,
                },
            });

            const collaboratorList: UserData[] = await getCollaboratorList(activeUser?.accountId);
            await logIntoDB(paths.collections.accountEvents, {
                code: EVENTS.ACCOUNT_NEW_COLLABORATOR_INVITED,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    email: collaboratorData?.email,
                    invitedBy: activeUser?.email,
                    role: collaboratorData?.role,
                },
            });
            await dao.insertCollectionData<EmailNotification<{
                organisation: AccountData['organisation'];
            }>>({
                collection: collections.notifications,
                data: {
                    accountId: accountData.id,
                    createdAt: Date.now(),
                    data: {
                        language: collaboratorData.preferredLanguage,
                        email: collaboratorData.email,
                        payload: {
                            organisation: accountData.organisation,
                        },
                    },
                    method: NOTIFICATION_METHODS.EMAIL,
                    sendAt: Date.now(),
                    type: NOTIFICATION_TYPES.USER_UPDATES.type,
                    subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.IS_INVITED_AS_COLLABORATOR,
                },
            });

            return res.send(getResponseData({collaboratorList}, true));
        }

        if (!collaboratorDataFromDB) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        if (action === userInvitationActions.reinvite) {
            await dao.insertCollectionData<EmailNotification<{
                organisation: AccountData['organisation'];
            }>>({
                collection: collections.notifications,
                data: {
                    accountId: accountData.id,
                    createdAt: Date.now(),
                    data: {
                        email: collaboratorDataFromDB.email,
                        language: collaboratorDataFromDB.preferredLanguage,
                        payload: {
                            organisation: accountData!.organisation,
                        },
                    },
                    method: NOTIFICATION_METHODS.EMAIL,
                    sendAt: Date.now(),
                    type: NOTIFICATION_TYPES.USER_UPDATES.type,
                    subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.IS_INVITED_AS_COLLABORATOR,
                },
            });
            await logIntoDB(paths.collections.accountEvents, {
                code: EVENTS.ACCOUNT_NEW_COLLABORATOR_RE_INVITED,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    inviter: activeUser?.email,
                    email: collaboratorData?.email,
                    role: collaboratorData?.role,
                },
            });

            return res.send(getResponseData({}, true));
        }

        if (action === userInvitationActions.edit) {
            let email = collaboratorDataFromDB.email;
            let role = collaboratorDataFromDB.role;

            if (collaboratorData?.newEmail && email !== collaboratorData?.newEmail) {
                if (collaboratorDataFromDB.state !== userStates.PENDING) {
                    return res.send(getResponseData({
                        directives: {
                            notification: {
                                message: NOTIFICATIONS.cannotUpdateEmailAfterAcceptance,
                                config: {
                                    variant: INFO_CODE_LEVELS.ERROR,
                                },
                                data: {
                                    email: collaboratorDataFromDB.email,
                                },
                            },
                        },
                    }, false));
                }

                email = collaboratorData?.newEmail;

                const [, emailExists] = await dao.getDocumentExists({
                    path: `${collections.users}/${email}`,
                });

                if (emailExists) {
                    return res.send(getResponseData({
                        directives: {
                            notification: {
                                message: NOTIFICATIONS.accountExists,
                                data: {
                                    email,
                                },
                            },
                        },
                    }, false));
                }

                await dao.setCollectionDocumentData<Pick<UserData, 'email'>>({
                    id: email,
                    collection: collections.users,
                    data: {
                        email,
                    },
                });

                await dao.deleteCollectionData({
                    id: collaboratorDataFromDB.email,
                    collection: collections.users,
                });
            }

            if (userRoles?.[collaboratorData?.newRole]?.isAssignable && role !== collaboratorData?.newRole) {
                role = collaboratorData?.newRole;

                await dao.setCollectionDocumentData<Pick<UserData, 'role'>>({
                    id: email,
                    collection: collections.users,
                    data: {
                        role,
                    },
                });
            }

            await logIntoDB(paths.collections.accountEvents, {
                code: EVENTS.ACCOUNT_COLLABORATOR_EDITED,
                level: INFO_CODE_LEVELS.INFO,
                timestamp: Date.now(),
                data: {
                    role,
                    email,
                    editor: activeUser?.email,
                },
            });

            const collaboratorList: UserData[] = await getCollaboratorList(activeUser?.accountId);

            return res.send(getResponseData({
                collaboratorList,
            }, true));
        }

        if (action === userInvitationActions.delete) {
            await dao.deleteCollectionData({
                id: collaboratorDataFromDB.email,
                collection: collections.users,
            });
            const collaboratorList: UserData[] = await getCollaboratorList(activeUser?.accountId);

            await dao.insertCollectionData<EmailNotification<{
                organisation: AccountData['organisation'];
            }>>({
                collection: collections.notifications,
                data: {
                    accountId: accountData.id,
                    createdAt: Date.now(),
                    data: {
                        email: collaboratorDataFromDB.email,
                        language: collaboratorDataFromDB.preferredLanguage,
                        payload: {
                            organisation: accountData.organisation,
                        },
                    },
                    method: NOTIFICATION_METHODS.EMAIL,
                    sendAt: Date.now(),
                    type: NOTIFICATION_TYPES.USER_UPDATES.type,
                    subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.IS_REMOVED_AS_COLLABORATOR,
                },
            });

            return res.send(getResponseData({collaboratorList}, true));
        }
    } catch (e) {}

    return sendErrorResponse(res);
};
