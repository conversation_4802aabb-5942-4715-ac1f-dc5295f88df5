import type {Request, Response} from 'express';
import {
    projectBuildTimeoutHandling,
} from '@w7-3/webeagle-resources/dist/config/project';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {LANGUAGES} from '@w7-3/webeagle-resources/dist/config/languages';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            timeZone,
            timeoutHandling,
            preferredLanguage,
        } = req.body?.data || {};
        const {accountId} = await getActiveUserData(req) as UserData;

        if (isNonEmptyString(timeZone)) {
            const dateString = new Date().toLocaleString('en', {timeZone});

            const [error] = await dao.setCollectionDocumentData<Pick<AccountData, 'timeZone'>>({
                id: accountId,
                collection: collections.accounts,
                data: {
                    timeZone,
                },
            });

            return res.send(getResponseData({dateString}, !error));
        }

        if (isNonEmptyString(LANGUAGES[preferredLanguage])) {
            const [error] = await dao.setCollectionDocumentData<Pick<AccountData, 'preferredLanguage'>>({
                id: accountId,
                collection: collections.accounts,
                data: {
                    preferredLanguage,
                },
            });

            return res.send(getResponseData({
                preferredLanguage,
            }, !error));
        }

        if (Object.values(projectBuildTimeoutHandling).includes(timeoutHandling)) {
            const [error] = await dao.setCollectionDocumentData<Pick<AccountData, 'timeoutHandling'>>({
                id: accountId,
                collection: collections.accounts,
                data: {
                    timeoutHandling,
                },
            });

            return res.send(getResponseData({timeoutHandling}, !error));
        }
    } catch (e) {}

    res.send(getResponseData({
        directives: {
            notification: {
                message: NOTIFICATIONS.genericError,
                config: {
                    variant: INFO_CODE_LEVELS.ERROR,
                },
            },
        },
    }, false));
};
