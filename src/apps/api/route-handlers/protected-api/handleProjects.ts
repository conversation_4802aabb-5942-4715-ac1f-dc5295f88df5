import getActiveUserData from '../../../../utils/getActiveUserData';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import getProjects from '../../../../utils/getProjects';
import type {Request, Response} from 'express';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';

export default async (req: Request, res: Response) => {
    try {
        const {
            includeDetails,
            filterIdList,
            filterStateList = [
                projectStates.draft,
                projectStates.pause,
                projectStates.active,
                projectStates.pause,
            ],
        } = req.body?.data || {};
        const {accountId} = await getActiveUserData(req) as UserData;
        const {projects} = await getProjects({
            accountId,
            params: {
                includeDetails,
                filterIdList,
                filterStateList,
            },
        });

        res.send(getResponseData({projects}, true));
    } catch (error) {
        return sendErrorResponse(res);
    }
};
