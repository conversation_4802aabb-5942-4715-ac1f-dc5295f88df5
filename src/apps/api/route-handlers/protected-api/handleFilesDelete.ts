import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getResponseData} from '../../../../utils/response';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {UserData} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {FilesConfig} from '@w7-3/webeagle-resources/types/firebase';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {accountId} = await getActiveUserData(req) as UserData;
        const paths = getPaths({accountId});
        const {
            id,
        } = req.body?.data || {};
        const [, fileData] = await dao.getCollectionDocumentData<FilesConfig>({
            id,
            collection: paths.collections.files,
        });

        if (!fileData?.uploadData?.data?.destination) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        const [oldFileDeleteError] = await dao.deleteStorageFile({
            storageFilePath: fileData.uploadData.data.destination,
        });

        if (oldFileDeleteError) {
            return res.send(getResponseData({
                error: oldFileDeleteError,
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        const [fileDataDeleteError] = await dao.deleteCollectionData({
            id,
            collection: paths.collections.files,
        });

        if (fileDataDeleteError) {
            return res.send(getResponseData({
                error: fileDataDeleteError,
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        res.send(getResponseData({data: {
            id,
        }}, true));
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
