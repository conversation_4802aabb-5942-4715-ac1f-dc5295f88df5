import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {
    visualTestStates,
} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import {vendors} from '@w7-3/webeagle-resources/dist/config/project';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    isNonEmptyArray,
    isNonEmptyString,
    isPositiveInt,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import getActiveUserData from '../../../../utils/getActiveUserData';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import type {
    ProjectConfig,
    ProjectData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {Request, Response} from 'express';
import dbInstance from '../../../../setup/setupDB';
import type {
    ProjectBuildOverviewData, SolutionSummaryE2EVisualTests,
} from '@w7-3/webeagle-resources/types/solutions';

const dao = dbInstance.getDAO();

type PayloadItem = {
    id: string;
    currentImage: string;
};

export default async (req: Request, res: Response) => {
    try {
        const {accountId, email} = await getActiveUserData(req) as UserData;
        const {
            projectId,
            buildId,
            newState,
            items,
            reviewData,
        } = req.body?.data || {};

        if (!visualTestStates[newState]
            || !isNonEmptyArray(items)
            || !items.every((item: PayloadItem) => {
                return (
                    isNonEmptyString(item?.id)
                    && isNonEmptyString(item?.currentImage)
                );
            })
            || typeof reviewData?.reviewComments !== 'string'
            || !isPositiveInt(reviewData.confidence)
        ) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));

            return;
        }

        const paths = getPaths({
            accountId,
            projectId,
            buildId,
        });

        const [, projectData] = await dao.getDocumentData<ProjectData>({
            path: paths.collections.project,
        });

        if (buildId !== projectData?.latestBuild?.index) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));

            return;
        }

        if (projectData?.latestBuild?.state === BUILD_STATES.running) {
            const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
                collection: paths.collections.configs,
                id: projectData.configId,
            });
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.cannotUpdateProjectWhenBuilding,
                    },
                    data: {
                        projectName: projectConfig?.data?.projectName,
                    },
                },
            }, false));

            return;
        }

        const [, projectBuildData] = await dao.getDocumentData<ProjectBuildOverviewData>({
            path: paths.collections.projectBuild,
        });
        const results = (projectBuildData?.solutionSummary as SolutionSummaryE2EVisualTests)?.e2eVisualTests?.results;

        if (!results) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericErrorWithRetry,
                    },
                },
            }, false));

            return;
        }

        const visualDiffBaselines = projectData?.visualDiffBaselines || {};
        items.forEach((data: PayloadItem) => {
            visualDiffBaselines[data?.id] = visualDiffBaselines[data?.id] || {};
            visualDiffBaselines[data?.id][buildId] = {
                id: getRandomString(32, libraryKeys.alphaNumericLowerCased),
                buildId,
                ts: Date.now(),
                author: {
                    email,
                    vendor: vendors.manual,
                },
                data: {
                    id: data.id,
                    state: newState,
                    currentImage: data.currentImage,
                    reviewData,
                },
            };
        });

        await dao.setDocumentData({
            path: paths.collections.project,
            data: {
                visualDiffBaselines,
            },
        });

        res.send(getResponseData({
            projectId,
            newState,
            items,
            visualDiffBaselines,
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
