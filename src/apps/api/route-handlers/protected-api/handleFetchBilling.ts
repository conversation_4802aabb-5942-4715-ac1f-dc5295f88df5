import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getResponseData} from '../../../../utils/response';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {
    AutomationCreditData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {billingCycle} = req.body?.data || {};

        if (!isNonEmptyString(billingCycle)) {
            res.send(getResponseData({}, false));
            return;
        }

        const {accountId} = await getActiveUserData(req) as UserData;
        const paths = getPaths({accountId});
        const [, automationCreditData] = await dao.getCollectionDocumentData<AutomationCreditData>({
            id: billingCycle,
            collection: paths.collections.billingCycles,
        });

        res.send(getResponseData({data: {
            automationCreditData,
        }}, true));
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
