import {getConfig} from 'app-config';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    EVENTS,
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    isNonEmptyArray,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    accountStates,
} from '@w7-3/webeagle-resources/dist/config/account';
import {
    BILLING_ACTION,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {
    NOTIFICATION_METHODS,
    NOTIFICATION_TYPES,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {
    accountNotificationTypes,
} from '@w7-3/webeagle-resources/dist/config/account';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getStripe} from '../../../../utils/stripe';
import {processLineItem} from '../../../../utils/stripe/processPayment';
import {logFromAPI, logIntoDB} from '../../../../utils/logger';
import dbInstance from '../../../../setup/setupDB';
import {
    handleLifeTimeAutomationCredits,
} from '../../../../utils/handleOneTimeItems';

import type {Request, Response} from 'express';
import type {
    AccountData,
    InfoCode,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    CartItem,
    Checkout,
    CheckoutTransaction,
    Invoice,
    StripeCheckoutLineItem,
    SubscriptionItem,
} from '@w7-3/webeagle-resources/types/administration';
import type {
    AccountNotification,
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (request: Request, response: Response) => {
    const config = getConfig();
    try {
        let event;

        const {
            webhooks,
            checkout,
            charges,
        } = getStripe();
        try {
            event = webhooks.constructEvent(request.body, request.headers['stripe-signature'], config?.STRIPE?.webhook_key);
        } catch (e) {
            return response.status(400).send({
                message: `Webhook Error: ${(e as Error).message}`,
            });
        }

        let accountId: AccountData['id'] = '';
        let checkoutSessionId: string = '';
        let accountData: AccountData | null = null;
        let hasSubscription = false;
        if (event.type === 'checkout.session.completed' && event.data.object.payment_status === 'paid') {
            const sessionWithLineItems = await checkout.sessions.retrieve(
                event.data.object.id,
                {
                    expand: [
                        'invoice',
                        'line_items',
                        'payment_intent',
                        'subscription',
                    ],
                },
            );
            const email = sessionWithLineItems.customer_details.email;
            const [error, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
                collection: collections.accounts,
                condition: {
                    fieldPath: 'customerData.id',
                    opStr: '==',
                    value: sessionWithLineItems.customer,
                },
                limit: 1,
            });

            if (error || !accountList?.[0]) {
                return response.status(400).send({
                    message: NOTIFICATIONS.stripeError,
                    error: {
                        name: EVENTS.STRIPE_UNKNOWN_CUSTOMER,
                    },
                    data: {email},
                });
            }

            accountData = accountList[0];
            accountId = accountData.id;
            const paths = getPaths({accountId});
            let chargeItem;
            let invoiceUrl = sessionWithLineItems.payment_intent
                ? sessionWithLineItems.payment_intent?.charges?.data?.[0]?.receipt_url
                : sessionWithLineItems.invoice.hosted_invoice_url;

            if (sessionWithLineItems.payment_intent?.latest_charge) {
                chargeItem = await charges.retrieve(
                    sessionWithLineItems.payment_intent.latest_charge
                );
                invoiceUrl = chargeItem.receipt_url;
            }

            checkoutSessionId = sessionWithLineItems.id;
            await dao.setCollectionDocumentData<Checkout>({
                id: checkoutSessionId,
                collection: paths.collections.checkouts,
                data: {
                    billingCycle: accountData.administration.billing.cycle,
                    created: sessionWithLineItems.created * 1000,
                    currency: sessionWithLineItems.currency,
                    email,
                    id: sessionWithLineItems.id,
                    invoiceUrl,
                    locale: sessionWithLineItems.locale,
                    paymentStatus: sessionWithLineItems.payment_status,
                    price: sessionWithLineItems.amount_total,
                    status: sessionWithLineItems.status,
                    context: BILLING_ACTION.CHECKOUT,
                },
            });

            hasSubscription = sessionWithLineItems.line_items.data.some((checkoutLineItem: StripeCheckoutLineItem) => {
                return checkoutLineItem.price!.type === 'recurring';
            });

            if (hasSubscription) {
                await logIntoDB(paths.collections.accountEvents, {
                    code: EVENTS.ACCOUNT_NEW_SUBSCRIPTION,
                    level: INFO_CODE_LEVELS.INFO,
                    timestamp: Date.now(),
                    data: {
                        checkoutSessionId,
                    },
                });
            }

            await Promise.all(
                sessionWithLineItems.line_items.data.map(async (checkoutLineItem: StripeCheckoutLineItem) => {
                    await processLineItem({
                        checkoutData: sessionWithLineItems,
                        accountId,
                        chargeItem,
                        checkoutLineItem,
                    });
                }));
        }

        if (event.type === 'customer.subscription.updated') {
            const {
                customer,
                cancel_at_period_end: isCancelled,
                current_period_end,
                items,
            } = event.data.object;
            const [error, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
                collection: collections.accounts,
                condition: {
                    fieldPath: 'customerData.id',
                    opStr: '==',
                    value: customer,
                },
                limit: 1,
            });

            if (error || !accountList?.[0]) {
                await logFromAPI({
                    code: EVENTS.STRIPE_UNKNOWN_CUSTOMER,
                    level: INFO_CODE_LEVELS.ERROR,
                    timestamp: Date.now(),
                    data: {
                        event: event.id,
                    },
                });

                return response.status(400).send({
                    message: NOTIFICATIONS.stripeError,
                    error: {
                        name: EVENTS.STRIPE_UNKNOWN_CUSTOMER,
                    },
                    data: {customer},
                });
            }

            accountData = accountList[0];
            accountId = accountData.id;

            const paths = getPaths({accountId});
            const infoCodes: InfoCode[] = [];

            await Promise.all(items.data.map(async (lineItem: SubscriptionItem) => {
                const [, transactionListBySubscription] = await dao.getAllCollectionDataAsList<CheckoutTransaction<true>>({
                    collection: paths.collections.subscriptions,
                    condition: {
                        fieldPath: 'data.subscriptionId',
                        opStr: '==',
                        value: lineItem.subscription,
                    },
                });

                const subscriptions = transactionListBySubscription?.filter((transaction) => {
                    return transaction.priceId === lineItem.plan.id && transaction.product === lineItem.plan.product;
                });

                if (!isNonEmptyArray(subscriptions)) {
                    infoCodes.push({
                        code: EVENTS.STRIPE_UNKNOWN_SUBSCRIPTION,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                        data: {
                            event: event.id,
                            subscription: lineItem.subscription,
                            priceId: lineItem.plan.id,
                            product: lineItem.plan.product,
                        },
                    });
                }

                await Promise.all(subscriptions.map(async (subscription) => {
                    const periodEnd = current_period_end * 1000;
                    const active = lineItem.plan.active;
                    const updateRequired = subscription.data.isCancelled !== isCancelled || subscription.active !== active || subscription.data.periodEnd !== periodEnd;

                    if (!updateRequired) {
                        return;
                    }

                    await dao.setCollectionDocumentData<{
                        data: {
                            isCancelled: CheckoutTransaction<true>['data']['isCancelled'];
                            periodEnd: CheckoutTransaction<true>['data']['periodEnd'];
                        },
                    }>({
                        id: subscription.id,
                        collection: paths.collections.subscriptions,
                        data: {
                            data: {
                                isCancelled,
                                periodEnd,
                            },
                        },
                    });
                }));
            }));

            await Promise.all(infoCodes.map(async (infoCode) => {
                await logFromAPI(infoCode);
            }));

            if (infoCodes.length > 0) {
                return response.status(400).send({
                    message: NOTIFICATIONS.stripeError,
                    error: {
                        name: EVENTS.STRIPE_UNKNOWN_SUBSCRIPTION,
                    },
                });
            }
        }

        if (isNonEmptyString(event?.data?.object?.customer) && [
            'invoice.payment_failed',
            'invoice.payment_succeeded',
        ].includes(event.type)) {
            const invoice = event.data.object;
            const [, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
                collection: collections.accounts,
                condition: {
                    fieldPath: 'customerData.id',
                    opStr: '==',
                    value: invoice.customer,
                },
                limit: 1,
            });

            accountData = accountList?.[0];

            if (!accountData) {
                return response.status(400).send({
                    message: NOTIFICATIONS.stripeError,
                    error: {
                        name: EVENTS.STRIPE_UNKNOWN_CUSTOMER,
                    },
                    data: {customer: invoice.customer},
                });
            }

            accountId = accountData.id;

            const paths = getPaths({
                accountId,
            });
            const [, checkoutData] = await dao.getCollectionDocumentData<Checkout>({
                id: invoice.id,
                collection: paths.collections.checkouts,
            });

            if (!checkoutData) {
                return response.status(200).send('Ok');
            }

            await dao.setCollectionDocumentData<Pick<Checkout, 'paymentStatus' | 'status'>>({
                id: checkoutData.id,
                collection: paths.collections.checkouts,
                data: {
                    paymentStatus: invoice.status!,
                    status: invoice.status!,
                },
            });

            const {
                accountNotification,
            } = {
                ['invoice.payment_succeeded']: {
                    accountNotification: {
                        code: EVENTS.AUTOMATION_CREDITS_AUTO_RENEW_SUCCESS,
                        level: INFO_CODE_LEVELS.INFO,
                    },
                },
                ['invoice.payment_failed']: {
                    accountNotification: {
                        code: EVENTS.AUTOMATION_CREDITS_AUTO_RENEW_FAILED,
                        level: INFO_CODE_LEVELS.ERROR,
                    },
                },
            }[event.type];

            const notificationId = getRandomString(32, libraryKeys.alphaNumericLowerCased);
            await dao.setCollectionDocumentData<AccountNotification>({
                id: notificationId,
                collection: paths.collections.accountNotifications,
                data: {
                    id: notificationId,
                    type: accountNotificationTypes.custom,
                    ts: Date.now(),
                    isNew: true,
                    infoCodes: [{
                        code: accountNotification.code,
                        level: accountNotification.level,
                        timestamp: Date.now(),
                        data: {
                            invoiceId: invoice.id,
                        },
                    }],
                },
            });

            if (event.type === 'invoice.payment_succeeded') {
                const itemList = invoice.lines.data.map((lineItem) => {
                    return {
                        priceId: lineItem.price.id,
                        quantity: lineItem.quantity as number,
                    };
                });
                await Promise.all(itemList.map(async (item: CartItem) => {
                    await handleLifeTimeAutomationCredits({
                        accountId,
                        checkoutId: invoice.id,
                        email: invoice.customer_email,
                        item,
                        loginSessionId: checkoutData.id,
                        source: checkoutData.id,
                    });
                }));
            }

            if (event.type === 'invoice.payment_failed') {
                await dao.insertCollectionData<EmailNotification<{
                    invoiceId?: Invoice['id'];
                    checkoutId?: Checkout['id'];
                }>>({
                    collection: collections.notifications,
                    data: {
                        accountId,
                        createdAt: Date.now(),
                        data: {
                            email: accountData.accountHolder,
                            language: accountData.preferredLanguage,
                            payload: {
                                invoiceId: invoice.id,
                                checkoutId: checkoutData.id,
                            },
                        },
                        method: NOTIFICATION_METHODS.EMAIL,
                        sendAt: Date.now(),
                        type: NOTIFICATION_TYPES.PAYMENT_UPDATES.type,
                        subType: NOTIFICATION_TYPES.PAYMENT_UPDATES.subTypes.PAYMENT_FAILED,
                    },
                });
            }
        }

        const checkoutSession = accountData?.checkoutPaymentIntents?.[checkoutSessionId];
        if (checkoutSession) {
            const autoRenewableItems = isNonEmptyArray(checkoutSession?.itemList) ? checkoutSession.itemList.filter((item: CartItem): boolean => {
                return item.autoRenew === true;
            }) : [];

            await dao.setCollectionDocumentData<{
                state: AccountData['state'],
                customerData?: {
                    automationCredits: {
                        items: unknown;
                        autoRenew: NonNullable<AccountData['customerData']['automationCredits']>['autoRenew'];
                    },
                },
                checkoutPaymentIntents: {
                    [checkoutSessionId: string]: unknown,
                },
            }>({
                id: accountId,
                collection: collections.accounts,
                data: {
                    state: accountStates.active,
                    checkoutPaymentIntents: {
                        [checkoutSessionId]: dao.FieldValue.delete(),
                    },
                    ...isNonEmptyArray(autoRenewableItems) && {
                        customerData: {
                            automationCredits: {
                                items: dao.FieldValue.arrayUnion(...autoRenewableItems),
                                autoRenew: true,
                            },
                        },
                    },
                },
            });
        }

        return response.status(200).send('Ok');
    } catch (e) {
        const error = e as Error;
        return response.status(400).send({
            message: NOTIFICATIONS.stripeError,
            error: {
                name: error.name,
                message: error.message,
            },
        });
    }
};
