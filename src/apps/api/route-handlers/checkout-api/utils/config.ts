import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import api from '@w7-3/webeagle-resources/dist/config/api';

export const apiWhiteListAllUsers = [
    `${api.prices}`,
];

export const apiWhiteListForUnverifiedUsers = [];

export const roleGuardsForAuthenticatedUsers = {
    [`${api.customerPortal}`]: [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
    ],
};
