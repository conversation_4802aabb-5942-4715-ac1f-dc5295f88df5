import type {Router} from 'express';
import * as express from 'express';
import api from '@w7-3/webeagle-resources/dist/config/api';
import checkoutInit from './checkoutInit';
import customerPortal from './customerPortal';
import paymentConfirmation from './paymentConfirmation';
import handlePrices from './handlePrices';
import handleAutomatedCreditPurchase from './handleAutomatedCreditPurchase';
import checkoutCancel from './checkoutCancel';
import {
    apiWhiteListAllUsers,
    apiWhiteListForUnverifiedUsers,
    roleGuardsForAuthenticatedUsers,
} from './utils/config';
import handleError from '../../utils/handleError';
import getAccessValidator from '../../utils/getAccessValidator';

const app: Router = express.Router();

app.use(handleError);

app.all('*', getAccessValidator({
    apiWhiteListAllUsers,
    apiWhiteListForUnverifiedUsers,
    roleGuardsForAuthenticatedUsers,
}));

app.post(api.checkoutCancel, checkoutCancel);

app.post(api.automatedCreditPurchase, handleAutomatedCreditPurchase);

app.post(api.checkoutInit, checkoutInit);

app.post(api.customerPortal, customerPortal);

app.post(api.paymentConfirmation, paymentConfirmation);

app.post(api.prices, handlePrices);

export default app;
