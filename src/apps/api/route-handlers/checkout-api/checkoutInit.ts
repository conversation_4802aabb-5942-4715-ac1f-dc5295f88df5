import type {Request, Response} from 'express';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    isNonEmptyArray,
    isNonEmptyString,
    isNumber,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {getStripe} from '../../../../utils/stripe';
import getCheckoutMode from '../../../../utils/getCheckoutMode';
import dbInstance from '../../../../setup/setupDB';
import getActiveUserData from '../../../../utils/getActiveUserData';
import logger from '../../../../utils/logger';
import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {CartItem} from '@w7-3/webeagle-resources/types/administration';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const {
        cartItemList: rawCartItemList,
        successUrl,
        cancelUrl,
        locale,
    } = req.body?.data || {};
    try {
        const {
            accountId,
            role,
        } = await getActiveUserData(req) as UserData;
        const [error, accountData] = await dao.getCollectionDocumentData<AccountData>({
            id: accountId,
            collection: collections.accounts,
        });

        if (error || !accountData) {
            res.send(getResponseData({
                error,
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                        config: {
                            variant: INFO_CODE_LEVELS.ERROR,
                        },
                    },
                },
            }, false));

            return;
        }

        const {
            accountHolder,
            customerData,
        } = accountData;
        const itemList = rawCartItemList
            .filter(({priceId, quantity}: CartItem) => {
                return isNonEmptyString(priceId) && isNumber(quantity);
            });

        if (itemList.length < 1) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                        config: {
                            variant: INFO_CODE_LEVELS.ERROR,
                        },
                    },
                },
            }, false));

            return;
        }

        const hasRights = [
            userRoles.ACCOUNT_HOLDER.key,
            userRoles.ADMIN.key,
            // @ts-expect-error type is ok
        ].includes(role);

        if (!hasRights) {
            res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.noRights,
                        config: {
                            variant: INFO_CODE_LEVELS.ERROR,
                        },
                        data: {
                            email: accountHolder,
                        },
                    },
                },
            }, false));

            return;
        }

        const mode = getCheckoutMode(itemList);
        const autoRenewableItems = itemList.filter(({autoRenew}) => autoRenew);
        const {checkout} = getStripe();
        const session = await checkout.sessions.create({
            customer: customerData.id,
            mode,
            line_items: itemList.map(({priceId, quantity}) => ({
                price: priceId,
                quantity,
            })),
            success_url: successUrl,
            cancel_url: cancelUrl,
            locale,
            phone_number_collection: {
                enabled: true,
            },
            ...isNonEmptyArray(autoRenewableItems) && mode === 'payment' && {
                payment_intent_data: {
                    setup_future_usage: 'off_session',
                },
            },
        });
        await dao.setCollectionDocumentData<Pick<AccountData, 'checkoutPaymentIntents'>>({
            id: accountId,
            collection: collections.accounts,
            data: {
                checkoutPaymentIntents: {
                    [session.id]: {
                        accountHolder,
                        id: session.id,
                        itemList,
                    },
                },
            },
        });

        res.send(getResponseData({
            url: session.url,
        }, true));
    } catch (error) {
        logger.error('Error in initCheckout', {
            error,
            rawCartItemList,
        });
        return sendErrorResponse(res);
    }
};
