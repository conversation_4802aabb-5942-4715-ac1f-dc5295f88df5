import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import {
    rawProducts,
    rawPrices,
} from '../../../../prices/inventory';
import type {Request, Response} from 'express';

export default async (_: Request, res: Response) => {
    try {
        const prices: Record<string, unknown> = {};
        const products: Record<string, unknown> = {};

        rawProducts.data.forEach(({
            id,
            name,
            active,
        }) => {
            if (!active) {
                return;
            }

            products[id] = {
                id,
                name,
            };
        });

        rawPrices.data.forEach(({
            id,
            product,
            currency,
            unit_amount,
            type,
            active,
        }: {
            id: string;
            product: string;
            currency: string;
            unit_amount: number;
            type: string;
            active: boolean;
        }) => {
            if (!active || !products[product]) {
                return;
            }

            prices[id] = {
                id,
                product,
                currency,
                unit_amount,
                type,
            };
        });

        res.send(getResponseData({
            prices,
            products,
        }, true));
    } catch (e) {
        return sendErrorResponse(res);
    }
};
