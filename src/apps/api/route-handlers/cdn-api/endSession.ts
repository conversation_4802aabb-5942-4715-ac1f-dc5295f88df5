import {getConfig} from 'app-config';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const [error, loginData] = await dao.getLoginData({
        idToken: String(req.headers.token),
    });

    if (error || !loginData) {
        res.sendStatus(403);

        return;
    }

    const [error2] = await dao.logout({
        loginData,
    });

    if (error2) {
        res.sendStatus(403);

        return;
    }

    const {SESSION_COOKIE_NAME} = getConfig();

    res.clearCookie(String(SESSION_COOKIE_NAME));
    res.send(getResponseData({}, true));
};
