import * as express from 'express';
import api from '@w7-3/webeagle-resources/dist/config/api';
import refreshSession from './refreshSession';
import endSession from './endSession';
import getAsset from './getAsset';
import handleError from '../../utils/handleError';
import type {Router, Request, Response} from 'express';

const app: Router = express.Router();

app.use(handleError);

app.post(api.refreshSession, refreshSession);

app.post(api.endSession, endSession);

app.get('*', getAsset);

app.all('*', async (_: Request, res: Response) => {
    res.sendStatus(403);
});

export default app;
