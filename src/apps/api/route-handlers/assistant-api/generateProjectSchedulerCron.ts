import {
    isNonEmptyString,
    isNonEmptyArray,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import projectSchedulerCron from '@w7-3/webeagle-resources/dist/webautomate/ai/instructions/generate-project-scheduler-cron.md';
import {getResponseData} from '../../../../utils/response';
import getAssistantResponseAsJSON from '../../../../utils/getAssistantResponseAsJSON';
import * as openai from '../../../../utils/assistants/openai';
// import * as deepseek from '../../../../utils/assistants/deepseek';
import type {Request, Response} from 'express';

const vendor = openai;

const standardError = {
    directives: {
        notification: {
            message: NOTIFICATIONS.aiServiceNotAvailable,
            config: {
                variant: INFO_CODE_LEVELS.ERROR,
            },
        },
    },
};

export default async (req: Request, res: Response) => {
    try {
        const {
            cronPrompt,
            language,
        } = req.body?.data || {};

        if (!isNonEmptyString(cronPrompt) || !isNonEmptyString(language)) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                        config: {
                            variant: INFO_CODE_LEVELS.ERROR,
                        },
                    },
                },
            }, false));
        }
        const completion = await vendor.getCompletion([
            {
                role: 'system',
                content: projectSchedulerCron,
            },
            {
                role: 'user',
                content: cronPrompt,
            },
        ]);

        const data = getAssistantResponseAsJSON<{
            success: boolean;
            cron: string;
            suggestions: Array<{text: string; cron: string}>;
            impossibleActions: Array<string>;
        }>(completion?.choices?.[0]?.message?.content);

        if (typeof data?.success !== 'boolean') {
            return res.send(getResponseData(standardError, false));
        }

        const suggestions: Array<string> = [];
        const cronSuggestions: Array<string> = [];

        if (isNonEmptyArray(data?.suggestions)) {
            data?.suggestions.map((item) => {
                if (isNonEmptyString(item?.text) && isNonEmptyString(item?.cron)) {
                    suggestions.push(item?.text);
                    cronSuggestions.push(item?.cron);
                }
            });
        }

        res.send(getResponseData({data: {
            ...data,
            suggestions,
            cronSuggestions,
        }}, data?.success === true));
    } catch (error) {
        res.send(getResponseData(standardError, false));
    }
};
