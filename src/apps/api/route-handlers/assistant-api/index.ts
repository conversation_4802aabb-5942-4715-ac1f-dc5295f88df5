import type {Router} from 'express';
import express from 'express';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import handleFAQ from './handleFAQ';
import validateJSCode from './validateJSCode';
import generateProjectSchedulerCron from './generateProjectSchedulerCron';
import validateSolutionCode from './validateSolutionCode';
import handleError from '../../utils/handleError';

const app: Router = express.Router();
const {
    assistantApi: {
        uris,
    },
} = serverlessAPI;

app.use(handleError);

app.post(uris.faq, handleFAQ);

app.post(uris.validateJSCode, validateJSCode);

app.post(uris.generateProjectSchedulerCron, generateProjectSchedulerCron);

app.post(uris.validateSolutionCode, validateSolutionCode);

export default app;
