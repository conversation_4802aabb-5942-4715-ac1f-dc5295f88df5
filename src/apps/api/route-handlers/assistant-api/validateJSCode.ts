import * as esprima from 'esprima';
import {getResponseData} from '../../../../utils/response';
import type {Request, Response} from 'express';

export default async (req: Request, res: Response) => {
    try {
        const {js} = req.body?.data || {};
        esprima.parseScript(js);

        res.send(getResponseData({}, true));
    } catch (e) {
        const error = e as Error;
        res.send(getResponseData({
            name: error.name,
            message: error.message,
        }, false));
    }
};
