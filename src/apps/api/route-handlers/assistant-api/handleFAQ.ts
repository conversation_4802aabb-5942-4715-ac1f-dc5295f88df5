import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getResponseData} from '../../../../utils/response';
import type {Request, Response} from 'express';
import {getAssistantMessages} from '../../../../utils/assistants/openai';

const assistantId = 'asst_7rx6JJUFCkOTAnFjvC7kAbDZ';

export default async (req: Request, res: Response) => {
    try {
        const {
            threadId,
            runId,
            prompt,
        } = req.body?.data || {};

        if (!isNonEmptyString(prompt)) {
            return res.send(getResponseData({
                directives: {
                    infoCodes: [{
                        code: NOTIFICATIONS.faqPromptMissing,
                        level: INFO_CODE_LEVELS.ERROR,
                        timestamp: Date.now(),
                    }],
                },
            }, false));
        }

        const {
            success,
            messages,
        } = await getAssistantMessages({
            assistantId,
            threadId,
            runId,
            content: [{
                type: 'text',
                text: prompt,
            }],
            attachments: [],
            sort: 'asc',
        });

        if (!success) {
            return res.send(getResponseData({}, false));
        }

        res.send(getResponseData({
            messages,
        }, true));
    } catch (e) {
        res.send(getResponseData({}, false));
    }
};
