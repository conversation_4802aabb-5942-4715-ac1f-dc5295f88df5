import {
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {getResponseData} from '../../../../utils/response';
import getAssistantResponseAsJSON
    from '../../../../utils/getAssistantResponseAsJSON';
import * as openai from '../../../../utils/assistants/openai';
// import * as deepseek from '../../../../utils/assistants/deepseek';
import solutionValidatorInstructions from '@w7-3/webeagle-resources/dist/webautomate/ai/instructions/solution-validator.md';
import type {Request, Response} from 'express';
import type {AIVerdict} from '@w7-3/webeagle-resources/types/solutions';

const standardError = {
    directives: {
        notification: {
            message: NOTIFICATIONS.aiServiceNotAvailable,
            config: {
                variant: INFO_CODE_LEVELS.ERROR,
            },
        },
    },
};

export default async (req: Request, res: Response) => {
    try {
        const {
            request,
            prompt,
            language,
        } = req.body?.data || {};

        if (!isNonEmptyString(request) || !isNonEmptyString(prompt) || !isNonEmptyString(language)) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                        config: {
                            variant: INFO_CODE_LEVELS.ERROR,
                        },
                    },
                },
            }, false));
        }

        const completion = await openai.getCompletion([
            {
                role: 'system',
                content: solutionValidatorInstructions,
            },
            {
                role: 'user',
                content: JSON.stringify({
                    request,
                    prompt,
                    language,
                }),
            },
        ]);

        const data = getAssistantResponseAsJSON<{
            success: boolean;
            verdict: AIVerdict;
            suggestions: Array<{text: string; cron: string}>;
            impossibleActions: Array<string>;
        }>(completion?.choices?.[0]?.message?.content);

        if (typeof data?.success !== 'boolean') {
            return res.send(getResponseData(standardError, false));
        }

        res.send(getResponseData({data}, data?.success === true && ['yes', 'unknown'].includes(data?.verdict)));
    } catch (error) {
        res.send(getResponseData(standardError, false));
    }
};
