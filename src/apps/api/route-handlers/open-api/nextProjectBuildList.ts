import getNextBuildListFromCron
    from '../../../../utils/getNextBuildListFromCron';
import {getResponseData, sendErrorResponse} from '../../../../utils/response';
import type {Request, Response} from 'express';

export default async (req: Request, res: Response) => {
    try {
        const {
            schedulerData,
            startDate,
            endDate,
            pageSize,
        } = req.body?.data || {};

        if (typeof pageSize === 'number' && pageSize > 0) {
            const nextBuildList = getNextBuildListFromCron({
                schedulerData,
                startDate,
                endDate,
                pageSize,
            });

            res.send(getResponseData({nextBuildList}, true));
            return;
        }
    } catch (e) {
        return sendErrorResponse(res);
    }

    return sendErrorResponse(res);
};
