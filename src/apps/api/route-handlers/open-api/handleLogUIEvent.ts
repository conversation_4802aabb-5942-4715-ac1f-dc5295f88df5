import {getResponseData} from '../../../../utils/response';
import {logUIEvent} from '../../../../utils/logger';
import type {Request, Response} from 'express';
import type {InfoCode} from '@w7-3/webeagle-resources/types/webautomate/global';

export default async (req: Request, res: Response) => {
    const {uiData} = req.body?.data as {
        uiData: {
            type: string,
            message: string,
            data: Record<string, any>,
        },
    } || {};
    await logUIEvent({
        code: uiData?.type,
        level: uiData?.type as InfoCode['level'],
        timestamp: Date.now(),
    }, uiData?.data);

    res.send(getResponseData({}, true));
};
