import {getConfig} from 'app-config';
import {setCookie} from '../../../../utils/cookies';
import {getResponseData} from '../../../../utils/response';
import logger from '../../../../utils/logger';
import getHasAccess from '../../utils/getHasAccess';
import type {Request, Response} from 'express';

export default async (req: Request, res: Response) => {
    const config = getConfig();
    const {ACCESS_CODE_KEY, ACCESS_CODE_VALUE} = config;
    let hasAccess = getHasAccess(req);

    if (!hasAccess) {
        hasAccess = req.body?.data?.code === ACCESS_CODE_VALUE;
    }
    try {
        if (hasAccess) {
            setCookie({
                res,
                name: ACCESS_CODE_KEY!,
                value: ACCESS_CODE_VALUE!,
            });
        }
    } catch (error) {
        hasAccess = true;
        logger.error('Cannot check API access', {error});
    }

    res.send(getResponseData({}, hasAccess));
};
