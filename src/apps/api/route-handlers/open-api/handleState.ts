import type {Request, Response} from 'express';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import getIsWebAutomateAdmin
    from '../../../../admin/utils/getIsWebAutomateAdmin';
import getBlacklistedDomains
    from '../../../../utils/getBlacklistedDomains';
import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';
import type {UIState} from '@w7-3/webeagle-resources/types/ui';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const [, loginData] = await dao.getLoginData({
        idToken: String(req.headers.token),
    }) as [Error, {email: string}];
    const [, userData] = await dao.getCollectionDocumentData<UserData>({
        id: loginData?.email,
        collection: collections.users,
    });
    const data: UIState = {
        accountId: userData?.accountId,
    };

    if (userData?.accountId) {
        const [error, accountData] = await dao.getCollectionDocumentData<AccountData>({
            id: userData.accountId,
            collection: collections.accounts,
        });

        if (error || !accountData) {
            res.send(getResponseData({
                error,
                message: `The account with id: ${userData.accountId} cannot be found.`,
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
            return;
        }

        const {
            state,
            created,
            customerData,
            timeZone,
            demoRequest,
            accountHolder,
            blacklistedDomains,
            domainVerifications,
            domainBuilds,
            timeoutHandling,
            organisation,
            preferredLanguage,
            administration,
        } = accountData;

        data.accountAudit = {
            next: administration.audit.current + administration.audit.period,
            active: administration.audit.active,
        };
        data.accountHolder = accountHolder;
        data.accountState = state;
        data.customerData = customerData;
        data.organisation = organisation;
        data.preferredLanguage = preferredLanguage;
        data.billingCycle = administration.billing.cycle;
        data.billingHistory = administration.billing.history;
        data.ts = Date.now();

        if ([
            userRoles.ACCOUNT_HOLDER.key,
            userRoles.ADMIN.key,
            userRoles.EDITOR.key,
            // @ts-expect-error type is ok
        ].includes(userData.role)) {
            data.accountCreated = created;
            data.timeZone = timeZone;
            data.timeoutHandling = timeoutHandling;
            data.collaboratorSeats = accountData.lifeTimeCollaboratorSeats.value;
            data.automationCredits = accountData.lifeTimeAutomationCredits.value;
            data.automationCreditsExpiry = accountData.lifeTimeAutomationCredits.expiry;

            const {
                data: {
                    globallyBlacklistedDomainList,
                },
            } = await getBlacklistedDomains({accountData});

            data.globallyBlacklistedDomainList = globallyBlacklistedDomainList;
            data.domainVerifications = domainVerifications;
            data.blacklistedDomains = blacklistedDomains;
            data.domainBuilds = domainBuilds;

            if (demoRequest) {
                data.demoRequest = demoRequest;
            }
        }

        const [, projectBuildQueue] = await dao.getAllCollectionDataAsList<ProjectBuildQueueItem>({
            collection: collections.buildQueue,
            condition: {
                fieldPath: 'accountId',
                opStr: operators.EQUAL,
                value: accountData.id,
            },
            limit: 1,
        });

        data.projectBuildQueue = projectBuildQueue;

        if (getIsWebAutomateAdmin(userData)) {
            data.isAlpha = true;
        }
    }

    res.send(getResponseData({data}, true));
};
