import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {
    INFO_CODE_LEVELS,
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    requestOptions,
    requestStates,
} from '@w7-3/webeagle-resources/dist/config/contactFormRequests';
import {
    isNonEmptyArray,
    isNonEmptyString,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {accountStates} from '@w7-3/webeagle-resources/dist/config/account';
import {
    NOTIFICATION_METHODS,
    NOTIFICATION_TYPES,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import {getResponseData} from '../../../../utils/response';
import dbInstance from '../../../../setup/setupDB';
import type {Request, Response} from 'express';
import type {
    AccountData,
    UserData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {SupportRequest} from '@w7-3/webeagle-resources/types/ui';
import type {
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    const data = req.body?.data || {};
    const request = requestOptions?.[data?.request];

    if (!request || !data?.message || !data?.email) {
        return res.send(getResponseData({
            directives: {
                notification: {
                    message: NOTIFICATIONS.genericError,
                },
            },
        }, false));
    }

    try {
        const [, loginData] = await dao.getLoginData({
            idToken: String(req.headers.token),
        });

        if (request?.isLoginRequired && !loginData?.email) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.loginRequired,
                        config: {
                            variant: INFO_CODE_LEVELS.WARNING,
                        },
                    },
                },
            }, false));
        }

        const [dbAccessError, requestDataList] = await dao.getAllCollectionDataAsList<SupportRequest>({
            collection: `${collections.admin}/${collections.requests}/${request.key}`,
            condition: {
                fieldPath: 'data.email',
                opStr: '==',
                value: data.email,
            },
            limit: 1,
        });

        if (dbAccessError || requestDataList?.[0]?.data?.request === request.key || isNonEmptyArray(requestDataList)) {
            if (request === requestOptions.demo.key) {
                return res.send(getResponseData({
                    directives: {
                        notification: {
                            message: NOTIFICATIONS.oneDemoRequestPolicy,
                        },
                    },
                }, false));
            }

            return res.send(getResponseData({
                error: dbAccessError,
                directives: {
                    notification: {
                        message: NOTIFICATIONS.oneGenericRequestPolicy,
                        data: {
                            requestOption: request.key,
                        },
                    },
                },
            }, false));
        }

        let userData: UserData | null = null;
        const id = getRandomString(32, libraryKeys.alphaNumericLowerCased);

        if (request?.isLoginRequired) {
            [, userData] = await dao.getCollectionDocumentData<UserData>({
                id: loginData!.email,
                collection: collections.users,
            });

            if (userData) {
                const [, accountData] = await dao.getCollectionDocumentData<AccountData>({
                    id: userData!.accountId,
                    collection: collections.accounts,
                });

                if (data.request === requestOptions.demo.key) {
                    if (isNonEmptyString(accountData?.demoRequest?.state)) {
                        return res.send(getResponseData({
                            directives: {
                                notification: {
                                    message: NOTIFICATIONS.oneDemoRequestPolicy,
                                },
                            },
                        }, false));
                    }

                    if (accountData?.state !== accountStates.new) {
                        return res.send(getResponseData({
                            directives: {
                                notification: {
                                    message: NOTIFICATIONS.genericError,
                                },
                            },
                        }, false));
                    }

                    await dao.setCollectionDocumentData<{
                        demoRequest: Partial<AccountData['demoRequest']>;
                    }>({
                        id: userData?.accountId,
                        collection: collections.accounts,
                        data: {
                            demoRequest: {
                                applicant: userData?.email,
                                id,
                                state: requestOptions.demo.states.PENDING,
                                ts: Date.now(),
                            },
                        },
                    });
                }
            }
        }

        const language = userData?.preferredLanguage || data.language;
        const [error] = await dao.setCollectionDocumentData<SupportRequest>({
            id,
            collection: `${collections.admin}/${collections.requests}/${request.key}`,
            data: {
                data,
                id,
                language,
                state: requestStates.PENDING,
                ts: Date.now(),
                user: userData ? {
                    accountId: userData?.accountId,
                    email: userData?.email,
                } : null,
            },
        });

        if (error) {
            return res.send(getResponseData({
                error,
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        const {
            subType,
            payload,
        } = data.request === requestOptions.demo.key ? {
            subType: NOTIFICATION_TYPES.REQUEST_UPDATES.subTypes.DEMO_REQUEST_RECEIVED,
            payload: {},
        } : {
            subType: NOTIFICATION_TYPES.REQUEST_UPDATES.subTypes.GENERIC_REQUEST_RECEIVED,
            payload: data,
        };
        await dao.insertCollectionData<EmailNotification<{} | SupportRequest['data']>>({
            collection: collections.notifications,
            data: {
                accountId: userData?.accountId as string,
                createdAt: Date.now(),
                data: {
                    language,
                    email: data.email,
                    payload,
                },
                method: NOTIFICATION_METHODS.EMAIL,
                sendAt: Date.now(),
                type: NOTIFICATION_TYPES.REQUEST_UPDATES.type,
                subType,
            },
        });

        res.send(getResponseData({}, true));
    } catch (e) {
        res.send(getResponseData({
            directives: {
                notification: {
                    message: NOTIFICATIONS.genericError,
                },
            },
        }, false));
    }
};
