import * as express from 'express';
import api from '@w7-3/webeagle-resources/dist/config/api';
import deleteUser from './deleteUser';
import handleError from './utils/handleError';
import checkApiAccess from './checkApiAccess';
import nextProjectBuildList from './nextProjectBuildList';
import createUser from './createUser';
import handleState from './handleState';
import handleSendRequest from './handleSendRequest';
import handleLogUIEvent from './handleLogUIEvent';
import type {Router} from 'express';

const app: Router = express.Router();

app.use(handleError);

app.post(api.checkApiAccess, checkApiAccess);

app.post(api.nextProjectBuildList, nextProjectBuildList);

app.post(api.logUIEvent, handleLogUIEvent);

app.post(api.state, handleState);

app.post(api.deleteUser, deleteUser);

app.post(api.createUser, createUser);

app.post(api.sendRequest, handleSendRequest);

export default app;
