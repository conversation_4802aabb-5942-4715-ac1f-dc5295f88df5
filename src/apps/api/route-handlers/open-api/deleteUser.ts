import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {
    NOTIFICATION_TYPES,
    NOTIFICATION_METHODS,
} from '@w7-3/webeagle-resources/dist/config/notifications';
import dbInstance from '../../../../setup/setupDB';
import {getResponseData} from '../../../../utils/response';
import getActiveUserData from '../../../../utils/getActiveUserData';
import type {Request, Response} from 'express';
import type {
    EmailNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export default async (req: Request, res: Response) => {
    try {
        const {
            language,
            isOrphan,
        } = req.body?.data || {};
        const [, loginData] = await dao.getLoginData({
            idToken: String(req.headers.token),
        });

        if (!loginData?.email) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.genericError,
                    },
                },
            }, false));
        }

        const userData = isOrphan ? null : await getActiveUserData(req);

        if (userData) {
            return res.send(getResponseData({
                directives: {
                    notification: {
                        message: NOTIFICATIONS.authCannotBeDeleted,
                    },
                },
            }, false));
        }

        await dao.deleteCollectionData({
            id: loginData?.email,
            collection: collections.users,
        });

        await dao.insertCollectionData<EmailNotification>({
            collection: collections.notifications,
            data: {
                accountId: '',
                createdAt: Date.now(),
                data: {
                    language,
                    email: loginData.email,
                    payload: undefined,
                },
                method: NOTIFICATION_METHODS.EMAIL,
                sendAt: Date.now(),
                type: NOTIFICATION_TYPES.USER_UPDATES.type,
                subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.DELETED_SELF,
            },
        });

        return res.send(getResponseData({}, true));
    } catch (e) {}

    res.send(getResponseData({}, false));
};
