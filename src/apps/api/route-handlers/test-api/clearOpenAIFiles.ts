import {sendErrorResponse} from '../../../../utils/response';
import type {Request, Response} from 'express';
import {
    deleteAllFiles,
    deleteAllVectorStores,
} from '../../../../utils/assistants/openai/danger';

export default async (req: Request, res: Response) => {
    try {
        await deleteAllFiles();
        await deleteAllVectorStores();
    } catch (e) {
        return sendErrorResponse(res);
    }

    return sendErrorResponse(res);
};
