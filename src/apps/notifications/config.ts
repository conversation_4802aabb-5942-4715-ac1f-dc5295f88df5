import '@w7-3/webeagle-setup';
import {
    NOTIFICATION_TYPES,
} from '@w7-3/webeagle-resources/dist/config/notifications';

export const notificationConfig = {
    AUTOMATION_CREDITS_RESET: {
        id: 'AUTOMATION_CREDITS_RESET',
        active: true,
        type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
        subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.AUTOMATION_CREDITS_RESET,
    },
    AUTOMATION_CREDITS_RESET_PENDING: {
        id: 'AUTOMATION_CREDITS_RESET_PENDING',
        active: true,
        type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
        subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.AUTOMATION_CREDITS_RESET_PENDING,
    },
    BUILD_COMPLETED: {
        id: 'BUILD_COMPLETED',
        active: true,
        type: NOTIFICATION_TYPES.PROJECT_BUILD_UPDATES.type,
        subType: NOTIFICATION_TYPES.PROJECT_BUILD_UPDATES.subTypes.BUILD_COMPLETED,
    },
    BUILD_SKIPPED_DUE_TO_OVERAGE: {
        id: 'BUILD_SKIPPED_DUE_TO_OVERAGE',
        active: true,
        type: NOTIFICATION_TYPES.PROJECT_BUILD_UPDATES.type,
        subType: NOTIFICATION_TYPES.PROJECT_BUILD_UPDATES.subTypes.BUILD_SKIPPED_DUE_TO_OVERAGE,
    },
    DELETED_SELF: {
        id: 'DELETED_SELF',
        active: true,
        type: NOTIFICATION_TYPES.USER_UPDATES.type,
        subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.DELETED_SELF,
    },
    DEMO_REQUEST_APPROVED: {
        id: 'DEMO_REQUEST_APPROVED',
        active: true,
        type: NOTIFICATION_TYPES.REQUEST_UPDATES.type,
        subType: NOTIFICATION_TYPES.REQUEST_UPDATES.subTypes.DEMO_REQUEST_APPROVED,
    },
    DEMO_REQUEST_DECLINED: {
        id: 'DEMO_REQUEST_DECLINED',
        active: true,
        type: NOTIFICATION_TYPES.REQUEST_UPDATES.type,
        subType: NOTIFICATION_TYPES.REQUEST_UPDATES.subTypes.DEMO_REQUEST_DECLINED,
    },
    DEMO_REQUEST_RECEIVED: {
        id: 'DEMO_REQUEST_RECEIVED',
        active: true,
        type: NOTIFICATION_TYPES.REQUEST_UPDATES.type,
        subType: NOTIFICATION_TYPES.REQUEST_UPDATES.subTypes.DEMO_REQUEST_RECEIVED,
    },
    DOMAIN_BLACKLISTED: {
        id: 'DOMAIN_BLACKLISTED',
        active: true,
        type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
        subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.DOMAIN_BLACKLISTED,
    },
    DOMAIN_WHITELISTED: {
        id: 'DOMAIN_WHITELISTED',
        active: true,
        type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
        subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.DOMAIN_WHITELISTED,
    },
    GENERIC_REQUEST_RECEIVED: {
        id: 'GENERIC_REQUEST_RECEIVED',
        active: true,
        type: NOTIFICATION_TYPES.REQUEST_UPDATES.type,
        subType: NOTIFICATION_TYPES.REQUEST_UPDATES.subTypes.GENERIC_REQUEST_RECEIVED,
    },
    IS_INVITED_AS_COLLABORATOR: {
        id: 'IS_INVITED_AS_COLLABORATOR',
        active: true,
        type: NOTIFICATION_TYPES.USER_UPDATES.type,
        subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.IS_INVITED_AS_COLLABORATOR,
    },
    IS_REMOVED_AS_COLLABORATOR: {
        id: 'IS_REMOVED_AS_COLLABORATOR',
        active: true,
        type: NOTIFICATION_TYPES.USER_UPDATES.type,
        subType: NOTIFICATION_TYPES.USER_UPDATES.subTypes.IS_REMOVED_AS_COLLABORATOR,
    },
    PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST: {
        id: 'PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST',
        active: true,
        type: NOTIFICATION_TYPES.PROJECT_UPDATES.type,
        subType: NOTIFICATION_TYPES.PROJECT_UPDATES.subTypes.PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST,
    },
    REGISTRATION_COMPLETED: {
        id: 'REGISTRATION_COMPLETED',
        active: true,
        type: NOTIFICATION_TYPES.ACCOUNT_UPDATES.type,
        subType: NOTIFICATION_TYPES.ACCOUNT_UPDATES.subTypes.REGISTRATION_COMPLETED,
    },
    PAYMENT_FAILED: {
        id: 'PAYMENT_FAILED',
        active: true,
        type: NOTIFICATION_TYPES.PAYMENT_UPDATES.type,
        subType: NOTIFICATION_TYPES.PAYMENT_UPDATES.subTypes.PAYMENT_FAILED,
    },
} as const;
