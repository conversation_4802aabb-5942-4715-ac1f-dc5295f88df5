import {MailService} from '@sendgrid/mail';
import {getConfig} from 'app-config';
import translate from '@w7-3/webeagle-resources/dist/i18n/translate';
import {getI18nData, getStandardEmailSections} from './utils';
import {getRecipientEmail, getSenderEmail} from '../../../../admin/utils/getEmailAddress';
import type {BuildNotificationData} from '@w7-3/webeagle-resources/types/webautomate/global';

const templateId = 'd-f41958b26e794d519e2322dd9fde89f7';

export default async ({
    language,
    email,
    notificationData,
}: {
    language: string,
    email: string,
    notificationData: BuildNotificationData,
}): Promise<{
    success: boolean,
    error: Error | null,
}> => {
    const config = getConfig();
    const {
        SENDGRID,
    } = config;
    const dynamicTemplateData = getStandardEmailSections({
        language,
    });
    const emailData = translate(language, 'emails.projectBuildEmail', {
        ...getI18nData(),
        projectName: notificationData?.build?.projectName,
        outcome: translate(language, `project.build.states.${notificationData?.build?.state}.label`),
        buildUrl: notificationData?.build?.buildReference.link,
        nextExecution: notificationData?.build?.nextExecution,
    });

    if (notificationData?.build?.nextExecution) {
        notificationData.build.info.itemList = notificationData.build.info.itemList || [];
        notificationData.build.info.itemList.push(emailData.nextExecution);
    }

    dynamicTemplateData.build = {
        info: {
            itemList: notificationData.build.info.itemList,
        },
        linkCheckerData: {
            label: notificationData.build.linkCheckerData.label,
            itemList: [
                notificationData.build.linkCheckerData.items.total,
                notificationData.build.linkCheckerData.items.success,
                notificationData.build.linkCheckerData.items.failure,
            ],
        },
        solutionData: {
            label: notificationData.build.solutionData.label,
            description: notificationData.build.solutionData.description,
            itemList: [
                notificationData.build.solutionData.items.total,
                notificationData.build.solutionData.items.success,
                notificationData.build.solutionData.items.failure,
            ],
        },
    };
    dynamicTemplateData.subject = emailData.subject;
    dynamicTemplateData.html.body = emailData.body;

    try {
        const mailService = new MailService();
        mailService.setApiKey(SENDGRID.API_KEY);
        await mailService.send({
            to: getRecipientEmail({email}),
            from: getSenderEmail(),
            templateId,
            dynamicTemplateData,
        });

        return {
            success: true,
            error: null,
        };
    } catch (e) {
        return {
            success: false,
            error: e as Error,
        };
    }
};
