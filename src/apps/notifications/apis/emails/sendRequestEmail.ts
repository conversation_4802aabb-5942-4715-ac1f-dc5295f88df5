import {MailService} from '@sendgrid/mail';
import {getConfig} from 'app-config';
import translate from '@w7-3/webeagle-resources/dist/i18n/translate';
import {
    roleOptions,
    interestOptions,
} from '@w7-3/webeagle-resources/dist/config/contactFormRequests';
import type {SupportRequest} from '@w7-3/webeagle-resources/types/ui';
import {getI18nData, getStandardEmailSections} from './utils';
import {getRecipientEmail, getSenderEmail} from '../../../../admin/utils/getEmailAddress';

const templateId = 'd-f41958b26e794d519e2322dd9fde89f7';

export default async ({
    language,
    email,
    payload,
}: {
    language: string,
    email: string,
    payload: SupportRequest['data'],
}): Promise<{
    success: boolean,
    error: Error | null,
}> => {
    const config = getConfig();
    const {
        SENDGRID,
    } = config;
    const dynamicTemplateData = getStandardEmailSections({
        language,
    });
    const emailData = translate(language, 'emails.sendRequestEmail', {
        ...getI18nData(),
    });

    dynamicTemplateData.subject = emailData.subject;
    dynamicTemplateData.html.body = emailData.body;
    dynamicTemplateData.html.additionalInfos.label = emailData.additionalInfos.label;
    dynamicTemplateData.html.additionalInfos.itemList = [
        {
            label: translate(language, 'form.request'),
            value: payload.request,
        },
        {
            label: translate(language, 'form.name'),
            value: payload.name,
        },
        {
            label: translate(language, 'form.lastName'),
            value: payload.lastName,
        },
        {
            label: translate(language, 'form.company'),
            value: payload.company,
        },
        {
            label: translate(language, 'form.website'),
            value: payload.website,
        },
        {
            label: translate(language, 'form.role'),
            value: roleOptions?.[payload.role]?.value,
        },
        {
            label: translate(language, 'form.interests'),
            value: payload.interests.map((item) => interestOptions[item]?.value).join(', '),
        },
        {
            label: translate(language, 'form.message'),
            value: payload.message,
        },
    ];

    try {
        const mailService = new MailService();
        mailService.setApiKey(SENDGRID.API_KEY);
        await mailService.send({
            to: getRecipientEmail({email}),
            from: getSenderEmail(),
            templateId,
            dynamicTemplateData,
        });

        return {
            success: true,
            error: null,
        };
    } catch (e) {
        return {
            success: false,
            error: e as Error,
        };
    }
};
