import {MailService} from '@sendgrid/mail';
import {getConfig} from 'app-config';
import translate from '@w7-3/webeagle-resources/dist/i18n/translate';
import {getI18nData, getStandardEmailSections} from './utils';
import {getRecipientEmail, getSenderEmail} from '../../../../admin/utils/getEmailAddress';

const templateId = 'd-f41958b26e794d519e2322dd9fde89f7';

export default async ({
    language,
    email,
    invoiceId,
}: {
    language: string,
    email: string,
    invoiceId: string,
}): Promise<{
    success: boolean,
    error: Error | null,
}> => {
    const config = getConfig();
    const {
        SENDGRID,
    } = config;
    const dynamicTemplateData = getStandardEmailSections({
        language,
    });
    const emailData = translate(language, 'emails.sendPaymentFailedEmail', {
        ...getI18nData({invoiceId}),
        invoiceId,
    });

    dynamicTemplateData.subject = emailData.subject;
    dynamicTemplateData.html.body = emailData.body;

    try {
        const mailService = new MailService();
        mailService.setApiKey(SENDGRID.API_KEY);
        await mailService.send({
            to: getRecipientEmail({email}),
            from: getSenderEmail(),
            templateId,
            dynamicTemplateData,
        });

        return {
            success: true,
            error: null,
        };
    } catch (e) {
        return {
            success: false,
            error: e as Error,
        };
    }
};
