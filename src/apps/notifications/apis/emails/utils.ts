import {getConfig} from 'app-config';
import translate from '@w7-3/webeagle-resources/dist/i18n/translate';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {PATH_PAGE, PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {getProjectURL} from '@w7-3/webeagle-resources/dist/libs/getURLs';
import {PATH_AUTH} from '@w7-3/webeagle-resources/dist/config/paths';
import {URL_PARAM_LANGUAGE} from '@w7-3/webeagle-resources/dist/config/languages';
import type {
    SolutionDataItem,
} from '@w7-3/webeagle-resources/types/webautomate/global';

export type StandardEmailSectionsData = {
    subject: string,
    html: {
        salutation: string,
        body: SolutionDataItem[],
        additionalInfos: {
            label: string,
            itemList: SolutionDataItem[],
        },
        closing: SolutionDataItem[],
        footer: {
            address: string,
            visitUs: string,
            url: string,
            domain: string,
            socials: {
                youtube: string,
                x: string,
                whatsapp: string,
            },
        },
    },
    build?: {
        info: {
            label?: string,
            itemList: SolutionDataItem[],
        },
        linkCheckerData?: {
            label: string,
            itemList: SolutionDataItem[],
        },
        solutionData?: {
            label: string,
            description: string,
            itemList: SolutionDataItem[],
        },
    },
};

export const getI18nData = (data: {
    email?: string,
    invoiceId?: string,
    language?: string,
    organisation?: string,
    projectId?: string,
} = {}) => {
    const config = getConfig();
    const {
        WEBSITE,
    } = config;

    return {
        returnObjects: true,
        interpolation: {
            escapeValue: false,
        },
        app: config.APP_NAME,
        url: config.WEBSITE,
        domain: config.APP_NAME,
        address: {
            street: config.COMPANY_STREET,
            city: config.COMPANY_CITY,
            state: config.COMPANY_STATE,
            country: config.COMPANY_COUNTRY,
        },
        routes: {
            accountDashboard: `${config.WEBSITE}${PATH_DASHBOARD.root}`,
            automationCredits: `${config.WEBSITE}${PATH_DASHBOARD.management.transactionList}?tab=automationCredits`,
            automationCreditsEdit: `${config.WEBSITE}${PATH_DASHBOARD.management.transactionList}?tab=automationCredits&edit-reload=1`,
            contactUs: `${config.WEBSITE}${PATH_PAGE.contact}`,
            createOrganisation: encodeURI(`${WEBSITE}${PATH_DASHBOARD.root}?${
                urlParameters.account.createOrg
            }=1`),
            documentation: `${config.WEBSITE}${PATH_PAGE.documentation}`,
            faqs: `${config.WEBSITE}${PATH_PAGE.documentation}`,
            features: `${config.WEBSITE}${PATH_PAGE.features}`,
            invoiceDetails: `${config.WEBSITE}${PATH_DASHBOARD.management.transactionList}?item=${data.invoiceId}`,
            projectSettingsLinkToDashboard: `${getProjectURL({
                websiteOrigin: config.WEBSITE!,
                projectId: data.projectId!,
            })}?${urlParameters.project.tab}=settings`,
            projectWizard: `${config.WEBSITE}${PATH_PAGE.projectWizard}`,
            shop: `${PATH_PAGE.pricing}?${urlParameters.pricing.tab}=otp`,
            termsAndConditions: `${config.WEBSITE}${PATH_PAGE.termsAndConditions}`,
            userInvitationLink: encodeURI(`${WEBSITE}${PATH_AUTH.register}?${
                urlParameters.account.organisation}=${data.organisation}&${
                urlParameters.account.guest}=${data.email}&${
                URL_PARAM_LANGUAGE}=${data.language}`),
        },
        /*
        socials: {
            youtube: config.COMPANY_SOCIALS.YOUTUBE,
            x: config.COMPANY_SOCIALS.X,
            whatsapp: config.COMPANY_SOCIALS.WHATSAPP,
        },
        */
    };
};

export const getStandardEmailSections = ({
    language,
}: {
    language: string,
}): StandardEmailSectionsData => {
    const html = translate(language, 'emails.standard', getI18nData());

    return {
        subject: translate(language, 'emails.standard.subject'),
        html: {
            salutation: html.salutation,
            body: html.body,
            additionalInfos: {
                label: html.additionalInfos?.label,
                itemList: html.additionalInfos.itemList || [],
            },
            closing: html.closing || [],
            footer: html.footer,
        },
    };
};
