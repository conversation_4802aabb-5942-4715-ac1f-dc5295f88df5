import {NOTIFICATION_TYPES} from '@w7-3/webeagle-resources/dist/config/notifications';
import getBuildNotificationData from '../data/webhooks/getBuildNotificationData';
import sendProjectBuildWebhook from '../apis/webhooks/sendProjectBuildWebhook';
import logger from '../../../utils/logger';
import type {
    ProjectBuildNotification,
    WebhookNotification,
} from '@w7-3/webeagle-resources/types/notifications';

export default async ({
    notification,
}: {
    notification: WebhookNotification<unknown>,
}): Promise<{
    success: boolean,
    error: Error | null,
}> => {
    const {
        type,
        subType,
    } = notification;
    const {
        PROJECT_BUILD_UPDATES,
    } = NOTIFICATION_TYPES;
    if (type === PROJECT_BUILD_UPDATES.type
        && subType === PROJECT_BUILD_UPDATES.subTypes.BUILD_COMPLETED) {
        const {
            data,
        } = notification as WebhookNotification<ProjectBuildNotification>;
        const notificationData = await getBuildNotificationData(data?.payload);

        return await sendProjectBuildWebhook({
            notification: notification as WebhookNotification<ProjectBuildNotification>,
            notificationData,
        });
    }

    logger.error('Unregistered notification type', {notification});

    return {success: false, error: null};
};
