import {getConfig} from 'app-config';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import translate from '@w7-3/webeagle-resources/dist/i18n/translate';
import date, {
    getDuration,
    humanReadableDateFormats,
} from '@w7-3/webeagle-resources/dist/libs/date';
import getNextExecution
    from '@w7-3/webeagle-resources/dist/libs/project-solution/getNextExecution';
import {
    getSummaryData,
} from '@w7-3/webeagle-resources/dist/libs/project-solution/summaries';
import {
    getProjectBuildURL,
    getProjectURL,
} from '@w7-3/webeagle-resources/dist/libs/getURLs';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import dbInstance from '../../../../setup/setupDB';
import getNextBuildListFromCron
    from '../../../../utils/getNextBuildListFromCron';
import type {
    AccountData,
    BuildNotificationData,
    ProjectConfig,
    ProjectData,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    ProjectBuildOverviewData,
} from '@w7-3/webeagle-resources/types/solutions';
import type {
    EmailNotification,
    ProjectBuildNotification,
} from '@w7-3/webeagle-resources/types/notifications';

const dao = dbInstance.getDAO();

export const getData = async ({
    buildNotification,
    language,
    projectData,
}: {
    buildNotification: ProjectBuildNotification;
    language: string;
    projectData: ProjectData;
}): Promise<BuildNotificationData> => {
    const app = getConfig().APP_NAME;
    const websiteOrigin = getConfig().WEBSITE as string;
    const {
        accountId,
        buildId,
        projectId,
    } = buildNotification;
    const paths = getPaths({
        accountId,
        projectId,
        buildId,
    });
    const tz = new Intl.DateTimeFormat().resolvedOptions().timeZone;
    const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
        collection: paths.collections.configs,
        id: projectData.configId,
    });
    const {
        summary: {
            schedulerData,
            notificationsData,
            schedulerEndDate,
        },
        projectName,
        solution: {
            appName,
        },
        target,
    } = projectConfig!.data;

    let previousBuildData;
    const [, requestData] = await dao.getCollectionDocumentData<ProjectBuildOverviewData>({
        collection: paths.collections.builds,
        id: `${buildId}`,
    });

    if (buildId > 1) {
        [, previousBuildData] = await dao.getCollectionDocumentData<ProjectBuildOverviewData>({
            collection: paths.collections.builds,
            id: `${buildId - 1}`,
        });
    }

    const solution = translate(language, `project.wizardSteps.solutionsConfigs.groups.${appName}.label`);
    const buildStart = date(requestData!.start).format(humanReadableDateFormats.full);
    const browser = translate(language, `project.wizardSteps.target.browser.groups.${target.browser.key}.label`);
    const subject = translate(language, 'project.build.report.title', {
        app,
        dateTime: buildStart,
        tz,
    });
    const linkCheckerData = getSummaryData({
        websiteOrigin,
        solutionKey: solutions.linkChecker.appName,
        language,
        timeZone: tz,
        projectBuildOverviewData: requestData,
        previousProjectBuildOverviewData: previousBuildData,
    });
    const solutionData = await getSummaryData({
        websiteOrigin,
        solutionKey: appName,
        language,
        timeZone: tz,
        projectBuildOverviewData: requestData,
        previousProjectBuildOverviewData: previousBuildData,
    });
    const [, accountData] = await dao.getCollectionDocumentData<AccountData>({
        id: accountId,
        collection: collections.accounts,
    }) as [Error, AccountData];
    const nextBuildList = getNextBuildListFromCron({
        schedulerData,
        startDate: Date.now(),
        endDate: schedulerEndDate,
        pageSize: 1,
    });

    return {
        email: notificationsData.email.main,
        subject,
        accountData,
        build: {
            info: {
                label: translate(language, 'project.build.report.buildInfo.label'),
                itemList: [
                    {
                        label: translate(language, 'project.name.label'),
                        value: projectName,
                        link: getProjectURL({
                            websiteOrigin,
                            projectId: buildNotification.projectId,
                        }),
                    },
                    {
                        label: translate(language, 'solution'),
                        value: solution,
                    },
                    {
                        label: translate(language, 'project.build.id'),
                        value: `#${buildId}`,
                        link: getProjectBuildURL({
                            websiteOrigin,
                            buildId: buildNotification.buildId,
                            projectId: buildNotification.projectId,
                        }),
                    },
                    {
                        label: translate(language, 'project.build.startedOn'),
                        value: `${buildStart} (${tz})`,
                    },
                    {
                        label: translate(language, 'project.build.duration'),
                        value: getDuration(requestData!.end - requestData!.start),
                    },
                    {
                        label: translate(language, 'project.wizardSteps.target.browser.label'),
                        value: `${target.device.viewport.width}x${target.device.viewport.height} - ${browser}`,
                    },
                ],
            },
            state: requestData!.state,
            projectName,
            linkCheckerData,
            solutionData,
            nextExecution: getNextExecution({language, nextBuildList, timeZone: tz}),
            buildReference: {
                label: translate(language, 'project.build.report.buildReference.label'),
                link: getProjectBuildURL({
                    websiteOrigin,
                    buildId: buildNotification.buildId,
                    projectId: buildNotification.projectId,
                }),
            },
        },
    };
};

export default async ({
    language,
    payload,
}: EmailNotification<ProjectBuildNotification>['data']): Promise<BuildNotificationData> => {
    const {
        accountId,
        buildId,
        projectId,
    } = payload;
    const paths = getPaths({
        accountId,
        projectId,
        buildId,
    });

    const [, projectData] = await dao.getCollectionDocumentData<ProjectData>({
        collection: paths.collections.projects,
        id: projectId,
    }) as [Error, ProjectData];

    return await getData({
        buildNotification: payload,
        language,
        projectData,
    });
};
