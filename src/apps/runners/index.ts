import '@w7-3/webeagle-setup';
import process from 'process';
import {getConfig} from 'app-config';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import logger from '../../utils/logger';
import Manager from '../../utils/Manager';
import dbInstance from '../../setup/setupDB';
import fetchProjectBuildQueueItem from './utils/fetchProjectBuildQueueItem';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';

const dao = dbInstance.getDAO();

(async () => {
    const [
        error,
        projectBuildQueue,
    ] = await dao.getAllCollectionDataAsList<ProjectBuildQueueItem>({
        collection: collections.buildQueue,
        condition: {
            fieldPath: 'isAssigned',
            opStr: operators.EQUAL,
            value: false,
        },
        filter: (item: ProjectBuildQueueItem) => {
            return item.executeAt <= Date.now();
        },
        orders: [{
            fieldPath: 'createdOn',
            directionStr: 'asc',
        }, {
            fieldPath: 'priority',
            directionStr: 'asc',
        }],
    });

    if (projectBuildQueue) {
        console.log(JSON.stringify(projectBuildQueue, null, 4));
        process.exit();
    }

    if (error) {
        logger.error('Runner: Error fetching build queue list: ', {error});
        process.exit(0);
    }

    const {
        projectBuildQueueItem: item,
        accountData,
        allowedSolutionItems,
    } = await fetchProjectBuildQueueItem({
        projectBuildQueue,
    });

    if (!item || !accountData) {
        process.exit(0);
    }

    const buildTimeConfigForBuilder = getConfig();

    try {
        const {
            id,
            accountId,
            projectId,
        } = item;
        const ts = Date.now();
        logger.info(`Runner: ${buildTimeConfigForBuilder.SERVER_ID} started.`, {
            buildQueueItemId: id,
            nodeId: buildTimeConfigForBuilder.SERVER_ID,
            processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID,
            ts,
        });
        const paths = getPaths({
            accountId,
            projectId,
        });
        const [, size] = await dao.getCollectionDocumentSize({
            collection: paths.collections.builds,
        });
        const buildId = (size || 0) + 1;
        const projectBuildQueueItem: ProjectBuildQueueItem<true> = {
            ...item,
            buildId,
            billingCycle: accountData.administration.billing.cycle,
            isAssigned: true,
            runner: {
                lastAttempt: ts,
                attempts: dao.FieldValue.arrayUnion({
                    ts,
                    nodeId: buildTimeConfigForBuilder.SERVER_ID!,
                    processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID!,
                }) as unknown as ProjectBuildQueueItem['runner']['attempts'],
                nodeId: buildTimeConfigForBuilder.SERVER_ID!,
                processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID!,
            },
        };

        await dao.setCollectionDocumentData<Partial<ProjectBuildQueueItem<true>>>({
            id: projectBuildQueueItem.id,
            collection: collections.buildQueue,
            data: projectBuildQueueItem,
        });

        const manager = new Manager({
            projectBuildQueueItem,
            accountData,
            allowedSolutionItems,
        });

        process.on('SIGTERM', async () => {
            await dao.setCollectionDocumentData<{
                runner: {
                    termination: ProjectBuildQueueItem['runner']['termination'];
                },
            }>({
                id: projectBuildQueueItem.id,
                collection: collections.buildQueue,
                data: {
                    runner: {
                        termination: {
                            ts,
                            reason: 'SIGTERM',
                        },
                    },
                },
            });
            await manager.cancelSession({
                state: BUILD_STATES.terminated,
            });
            process.exit(0);
        });

        const {
            success,
        } = await manager.buildProject();

        if (!success) {
            logger.info(`Runner: ${buildTimeConfigForBuilder.SERVER_ID} finished with error.`, {
                buildQueueItemId: projectBuildQueueItem.id,
                nodeId: buildTimeConfigForBuilder.SERVER_ID,
                processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID,
                buildId,
                ts,
            });
            return;
        }

        logger.info(`Runner: ${buildTimeConfigForBuilder.SERVER_ID} finished successfully.`, {
            buildQueueItemId: projectBuildQueueItem.id,
            nodeId: buildTimeConfigForBuilder.SERVER_ID,
            processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID,
            buildId,
            ts,
        });
    } catch (error) {
        const {
            id,
            accountId,
            projectId,
        } = item;
        logger.info(`Runner: ${buildTimeConfigForBuilder.SERVER_ID} interrupted with error.`, {
            buildQueueItemId: id,
            accountId,
            projectId,
            nodeId: buildTimeConfigForBuilder.SERVER_ID,
            processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID,
            ts: Date.now(),
        });
    }

    process.exit(0);
})();
