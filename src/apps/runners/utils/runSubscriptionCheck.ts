import {
    PREPAID_SUBSCRIPTION_KEY,
} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import {isPositiveInt} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getAutoRenewCreditsSummary} from '@w7-3/webeagle-resources/dist/libs/accounts';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';
import type {CheckoutTransaction} from '@w7-3/webeagle-resources/types/administration';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';

export const isSubscriptionOverageOk = ({
    subscription,
    accountData,
}: {
    subscription: CheckoutTransaction<true> | null;
    accountData: AccountData | null;
}): boolean => {
    const autoRenewCreditsSummary = getAutoRenewCreditsSummary({accountData});

    return Boolean(
        !subscription?.data.subscriptionSummary.hasEnteredOverage
        || autoRenewCreditsSummary.isActive
    );
};

export const isSubscriptionOk = ({
    item,
    subscription,
    accountData,
}: {
    item: ProjectBuildQueueItem;
    subscription: CheckoutTransaction<true> | null;
    accountData: AccountData | null;
}): boolean => {
    if (item.subscription === PREPAID_SUBSCRIPTION_KEY) {
        return isPositiveInt(accountData?.lifeTimeAutomationCredits?.value);
    }

    return subscription?.active === true;
};

export default ({
    item,
    subscription,
    accountData,
}: {
    item: ProjectBuildQueueItem;
    subscription: CheckoutTransaction<true> | null;
    accountData: AccountData | null;
}): {
    isSubscriptionOk: boolean;
    isSubscriptionOverageOk: boolean;
} => {
    return {
        isSubscriptionOk: isSubscriptionOk({item, subscription, accountData}),
        isSubscriptionOverageOk: isSubscriptionOverageOk({subscription, accountData}),
    };
};
