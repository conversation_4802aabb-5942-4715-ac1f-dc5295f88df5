import {
    PREPAID_SUBSCRIPTION_KEY,
} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import dbInstance from '../../../setup/setupDB';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';
import type {CheckoutTransaction} from '@w7-3/webeagle-resources/types/administration';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

export default async ({
    item,
}: {
    item: ProjectBuildQueueItem;
}): Promise<{
    subscription: CheckoutTransaction<true> | null,
    accountData: AccountData | null,
}> => {
    const paths = getPaths({
        accountId: item.accountId,
        projectId: item.projectId,
    });
    const [, accountData] = await dao.getDocumentData<AccountData>({
        path: paths.collections.account,
    });

    if (item.subscription === PREPAID_SUBSCRIPTION_KEY) {
        return {
            subscription: null,
            accountData,
        };
    }

    const [, subscription] = await dao.getCollectionDocumentData<CheckoutTransaction<true>>({
        collection: paths.collections.subscriptions,
        id: item.subscription,
    });

    return {
        subscription,
        accountData,
    };
};
