import {getConfig} from 'app-config';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import awaitOn from '@w7-3/webeagle-resources/dist/libs/awaitOn';
import {
    PROJECT_DEQUEUE_REASONS,
} from '@w7-3/webeagle-resources/dist/config/project';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {
    PREPAID_SUBSCRIPTION_KEY,
} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import runAccountCheck from './runAccountCheck';
import runProjectValidityCheck from './runProjectValidityCheck';
import runSubscriptionCheck from './runSubscriptionCheck';
import runDomainBlacklistCheck, {
    handleBlockedDomain,
} from './runDomainBlacklistCheck';
import getAccountData from './getAccountData';
import checkParallelBuild from './runParallelBuildCheck';
import projectBuildDequeueItem from '../../../utils/projects/projectBuildDequeueItem';
import dbInstance from '../../../setup/setupDB';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';
import type {
    AccountData,
    ProjectConfig,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    CheckoutTransaction,
} from '@w7-3/webeagle-resources/types/administration';
import type {
    AllowedSolutionItems,
} from '@w7-3/webeagle-resources/types/solutions';

const dao = dbInstance.getDAO();

export const getProjectData = async ({
    accountId,
    projectId,
}): Promise<{
    projectConfig: ProjectConfig | null,
}> => {
    const paths = getPaths({
        accountId,
        projectId,
    });
    const [, projectConfig] = await dao.getCollectionDocumentData<ProjectConfig>({
        collection: paths.collections.configs,
        id: 'current',
    });

    return {
        projectConfig,
    };
};

export default async ({
    projectBuildQueue,
}: {
    projectBuildQueue: Array<ProjectBuildQueueItem>;
}): Promise<{
    projectBuildQueueItem: ProjectBuildQueueItem | null,
    accountData: AccountData | null,
    allowedSolutionItems: AllowedSolutionItems;
}> => {
    const allowedSolutionItems = {
        dataExtractions: false,
        e2eVisualTests: false,
        screenshots: false,
        screenVideos: false,
        lighthouse: false,
        urlChallenge: false,
    };
    if (!isNonEmptyArray(projectBuildQueue)) {
        return {
            projectBuildQueueItem: null,
            accountData: null,
            allowedSolutionItems,
        };
    }

    let projectBuildQueueItem: ProjectBuildQueueItem | null = null;
    let accountData: AccountData | null = null;
    let subscription: CheckoutTransaction<true> | null = null;

    for (const item of projectBuildQueue) {
        ({
            subscription,
            accountData,
        } = await getAccountData({item}));

        if (!accountData) {
            continue;
        }

        const {isAccountOk} = runAccountCheck({
            accountData,
        });
        const {projectConfig} = await getProjectData({
            accountId: item.accountId,
            projectId: item.projectId,
        });

        if (!projectConfig?.data) {
            continue;
        }

        const projectName = projectConfig.data.projectName;

        if (!isAccountOk) {
            await awaitOn(projectBuildDequeueItem({
                accountId: item.accountId,
                id: item.id,
                projectId: item.projectId,
                reason: PROJECT_DEQUEUE_REASONS.accountProblem,
                vendor: item.vendor,
                data: {
                    projectId: item.projectId,
                    projectName,
                    blacklistedDomainListInTarget: [],
                    email: accountData!.accountHolder,
                    language: accountData!.preferredLanguage,
                },
            }));

            continue;
        }

        const {
            isSubscriptionOk,
            isSubscriptionOverageOk,
        } = runSubscriptionCheck({
            item,
            subscription,
            accountData,
        });

        if (!isSubscriptionOk) {
            await awaitOn(projectBuildDequeueItem({
                accountId: item.accountId,
                id: item.id,
                projectId: item.projectId,
                reason: PROJECT_DEQUEUE_REASONS.subscriptionProblem,
                vendor: item.vendor,
                data: {
                    projectId: item.projectId,
                    projectName,
                    blacklistedDomainListInTarget: [],
                    email: accountData!.accountHolder,
                    language: accountData!.preferredLanguage,
                },
            }));

            continue;
        }

        if (!isSubscriptionOverageOk) {
            await awaitOn(projectBuildDequeueItem({
                accountId: item.accountId,
                id: item.id,
                projectId: item.projectId,
                reason: PROJECT_DEQUEUE_REASONS.overageProblem,
                vendor: item.vendor,
                data: {
                    projectId: item.projectId,
                    projectName,
                    blacklistedDomainListInTarget: [],
                    email: accountData!.accountHolder,
                    language: accountData!.preferredLanguage,
                },
            }));

            continue;
        }

        const {isProjectValid} = await runProjectValidityCheck({item});

        if (!isProjectValid) {
            await awaitOn(projectBuildDequeueItem({
                accountId: item.accountId,
                id: item.id,
                projectId: item.projectId,
                reason: PROJECT_DEQUEUE_REASONS.noProblem,
                vendor: item.vendor,
                data: {
                    projectId: item.projectId,
                    projectName,
                    blacklistedDomainListInTarget: [],
                    email: accountData!.accountHolder,
                    language: accountData!.preferredLanguage,
                },
            }));

            continue;
        }

        const {isParallelBuildOk} = checkParallelBuild({
            item,
            projectBuildQueue,
            subscription,
        });

        if (!isParallelBuildOk) {
            const buildTimeConfigForBuilder = getConfig();
            await dao.setCollectionDocumentData<{
                executeAt: number,
                runner: {
                    attempts: ProjectBuildQueueItem['runner']['attempts'],
                },
            }>({
                id: item.id,
                collection: collections.buildQueue,
                data: {
                    executeAt: Date.now() + 1000 * 60 * 5,
                    runner: {
                        attempts: dao.FieldValue.arrayUnion({
                            ts: Date.now(),
                            reason: PROJECT_DEQUEUE_REASONS.parallelBuildProblem,
                            nodeId: buildTimeConfigForBuilder.SERVER_ID!,
                            processId: buildTimeConfigForBuilder.SERVER_PROCESS_ID!,
                        }) as unknown as ProjectBuildQueueItem['runner']['attempts'],
                    },
                },
            });

            continue;
        }

        const {blacklistedDomainListInTarget} = await runDomainBlacklistCheck({
            projectConfig,
            accountData,
        });

        if (blacklistedDomainListInTarget.length > 0) {
            await handleBlockedDomain({
                accountData,
                blacklistedDomainListInTarget,
                buildId: item.buildId,
                projectConfig,
                projectId: item.projectId,
            });
        }

        projectBuildQueueItem = item;
        break;
    }

    if (subscription?.data?.subscriptionSummary?.restContingent) {
        Object.entries(subscription.data.subscriptionSummary.restContingent.solutions.items).forEach(([key, value]) => {
            allowedSolutionItems[key] = value.active;
        });
    }

    if (projectBuildQueueItem?.subscription === PREPAID_SUBSCRIPTION_KEY) {
        Object.keys(allowedSolutionItems).forEach((key) => {
            allowedSolutionItems[key] = true;
        });
    }

    return {
        projectBuildQueueItem,
        accountData,
        allowedSolutionItems,
    };
};
