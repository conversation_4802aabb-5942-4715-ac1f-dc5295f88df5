import {accountStates} from '@w7-3/webeagle-resources/dist/config/account';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';

export default ({
    accountData,
}: {
    accountData: AccountData;
}): {
    isAccountOk: boolean;
} => {
    if (!accountData?.state) {
        return {
            isAccountOk: false,
        };
    }

    return {
        isAccountOk: ![
            accountStates.suspended,
            accountStates.inactive,
        ].includes(accountData?.state),
    };
};
