import '@w7-3/webeagle-setup';
import process from 'process';
import {getConfig} from 'app-config';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {
    isNonEmptyArray,
    isPositiveInt,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import logger from '../../utils/logger';
import dbInstance from '../../setup/setupDB';
import processProjectBuildQueueItem from './utils/processProjectBuildQueueItem';
import type {
    ProjectBuildQueueItem,
} from '@w7-3/webeagle-resources/types/project';

const dao = dbInstance.getDAO();

(async () => {
    const config = getConfig();
    const [error, projectBuildQueue] = await dao.getAllCollectionDataAsList<ProjectBuildQueueItem<true>>({
        collection: collections.buildQueue,
        condition: {
            fieldPath: 'isAssigned',
            opStr: operators.EQUAL,
            value: true,
        },
        filter: (item: ProjectBuildQueueItem<true>) => {
            return item.isCompleted || item?.runner?.nodeId === config.SERVER_ID;
        },
    });

    if (error) {
        logger.error('Error fetching build queue list: ', {error});
        process.exit(0);
    }

    if (!isNonEmptyArray(projectBuildQueue)) {
        process.exit(0);
    }

    await Promise.all(projectBuildQueue.map(async (projectBuildQueueItem) => {
        const {
            isCompleted,
            heartbeat,
        } = projectBuildQueueItem;
        if (isCompleted) {
            await processProjectBuildQueueItem({
                projectBuildQueueItem,
            });
            return;
        }

        if (!isPositiveInt(heartbeat.last)
            || heartbeat.last! + config.APP_DATA.BUILDER_HEARTBEAT_TIMEOUT >= Date.now()) {
            return;
        }

        await processProjectBuildQueueItem({
            projectBuildQueueItem,
            isTimeout: true,
        });
    }));

    process.exit(0);
})();

