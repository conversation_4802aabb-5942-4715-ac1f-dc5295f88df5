import '@w7-3/webeagle-setup';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {operators} from '@w7-3/webeagle-resources/dist/config/firebase';
import {accountStates} from '@w7-3/webeagle-resources/dist/config/account';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import logger from '../../utils/logger';
import dbInstance from '../../setup/setupDB';
import resetSubscriptions from './utils/resetSubscriptions';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    CheckoutTransaction,
} from '@w7-3/webeagle-resources/types/administration';

const dao = dbInstance.getDAO();

(async () => {
    const [error, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
        collection: collections.accounts,
        condition: {
            fieldPath: 'state',
            opStr: operators.IN,
            value: [
                accountStates.new,
                accountStates.demo,
                accountStates.active,
                accountStates.gracePeriod,
            ],
        },
        filter: (item: AccountData) => {
            return !item.administration.audit.active && item.administration.audit.current + item.administration.audit.period >= Date.now();
        },
    });

    if (error) {
        logger.error('Failed to fetch account list', {error});
        return;
    }

    if (!isNonEmptyArray(accountList)) {
        return;
    }

    await Promise.allSettled(accountList.map(async (accountData) => {
        try {
            const auditTs = Date.now();
            const {
                id: accountId,
            } = accountData;
            const paths = getPaths({
                accountId,
            });
            const [
                subscriptionsError,
                subscriptions,
            ] = await dao.getAllCollectionDataAsList<CheckoutTransaction<true>>({
                collection: paths.collections.subscriptions,
                condition: {
                    fieldPath: 'isSubscriptionItem',
                    opStr: operators.EQUAL,
                    value: true,
                },
            });

            if (subscriptionsError) {
                logger.error('Failed to fetch subscription data', {
                    error: subscriptionsError,
                    accountId,
                });

                return;
            }

            await resetSubscriptions({
                auditTs,
                accountData,
                subscriptions,
            });
        } catch (error) {
            logger.error('Failed to update subscription contingent', {error});
        }
    }));
})();

