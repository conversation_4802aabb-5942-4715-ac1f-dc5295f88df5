import {
    ACCOUNT_BILLING_PERIOD_STANDARD,
} from '@w7-3/webeagle-resources/dist/config/account';
import {
    BILLING_REQUEST_TYPES,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {getPaths} from '@w7-3/webeagle-resources/dist/libs/collections';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    CREDIT_EVENT_CONTEXTS,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {EMAILS} from '@w7-3/webeagle-resources/dist/config/w73';
import daoInstance from '../../../setup/setupDB';
import logger from '../../../utils/logger';
import getOverageBillingData from './getOverageBillingData';
import {handleConsumption} from '../../../utils/credits';
import type {
    BillingRequest,
    BillingRequestOverageData,
    CheckoutTransaction,
    SubscriptionPackage,
} from '@w7-3/webeagle-resources/types/administration';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = daoInstance.getDAO();

export default async ({
    auditTs,
    accountData,
    subscriptions,
}: {
    auditTs: number;
    accountData: AccountData,
    subscriptions: CheckoutTransaction<true>[],
}) => {
    const timestamp = Date.now();
    const accountId = accountData.id;
    const paths = getPaths({
        accountId,
        billingCycle: accountData.administration.billing.cycle,
    });

    await Promise.allSettled(subscriptions.map(async (subscription) => {
        let active = subscription.active;
        if (!active) {
            return;
        }

        if (subscription.monthlyReset.nextResetTimestamp > timestamp) {
            return;
        }

        if (subscription.data.isCancelled && subscription.data.periodEnd < timestamp) {
            active = false;
        }

        const subscriptionId = subscription.id;
        const billingData = getOverageBillingData({
            accountData,
            subscription,
        });
        const resetCount = subscription.monthlyReset.count;
        const previousResetTimestamp = Date.now();
        const nextResetTimestamp = previousResetTimestamp + ACCOUNT_BILLING_PERIOD_STANDARD;
        const billableActions = billingData.items.filter((item) => item.quantity > 0);

        if (isNonEmptyArray(billableActions)) {
            const [billingRequestCreationError, billingRequestId] = await dao.insertCollectionData<BillingRequest<BillingRequestOverageData>>({
                collection: paths.collections.billingRequests,
                data: {
                    type: BILLING_REQUEST_TYPES.OVERAGE,
                    accountId,
                    created: Date.now(),
                    billingCycle: accountData.administration.billing.cycle,
                    items: billableActions,
                    data: {
                        subscriptionId,
                        resetCount,
                    },
                },
            });

            if (billingRequestCreationError) {
                logger.error('Failed to create billing request', {
                    error: billingRequestCreationError,
                    accountId,
                    subscriptionId,
                });

                return;
            }

            await handleConsumption({
                accountId,
                billableActions,
                buildId: undefined,
                context: CREDIT_EVENT_CONTEXTS.MONTHLY_SUBSCRIPTION_SETTLEMENT,
                email: EMAILS.NO_REPLY,
                loginSessionId: JSON.stringify({auditTs, billingRequestId}),
                projectId: undefined,
                source: 'resetSubscriptions',
            });
        }

        await dao.setCollectionDocumentData<{
            active: CheckoutTransaction<true>['active'],
            data: {
                subscriptionSummary: Pick<CheckoutTransaction<true>['data']['subscriptionSummary'], 'hasEnteredOverage' | 'restContingent'>,
            },
            monthlyReset: CheckoutTransaction<true>['monthlyReset'],
        }>({
            id: subscription.id,
            collection: paths.collections.subscriptions,
            data: {
                active,
                data: {
                    subscriptionSummary: {
                        hasEnteredOverage: false,
                        restContingent: subscription.data.subscriptionSummary!.initialContingent as SubscriptionPackage,
                    },
                },
                monthlyReset: {
                    count: resetCount + 1,
                    nextResetTimestamp,
                    previousResetTimestamp,
                },
            },
        });
    }));
};
