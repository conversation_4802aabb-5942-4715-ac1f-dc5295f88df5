import {
    getSubscriptionConsumption,
} from '@w7-3/webeagle-resources/dist/libs/subscriptions/consumption';
import type {
    BillingRequest,
    CheckoutTransaction,
} from '@w7-3/webeagle-resources/types/administration';
import type {
    AccountData,
} from '@w7-3/webeagle-resources/types/webautomate/global';

export default ({
    subscription,
}: {
    accountData: AccountData,
    subscription: CheckoutTransaction<true>,
}): {
    success: boolean,
    items: BillingRequest['items'],
} => {
    const items: BillingRequest['items'] = [];
    const consumption = getSubscriptionConsumption({
        subscription,
    });

    for (const billableAction in consumption) {
        const item = consumption[billableAction];

        if (item.overage > 0) {
            items.push({
                billableAction,
                quantity: item.overage,
            });
        }
    }

    return {
        success: true,
        items,
    };
};
