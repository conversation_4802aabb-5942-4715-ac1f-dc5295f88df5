import '@w7-3/webeagle-setup';
import collections from '@w7-3/webeagle-resources/dist/config/collections';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import logger from '../../utils/logger';
import dbInstance from '../../setup/setupDB';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';

const dao = dbInstance.getDAO();

(async () => {
    const [error, accountList] = await dao.getAllCollectionDataAsList<AccountData>({
        collection: collections.accounts,
    });

    if (error || !isNonEmptyArray(accountList)) {
        logger.error('Failed to fetch account list in account-audit-manager', {error});
        return;
    }

    try {
        await Promise.allSettled(accountList.map(async () => {
            throw new Error('Not implemented');
        }));
    } catch (error) {
        logger.error('Failed to update subscription contingent', {error});
    }
})();

