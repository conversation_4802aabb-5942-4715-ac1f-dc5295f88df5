import {
    accountStates,
    ACCOUNT_STATE_TRANSITION_CYCLE,
} from '@w7-3/webeagle-resources/dist/config/account';
import type {AccountData} from '@w7-3/webeagle-resources/types/webautomate/global';
import type {
    CheckoutTransaction,
} from '@w7-3/webeagle-resources/types/administration';

export default ({
    accountData,
    subscriptions,
}: {
    accountData: AccountData;
    subscriptions: CheckoutTransaction<true>[],
}): {
    state: AccountData['state'];
} => {
    const {state} = accountData;
    if (![
        accountStates.active,
        accountStates.demo,
        accountStates.gracePeriod,
        accountStates.new,
    ].includes(state)) {
        return {
            state,
        };
    }

    const hasRecentlySubscribed = subscriptions.some((subscription) => {
        return subscription.active || (subscription.data.periodEnd + ACCOUNT_STATE_TRANSITION_CYCLE) > Date.now();
    });
    const hasRecentlyPurchasedCredits = accountData.lifeTimeAutomationCredits.expiry > (Date.now() + ACCOUNT_STATE_TRANSITION_CYCLE);

    if (hasRecentlySubscribed || hasRecentlyPurchasedCredits) {
        return {
            state: accountStates.active,
        };
    }

    if ([
        accountStates.new,
        accountStates.demo,
        accountStates.active,
    ].includes(accountData.state)) {
        return {
            state: accountStates.gracePeriod,
        };
    }

    if ([
        accountStates.gracePeriod,
    ].includes(accountData.state)) {
        return {
            state: accountStates.inactive,
        };
    }

    return {
        state: accountData.state,
    };
};
