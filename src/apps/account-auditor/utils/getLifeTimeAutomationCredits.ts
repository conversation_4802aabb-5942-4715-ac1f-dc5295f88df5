import {
    CREDIT_EVENT_CONTEXTS,
    CREDIT_EVENTS,
} from '@w7-3/webeagle-resources/dist/config/administration';
import {EMAILS} from '@w7-3/webeagle-resources/dist/config/w73';
import {
    ACCOUNT_AUDIT_CYCLE_STANDARD,
} from '@w7-3/webeagle-resources/dist/config/account';
import {handleCreditEvents} from '../../../utils/credits';
import type {
    AccountData,
    AutomationCreditEvent,
} from '@w7-3/webeagle-resources/types/webautomate/global';
import logger from '../../../utils/logger';

export default async ({
    accountData,
    auditTs,
}: {
    accountData: AccountData,
    auditTs: number,
}): Promise<{
    isReset: boolean,
    isResetPending: boolean,
    lifeTimeAutomationCredits: AccountData['lifeTimeAutomationCredits'],
    value: number,
}> => {
    const {
        lifeTimeAutomationCredits,
    } = accountData;
    const {expiry, value} = lifeTimeAutomationCredits;
    const isReset = lifeTimeAutomationCredits.expiry <= Date.now();
    const isResetPending = !isReset && lifeTimeAutomationCredits.expiry <= Date.now() - ACCOUNT_AUDIT_CYCLE_STANDARD;

    if (value <= 0 || isReset) {
        return {
            isReset: false,
            isResetPending,
            lifeTimeAutomationCredits,
            value,
        };
    }

    const quantityBefore = accountData.lifeTimeAutomationCredits.value;
    const quantityAfter = 0;
    const data: AutomationCreditEvent<'expiration'>['data'] = [{
        quantityBefore,
        quantityAfter,
        expiry,
        quantity: quantityBefore,
    }];

    const [error] = await handleCreditEvents({
        accountData,
        event: {
            context: CREDIT_EVENT_CONTEXTS.EXPIRATION,
            data,
            email: EMAILS.AI,
            loginSessionId: JSON.stringify({auditTs}),
            source: 'accountAuditor',
            type: CREDIT_EVENTS.EXPIRATION,
        },
    });

    if (error) {
        logger.error('Failed to expire credits', {
            error,
            accountId: accountData.id,
        });

        return {
            isReset: false,
            isResetPending,
            lifeTimeAutomationCredits,
            value,
        };
    }

    return {
        isReset: true,
        isResetPending,
        lifeTimeAutomationCredits: {
            value: 0,
            expiry: 0,
        },
        value,
    };
};
