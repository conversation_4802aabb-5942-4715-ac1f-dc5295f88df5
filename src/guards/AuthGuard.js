import {useEffect, useState} from 'react';
import {Navigate, useLocation} from 'react-router-dom';
import PropTypes from 'prop-types';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {PATH_HOME} from '@w7-3/webeagle-resources/dist/config/paths';
import useAuth from '../hooks/useAuth';
import LoginPage from '../pages/authentication/LoginPage';
import useLocales from '../hooks/useLocales';
import useNotification from '../custom/hooks/useNotification';

AuthGuard.propTypes = {
    goHome: PropTypes.bool,
    children: PropTypes.node,
    info: PropTypes.string,
};

export default function AuthGuard({children, goHome, info}) {
    const {isAuthenticated} = useAuth();
    const {translate} = useLocales();
    const {pathname} = useLocation();
    const [requestedLocation, setRequestedLocation] = useState(null);
    const notify = useNotification();

    useEffect(() => {
        if (isAuthenticated || !goHome) {
            return;
        }

        queueCallback(() => {
            notify({
                message: translate('directives.notifications.loginRequired'),
            });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated, goHome]);

    useEffect(() => {
        if (isAuthenticated || !info) {
            return;
        }

        queueCallback(() => {
            notify({
                message: info,
                autoHideDuration: null,
            });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated, info]);

    if (!isAuthenticated) {
        if (goHome) {
            return <Navigate to={PATH_HOME}/>;
        }

        if (pathname !== requestedLocation) {
            setRequestedLocation(pathname);
        }

        return <LoginPage isInline/>;
    }

    if (requestedLocation && pathname !== requestedLocation) {
        setRequestedLocation(null);
        return <Navigate to={requestedLocation}/>;
    }

    return <>{children}</>;
}
