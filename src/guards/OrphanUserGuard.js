import {useState} from 'react';
import PropTypes from 'prop-types';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>r, FormControlLabel, Radio, RadioGroup, Stack, Typography} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {orphanUser} from '@w7-3/webeagle-resources/dist/config/users';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import PubSub from 'pubsub-js';
import useLocales from '../hooks/useLocales';
import PageWrapper from '../custom/components/PageWrapper';
import MainNavbar from '../layouts/main/MainNavbar';
import SubmitButton from '../custom/components/utils/SubmitButton';
import {globalConfig} from '../config/setup';
import TextFieldStyle from '../custom/components/utils/TextFieldStyle';
import {getAPIPath, getOpenAPIPath} from '../custom/utils/getPath';
import useApiCaller from '../custom/hooks/useApiCaller';
import {setDeferredTask, TASK_TYPES} from '../custom/components/handlers/DeferredTasksHandler';
import useAuth from '../hooks/useAuth';
import {getTimeZone} from '../custom/utils/timezone';
import {getSessionStorageItem} from '../custom/utils/storage';

const OrphanUserGuard = ({isInline, email}) => {
    const {translate, currentLang} = useLocales();
    const [decision, setDecision] = useState(orphanUser.createAccount);
    const [organisation, setAccountName] = useState(email?.split('@')?.[0] || '');
    const {authenticatedUser, logout, deleteAuthenticatedUser} = useAuth();
    const apiCaller = useApiCaller({
        handleRedirectDirective: false,
        handleNotificationDirective: false,
        handleInfoCodeDirectives: false,
        handleModalDirective: false,
        handleCart: false,
    });
    const isNewServiceProvidedAccount = [
        'loginWithGoogle',
        'loginWithFaceBook',
        'loginWithTwitter',
    ].includes(getSessionStorageItem('lastAuthAction'));
    const failureCallback = () => {
        PubSub.publish('SHOW.NOTIFICATION', {
            message: translate(`directives.notifications.${NOTIFICATIONS.genericErrorWithRetry}`),
            variant: 'error',
        });
    }
    const handleCreateAccount = () => {
        apiCaller({
            uri: getOpenAPIPath(api.createUser),
            data: {
                email,
                organisation,
                language: currentLang.value,
                isNewServiceProvidedAccount,
            },
            successCallback: async () => {
                setDeferredTask({
                    type: TASK_TYPES.notification,
                    options: {
                        message: translate('register.validation.accountCreateSuccess', {
                            app: globalConfig.appName,
                        }),
                        variant: 'success',
                    }
                });

                if (authenticatedUser?.emailVerified) {
                    apiCaller({
                        uri: getAPIPath(api.completeRegistration),
                        data: {
                            timeZone: getTimeZone(),
                            isNewServiceProvidedAccount,
                        },
                        successCallback: async () => {
                            window.location.reload();
                        },
                    });

                    return;
                }

                window.location.reload();
            },
            failureCallback,
            errorCallback: failureCallback,
        });
    };
    const handleDecision = () => {
        switch (decision) {
            case orphanUser.createAccount:
                handleCreateAccount();
                break;
            case orphanUser.contactAdmin:
                queueCallback(logout);
                break;
            case orphanUser.deleteUser:
                PubSub.publish('REQUIRE.RECENT.LOGIN', {
                    callback: () => {
                        deleteAuthenticatedUser({isOrphan: true});
                    },
                });
                break;
            default:
                failureCallback();
        }
    };
    const options = [
        orphanUser.createAccount,
        orphanUser.contactAdmin,
        ...isNewServiceProvidedAccount ? [] : [orphanUser.deleteUser],
    ];

    return (
        <PageWrapper
            title={translate('collaborators.restrictedContent.label')}
            sx={{
                display: 'flex',
                minHeight: '100%',
                alignItems: 'center',
            }}
        >
            {
                !isInline &&
                <MainNavbar/>
            }
            <Stack spacing={3}>
                {
                    isNewServiceProvidedAccount ? (
                        <Alert severity="success">
                            <AlertTitle>{translate('collaborators.registrationByServiceProvidedAccount.label', {
                                app: globalConfig.domain,
                            })}</AlertTitle>
                            <Typography component="p" variant="heading" sx={{mb: 2}}>
                                {translate('collaborators.registrationByServiceProvidedAccount.accountRequired')}
                            </Typography>
                        </Alert>
                    ) : (
                        <Alert
                            variant="outlined"
                            severity="error"
                            sx={{mb: 3}}>
                            <AlertTitle>{translate('collaborators.orphanUser.label')}</AlertTitle>
                            <Typography component="p" variant="heading" sx={{mb: 2}}>
                                {translate('collaborators.orphanUser.description', {
                                    email,
                                })}
                            </Typography>
                        </Alert>
                    )
                }
                <Typography variant='subtitle2'>
                    {translate('collaborators.orphanUser.options.label')}
                </Typography>
                <RadioGroup
                    name="decision"
                    value={decision}
                    onChange={(_, newValue) => {
                        setDecision(newValue);
                    }}
                    sx={{
                        mt: 3,
                        display: 'flex',
                        justifyContent: 'left'
                    }}
                >
                    {options.map((value) => (
                        <Stack key={value} spacing={3} sx={{pb: 3}}>
                            <FormControlLabel
                                value={value}
                                label={translate(`collaborators.orphanUser.options.${value}.label`, {
                                    app: globalConfig.domain,
                                })}
                                control={<Radio />}
                            />
                            {
                                value === orphanUser.createAccount &&
                                <TextFieldStyle
                                    autoComplete="accountname"
                                    type="text"
                                    label={`${translate('form.organisation')}`}
                                    value={organisation}
                                    onChange={(e) => setAccountName(e.target.value)}
                                />
                            }
                            <Typography component="p" variant="caption" sx={{color: 'text.secondary'}}>
                                {translate(`collaborators.orphanUser.options.${value}.description`)}
                            </Typography>
                            <Divider sx={{borderStyle: 'dashed'}}/>
                        </Stack>
                    ))}
                </RadioGroup>
                <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                    <SubmitButton
                        label={translate('continue')}
                        onClick={handleDecision}
                        isValid
                    />
                </Stack>
            </Stack>
        </PageWrapper>
    );
};

OrphanUserGuard.propTypes = {
    isInline: PropTypes.bool,
    email: PropTypes.string,
};

export default OrphanUserGuard;

