import PropTypes from 'prop-types';
import {Alert, AlertTitle, Box, Container} from '@material-ui/core';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {connect} from 'react-redux';
import useLocales from '../hooks/useLocales';
import useAuth from '../hooks/useAuth';

const propTypes = {
    accessibleRoles: PropTypes.array,
    children: PropTypes.node,
    isCovert: PropTypes.bool,
    accountHolder: PropTypes.string,
};

const defaultProps = {
    accessibleRoles: [],
};

const mapStateToProps = ({state}) => {
    const {accountHolder} = state;

    return {
        accountHolder,
    };
};

export const useIsAccessible = ({accessibleRoles}) => {
    const {user} = useAuth();

    return accessibleRoles.includes(user.role) || [
        userRoles.ACCOUNT_HOLDER.key,
        userRoles.ADMIN.key,
    ].includes(user.role);
}

const PrivilegedContent = ({accessibleRoles, children, isCovert, accountHolder}) => {
    const {translate} = useLocales();
    const isAccessible = useIsAccessible({accessibleRoles});

    if (isAccessible) {
        return <>{children}</>;
    }

    if (isCovert) {
        return null;
    }

    return (
        <Container sx={{py: 3}}>
            <Alert severity="error">
                <AlertTitle>{translate('collaborators.restrictedContent.label')}</AlertTitle>
                <Box component="p">
                    {translate('directives.notifications.noRights', {email: accountHolder})}
                </Box>
            </Alert>
        </Container>
    );
};

PrivilegedContent.propTypes = propTypes;
PrivilegedContent.defaultProps = defaultProps;

export default connect(mapStateToProps)(PrivilegedContent);
