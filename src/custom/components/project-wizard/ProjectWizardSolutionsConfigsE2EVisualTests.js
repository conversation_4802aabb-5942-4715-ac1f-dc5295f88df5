import {connect} from 'react-redux';
import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {Stack, Typography} from '@material-ui/core';
import {visualCompareOptions} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import {ACTIONS, solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {SCREENSHOT_IMAGE_TYPE, SELECTOR_TARGETS} from '@w7-3/webeagle-resources/dist/config/screenshots';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {updateSolutionConfig} from '../../../redux/projectAssistant/store';
import E2EVisualTestsDialogForm from '../forms/E2EVisualTestsDialogForm';
import E2EVisualTestFields from '../forms/E2EVisualTestFields';
import ProjectSolutionStepBlock from '../forms/ProjectSolutionStepBlock';
import ProjectItemLabels from '../project/ProjectItemLabels';
import getProjectSolutionStepItem from '../../utils/getProjectSolutionStepItem';
import E2EVisualTestsSelectionDialogForm from '../forms/E2EVisualTestsSelectionDialogForm';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.e2eVisualTests';

const propTypes = {
    config: PropTypes.shape({
        stepList: PropTypes.array,
        highlightColor: PropTypes.string,
        clustersSize: PropTypes.number,
        acceptFirstShot: PropTypes.bool,
    }),
};

const mapStateToProps = ({projectAssistant}) => {
    return {
        config: projectAssistant?.solution?.config,
    };
};

const PreviewFooter = ({item}) => {
    return (
        <ProjectItemLabels
            item={item}
            i18nContext={`${i18nContext}.groups.${item.categoryId}`}
        />
    );
};

PreviewFooter.propTypes = {
    item: PropTypes.object,
};

const ProjectWizardSolutionsConfigsE2EVisualTests = ({config}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const [stepList, updateStepList] = useState(config?.stepList || []);
    const [acceptFirstShot, setAcceptFirstShot] = useState(config?.acceptFirstShot || true);
    const [highlightColor, setHighlightColor] = useState(
        config?.highlightColor || visualCompareOptions.highlightColor);
    const [clustersSize, setClustersSize] = useState(
        config?.clustersSize || visualCompareOptions.clustersSize);

    const save = ({stepList}) => {
        updateStepList(stepList);
    };

    const getSetNewItem = ({translate, setDialogState}) => (
        ({stepType, item: {categoryId, type}, originalStepIndex}) => {
            const item = getProjectSolutionStepItem({
                originalStepIndex,
                type,
                categoryId,
                stepType,
                solution: solutions.e2eVisualTests.key,
                tempLabel: translate(`${i18nContext}.groups.${categoryId}.options.${type}.label`),
                value: {
                    target: SCREENSHOT_IMAGE_TYPE.fullPage,
                    selectorTargets: SELECTOR_TARGETS.first,
                    customSelectorTargets: '',
                },
            });

            setDialogState({
                show: true,
                item,
                action: ACTIONS.add,
            });
        }
    );

    useEffect(() => {
        dispatch(updateSolutionConfig({
            clustersSize,
            highlightColor,
            acceptFirstShot,
            stepList,
        }));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [clustersSize, highlightColor, acceptFirstShot, stepList]);

    return (
        <Stack spacing={3}>
            <Typography variant="subtitle1">
                {translate(`${i18nContext}.items.title`)}
            </Typography>
            <E2EVisualTestFields
                {...{
                    highlightColor,
                    setHighlightColor,
                    clustersSize,
                    setClustersSize,
                    acceptFirstShot,
                    setAcceptFirstShot,
                }}
            />
            <ProjectSolutionStepBlock
                onChange={save}
                value={stepList}
                request={{
                    label: translate(`${i18nContext}.label`),
                    config: {
                        key: solutionStepTypes.solution,
                        i18nContext,
                        PreviewFooter,
                        SelectionDialogForm: E2EVisualTestsSelectionDialogForm,
                        ItemDialogForm: E2EVisualTestsDialogForm,
                        getSetNewItem,
                    },
                }}
                labels={{
                    abortion: translate(`${i18nContext}.failStopLabel`),
                }}
                actionItemRendererProps={{
                    showAbortOnError: true,
                    showIgnoreOnError: false,
                }}
            />
        </Stack>
    );
};

ProjectWizardSolutionsConfigsE2EVisualTests.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardSolutionsConfigsE2EVisualTests);
