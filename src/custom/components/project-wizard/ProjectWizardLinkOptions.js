import PropTypes from 'prop-types';
import {But<PERSON>, Stack, Typography} from '@material-ui/core';
import {lazy, useEffect, useState} from 'react';
import {connect} from 'react-redux';
import {linkOptions, steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../hooks/useLocales';
import {setLinkOptions} from '../../../redux/projectAssistant/store';
import {useDispatch} from '../../../redux/store';
import LoadingScreen from '../../../components/LoadingScreen';
import LabelStyle from '../utils/LabelStyle';
import DefinitionList from '../utils/DefinitionList';
import SettingItemChange from '../utils/SettingItemChange';
import Drawer from '../forms/Drawer';
import ProjectPreviewLinkOptions from './project-preview/ProjectPreviewLinkOptions';
import ProjectWizardTargetPreview from './project-wizard-target/ProjectWizardTargetPreview';
import Loadable from '../utils/Loadable';

const ProjectWizardLinkOptionsContentUrlSelection = Loadable(lazy(() => import('./ProjectWizardLinkOptionsContentUrlSelection')));
const ProjectWizardLinkOptionsContentUrlNormalization = Loadable(lazy(() => import('./ProjectWizardLinkOptionsContentUrlNormalization')));
const ProjectWizardLinkOptionsContentUrlFiltering = Loadable(lazy(() => import('./ProjectWizardLinkOptionsContentUrlFiltering')));
const i18nContext = 'project.wizardSteps.linkOptions.groups';
const renderers = {
    [linkOptions.urlSelection.key]: ProjectWizardLinkOptionsContentUrlSelection,
    [linkOptions.urlNormalization.key]: ProjectWizardLinkOptionsContentUrlNormalization,
    [linkOptions.urlFiltering.key]: ProjectWizardLinkOptionsContentUrlFiltering,
};

const TabContent = ({setting, children}) => {
    const {translate} = useLocales();
    const Renderer = renderers[setting.key];

    if (!Renderer) {
        return null;
    }

    return (
        <>
            <Typography component="p" sx={{color: 'text.secondary', mb: 1}}>
                {translate(`${i18nContext}.${setting.key}.title`)}
            </Typography>
            <Stack direction={{xs: 'column', sm: 'row'}}
                   alignItems={{xs: 'left', sm: 'center'}}
                   justifyContent="space-between" spacing={1}>
                <Typography component="p" variant="heading" sx={{
                    mb: {xs: 0, sm: 3},
                    mr: {xs: 0, sm: 3},
                }}>
                    {translate(`${i18nContext}.${setting.key}.description`)}
                </Typography>
                {children}
            </Stack>
            <Renderer />
        </>
    );
}

TabContent.propTypes = {
    setting: PropTypes.object,
    children: PropTypes.node,
};

const mapStateToProps = ({projectAssistant}) => {
    const {
        linkOptions: {
            activeMenuItem,
        },
    } = projectAssistant.configData;

    return {
        activeMenuItem,
    };
};

const propTypes = {
    activeMenuItem: PropTypes.object,
    solution: PropTypes.object,
    children: PropTypes.node,
};

const ProjectWizardLinkOptions = ({activeMenuItem, solution, children}) => {
    const TAB_ITEMS = Object.values(linkOptions);
    const [activeTab, setActiveTab] = useState(activeMenuItem?.key);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const handleChange = (_, newValue) => {
        setActiveTab(newValue);
        dispatch(setLinkOptions({activeMenuItem: linkOptions[newValue]}));
    };

    useEffect(() => {
        if (activeTab || TAB_ITEMS.length < 1) {
            return;
        }

        setActiveTab(TAB_ITEMS[0].key);
        handleChange(undefined, TAB_ITEMS[0].key);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeTab]);

    if (!activeTab) {
        return (
            <LoadingScreen/>
        );
    }

    return (
        <div>
            <ProjectWizardTargetPreview
                Component={ProjectPreviewLinkOptions}
            />
            <SettingItemChange
                onClick={() => setIsFormOpen(true)}
                sx={{
                    mt: 3,
                    fontSize: '.75em',
                    alignSelf: 'flex-start',
                    iconProps: {
                        width: 20,
                        height: 20,
                    },
                }}
            />
            <Drawer
                open={isFormOpen}
                onClose={() => setIsFormOpen(false)}
                title={(
                    <Typography variant="subtitle1">
                        {translate(`project.wizardSteps.${steps.linkOptions.key}.previewLabel`, {
                            solution: translate(`project.wizardSteps.solutionsConfigs.groups.${solution?.key}.label`),
                        })}
                    </Typography>
                )}
            >
                <DefinitionList
                    dataList={TAB_ITEMS.map((setting) => ({
                        key: setting.key,
                        label: (
                            <LabelStyle>
                                {translate(`project.wizardSteps.linkOptions.groups.${setting.key}.label`)}
                            </LabelStyle>
                        ),
                        node: (
                            <>
                                <TabContent setting={setting} />
                                {children}
                            </>
                        ),
                    }))}
                    variant={DefinitionList.VARIANTS.tabs}
                />
                <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                    <Button
                        color="inherit"
                        onClick={() => setIsFormOpen(false)}>
                        {translate('close')}
                    </Button>
                </Stack>
            </Drawer>
        </div>
    );
}

ProjectWizardLinkOptions.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardLinkOptions);
