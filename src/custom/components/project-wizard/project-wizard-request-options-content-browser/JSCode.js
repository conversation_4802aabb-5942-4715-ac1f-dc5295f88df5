import PubSub from 'pubsub-js';
import {But<PERSON>, Stack} from '@material-ui/core';
import {REQUEST_WAIT_UNTIL} from '@w7-3/webeagle-resources/dist/config/request';
import {useState} from 'react';
import PropTypes from 'prop-types';
import SubmitButton from '../../utils/SubmitButton';
import CodeTextFieldJS from '../../forms/CodeTextFieldJS';
import useLocales from '../../../../hooks/useLocales';

const i18nContext = 'project.wizardSteps.requestOptions.groups.browser';

const propTypes = {
    id: PropTypes.string,
    jsCode: PropTypes.object,
    setJSCode: PropTypes.func,
};

const JSCode = (props) => {
    const {translate} = useLocales();
    const [jsCode, setJsCode] = useState(props.jsCode);

    return (
        <Stack spacing={3}>
            <CodeTextFieldJS
                label={translate(`${i18nContext}.fields.waitUntil.options.${REQUEST_WAIT_UNTIL.js}.code.label`)}
                script={jsCode}
                onChange={setJsCode}
                prePlaceholder="function getIsPageLoaded() {"
                placeholder={[
                    `/* // ${translate('exampleCode')}`,
                    '/* var targetElement = document.querySelectorAll(".target");',
                    ' *',
                    ' *  return targetElement !== null;',
                    ' */',
                ].join('\n')}
                postPlaceholder="}"
                modifier="bool"
                isFunctionContext
                showReset
                editorProps={{
                    maxWidth: '100%',
                }}
            />
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={() => {
                    PubSub.publish('HIDE.DIALOG', {id: props.id});
                }}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        props.setJSCode(jsCode);
                        PubSub.publish('HIDE.DIALOG', {id: props.id});
                    }}
                    isValid={jsCode?.isValid && jsCode?.isValidated}
                />
            </Stack>
        </Stack>
    );
};

JSCode.propTypes = propTypes;

export default JSCode;
