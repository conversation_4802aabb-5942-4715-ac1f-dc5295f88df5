import PropTypes from 'prop-types';
import {useState} from 'react';
import {<PERSON><PERSON>, Checkbox, Divider, FormControlLabel, FormGroup, Stack, Typography} from '@material-ui/core';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    values: PropTypes.object,
    onChange: PropTypes.func,
};

const ProjectWizardLogLevels = ({values, onChange}) => {
    const {translate} = useLocales();
    const [logLevels, setLogLevels] = useState(values || {});

    return (
        <FormGroup>
            {
                Object.values(INFO_CODE_LEVELS).map((item) => {
                    if (INFO_CODE_LEVELS.DEBUG === item) {
                        return null;
                    }

                    const info = translate([`logs.type.options.${item}.info`, 'void']);

                    return (
                        <Stack
                            key={item}
                            spacing={1}
                        >
                            <FormControlLabel
                                control={<Checkbox
                                    checked={item === INFO_CODE_LEVELS.ERROR || logLevels[item]}
                                    onChange={(_, value) => {
                                        const newLogLevels = {
                                            ...logLevels,
                                            [item]: value,
                                        };
                                        setLogLevels(newLogLevels);

                                        onChange(newLogLevels);
                                    }}
                                />}
                                label={(
                                    <Typography>
                                        {translate(`logs.type.options.${item}.label`)}
                                    </Typography>
                                )}
                                disabled={item === INFO_CODE_LEVELS.ERROR}
                            />
                            <Typography variant="caption" sx={{color: 'text.secondary'}}>
                                {translate(`logs.type.options.${item}.description`)}
                            </Typography>
                            {
                                info && (
                                    <Alert severity="info">
                                        {info}
                                    </Alert>
                                )
                            }
                            <Divider sx={{my: 1}}/>
                        </Stack>
                    );
                })
            }
        </FormGroup>
    );
};

ProjectWizardLogLevels.propTypes = propTypes;

export default ProjectWizardLogLevels;
