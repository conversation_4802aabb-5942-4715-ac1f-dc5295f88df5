import {useState} from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {Box, Card, Grid, IconButton, MenuItem} from '@material-ui/core';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {useProject} from '../../hooks/useProject';
import useLocales from '../../../hooks/useLocales';
import MenuPopover from '../../../components/MenuPopover';
import ProjectPreviewTarget from './project-preview/ProjectPreviewTarget';
import ProjectPreviewSummary from './project-preview/ProjectPreviewSummary';
import ProjectPreviewHeader from './project-preview/ProjectPreviewHeader';
import ProjectSubscriptionInfo from '../subscriptions/ProjectSubscriptionInfo';
import ProjectPreviewLinkOptions from './project-preview/ProjectPreviewLinkOptions';
import ProjectPreviewRequestOptions from './project-preview/ProjectPreviewRequestOptions';
import ProjectPreviewSolutionsConfigs from './project-preview/ProjectPreviewSolutionsConfigs';
import ProjectPreviewSolutionSelection from './project-preview/ProjectPreviewSolutionSelection';
import LoadingScreen from '../../../components/LoadingScreen';

const propTypes = {
    configData: PropTypes.object,
    projectId: PropTypes.string,
    isEditable: PropTypes.bool,
};

const defaultProps = {
    isEditable: true,
};

export const mapStateToProps = ({projectAssistant}, {configData}) => {
    if (configData) {
        return {
            configData,
        };
    }

    return {
        configData: projectAssistant.configData,
    };
};

const ProjectWizardPreview = ({
    configData,
    projectId,
    isEditable,
}) => {
    const {translate} = useLocales();
    const [openMenu, updateMenu] = useState({
        open: false,
    });
    const {goToProjectEdit} = useProject();

    if (!configData) {
        return (
            <Box sx={{m: 'auto', textAlign: 'center'}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const solution = translate(`project.wizardSteps.solutionsConfigs.groups.${configData.solution.appName}.label`);
    const setOpenMenu = isEditable ? updateMenu : undefined;

    return (
        <>
            <Grid container spacing={3} sx={{
                '& .MuiGrid-item > *': {
                    height: '100%',
                },
            }}>
                {
                    isEditable && (
                        <Grid item xs={12}>
                            <Card>
                                <ProjectSubscriptionInfo
                                    projectId={projectId}
                                    subscriptionId={configData.subscriptionId}
                                />
                            </Card>
                        </Grid>
                    )
                }
                <Grid item xs={12} sm={6}>
                    <ProjectPreviewHeader
                        title={translate('project.name.label')}
                        {...{
                            configData,
                            projectId,
                            setOpenMenu,
                        }}
                    />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <ProjectPreviewSolutionSelection
                        title={translate('project.wizardSteps.solutionSelection.previewLabel')}
                        {...{
                            configData,
                            projectId,
                            setOpenMenu,
                        }}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Card sx={{p: 3}}>
                        <ProjectPreviewTarget
                            title={translate('project.wizardSteps.target.previewLabel')}
                            {...{
                                configData,
                                projectId,
                                setOpenMenu,
                            }}
                        />
                    </Card>
                </Grid>
                {
                    configData.solution.key !== solutions.linkChecker.appName && (
                        <>
                            <Grid item xs={12} lg={6}>
                                <Card sx={{p: 3}}>
                                    <ProjectPreviewLinkOptions
                                        title={translate('project.wizardSteps.linkOptions.previewLabel', {solution})}
                                        {...{
                                            configData,
                                            projectId,
                                            setOpenMenu,
                                        }}
                                    />
                                </Card>
                            </Grid>
                            <Grid item xs={12} lg={6}>
                                <Card sx={{p: 3}}>
                                    <ProjectPreviewRequestOptions
                                        title={translate('project.wizardSteps.requestOptions.previewLabel', {solution})}
                                        {...{
                                            configData,
                                            projectId,
                                            setOpenMenu,
                                        }}
                                    />
                                </Card>
                            </Grid>
                            <Grid item xs={12} lg={6}>
                                <ProjectPreviewSolutionsConfigs
                                    title={translate('project.wizardSteps.solutionsConfigs.previewLabel', {solution})}
                                    {...{
                                        configData,
                                        projectId,
                                        setOpenMenu,
                                    }}
                                />
                            </Grid>
                        </>
                    )
                }
                <Grid item xs={12} lg={6}>
                    <Card sx={{p: 3}}>
                        <ProjectPreviewSummary
                            title={translate('project.wizardSteps.summary.label')}
                            {...{
                                configData,
                                projectId,
                                setOpenMenu,
                            }}
                        />
                    </Card>
                </Grid>
            </Grid>
            <MenuPopover
                sx={{width: 'auto'}}
                open={openMenu?.open}
                onClose={() => setOpenMenu({
                    ...openMenu,
                    open: false,
                })}
                anchorEl={openMenu?.ref?.current}>
                {
                    [
                        {
                            icon: <Icon icon="eva:link-2-outline" width={40} height={40} sx={{pr: .5}}/>,
                            label: translate([`project.wizardSteps.${openMenu?.selector}.previewLabel`, 'project.edit.label'], {
                                solution,
                            }),
                            onClick: () => {
                                goToProjectEdit({
                                    projectId,
                                    step: openMenu?.selector,
                                });
                            }
                        },
                    ]
                        .map(({icon, label, onClick, ...rest}) => (
                            <MenuItem
                                key={label}
                                onClick={() => {
                                    setOpenMenu({});
                                    onClick?.();
                                }}
                                sx={{typography: 'body2', py: 1, px: 2.5}}
                                {...rest}>
                                <IconButton
                                    color="primary"
                                >
                                    {icon}
                                </IconButton>
                                {label}
                            </MenuItem>
                        ))}
            </MenuPopover>
        </>
    );
};

ProjectWizardPreview.defaultProps = defaultProps;

ProjectWizardPreview.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardPreview);

