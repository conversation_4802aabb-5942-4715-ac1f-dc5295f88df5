import {<PERSON>, <PERSON><PERSON>eader, Icon<PERSON>utton, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import {useRef} from 'react';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {getDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../../hooks/useLocales';
import DefinitionList from '../../utils/DefinitionList';

const propTypes = {
    configData: PropTypes.object,
    title: PropTypes.string,
    projectId: PropTypes.string,
    setOpenMenu: PropTypes.func,
};

const ProjectPreviewRequestOptions = ({
    configData,
    title,
    projectId,
    setOpenMenu,
}) => {
    const {translate, allLang} = useLocales();
    const ref = useRef(null);
    const browserLanguage = configData?.requestOptions?.browser?.language;
    const requestTimeout = configData?.requestOptions?.browser?.linkRequestTimeout;
    const cookies = configData?.requestOptions?.storage?.requestCookies || [];
    const sessionStorageList = configData?.requestOptions?.storage?.sessionStorage || [];
    const localStorageList = configData?.requestOptions?.storage?.localStorage || [];
    const requestHeaders = configData?.requestOptions?.resources?.requestHeaders || [];
    const requestParameters = configData?.requestOptions?.resources?.requestParameters || [];
    const resourcesInjection = configData?.requestOptions?.resources?.resourcesInjection || [];
    const requestBlockers = configData?.requestOptions?.resources?.requestBlockers || [];
    const requestMocks = configData?.requestOptions?.resources?.requestMocks || [];
    const requestOverrides = configData?.requestOptions?.resources?.requestOverrides || [];
    const responseOverrides = configData?.requestOptions?.resources?.responseOverrides || [];
    const showMenu = Boolean(projectId) && typeof setOpenMenu === 'function';
    return (
        <>
            {
                (isNonEmptyString(title) || showMenu) && (
                    <CardHeader
                        sx={{p: 0, pb: 3}}
                        title={title}
                        action={
                            Boolean(projectId) && typeof setOpenMenu === 'function' &&
                            <IconButton
                                onClick={() => setOpenMenu({
                                    open: true,
                                    selector: steps.requestOptions.key,
                                    ref,
                                })}
                                ref={ref}
                                sx={{ml: 1}}
                                size="large"
                            >
                                <Icon icon="eva:more-vertical-outline" sx={{width: 40, height: 40}} />
                            </IconButton>
                        }
                    />
                )
            }
            <DefinitionList dataList={[
                getOptionalMap(browserLanguage, {
                    key: translate('project.wizardSteps.requestOptions.groups.browser.fields.language.label'),
                    node: (
                        allLang.filter(({value}) => value === browserLanguage).map((option) => (
                            <Stack direction="row" spacing={1} key={option.value}>
                                <Box component="img" alt={option.label} src={option.icon}/>
                                <Typography variant="body2" sx={{mr: 1}}>
                                    {option.label}
                                </Typography>
                            </Stack>
                        ))
                    ),
                }),
                getOptionalMap(requestTimeout, {
                    key: translate('project.wizardSteps.requestOptions.groups.browser.fields.linkRequestTimeout.label'),
                    node: (
                        getDuration(requestTimeout)
                    ),
                }),
                getOptionalMap(
                    isNonEmptyArray(cookies) ||
                    isNonEmptyArray(sessionStorageList) ||
                    isNonEmptyArray(localStorageList), {
                        key: translate('project.wizardSteps.requestOptions.groups.storage.label'),
                        node: (
                            <div>
                                {[
                                    [cookies, 'requestCookies'],
                                    [sessionStorageList, 'sessionStorage'],
                                    [localStorageList, 'localStorage'],
                                ].map(([list, key]) => (
                                    isNonEmptyArray(list) && (
                                        <div key={key}>
                                            {translate(`project.wizardSteps.requestOptions.groups.storage.fields.${key}.title`)}
                                            {` (${cookies.length})`}
                                        </div>
                                    )
                                ))}
                            </div>
                        ),
                    }),
                getOptionalMap(
                    isNonEmptyArray(requestHeaders) ||
                    isNonEmptyArray(requestParameters) ||
                    isNonEmptyArray(resourcesInjection) ||
                    isNonEmptyArray(requestBlockers) ||
                    isNonEmptyArray(requestMocks) ||
                    isNonEmptyArray(requestOverrides) ||
                    isNonEmptyArray(responseOverrides), {
                        key: translate('project.wizardSteps.requestOptions.groups.resources.label'),
                        node: (
                            <div>
                                {[
                                    [requestHeaders, 'requestHeaders'],
                                    [requestParameters, 'requestParameters'],
                                    [resourcesInjection, 'resourcesInjection'],
                                    [requestBlockers, 'requestBlockers'],
                                    [requestMocks, 'requestMocks'],
                                    [requestOverrides, 'requestOverrides'],
                                    [responseOverrides, 'responseOverrides'],
                                ].map(([list, key]) => (
                                    isNonEmptyArray(list) && (
                                        <div key={key}>
                                            {translate(`project.wizardSteps.requestOptions.groups.resources.fields.${key}.title`)}
                                            {` (${list.length})`}
                                        </div>
                                    )
                                ))}
                            </div>
                        ),
                    }),
            ]}/>
        </>
    );
};

ProjectPreviewRequestOptions.propTypes = propTypes;

export default ProjectPreviewRequestOptions;
