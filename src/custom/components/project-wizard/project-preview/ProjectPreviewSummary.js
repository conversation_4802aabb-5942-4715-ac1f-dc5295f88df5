import {<PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>on, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import {useRef} from 'react';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../../hooks/useLocales';
import DefinitionList from '../../utils/DefinitionList';
import ItemLabelStatus from '../../utils/ItemLabelStatus';
import ProjectBuild from '../../project/ProjectBuild';

const propTypes = {
    configData: PropTypes.object,
    title: PropTypes.string,
    projectId: PropTypes.string,
    setOpenMenu: PropTypes.func,
};

const ProjectPreviewSummary = ({
    configData,
    title,
    projectId,
    setOpenMenu,
}) => {
    const {translate} = useLocales();
    const ref = useRef(null);
    const showMenu = Boolean(projectId) && typeof setOpenMenu === 'function';

    return (
        <>
            {
                (isNonEmptyString(title) || showMenu) && (
                    <CardHeader
                        sx={{p: 0, pb: 3}}
                        title={title}
                        action={
                            Boolean(projectId) && typeof setOpenMenu === 'function' &&
                            <IconButton
                                onClick={() => setOpenMenu({
                                    open: true,
                                    selector: steps.summary.key,
                                    ref,
                                })}
                                ref={ref}
                                sx={{ml: 1}}
                                size="large"
                            >
                                <Icon icon="eva:more-vertical-outline" sx={{width: 40, height: 40}} />
                            </IconButton>
                        }
                    />
                )
            }
            <DefinitionList dataList={[
                {
                    key: translate('project.wizardSteps.summary.fields.notifications.label'),
                    node: (
                        <Stack
                            spacing={1}
                            flexWrap="wrap"
                        >
                            {
                                Object.keys(configData.summary.notifications).map((notification) => {
                                    return (
                                        <Stack
                                            key={notification}
                                            spacing={1}>
                                            <ItemLabelStatus
                                                item={{
                                                    title: translate(`products.notificationProducts.items.${notification}Notification.label`),
                                                    active: configData.summary.notifications[notification],
                                                }}
                                                sx={{pt: 1, pr: 1}}
                                            />
                                            {
                                                configData.summary.notifications[notification] && (
                                                    <>
                                                        {
                                                            notification === 'email' && (
                                                                <Typography
                                                                    variant="body2"
                                                                    component="div"
                                                                    sx={{
                                                                        pl: 2,
                                                                    }}
                                                                >
                                                                    {configData.summary.notificationsData[notification].main}
                                                                </Typography>
                                                            )
                                                        }
                                                        {
                                                            notification === 'webhooks' && (
                                                                <Typography
                                                                    variant="body2"
                                                                    component="div"
                                                                    sx={{
                                                                        pl: 2,
                                                                    }}
                                                                >
                                                                    {configData.summary.notificationsData[notification].url}
                                                                </Typography>
                                                            )
                                                        }
                                                    </>
                                                )
                                            }
                                        </Stack>
                                    );
                                })
                            }
                        </Stack>
                    ),
                },
                getOptionalMap(Boolean(configData.summary.activateScheduler), {
                    key: translate('project.wizardSteps.summary.fields.scheduler.label'),
                    node: (
                        <Box sx={{
                            mx: (theme) => `-${theme.spacing(2)} !important;`,
                            mt: (theme) => `-${theme.spacing(1)} !important;`,
                        }}>
                            <ProjectBuild
                                project={{configData}}
                                isPreview
                            />
                        </Box>
                    ),
                }),
                {
                    key: translate('project.wizardSteps.summary.fields.logs.label'),
                    node: (
                        <Stack
                            spacing={1}
                            flexWrap="wrap"
                        >
                            {
                                Object.keys(configData.summary.logLevels).map((logLevel) => {
                                    return (
                                        <ItemLabelStatus
                                            key={logLevel}
                                            item={{
                                                title: translate(`logs.type.options.${logLevel}.description`),
                                                active: configData.summary.logLevels[logLevel],
                                            }}
                                            sx={{pt: 1, pr: 1}}
                                        />
                                    );
                                })
                            }
                        </Stack>
                    ),
                },
            ]}/>
        </>
    );
};

ProjectPreviewSummary.propTypes = propTypes;

export default ProjectPreviewSummary;
