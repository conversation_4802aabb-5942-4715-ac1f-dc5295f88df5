import {Card, CardHeader, IconButton} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import {useRef} from 'react';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../../hooks/useLocales';

const propTypes = {
    configData: PropTypes.object,
    title: PropTypes.string,
    projectId: PropTypes.string,
    setOpenMenu: PropTypes.func,
};

const ProjectPreviewSolutionSelection = ({
    configData,
    title,
    projectId,
    setOpenMenu,
}) => {
    const {translate} = useLocales();
    const ref = useRef(null);
    const solution = translate(`project.wizardSteps.solutionsConfigs.groups.${configData.solution.key}.label`);
    const showMenu = Boolean(projectId) && typeof setOpenMenu === 'function';

    return (
        <Card sx={{p: 3}}>
            {
                (isNonEmptyString(title) || showMenu) && (
                    <CardHeader
                        sx={{p: 0, pb: 3}}
                        title={title}
                        action={
                            Boolean(projectId) && typeof setOpenMenu === 'function' &&
                            <IconButton
                                size="large"
                                onClick={() => setOpenMenu({
                                    open: true,
                                    selector: steps.solutionSelection.key,
                                    ref,
                                })}
                                ref={ref}
                                sx={{ml: 1}}>
                                <Icon icon="eva:more-vertical-outline" sx={{width: 40, height: 40}} />
                            </IconButton>
                        }
                    />
                )
            }
            {solution}
        </Card>
    );
};

ProjectPreviewSolutionSelection.propTypes = propTypes;

export default ProjectPreviewSolutionSelection;
