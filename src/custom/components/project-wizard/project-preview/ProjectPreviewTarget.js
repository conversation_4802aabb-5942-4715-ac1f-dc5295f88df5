import PubSub from 'pubsub-js';
import {Box, Button, CardHeader, IconButton, Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import {useRef} from 'react';
import {LIGHTHOUSE_DEVICES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../../hooks/useLocales';
import DefinitionList from '../../utils/DefinitionList';
import ExternalLink from '../../utils/ExternalLink';
import ItemLabelStatus from '../../utils/ItemLabelStatus';
import DevicePreview from '../../forms/DevicePreview';
import getProjectFieldDisplayManager from '../../../utils/getProjectFieldDisplayManager';

const propTypes = {
    configData: PropTypes.object,
    title: PropTypes.string,
    projectId: PropTypes.string,
    setOpenMenu: PropTypes.func,
};

const ProjectPreviewTarget = ({
    configData,
    title,
    projectId,
    setOpenMenu,
}) => {
    const {translate} = useLocales();
    const ref = useRef(null);
    const isLighthouseProject = configData.solution.key === solutions.lighthouse.appName;
    const accessManager = getProjectFieldDisplayManager({translate});
    const showMenu = Boolean(projectId) && typeof setOpenMenu === 'function';

    return (
        <>
            {
                (isNonEmptyString(title) || showMenu) && (
                    <CardHeader
                        sx={{p: 0, pb: 3}}
                        title={title}
                        action={
                            showMenu &&
                            <IconButton
                                onClick={() => setOpenMenu({
                                    open: true,
                                    selector: steps.target.key,
                                    ref,
                                })}
                                ref={ref}
                                sx={{ml: 1}}
                                size="large"
                            >
                                <Icon icon="eva:more-vertical-outline" sx={{width: 40, height: 40}} />
                            </IconButton>
                        }
                    />
                )
            }
            <DefinitionList dataList={[
                {
                    key: translate(`project.wizardSteps.target.checkType.groups.${configData.target.checkType}.label`),
                    node: (
                        <Stack mt={1}>
                            {
                                Object.values(configData.target.urls)
                                    .map(({url}) => (
                                            <ExternalLink
                                                key={url}
                                                url={url}
                                                rootProps={{
                                                    flexWrap: 'wrap',
                                                }}
                                            />
                                        ),
                                    )
                            }
                        </Stack>
                    ),
                },
                {
                    key: translate('project.wizardSteps.target.browser.label'),
                    node: translate(`project.wizardSteps.target.browser.groups.${configData.target.browser.key}.label`),
                },
                getOptionalMap(!isLighthouseProject, {
                    key: translate('project.wizardSteps.target.executionStrategy.label'),
                    node: translate(`project.wizardSteps.target.executionStrategy.groups.${configData.target.executionStrategy}.label`),
                }),
                getOptionalMap(isLighthouseProject, {
                    key: translate('project.wizardSteps.target.device.label'),
                    node: (
                        <Stack spacing={1} direction="row" alignItems="center" flexWrap="wrap">
                            {
                                Object.values(LIGHTHOUSE_DEVICES).map(({key}) => {
                                    return (
                                        <ItemLabelStatus
                                            key={key}
                                            item={{
                                                title: translate(`project.wizardSteps.target.device.lighthouse.targets.${key}`),
                                                active: configData.target.lighthouseDevices.includes(key),
                                            }}
                                        />
                                    );
                                })
                            }
                        </Stack>
                    ),
                }),
                getOptionalMap(!isLighthouseProject, {
                    key: translate('project.wizardSteps.target.device.label'),
                    node: (
                        <Box>
                            <Button
                                size="large"
                                variant="outlined"
                                onClick={() => {
                                    PubSub.publish('SHOW.DIALOG', {
                                        type: 'custom',
                                        dialogProps: {
                                            title: configData.target?.device?.name,
                                        },
                                        children: (
                                            <DevicePreview deviceData={configData.target.device} />
                                        ),
                                    });
                                }}>
                                {accessManager.device.q({configData})}
                            </Button>
                        </Box>
                    ),
                }),
            ]}/>
        </>
    );
};

ProjectPreviewTarget.propTypes = propTypes;

export default ProjectPreviewTarget;
