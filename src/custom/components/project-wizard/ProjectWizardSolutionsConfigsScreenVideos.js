import {connect} from 'react-redux';
import {useState} from 'react';
import PropTypes from 'prop-types';
import {Stack} from '@material-ui/core';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {updateSolutionConfig} from '../../../redux/projectAssistant/store';
import ProjectSolutionStepBlock from '../forms/ProjectSolutionStepBlock';
import TimeoutSlider from '../utils/TimeoutSlider';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.screenVideos';

const propTypes = {
    config: PropTypes.shape({
        stepList: PropTypes.array,
        stepDelay: PropTypes.number,
    }),
};

const mapStateToProps = ({projectAssistant}) => {
    const {
        solution: {
            config,
        },
    } = projectAssistant.configData;

    return {
        config,
    };
};

const ProjectWizardSolutionsConfigsScreenVideos = ({config}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const [stepList, updateStepList] = useState(config?.stepList || []);
    const [stepDelay, setVideoStepDelay] = useState(config?.stepDelay || 300);
    const save = ({stepList}) => {
        dispatch(updateSolutionConfig({
            stepList,
            stepDelay,
        }));
        updateStepList(stepList);
    };

    return (
        <Stack
            spacing={3}
            justifyContent="space-between"
            sx={{width: '100%'}}
        >
            <TimeoutSlider
                value={stepDelay}
                onChange={(stepDelay) => {
                    dispatch(updateSolutionConfig({
                        stepList,
                        stepDelay,
                    }));
                    setVideoStepDelay(stepDelay);
                }}
                label={translate('workflowSteps.delay')}
            />
            <ProjectSolutionStepBlock
                onChange={save}
                value={stepList}
                labels={{
                    abortion: translate(`${i18nContext}.failStopLabel`),
                }}
                actionItemRendererProps={{
                    showAbortOnError: true,
                    showIgnoreOnError: false,
                }}
            />
        </Stack>
    );
};

ProjectWizardSolutionsConfigsScreenVideos.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardSolutionsConfigsScreenVideos);
