import {connect} from 'react-redux';
import {useEffect} from 'react';
import PropTypes from 'prop-types';
import {Controller, useForm} from 'react-hook-form';
import {
    Alert,
    Box,
    Button,
    Divider,
    FormControl,
    FormControlLabel,
    FormGroup,
    Radio,
    RadioGroup,
    Slider,
    Stack,
    Switch,
    Typography,
} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {getDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import {MAX_LINK_REQUEST_TIMEOUT} from '@w7-3/webeagle-resources/dist/config/misc';
import {REQUEST_WAIT_UNTIL} from '@w7-3/webeagle-resources/dist/config/request';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {setRequestOptions} from '../../../redux/projectAssistant/store';
import LanguageChange from '../LanguageChange';
import LabelStyle from '../utils/LabelStyle';
import CodeTextFieldJS from '../forms/CodeTextFieldJS';
import JSCode from './project-wizard-request-options-content-browser/JSCode';

const i18nContext = 'project.wizardSteps.requestOptions.groups.browser';
const MIN_LINK_REQUEST_TIMEOUT = 500;
const REQUEST_TIMEOUT_STEP = 500;
const mapStateToProps = ({projectAssistant}) => {
    const {
        requestOptions: {
            browser,
        },
    } = projectAssistant.configData;

    return {
        browser,
    };
};

const propTypes = {
    browser: PropTypes.object,
};

const ProjectWizardRequestOptionsContentBrowser = ({browser}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const {
        control,
        watch,
        setValue,
    } = useForm({
        mode: 'onChange',
        defaultValues: browser,
    });

    useEffect(() => {
        return () => {
            const browser = watch();
            dispatch(setRequestOptions({
                browser: {
                    ...browser,
                },
            }));
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    const linkRequestTimeout = watch('linkRequestTimeout');
    const waitUntil = watch('waitUntil');

    const editJSCode = () => {
        const id = getRandomString(16, libraryKeys.alphaNumericLowerCased);
        PubSub.publish('SHOW.DIALOG', {
            id,
            type: 'custom',
            hideActions: true,
            dialogProps: {
                title: translate(`${i18nContext}.fields.waitUntil.options.${REQUEST_WAIT_UNTIL.js}.code.description`),
            },
            children: (
                <Controller
                    name={`${'waitUntil'}.jsCode`}
                    control={control}
                    render={(jsCode) => {
                        return (
                            <JSCode
                                id={id}
                                jsCode={jsCode.field.value}
                                setJSCode={(value) => {
                                    setValue(`${'waitUntil'}.option`, REQUEST_WAIT_UNTIL.js);
                                    jsCode.field.onChange(value);
                                }}
                            />
                        );
                    }}
                />
            ),
        });
    };

    return (
        <Stack spacing={3}>
            <LabelStyle>{translate(`${i18nContext}.fields.language.label`)}</LabelStyle>
            <Controller
                name="language"
                control={control}
                render={({field}) => {
                    return (
                        <LanguageChange
                            language={field.value}
                            onChange={field.onChange}
                        />
                    );
                }}
            />
            <Divider sx={{my: 3}} />
            <LabelStyle>{translate(`${i18nContext}.fields.${'deactivateJS'}.label`)}</LabelStyle>
            <Stack spacing={3}>
                <Typography component="p" variant="heading" sx={{mb: 2}}>
                    {translate(`${i18nContext}.fields.${'deactivateJS'}.description`)}
                </Typography>
                <FormGroup row>
                    <Controller
                        name="deactivateJS"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={translate(`${i18nContext}.fields.${'deactivateJS'}.label`)}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                </FormGroup>
            </Stack>
            <Divider sx={{my: 3}} />
            <LabelStyle>{translate(`${i18nContext}.fields.waitUntil.label`)}</LabelStyle>
            <Stack spacing={3} sx={{px: 3}}>
                <Typography>
                    {translate(`${i18nContext}.fields.waitUntil.description`)}
                </Typography>
                <Controller
                    name={`${'waitUntil'}.option`}
                    control={control}
                    render={(option) => {
                        return (
                            <FormControl component="fieldset">
                                <RadioGroup
                                    value={option.field.value}
                                    onChange={(_, value) => {
                                        if (value === REQUEST_WAIT_UNTIL.js && !waitUntil?.jsCode) {
                                            return;
                                        }

                                        option.field.onChange(value);
                                    }}>
                                    {
                                        Object.values(REQUEST_WAIT_UNTIL).map((value) => {
                                            if (value === REQUEST_WAIT_UNTIL.js) {
                                                return null;

                                            }
                                            return (
                                                <FormControlLabel
                                                    key={value}
                                                    value={value}
                                                    control={<Radio/>}
                                                    label={translate(`${i18nContext}.fields.waitUntil.options.${value}.label`)}
                                                />
                                            );
                                        })
                                    }
                                    <FormControlLabel
                                        value={REQUEST_WAIT_UNTIL.js}
                                        control={<Radio onChange={() => {
                                            if (waitUntil?.jsCode) {
                                                return;
                                            }

                                            editJSCode();
                                        }}/>}
                                        label={translate(`${i18nContext}.fields.waitUntil.options.${REQUEST_WAIT_UNTIL.js}.label`)}
                                    />
                                    {
                                        option.field.value === REQUEST_WAIT_UNTIL.js && waitUntil?.jsCode && (
                                            <>
                                                <CodeTextFieldJS
                                                    script={waitUntil.jsCode}
                                                    prePlaceholder="function getIsPageLoaded() {"
                                                    postPlaceholder="}"
                                                    modifier="bool"
                                                    isFunctionContext
                                                    readOnly
                                                />
                                                <Box sx={{display: 'flex', justifyContent: 'flex-end', mt: 1}}>
                                                    <Button
                                                        variant="outlined"
                                                        onClick={editJSCode}>
                                                        {translate(`${i18nContext}.fields.waitUntil.options.${REQUEST_WAIT_UNTIL.js}.code.edit`)}
                                                    </Button>
                                                </Box>
                                            </>
                                        )
                                    }
                                </RadioGroup>
                            </FormControl>
                        );
                    }}
                />
            </Stack>
            <Divider sx={{my: 3}} />
            <LabelStyle>{translate(`${i18nContext}.fields.linkRequestTimeout.label`)}</LabelStyle>
            <Stack spacing={3} sx={{px: 3}}>
                <Slider
                    size="medium"
                    marks={[
                        {value: MIN_LINK_REQUEST_TIMEOUT, label: getDuration(MIN_LINK_REQUEST_TIMEOUT)},
                        {value: MAX_LINK_REQUEST_TIMEOUT / 3, label: getDuration(MAX_LINK_REQUEST_TIMEOUT / 3)},
                        {value: MAX_LINK_REQUEST_TIMEOUT / 2, label: getDuration(MAX_LINK_REQUEST_TIMEOUT / 2)},
                        {value: MAX_LINK_REQUEST_TIMEOUT, label: getDuration(MAX_LINK_REQUEST_TIMEOUT)},
                    ]}
                    value={linkRequestTimeout}
                    min={MIN_LINK_REQUEST_TIMEOUT}
                    step={REQUEST_TIMEOUT_STEP}
                    max={MAX_LINK_REQUEST_TIMEOUT}
                    valueLabelDisplay="auto"
                    getAriaValueText={getDuration}
                    valueLabelFormat={getDuration}
                    onChange={(_, value) => {
                        setValue('linkRequestTimeout', value);
                    }}
                />
                <Alert severity="info">
                    <Typography>
                        {translate(`${i18nContext}.fields.linkRequestTimeout.description`, {
                            duration: getDuration(linkRequestTimeout),
                        })}
                    </Typography>
                </Alert>
            </Stack>
        </Stack>
    );
};

ProjectWizardRequestOptionsContentBrowser.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardRequestOptionsContentBrowser);
