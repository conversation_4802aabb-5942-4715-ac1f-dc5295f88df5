import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Box, IconButton, Stack, Typography} from '@material-ui/core';
import {useState} from 'react';
import {Icon} from '@iconify/react';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import ProjectWizardHeaderName from './project-wizard-header/ProjectWizardHeaderName';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {setProjectName} from '../../../redux/projectAssistant/store';
import ProjectWizardHeaderOptions from './project-wizard-header/ProjectWizardHeaderOptions';

const mapStateToProps = ({projectAssistant}) => {
    const {
        configData,
        action,
    } = projectAssistant;
    return {
        action,
        projectName: configData.projectName,
    };
};

const propTypes = {
    projectName: PropTypes.string,
};

const ProjectWizardHeader = ({projectName}) => {
    const {translate} = useLocales();
    const [isEditMode, setIsEditMode] = useState(false);
    const dispatch = useDispatch();

    return (
        <Box sx={{mt: 5}} id={steps.void.key}>
            <Typography
                variant="subtitle1"
                paragraph>
                {translate('project.name.label')}
            </Typography>
            <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                spacing={3}>
                <Typography sx={{display: 'flex', alignItems: 'center'}}>
                    {projectName}
                    <IconButton
                        sx={{ml: 1}}
                        onClick={() => {
                            setIsEditMode(true);
                        }}
                        edge="end"
                        color="primary"
                    >
                        <Icon icon="eva:edit-outline" width={40} height={40}/>
                    </IconButton>
                </Typography>
                <ProjectWizardHeaderOptions />
            </Stack>
            {
                isEditMode && (
                    <ProjectWizardHeaderName
                        projectName={projectName}
                        handleClose={({value}) => {
                            setIsEditMode(false);
                            dispatch(setProjectName(value));
                        }}
                    />
                )
            }
        </Box>
    );
};

ProjectWizardHeader.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardHeader);
