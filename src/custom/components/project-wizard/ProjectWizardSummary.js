import {useState} from 'react';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Button,
    Divider,
    FormControlLabel,
    FormGroup,
    Stack,
    Switch,
    TextField,
    Typography,
} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import {Controller, useForm} from 'react-hook-form';
import * as subscriptions from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import DateTimePicker from '@material-ui/lab/DateTimePicker';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import getOptionalList from '@w7-3/webeagle-resources/dist/libs/getOptionalList';
import Drawer from '../forms/Drawer';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {updateSummary} from '../../../redux/projectAssistant/store';
import ProjectSchedulerRandom from '../forms/ProjectSchedulerRandom';
import ProjectSchedulerCron from '../forms/ProjectSchedulerCron';
import LabelStyle from '../utils/LabelStyle';
import DefinitionList from '../utils/DefinitionList';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import ProjectSubscriptionInfo from '../subscriptions/ProjectSubscriptionInfo';
import ProjectNotificationWebhookDialogForm from '../forms/ProjectNotificationWebhookDialogForm';
import ProjectNotificationEmailDialogForm from '../forms/ProjectNotificationEmailDialogForm';
import ProjectWizardLogLevels from './ProjectWizardLogLevels';
import ProjectWizardTargetPreview from './project-wizard-target/ProjectWizardTargetPreview';
import ProjectPreviewSummary from './project-preview/ProjectPreviewSummary';
import SettingItemChange from '../utils/SettingItemChange';

const i18nContext = 'project.wizardSteps.summary';

const FORM_FIELDS = {
    schedulerData: 'schedulerData',
    notificationsData: 'notificationsData',
    activateScheduler: 'activateScheduler',
    notifications: 'notifications',
    schedulerStartDate: 'schedulerStartDate',
    schedulerEndDate: 'schedulerEndDate',
    logLevels: 'logLevels',
};

const notificationRendererList = [
    {type: subscriptions.packages.notifications.email.appName, Component: ProjectNotificationEmailDialogForm},
    {type: subscriptions.packages.notifications.webhooks.appName, Component: ProjectNotificationWebhookDialogForm},
];
const availableNotificationList = notificationRendererList.map(({type}) => type);
const mapStateToProps = ({projectAssistant, state}) => {
    const {
        transactionList,
    } = state;
    const {
        configData,
    } = projectAssistant;
    const subscription = isNonEmptyString(configData.subscriptionId) && transactionList?.find(({id}) => {
        return id === configData.subscriptionId;
    });
    const projectNotifications = {};

    availableNotificationList.forEach((notification) => {
        if (!subscription) {
            projectNotifications[notification] = true;
            return;
        }

        projectNotifications[notification] = subscription?.data?.subscriptionSummary?.restContingent?.notifications?.[notification]?.active;
    });

    return {
        configData,
        projectNotifications,
    };
};

const propTypes = {
    configData: PropTypes.object,
    projectNotifications: PropTypes.object,
};

const ProjectWizardSummary = ({configData, projectNotifications}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [nextBuildList, setNextBuildList] = useState([]);
    const [visibleForm, setVisibleForm] = useState();
    const {
        control,
        watch,
        setValue,
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            ...configData.summary,
            [FORM_FIELDS.schedulerStartDate]: Math.max(Date.now(), configData.summary?.[FORM_FIELDS.schedulerStartDate]),
        },
    });
    const {
        [FORM_FIELDS.schedulerStartDate]: startDate,
        [FORM_FIELDS.schedulerEndDate]: endDate,
    } = watch();
    const updateStore = () => {
        const summary = watch();
        dispatch(updateSummary(summary));
    }
    const setTriggerStartDate = (date) => {
        const ts = date.getTime();
        setValue(FORM_FIELDS.schedulerStartDate, ts);

        if (ts >= endDate) {
            setValue(FORM_FIELDS.schedulerEndDate, null);
        }

        setValue(FORM_FIELDS.schedulerData, null);
        setNextBuildList([]);
    };
    const setTriggerEndDate = (date) => {
        setValue(FORM_FIELDS.schedulerEndDate, date.getTime());
    };
    const nextBuild = nextBuildList?.[0];

    return (
        <div>
            <ProjectWizardTargetPreview
                Component={ProjectPreviewSummary}
            />
            <SettingItemChange
                onClick={() => setIsFormOpen(true)}
                sx={{
                    mt: 3,
                    fontSize: '.75em',
                    alignSelf: 'flex-start',
                    iconProps: {
                        width: 20,
                        height: 20,
                    },
                }}
            />
            <Drawer
                open={isFormOpen}
                onClose={() => setIsFormOpen(false)}
                title={(
                    <Typography variant="subtitle1">
                        {translate('project.wizardSteps.solutionSelection.label')}
                    </Typography>
                )}
                isFullScreen
            >
                {
                    configData.subscriptionId && (
                        <Accordion defaultExpanded>
                            <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                                <LabelStyle>
                                    {translate(`${i18nContext}.fields.subscription.label`)}
                                </LabelStyle>
                            </AccordionSummary>
                            <AccordionDetails>
                                <ProjectSubscriptionInfo
                                    projectId={configData.projectId}
                                    subscriptionId={configData.subscriptionId}/>
                            </AccordionDetails>
                        </Accordion>
                    )
                }
                <DefinitionList
                    dataList={[
                        {
                            key: 'logs',
                            label: (
                                <LabelStyle>
                                    {translate(`${i18nContext}.fields.logs.label`)}
                                </LabelStyle>
                            ),
                            node: (
                                <Stack spacing={3}>
                                    <Typography component="p" variant="body2" sx={{mb: 3}}>
                                        {translate(`${i18nContext}.fields.logs.description`)}
                                    </Typography>
                                    <Controller
                                        name={FORM_FIELDS.logLevels}
                                        control={control}
                                        render={({field}) => {
                                            return (
                                                <ProjectWizardLogLevels
                                                    values={field.value}
                                                    onChange={field.onChange}
                                                />
                                            );
                                        }}
                                    />
                                </Stack>
                            ),
                        },
                        {
                            key: 'scheduler',
                            label: (
                                <LabelStyle>
                                    {translate(`${i18nContext}.fields.scheduler.label`)}
                                </LabelStyle>
                            ),
                            node: (
                                <Stack spacing={3}>
                                    <Controller
                                        name={FORM_FIELDS.activateScheduler}
                                        control={control}
                                        render={({field}) => {
                                            return (
                                                <>
                                                    <FormControlLabel
                                                        label={translate(`${i18nContext}.fields.scheduler.activate`)}
                                                        control={
                                                            <Switch
                                                                onChange={(_, value) => {
                                                                    field.onChange(value);
                                                                }}
                                                                checked={field.value}
                                                            />
                                                        }
                                                    />
                                                    {
                                                        field.value &&
                                                        <Stack spacing={3}>
                                                            <Divider/>
                                                            <LabelStyle>
                                                                {translate(`${i18nContext}.fields.scheduler.validity`)}
                                                            </LabelStyle>
                                                            <Stack spacing={{
                                                                xs: 2,
                                                                sm: 3,
                                                            }} direction={{
                                                                xs: 'column',
                                                                sm: 'row',
                                                            }}>
                                                                <Controller
                                                                    name={FORM_FIELDS.schedulerStartDate}
                                                                    control={control}
                                                                    render={({
                                                                        field,
                                                                        fieldState: {error},
                                                                    }) => (
                                                                        <DateTimePicker
                                                                            {...field}
                                                                            minDate={Date.now()}
                                                                            label={translate(`${i18nContext}.fields.scheduler.startDate`)}
                                                                            renderInput={(params) => (
                                                                                <TextField
                                                                                    {...params}
                                                                                    error={Boolean(error?.message)}/>
                                                                            )}
                                                                            inputFormat="dd.MM.yyyy, HH:mm:ss"
                                                                            onChange={setTriggerStartDate}
                                                                        />
                                                                    )}
                                                                />
                                                                <Controller
                                                                    name={FORM_FIELDS.schedulerEndDate}
                                                                    control={control}
                                                                    render={({
                                                                        field,
                                                                        fieldState: {error},
                                                                    }) => (
                                                                        <DateTimePicker
                                                                            {...field}
                                                                            minDate={startDate}
                                                                            label={translate(`${i18nContext}.fields.scheduler.endDate`)}
                                                                            renderInput={(params) => (
                                                                                <TextField
                                                                                    {...params}
                                                                                    error={Boolean(error?.message)}/>
                                                                            )}
                                                                            inputFormat="dd.MM.yyyy, HH:mm:ss"
                                                                            onChange={setTriggerEndDate}
                                                                        />
                                                                    )}
                                                                />
                                                            </Stack>
                                                            <Divider/>
                                                            <LabelStyle>
                                                                {translate(`${i18nContext}.fields.scheduler.buildTime`)}
                                                            </LabelStyle>
                                                            <Controller
                                                                name={FORM_FIELDS.schedulerData}
                                                                control={control}
                                                                render={({field}) => {
                                                                    return (
                                                                        <>
                                                                            <DefinitionList
                                                                                dataList={[{
                                                                                    key: translate(`${i18nContext}.fields.scheduler.configurator.types.label`),
                                                                                    node: (
                                                                                        field.value ? (
                                                                                            <Stack
                                                                                                alignItems="center"
                                                                                                direction="row"
                                                                                                spacing={1}
                                                                                            >
                                                                                                <Box sx={{pl: .125}}>
                                                                                                    {translate(`project.wizardSteps.summary.fields.scheduler.configurator.types.options.${field.value.type}.description`, {
                                                                                                        cronPrompt: field.value?.config?.cronPrompt,
                                                                                                        frequency: field.value?.config?.frequency,
                                                                                                        frequencyQualifier: translate(`project.wizardSteps.summary.fields.scheduler.configurator.types.options.${field.value.type}.frequency.qualifierOptions.${field.value.config.frequencyQualifier}`),
                                                                                                    })}
                                                                                                </Box>
                                                                                                <SettingItemChange
                                                                                                    onClick={() => {
                                                                                                        PubSub.publish(`SHOW_PROJECT_SCHEDULER.${field.value.type}`, field.value);
                                                                                                    }}
                                                                                                    sx={{
                                                                                                        alignSelf: 'flex-start',
                                                                                                        iconProps: {
                                                                                                            width: 20,
                                                                                                            height: 20,
                                                                                                        },
                                                                                                    }}
                                                                                                />
                                                                                            </Stack>
                                                                                        ) : (
                                                                                            <Typography
                                                                                                variant="caption"
                                                                                                sx={{
                                                                                                    pt: .5,
                                                                                                    color: 'error.main',
                                                                                                }}>
                                                                                                {translate('form.validation.toBeDefined')}
                                                                                            </Typography>
                                                                                        )
                                                                                    ),
                                                                                },
                                                                                ...getOptionalList(field.value && nextBuild, [{
                                                                                    key: translate('project.build.schedule.nextBuild'),
                                                                                    node: (
                                                                                        <Box sx={{pl: .125}}>
                                                                                            {fDateTimeHumanReadable(nextBuild)}
                                                                                        </Box>
                                                                                    ),
                                                                                }])]}
                                                                                variant={DefinitionList.VARIANTS.vertical}
                                                                            />
                                                                            <Stack
                                                                                direction={{xs: 'column', md: 'row'}}
                                                                                alignItems="center"
                                                                                spacing={1.5}>
                                                                                <ProjectSchedulerRandom
                                                                                    onChange={(data) => {
                                                                                        field.onChange(data.schedulerData);
                                                                                        setNextBuildList(data.schedulerData.nextBuildList);
                                                                                        queueCallback(updateStore);
                                                                                    }}
                                                                                    startDate={startDate}
                                                                                    endDate={endDate}
                                                                                    defaultConfig={field.value}
                                                                                />
                                                                                <ProjectSchedulerCron
                                                                                    startDate={startDate}
                                                                                    onChange={(data) => {
                                                                                        field.onChange(data.schedulerData);
                                                                                        setNextBuildList(data.schedulerData.nextBuildList);
                                                                                        queueCallback(updateStore);
                                                                                    }}
                                                                                    defaultConfig={field.value?.config}
                                                                                />
                                                                            </Stack>
                                                                        </>
                                                                    );
                                                                }}
                                                            />
                                                        </Stack>
                                                    }
                                                </>
                                            );
                                        }}
                                    />
                                </Stack>
                            ),
                        },
                        {
                            key: 'notifications',
                            label: (
                                <LabelStyle>
                                    {translate(`${i18nContext}.fields.notifications.label`)}
                                </LabelStyle>
                            ),
                            node: (
                                <Stack spacing={3}>
                                    <FormGroup>
                                        {notificationRendererList.map(({type, Component}) => (
                                            <Box key={type} sx={{mb: 3}}>
                                                <Controller
                                                    name={`${FORM_FIELDS.notifications}.${type}`}
                                                    control={control}
                                                    render={({field}) => {
                                                        return (
                                                            <>
                                                                <FormControlLabel
                                                                    label={translate(`products.notificationProducts.items.${type}Notification.label`)}
                                                                    control={
                                                                        <Switch
                                                                            checked={field.value}
                                                                            onChange={(_, value) => {
                                                                                field.onChange(false);
                                                                                queueCallback(updateStore);
                                                                                if (!value) {
                                                                                    return;
                                                                                }
                                                                                if (!projectNotifications[type]) {
                                                                                    PubSub.publish('SHOW.DIALOG', {
                                                                                        type: NOTIFICATIONS.subscription,
                                                                                        data: {
                                                                                            projectSubscriptionSummary: {
                                                                                                projectNotifications: {
                                                                                                    [type]: true,
                                                                                                },
                                                                                            },
                                                                                        },
                                                                                    });
                                                                                    return;
                                                                                }
                                                                                setVisibleForm(type);
                                                                            }}
                                                                        />
                                                                    }
                                                                />
                                                                {
                                                                    field.value && (
                                                                        <Component
                                                                            defaultValues={configData.summary?.[FORM_FIELDS.notificationsData]?.[type]}
                                                                            onEdit={() => setVisibleForm(type)}
                                                                            isPreview
                                                                        />
                                                                    )
                                                                }
                                                                {
                                                                    visibleForm === type && (
                                                                        <Component
                                                                            defaultValues={configData.summary?.[FORM_FIELDS.notificationsData]?.[type]}
                                                                            handleCancel={() => {
                                                                                setVisibleForm(undefined);
                                                                            }}
                                                                            handleSave={(value) => {
                                                                                field.onChange(true);
                                                                                setValue(
                                                                                    `${FORM_FIELDS.notificationsData}.${type}`,
                                                                                    value,
                                                                                );
                                                                                setVisibleForm(undefined);
                                                                                queueCallback(updateStore);
                                                                            }}
                                                                        />
                                                                    )
                                                                }
                                                            </>
                                                        );
                                                    }}
                                                />
                                            </Box>
                                        ))}
                                    </FormGroup>
                                </Stack>
                            ),
                        },
                    ]}
                    variant={DefinitionList.VARIANTS.tabs}
                />
                <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                    <Button
                        color="inherit"
                        onClick={() => setIsFormOpen(false)}>
                        {translate('close')}
                    </Button>
                </Stack>
            </Drawer>
        </div>
    );
};

ProjectWizardSummary.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardSummary);
