import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {Box, Checkbox, FormControlLabel, Stack, Switch, Typography} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {useSnackbar} from 'notistack5';
import {LIGHTHOUSE_FIELDS} from '@w7-3/webeagle-resources/dist/config/lighthouse';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {toggleArrayStringItem} from '../../utils/toggle';
import NoAccessPopover from '../utils/NoAccessPopover';
import InfoPopover from '../utils/InfoPopover';
import {updateSolutionConfig} from '../../../redux/projectAssistant/store';
import LabelStyle from '../utils/LabelStyle';
import DefinitionList from '../utils/DefinitionList';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.lighthouse';
const FORM_FIELDS = {
    categoryList: 'categoryList',
    lighthouseOutputFormats: 'lighthouseOutputFormats',
};

const propTypes = {
    config: PropTypes.shape({
        categoryList: PropTypes.array,
        lighthouseOutputFormats: PropTypes.array,
    }),
};

const mapStateToProps = ({projectAssistant}) => {
    return {
        config: projectAssistant?.solution?.config,
    };
};

const ProjectWizardSolutionsConfigsLighthouse = ({config}) => {
    const dispatch = useDispatch();
    const {translate} = useLocales();
    const {enqueueSnackbar} = useSnackbar();
    const {
        watch,
        control,
    } = useForm({
        mode: 'onChange',
        defaultValues: config,
    });
    const updateStore = () => {
        queueCallback(() => {
            dispatch(updateSolutionConfig(watch()));
        });
    };

    return (
        <DefinitionList
            dataList={[
                {
                    key: '1',
                    label: (
                        <LabelStyle>
                            {translate(`${i18nContext}.categories.previewLabel`)}
                        </LabelStyle>
                    ),
                    node: (
                        <Stack spacing={3}>
                            <Stack spacing={1}>
                                <Typography component="p" variant="caption" sx={{color: 'text.secondary'}}>
                                    {translate(`${i18nContext}.categories.title`)}
                                </Typography>
                                <Typography variant="subtitle1">
                                    {translate(`${i18nContext}.categories.label`)}
                                </Typography>
                            </Stack>
                            <Controller
                                name={FORM_FIELDS.categoryList}
                                control={control}
                                render={({
                                    field,
                                }) => {
                                    return (
                                        <>
                                            {
                                                Object.values(LIGHTHOUSE_FIELDS.categories)
                                                    .map((category) => {
                                                        const categoryLabel = translate(`${i18nContext}.categories.fields.${category}.label`);
                                                        const description = translate([`${i18nContext}.categories.fields.${category}.description`, 'void']);
                                                        return (
                                                            <Stack key={category}
                                                                   direction="row"
                                                                   alignItems="center">
                                                                <FormControlLabel
                                                                    label={categoryLabel}
                                                                    control={
                                                                        <Switch
                                                                            onChange={(_, isChecked) => {
                                                                                const newList = toggleArrayStringItem({
                                                                                    item: category,
                                                                                    list: field.value,
                                                                                    add: isChecked
                                                                                });

                                                                                if (newList.length < 1) {
                                                                                    enqueueSnackbar(translate('anOptionIsMandatory'), {
                                                                                        variant: 'info',
                                                                                    });
                                                                                    field.onChange([category]);
                                                                                    updateStore();
                                                                                    return;
                                                                                }

                                                                                field.onChange(newList);
                                                                                updateStore();
                                                                            }}
                                                                            checked={field.value.includes(category)}
                                                                        />
                                                                    }
                                                                />
                                                                <InfoPopover>
                                                                    <>
                                                                        {
                                                                            categoryLabel &&
                                                                            <Typography variant="subtitle1" gutterBottom>
                                                                                {categoryLabel}
                                                                            </Typography>
                                                                        }
                                                                        {
                                                                            description &&
                                                                            <Typography variant="body" gutterBottom>
                                                                                {description}
                                                                            </Typography>
                                                                        }
                                                                    </>
                                                                </InfoPopover>
                                                            </Stack>
                                                        );
                                                    })
                                            }
                                        </>
                                    );
                                }}
                            />
                        </Stack>
                    ),
                },
                {
                    key: '2',
                    label: (
                        <LabelStyle>
                            {translate(`${i18nContext}.output.previewLabel`)}
                        </LabelStyle>
                    ),
                    node: (
                        <Stack spacing={3}>
                            <Stack spacing={1}>
                                <Typography component="p" variant="caption" sx={{color: 'text.secondary'}}>
                                    {translate(`${i18nContext}.output.title`)}
                                </Typography>
                                <Typography variant="subtitle1">
                                    {translate(`${i18nContext}.output.label`)}
                                </Typography>
                            </Stack>
                            <Controller
                                name={FORM_FIELDS.lighthouseOutputFormats}
                                control={control}
                                render={({field}) => (
                                    <>
                                        {
                                            Object.values(LIGHTHOUSE_FIELDS.outputs)
                                                .map((format) => {
                                                    const isSelected = field.value === format;
                                                    const isDisabled = !Object.values(LIGHTHOUSE_FIELDS.outputs).includes(format);
                                                    return (
                                                        <FormControlLabel
                                                            key={format}
                                                            value={format}
                                                            control={<Checkbox
                                                                checked={field.value.includes(format)}
                                                                onChange={(_, isChecked) => {
                                                                    field.onChange(toggleArrayStringItem({
                                                                        item: format,
                                                                        list: field.value,
                                                                        add: isChecked
                                                                    }));
                                                                    updateStore();
                                                                }}
                                                            />}
                                                            label={(
                                                                <Box
                                                                    sx={{
                                                                        display: 'flex',
                                                                        textTransform: 'uppercase',
                                                                        ...getOptionalMap(!isSelected, {
                                                                            color: 'text.secondary',
                                                                        })
                                                                    }}>
                                                                    {translate(`${i18nContext}.output.fields.${format}.label`)}
                                                                    {isDisabled && <NoAccessPopover/>}
                                                                </Box>
                                                            )}
                                                            disabled={isDisabled}
                                                        />
                                                    );
                                                })
                                        }
                                        <FormControlLabel
                                            label={translate(`${i18nContext}.output.fields.none.label`)}
                                            control={
                                                <Switch
                                                    onChange={(_, isChecked) => {
                                                        field.onChange(isChecked ? [] : LIGHTHOUSE_FIELDS.outputs.html);
                                                        updateStore();
                                                    }}
                                                    checked={field.value.length === 0}
                                                />
                                            }
                                            sx={{minWidth: '100%'}}
                                        />
                                    </>
                                )}
                            />
                        </Stack>
                    )
                }
            ]}
            variant={DefinitionList.VARIANTS.tabs}
        />
    );
};

ProjectWizardSolutionsConfigsLighthouse.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardSolutionsConfigsLighthouse);
