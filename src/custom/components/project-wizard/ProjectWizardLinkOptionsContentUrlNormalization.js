import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import {connect} from 'react-redux';
import {Controller, useFieldArray, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {
    Alert,
    Button,
    Divider,
    FormControl,
    FormControlLabel,
    ListItem,
    Stack,
    Switch,
    TextField,
    Typography,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import getNormalizedUrl from '@w7-3/webeagle-resources/dist/libs/getNormalizedUrl';
import {setLinkOptions} from '../../../redux/projectAssistant/store';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import CodeZone from '../utils/CodeZone';
import {getFullUrlSchema} from '../../utils/formSchema';
import DeleteButton from '../utils/DeleteButton';
import LabelStyle from '../utils/LabelStyle';

const i18nContext = 'project.wizardSteps.linkOptions.groups.urlNormalization';
const testUrls = 'testUrls';
const mapStateToProps = ({projectAssistant}) => {
    const {
        linkOptions: {
            normalizations,
        },
    } = projectAssistant.configData;

    return {
        normalizations,
    };
};
const propTypes = {
    normalizations: PropTypes.object,
    dispatch: PropTypes.func,
};
const testUrlsData = [{
    url: 'http://user:<EMAIL>/?q=value#hash',
}, {
    url: 'http://user:<EMAIL>/path-1/path-2?q=value#hash',
}];

const ProjectWizardLinkOptionsContentUrlNormalization = ({normalizations: cachedNormalizations}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const [normalizations, setNormalizations] = useState(cachedNormalizations);
    const updateState = (value) => {
        setNormalizations({
            ...normalizations,
            ...value,
        });
    };
    const FormSchema = Yup.object().shape({
        testUrls: Yup.array()
            .of(
                Yup.object().shape({
                    url: getFullUrlSchema({translate}),
                }),
            ),
    });
    const {control} = useForm({
        mode: 'onChange',
        resolver: yupResolver(FormSchema),
        defaultValues: {
            ...normalizations,
            testUrls: testUrlsData,
        },
    });
    const {fields, remove, append} = useFieldArray({
        control,
        name: testUrls,
    });

    useEffect(() => {
        dispatch(setLinkOptions({normalizations}));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [normalizations]);

    return (
        <Stack spacing={3}>
            {
                Object.keys(normalizations).map((field) => {
                    const helperText = translate([`${i18nContext}.fields.${field}.helperText`, 'void']);
                    return (
                        <Stack key={field} spacing={1}>
                            <FormControlLabel
                                label={translate(`${i18nContext}.fields.${field}.label`)}
                                control={
                                    <Switch
                                        onChange={() => {
                                            updateState({[field]: !normalizations[field]});
                                        }}
                                        checked={normalizations[field]}
                                    />
                                }
                            />
                            {
                                Boolean(helperText) &&
                                <Typography component="p">
                                    {helperText}
                                </Typography>
                            }
                            <Divider/>
                        </Stack>
                    );
                })
            }
            <CodeZone>
                <Stack spacing={3} sx={{width: '100%'}}>
                    <LabelStyle>
                        {translate('testLink')}
                    </LabelStyle>
                    <FormControl component="fieldset">
                        {fields.map((item, index) => (
                            <ListItem key={item.id}
                                      sx={{px: 0}}
                                      spacing={{
                                          xs: 1,
                                          md: 3,
                                      }}>
                                <Controller
                                    name={`${testUrls}.${index}.url`}
                                    control={control}
                                    render={({
                                                 field,
                                                 fieldState: {error},
                                             }) => {
                                        const hasError = Boolean(error?.message);
                                        const resultText = !hasError && field.value.length > 0 && translate(`${i18nContext}.result`, {
                                            result: getNormalizedUrl(field.value, normalizations),
                                        });
                                        return (
                                            <Stack spacing={1}
                                                   sx={{width: '100%'}}>
                                                <Stack direction="row" spacing={1}>
                                                    <Stack spacing={1} sx={{flexGrow: 1}}>
                                                        <TextField
                                                            {...field}
                                                            type="url"
                                                            fullWidth
                                                        />
                                                        {hasError && (
                                                            <Typography
                                                                sx={{color: 'error.main'}}>
                                                                {error?.message}
                                                            </Typography>
                                                        )}
                                                    </Stack>
                                                    <DeleteButton
                                                        onClick={() => remove(index)}
                                                    />
                                                </Stack>
                                                {
                                                    resultText &&
                                                    <Alert severity="info">
                                                        <Typography>{resultText}</Typography>
                                                    </Alert>
                                                }
                                                <Divider sx={{pt: 3, mb: 3}} />
                                            </Stack>
                                        )
                                    }}
                                />

                            </ListItem>
                        ))}
                    </FormControl>
                    <Button variant="outlined"
                            size="large"
                            startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                            onClick={() =>
                                append({
                                    url: '',
                                })
                            }>
                        {translate('addTestLink')}
                    </Button>
                </Stack>
            </CodeZone>
        </Stack>
    );
};

ProjectWizardLinkOptionsContentUrlNormalization.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardLinkOptionsContentUrlNormalization);
