import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {useNavigate} from 'react-router-dom';
import {Alert, AlertTitle, Button, IconButton, Stack, Typography} from '@material-ui/core';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../../hooks/useLocales';
import {useDispatch} from '../../../../redux/store';
import {updateProjectAssistant} from '../../../../redux/projectAssistant/store';
import scrollToStep from '../../../utils/scrollToStep';

const ValidationAlert = ({children, isTop, validation}) => {
    const {translate} = useLocales();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const action = (
        <IconButton
            onClick={() => {
                dispatch(updateProjectAssistant({
                    showValidation: false,
                }));
            }}
            color="inherit">
            <Icon icon="eva:close-outline" width={40} height={40}/>
        </IconButton>
    );

    if (validation.success) {
        return (
            <Alert
                severity="success"
                action={action}>
                {translate('directives.notifications.PROJECT_CONFIG_OK')}
            </Alert>
        );
    }

    return (
        <>
            <Alert
                severity="error"
                action={action}
                sx={{
                    '.MuiAlert-message': {
                        flexGrow: 1,
                    },
                }}
            >
                <AlertTitle>{translate('project.configuration.error')}</AlertTitle>
                <Typography
                    variant="subtitle1"
                    sx={{my: 3}}>
                    {translate(`directives.notifications.${validation.reason}`, validation.data?.matchingConfig?.data)}
                    <IconButton
                        onClick={() => {
                            scrollToStep(validation.step);
                        }}
                        color="inherit"
                        sx={{ml: 1}}
                    >
                        <Icon
                            icon={isTop ? 'eva:arrow-ios-downward-fill' : 'eva:arrow-ios-upward-fill'}
                            width={40}
                            height={40}
                        />
                    </IconButton>
                </Typography>
                {
                    validation.data?.matchingConfig?.data?.projectId && (
                        <Stack direction="row" sx={{width: '100%'}} justifyContent="flex-end" spacing={3}>
                            <Button
                                color="inherit"
                                variant="outlined"
                                onClick={() => {
                                    PubSub.publish('SHOW.DIALOG', {
                                        type: 'projectPreview',
                                        props: {
                                            configData: validation.data?.matchingConfig?.data,
                                            projectId: validation.data?.matchingConfig?.data?.projectId,
                                            isEditable: false,
                                        },
                                    });
                                }}
                            >
                                {translate('project.build.showConfig')}
                            </Button>
                            <Button
                                color="inherit"
                                variant="outlined"
                                onClick={() => {
                                    navigate(`${PATH_DASHBOARD.general.projects.root}/${validation.data?.matchingConfig?.data?.projectId}`);
                                }}
                            >
                                {translate('project.cta')}
                            </Button>
                        </Stack>
                    )
                }
            </Alert>
            {children}
        </>
    );
};

ValidationAlert.propTypes = {
    validation: PropTypes.object,
    children: PropTypes.node,
    isTop: PropTypes.bool,
};

export default ValidationAlert;
