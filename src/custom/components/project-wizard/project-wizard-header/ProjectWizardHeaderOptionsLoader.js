import PropTypes from 'prop-types';
import {connect, useDispatch} from 'react-redux';
import PubSub from 'pubsub-js';
import {Fragment, useState} from 'react';
import {
    Box,
    Button,
    Divider,
    IconButton,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Stack,
    Typography,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import closeOutline from '@iconify/icons-eva/close-outline';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {ACTIONS} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../../hooks/useLocales';
import SearchStyle from '../../utils/SearchStyle';
import {updateProjectAssistant} from '../../../../redux/projectAssistant/store';
import getProjectFieldDisplayManager from '../../../utils/getProjectFieldDisplayManager';

const propTypes = {
    contextId: PropTypes.string,
    projects: PropTypes.object,
};

const mapStateToProps = ({state, projectAssistant}) => {
    const {
        projects,
    } = state;
    const {
        configData,
    } = projectAssistant;

    return {
        projects,
        configData,
    };
};

const ProjectWizardHeaderOptionsLoader = ({projects, contextId}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const projectFieldsManager = getProjectFieldDisplayManager({translate});
    const projectList = projects ? Object.entries(projects) : [];
    const [query, setQuery] = useState('');
    const filteredProjectList = projectList.filter(([, project]) => {
        return JSON.stringify(project?.configData || {}).toLowerCase().indexOf(query.toLowerCase()) !== -1;
    });
    const handleSelection = (project) => {
        dispatch(updateProjectAssistant({
            action: ACTIONS.clone,
            configData: {
                ...project.configData,
                projectName: `${translate('project.clone.prefix')} ${project.configData.projectName}`,
            },
        }));
        PubSub.publish('HIDE.DIALOG', {id: contextId});
        PubSub.publish('SHOW.NOTIFICATION', {
            message: translate('project.clone.success', {projectName: project?.configData?.projectName}),
            variant: 'info',
        });
    };
    const showProjectConfig = (project) => {
        PubSub.publish('SHOW.DIALOG', {
            type: 'projectPreview',
            dialogProps: {
                title: translate('project.configuration.label'),
                isFullScreen: true,
            },
            props: {
                configData: project.configData,
                isEditable: false,
            },
        });
    };

    return (
        <>
            <SearchStyle
                value={query}
                onChange={(event) => setQuery(event.target.value)}
                placeholder={translate('project.list.filter')}
            />
            <List component="nav" aria-label="main mailbox folders">
                {
                    !isNonEmptyArray(filteredProjectList) ? (
                        <Typography variant="body2" align="center">
                            {translate('project.list.search.notice', {searchQuery: query})}
                        </Typography>
                    ) : (
                    filteredProjectList
                        .map(([id, project]) => {
                            return (
                                <Fragment key={id}>
                                    <ListItemButton>
                                        <ListItemText
                                            primary={project?.configData?.projectName}
                                            primaryTypographyProps={{typography: 'subtitle2', mb: 1.5}}
                                            secondary={(
                                                <Box
                                                    component="span"
                                                    sx={{display: 'flex', flexWrap: 'nowrap', overflow: 'scroll'}}
                                                >
                                                    {
                                                        [
                                                            projectFieldsManager.solution?.q(project),
                                                            projectFieldsManager.urls?.q(project),
                                                        ].map((label, index) => (
                                                            <Box
                                                                key={index}
                                                                component="span"
                                                                sx={{
                                                                    p: 1,
                                                                    m: 1,
                                                                    boxShadow: (theme) => theme.customShadows.z20,
                                                                    border: (theme) => `solid 1px ${theme.palette.grey[500_8]}`,
                                                                    minWidth: 'fit-content',
                                                                }}
                                                            >{label}
                                                            </Box>
                                                        ))
                                                    }
                                                </Box>
                                            )}
                                            onClick={() => {
                                                handleSelection(project);
                                            }}
                                        />
                                        <ListItemIcon sx={{
                                            my: 'auto',
                                        }}>
                                            <IconButton
                                                onClick={() => {
                                                    showProjectConfig(project);
                                                }}
                                                edge="end"
                                                color="primary">
                                                <Icon icon="eva:eye-outline"/>
                                            </IconButton>
                                        </ListItemIcon>
                                    </ListItemButton>
                                    <Divider/>
                                </Fragment>
                            );
                        })
                    )
                }
            </List>
            <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                <Button
                    color="inherit"
                    startIcon={<Icon icon={closeOutline} width={40} height={40} />}
                    onClick={() => {
                        PubSub.publish('HIDE.MODAL.ALL');
                    }}>
                    {translate('close')}
                </Button>
            </Stack>
        </>
    );
}

ProjectWizardHeaderOptionsLoader.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardHeaderOptionsLoader);
