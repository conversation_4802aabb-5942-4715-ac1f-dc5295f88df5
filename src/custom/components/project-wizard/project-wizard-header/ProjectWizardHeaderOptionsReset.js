import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {resetProjectWizard} from '../../../../redux/projectAssistant/store';
import useLocales from '../../../../hooks/useLocales';
import {useDispatch} from '../../../../redux/store';

const ProjectWizardHeaderOptionsReset = () => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    return (
        <Alert severity="info"
               sx={{p: 3}}>
            <AlertTitle>
                {translate('project.new.resetPrompt.label')}
            </AlertTitle>
            <Typography align="center">
                {translate('project.new.resetPrompt.description')}
            </Typography>
            <Stack
                spacing={3}
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                sx={{mt: 3}}
            >
                <Button onClick={() => {
                    PubSub.publish('HIDE.MODAL.ALL');
                }}>
                    {translate('project.new.resetPrompt.cancel')}
                </Button>
                <Button
                    size="large"
                    variant="contained"
                    onClick={() => {
                        window.history.replaceState(
                            {},
                            document.title,
                            window.location.href.split('?')[0],
                        );
                        dispatch(resetProjectWizard());
                        PubSub.publish('SHOW.NOTIFICATION', {
                            message: translate('project.new.success'),
                            variant: 'info',
                        });
                        PubSub.publish('HIDE.MODAL.ALL');
                    }}
                >
                    {translate('project.new.resetPrompt.confirm')}
                </Button>
            </Stack>
        </Alert>
    );
};

export default ProjectWizardHeaderOptionsReset;
