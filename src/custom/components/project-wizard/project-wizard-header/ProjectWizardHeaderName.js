import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {Alert, Button, IconButton, InputAdornment, OutlinedInput, Stack, Typography} from '@material-ui/core';
import {useEffect, useRef, useState} from 'react';
import {styled} from '@material-ui/core/styles';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {MAX_PROJECT_NAME_LENGTH} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../../hooks/useLocales';
import Drawer from '../../forms/Drawer';
import SubmitButton from '../../utils/SubmitButton';

const InputStyle = styled(OutlinedInput)(({theme}) => {
    return {
        width: '100%',
        marginBottom: `${theme.spacing(3)} !important;`,
        transition: theme.transitions.create(['box-shadow', 'width'], {
            easing: theme.transitions.easing.easeInOut,
            duration: theme.transitions.duration.shorter,
        }),
        '& fieldset': {
            borderColor: `${theme.palette.grey[500_32]} !important`,
        },
        [theme.breakpoints.up('md')]: {
            '&.Mui-focused': {width: '100%', boxShadow: theme.customShadows.z8},
        },
    };
});

const propTypes = {
    projectName: PropTypes.string,
    handleClose: PropTypes.func,
};

const ProjectWizardHeaderName = ({projectName, handleClose}) => {
    const {translate} = useLocales();
    const inputRef = useRef(null);
    const [value, setValue] = useState(projectName || '');
    const handleCancel = () => {
        handleClose({value: projectName});
    };

    useEffect(() => {
        queueCallback(() => {
            inputRef.current?.focus();
        });
    }, []);

    const isValid = value.trim().length > 0 && value.length < MAX_PROJECT_NAME_LENGTH;

    return (
        <Drawer
            open
            onClose={handleCancel}
            title={(
                <Typography variant="subtitle1">
                    {`${translate('project.name.label')}...`}
                </Typography>
            )}
        >
            <Stack spacing={3}>
                <Alert severity="info" sx={{my: 2}}>
                    {translate('project.name.notifications.enterProjectName')}
                </Alert>
                <Stack spacing={1}>
                    <InputStyle
                        value={value}
                        onChange={(e) => {
                            setValue(e.target.value);
                        }}
                        placeholder={`${translate('project.name.label')}...`}
                        inputProps={{
                            'aria-autocomplete': 'none',
                        }}
                        endAdornment={(
                            <InputAdornment position="end">
                                <IconButton
                                    onClick={() => {
                                        setValue('');
                                    }}
                                    edge="end"
                                    color="primary">
                                    <Icon icon="eva:close-outline"/>
                                </IconButton>
                            </InputAdornment>
                        )}
                        sx={{
                            border: 0,
                        }}
                        inputRef={inputRef}
                    />
                    <Typography
                        variant="body2"
                        sx={{
                            color: 'error.main',
                            mb: 1,
                        }}>
                        {!value.trim().length && translate('project.name.notifications.required')}
                        {value.length >= MAX_PROJECT_NAME_LENGTH && translate('project.name.notifications.length', {max: MAX_PROJECT_NAME_LENGTH})}
                    </Typography>
                </Stack>
                <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                    <Button
                        color="inherit"
                        onClick={handleCancel}
                        variant="text"
                        sx={{textTransform: 'none'}}>
                        {translate('cancel')}
                    </Button>
                    <SubmitButton
                        onClick={() => {
                            handleClose({value});
                        }}
                        label={translate('save')}
                        isValid={isValid}
                    />
                </Stack>
            </Stack>
        </Drawer>
    );
}

ProjectWizardHeaderName.propTypes = propTypes;

export default ProjectWizardHeaderName;
