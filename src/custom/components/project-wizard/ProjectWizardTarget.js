import {Fragment, lazy, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {styled} from '@material-ui/core/styles';
import {connect} from 'react-redux';
import {
    Alert,
    Autocomplete,
    Box,
    Button,
    Checkbox,
    Divider,
    FormControl,
    FormControlLabel,
    Radio,
    RadioGroup,
    Stack,
    Typography,
} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import {desktopDeviceList, mobileDeviceList, newDevicePlaceholder} from '@w7-3/webeagle-resources/dist/config/devices';
import {BROWSERS, RUN_MODES, LIGHTHOUSE_DEVICES, EXECUTION_STRATEGY} from '@w7-3/webeagle-resources/dist/config/scrapper';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import getOptionalList from '@w7-3/webeagle-resources/dist/libs/getOptionalList';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../hooks/useLocales';
import {setTarget} from '../../../redux/projectAssistant/store';
import NoAccessPopover from '../utils/NoAccessPopover';
import {useDispatch} from '../../../redux/store';
import InfoPopover from '../utils/InfoPopover';
import BrowserCard from '../utils/BrowserCard';
import TextFieldStyle from '../utils/TextFieldStyle';
import URLCaptureDialogForm from '../forms/URLCaptureDialogForm';
import Drawer from '../forms/Drawer';
import ProjectPreviewTarget from './project-preview/ProjectPreviewTarget';
import ProjectWizardTargetPreview from './project-wizard-target/ProjectWizardTargetPreview';
import SettingItemChange from '../utils/SettingItemChange';
import AddButton from '../utils/AddButton';
import Loadable from '../utils/Loadable';
import {getDeviceName} from '../../utils/device';
import LabelStyle from '../utils/LabelStyle';

const DeviceDialogForm = Loadable(lazy(() => import('../forms/DeviceDialogForm')));

const RadioGroupStyle = styled(RadioGroup)(({theme}) => ({
    [theme.breakpoints.up('md')]: {
        flexDirection: 'row',
    },
}));

const mapStateToProps = ({projectAssistant}) => {
    const {
        availableRunModes,
        target,
        solution,
    } = projectAssistant.configData;

    return {
        availableRunModes,
        target,
        solution,
    };
};

const propTypes = {
    availableRunModes: PropTypes.array,
    target: PropTypes.object,
    solution: PropTypes.object,
    editing: PropTypes.shape({
        isNotEditable: PropTypes.bool,
        editNotice: PropTypes.string,
    }),
};

const ProjectWizardTarget = ({availableRunModes, target, solution, editing}) => {
    const {translate} = useLocales();
    const [isFormOpen, setIsFormOpen] = useState(false);
    const dispatch = useDispatch();
    const [availableDeviceList, setAvailableDeviceList] = useState([
        ...desktopDeviceList,
        ...mobileDeviceList,
        ...getOptionalList(target?.device?.isCustom, [target?.device]),
    ]);
    const [urls, setUrls] = useState(target?.urls);
    const [initialUrls] = useState(target?.urls);
    const [urlCaptureConfig, setUrlCaptureConfig] = useState(null);
    const FormSchema = Yup.object().shape({
        device: Yup.mixed()
            .test(undefined, translate('project.wizardSteps.target.device.errorMessage'), Boolean),
        lighthouseDevices: Yup.array()
            .min(1, translate('project.wizardSteps.target.device.errorMessage')),
        browser: Yup.mixed()
            .test(undefined, translate('project.wizardSteps.target.browser.description'), Boolean),
    });
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(FormSchema),
        defaultValues: target,
    });
    const {
        watch,
        control,
        setValue,
    } = formContext;
    const currentData = watch();
    const isLighthouseProject = solution?.key === solutions.lighthouse.appName;
    const FILTERED_MODES = solution?.availableRunModes?.filter((mode) => {
        return availableRunModes.includes(mode);
    });
    const hasErrors = !isNonEmptyArray(urls) || !currentData.device;
    const isEmpty = (!isNonEmptyArray(initialUrls) || hasErrors) && !isFormOpen;
    const Wrapper = isEmpty ? Fragment : ({children}) => {
        return (
            <Drawer
                open={isFormOpen}
                onClose={() => setIsFormOpen(false)}
                title={(
                    <Typography variant="subtitle1">
                        {translate(`project.wizardSteps.${steps.target.key}.previewLabel`, {
                            solution: translate(`project.wizardSteps.solutionsConfigs.groups.${solution?.key}.label`),
                        })}
                    </Typography>
                )}
            >
                {children}
                <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                    <Button
                        color="inherit"
                        onClick={() => setIsFormOpen(false)}>
                        {translate('close')}
                    </Button>
                </Stack>
            </Drawer>
        );
    };
    const handleNewDevice = ({
        title,
        device,
        handleSave,
        handleCancel,
    }) => {
        PubSub.publish('SHOW.DIALOG', {
            type: 'custom',
            hideActions: true,
            dialogProps: {
                title,
            },
            children: (
                <DeviceDialogForm
                    device={device}
                    availableDeviceList={availableDeviceList.filter((item) => item.name !== device.name)}
                    callbacks={{handleCancel, handleSave}}
                />
            ),
        });
    };

    useEffect(() => {
        dispatch(setTarget({
            ...currentData,
            urls,
        }));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        urls,
        currentData?.browser?.key,
        currentData?.checkType,
        currentData?.device?.name,
        currentData?.lighthouseDevices?.length,
        currentData?.executionStrategy,
    ]);

    return (
        <div>
            {
                !isEmpty && (
                    <>
                        <ProjectWizardTargetPreview
                            Component={ProjectPreviewTarget}
                        />
                        <SettingItemChange
                            onClick={() => setIsFormOpen(true)}
                            sx={{
                                mt: 3,
                                fontSize: '.75em',
                                alignSelf: 'flex-start',
                                iconProps: {
                                    width: 20,
                                    height: 20,
                                },
                            }}
                        />
                    </>
                )
            }
            <Wrapper>
                <Stack spacing={3}>
                    {
                        editing?.isNotEditable &&
                        <Alert severity="warning" sx={{mb: 3}}>
                            {translate(editing?.editNotice)}
                        </Alert>
                    }
                    <Stack spacing={2}>
                        <InfoPopover
                            label={translate('project.wizardSteps.target.checkType.label')}
                            isLabel>
                            <>
                                {
                                    FILTERED_MODES
                                        ?.map((mode) => {
                                            return (
                                                <Fragment key={mode}>
                                                    <LabelStyle>
                                                        {translate(`project.wizardSteps.target.checkType.groups.${mode}.label`)}
                                                    </LabelStyle>
                                                    <Typography>
                                                        {translate(`project.wizardSteps.target.checkType.groups.${mode}.description`)}
                                                    </Typography>
                                                </Fragment>
                                            );
                                        })
                                }
                            </>
                        </InfoPopover>
                        <Controller
                            name="checkType"
                            control={control}
                            render={({field}) => {
                                return (
                                    <>
                                        <RadioGroupStyle
                                            {...field}>
                                            {
                                                FILTERED_MODES
                                                    ?.map((mode, index) => {
                                                        const isDisabled = !availableRunModes.includes(mode);
                                                        return (
                                                            <Stack
                                                                key={mode}
                                                                direction="row"
                                                                alignItems="center"
                                                                spacing={.5}
                                                                sx={{pr: 2, ml: 0}}>
                                                                <FormControlLabel
                                                                    value={mode}
                                                                    control={<Radio />}
                                                                    label={(
                                                                        <Box sx={{display: 'flex'}}>
                                                                            {translate(`project.wizardSteps.target.checkType.groups.${mode}.label`)}
                                                                            {isDisabled && <NoAccessPopover/>}
                                                                        </Box>
                                                                    )}
                                                                    disabled={editing?.isNotEditable || isDisabled}
                                                                    sx={{
                                                                        mr: 0,
                                                                    }}
                                                                />
                                                                {
                                                                    (index < FILTERED_MODES.length - 1) &&
                                                                    <Divider
                                                                        orientation="vertical"
                                                                        sx={{pl: 1}}
                                                                        flexItem/>
                                                                }
                                                            </Stack>
                                                        );
                                                    })
                                            }
                                        </RadioGroupStyle>
                                    </>
                                );
                            }}
                        />
                        {
                            isNonEmptyArray(urls) && (
                                <FormControl component="fieldset">
                                    {urls.map((item, index) => (
                                        <URLCaptureDialogForm
                                            key={item.url}
                                            defaultValues={item}
                                            onEdit={() => {
                                                setUrlCaptureConfig({
                                                    disableUrlField: editing?.isNotEditable,
                                                    handleCancel: () => {
                                                        setUrlCaptureConfig(null);
                                                    },
                                                    defaultValues: item,
                                                    handleSave: (value) => {
                                                        const list = [...urls];

                                                        list[index] = value;
                                                        setUrls(list);
                                                        setUrlCaptureConfig(null);
                                                    },
                                                });
                                            }}
                                            {...getOptionalMap(!editing?.isNotEditable, {
                                                onDelete: () => {
                                                    const list = [...urls];

                                                    list.splice(index, 1);
                                                    setUrls(list);
                                                },
                                            })}
                                            iconOnly
                                            isPreview
                                        />
                                    ))}
                                </FormControl>
                            )
                        }
                        {
                            !editing?.isNotEditable &&
                            <div>
                                <Button
                                    variant="text"
                                    size="large"
                                    color={isNonEmptyArray(urls) ? 'primary' : 'error'}
                                    startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                                    onClick={() => {setUrlCaptureConfig({
                                        handleCancel: () => {
                                            setUrlCaptureConfig(null);
                                        },
                                        defaultValues: {},
                                        handleSave: (value) => {
                                            setUrls([
                                                ...urls,
                                                value,
                                            ]);
                                            setUrlCaptureConfig(null);
                                        },
                                    });
                                    }}>
                                    {translate('project.wizardSteps.target.checkType.moreLabel')}
                                </Button>
                            </div>
                        }
                        {
                            !isLighthouseProject && (
                                <>
                                    <InfoPopover
                                        label={translate('project.wizardSteps.target.browser.label')}
                                        isLabel>
                                        {translate('project.wizardSteps.target.browser.description')}
                                    </InfoPopover>
                                    <Controller
                                        name="browser"
                                        control={control}
                                        render={({
                                            field, fieldState: {error},
                                        }) => (
                                            <Stack spacing={2}>
                                                <RadioGroup
                                                    value={field.value}
                                                    row>
                                                    {Object.values(BROWSERS).map((item) => {
                                                        return (
                                                            <FormControlLabel
                                                                key={item?.key}
                                                                label={(
                                                                    <BrowserCard item={item}/>
                                                                )}
                                                                control={<Radio
                                                                    checked={item?.key === field.value?.key}
                                                                    onChange={() => {
                                                                        field.onChange(item);
                                                                    }}
                                                                />}
                                                                disabled={editing?.isNotEditable}
                                                            />
                                                        );
                                                    })}
                                                </RadioGroup>
                                                {
                                                    error?.message &&
                                                    <Typography gutterBottom variant="body2" sx={{color: 'error.main'}}>
                                                        {error?.message}
                                                    </Typography>
                                                }
                                            </Stack>
                                        )}
                                    />
                                </>
                            )
                        }
                    </Stack>
                    {
                        isLighthouseProject &&
                            <Controller
                                name="lighthouseDevices"
                                control={control}
                                render={({field, fieldState: {error}}) => (
                                    <Stack spacing={2}>
                                        <InfoPopover
                                            label={translate('project.wizardSteps.target.device.label')}
                                            isLabel>
                                            {translate('project.wizardSteps.target.device.description')}
                                        </InfoPopover>
                                        <Stack direction="row">
                                            {
                                                Object.values(LIGHTHOUSE_DEVICES).map(({key}) => (
                                                    <FormControlLabel
                                                        key={key}
                                                        control={
                                                            <Checkbox
                                                                checked={field.value.includes(key)}
                                                                disabled={editing?.isNotEditable}
                                                                onChange={() => {
                                                                    const newValue = field.value.includes(key) ?
                                                                        field.value.filter((item) => item !== key) :
                                                                        [...field.value, key];

                                                                    field.onChange(newValue);
                                                                }}
                                                                size="large"
                                                            />
                                                        }
                                                        label={translate(`project.wizardSteps.target.device.lighthouse.targets.${key}`)}
                                                    />
                                                ))
                                            }
                                        </Stack>
                                        {
                                            error?.message &&
                                            <Typography gutterBottom variant="body2" sx={{color: 'error.main'}}>
                                                {error?.message}
                                            </Typography>
                                        }
                                    </Stack>
                                )}
                            />
                    }
                    {
                        (
                            (isNonEmptyArray(urls) && urls.length > 1) ||
                            currentData?.checkType === RUN_MODES.page
                        ) && (
                            <Stack
                                alignItems="center"
                                spacing={3}
                            >
                                <Controller
                                    name="executionStrategy"
                                    control={control}
                                    render={({
                                        field,
                                    }) => {
                                        return (
                                            <Stack
                                                sx={{width: '100%'}}
                                                spacing={2}>
                                                <InfoPopover
                                                    label={translate('project.wizardSteps.target.executionStrategy.label')}
                                                    isLabel>
                                                    <>
                                                        {
                                                            Object.values(EXECUTION_STRATEGY).map((strategy) => {
                                                                return (
                                                                    <Stack
                                                                        key={strategy.key}
                                                                        spacing={1}
                                                                        sx={{pb: 3}}>
                                                                        <LabelStyle>
                                                                            {translate(`project.wizardSteps.target.executionStrategy.groups.${strategy.key}.label`)}
                                                                        </LabelStyle>
                                                                        <Typography>
                                                                            {translate(`project.wizardSteps.target.executionStrategy.groups.${strategy.key}.description`)}
                                                                        </Typography>
                                                                        <i>
                                                                            {translate('project.wizardSteps.target.executionStrategy.useCases')}
                                                                        </i>
                                                                        <p>
                                                                            {translate(`project.wizardSteps.target.executionStrategy.groups.${strategy.key}.useCase`)}
                                                                        </p>
                                                                    </Stack>
                                                                );
                                                            })
                                                        }
                                                    </>
                                                </InfoPopover>
                                                <RadioGroupStyle
                                                    {...field}>
                                                    {
                                                        Object.values(EXECUTION_STRATEGY).map((strategy, index) => {
                                                            const isDisabled = false;
                                                            return (
                                                                <Stack
                                                                    key={strategy.key}
                                                                    direction="row"
                                                                    alignItems="center"
                                                                    spacing={.5}
                                                                    sx={{pr: 2, ml: 0}}>
                                                                    <FormControlLabel
                                                                        value={strategy.key}
                                                                        control={<Radio />}
                                                                        label={(
                                                                            <Box sx={{display: 'flex'}}>
                                                                                {translate(`project.wizardSteps.target.executionStrategy.groups.${strategy.key}.label`)}
                                                                                {isDisabled && <NoAccessPopover/>}
                                                                            </Box>
                                                                        )}
                                                                        disabled={editing?.isNotEditable || isDisabled}
                                                                        sx={{
                                                                            mr: 0,
                                                                        }}
                                                                    />
                                                                    {
                                                                        (index < Object.keys(EXECUTION_STRATEGY).length - 1) &&
                                                                        <Divider
                                                                            orientation="vertical"
                                                                            sx={{pl: 1}}
                                                                            flexItem/>
                                                                    }
                                                                </Stack>
                                                            );
                                                        })
                                                    }
                                                </RadioGroupStyle>
                                            </Stack>
                                        );
                                    }}
                                />
                            </Stack>
                        )
                    }
                    {
                        !isLighthouseProject && (
                            <Controller
                                name="device"
                                control={control}
                                render={({
                                    field, fieldState: {error},
                                }) => {
                                    return (
                                        <Stack sx={{width: '100%'}}
                                               spacing={2}>
                                            <InfoPopover
                                                label={translate('project.wizardSteps.target.device.label')}
                                                isLabel>
                                                {translate('project.wizardSteps.target.device.description')}
                                            </InfoPopover>
                                            <Stack
                                                direction="row"
                                                alignItems="center"
                                                justifyContent="flex-end"
                                                spacing={3}>
                                                <Autocomplete
                                                    {...field}
                                                    sx={{flexGrow: '1'}}
                                                    control={control}
                                                    defaultValue={target.device}
                                                    options={availableDeviceList}
                                                    getOptionLabel={(device) => getDeviceName({device, translate})}
                                                    renderOption={(props, device, {selected}) => {
                                                        const {
                                                            viewport: {
                                                                width,
                                                                height,
                                                                deviceScaleFactor,
                                                                isMobile,
                                                                hasTouch,
                                                                isLandscape,
                                                            },
                                                        } = device;
                                                        return (
                                                            <Fragment key={device.name}>
                                                                <Stack {...props} spacing={1}>
                                                                    <Stack
                                                                        direction="row"
                                                                        alignItems="center"
                                                                        justifyContent="left"
                                                                        spacing={1}
                                                                        sx={{
                                                                            width: '100%',
                                                                        }}>
                                                                        <Radio checked={selected}/>
                                                                        {getDeviceName({device, translate})}
                                                                        {
                                                                            device.isCustom && (
                                                                                <SettingItemChange
                                                                                    onClick={() => {
                                                                                        handleNewDevice({
                                                                                            title: `${translate('customDevice.edit')}: ${device.name}`,
                                                                                            device,
                                                                                            handleSave: ({device}) => {
                                                                                                const list = [...availableDeviceList];
                                                                                                const index = list.findIndex(item => item.id === device.id);

                                                                                                PubSub.publish('HIDE.DIALOG.ALL');
                                                                                                if (index < 0) {
                                                                                                    return;
                                                                                                }

                                                                                                list[index] = device;
                                                                                                setAvailableDeviceList(list);
                                                                                                setValue('device', list[index]);

                                                                                            },
                                                                                            handleCancel: () => {
                                                                                                PubSub.publish('HIDE.DIALOG.ALL');
                                                                                            },
                                                                                        });
                                                                                    }}
                                                                                    sx={{
                                                                                        fontSize: '.75em',
                                                                                        alignSelf: 'flex-start',
                                                                                        iconProps: {
                                                                                            width: 20,
                                                                                            height: 20,
                                                                                        },
                                                                                    }}
                                                                                />
                                                                            )
                                                                        }
                                                                    </Stack>
                                                                    <Typography variant="caption" sx={{
                                                                        width: '100%',
                                                                        pl: 1,
                                                                    }}>
                                                                        ({
                                                                        [
                                                                            `${width}x${height}`,
                                                                            `${translate('customDevice.fields.viewport.deviceScaleFactor')}: ${deviceScaleFactor}`,
                                                                            isMobile && translate('customDevice.fields.viewport.isMobile'),
                                                                            hasTouch && translate('customDevice.fields.viewport.hasTouch'),
                                                                            isLandscape ?
                                                                                translate('customDevice.fields.viewport.orientation.portrait') :
                                                                                translate('customDevice.fields.viewport.orientation.landscape'),
                                                                        ].filter(Boolean).join(', ')
                                                                    })
                                                                    </Typography>
                                                                </Stack>
                                                                <Divider/>
                                                            </Fragment>
                                                        );
                                                    }}
                                                    renderInput={(params) => (
                                                        <TextFieldStyle
                                                            {...params}
                                                            placeholder={translate('project.wizardSteps.target.device.description')}
                                                        />
                                                    )}
                                                    onChange={(_, newValue) => {
                                                        field.onChange(newValue);
                                                    }}
                                                    disabled={editing?.isNotEditable}
                                                />
                                                {
                                                    !editing?.isNotEditable &&
                                                    <AddButton
                                                        onClick={() => {
                                                            handleNewDevice({
                                                                title: translate('customDevice.add'),
                                                                device: newDevicePlaceholder,
                                                                handleSave: ({device}) => {
                                                                    setAvailableDeviceList([
                                                                        ...availableDeviceList,
                                                                        device,
                                                                    ]);
                                                                    setValue('device', device);

                                                                    PubSub.publish('HIDE.DIALOG.ALL');
                                                                },
                                                                handleCancel: () => {
                                                                    PubSub.publish('HIDE.DIALOG.ALL');
                                                                },
                                                            });
                                                        }}
                                                        label={translate('customDevice.add')}
                                                        sx={{
                                                            my: 'auto',
                                                            alignSelf: 'flex-end',
                                                        }}
                                                    />
                                                }
                                            </Stack>
                                            {
                                                error?.message &&
                                                <Typography
                                                    gutterBottom
                                                    variant="body2"
                                                    sx={{color: 'error.main'}}>
                                                    {error?.message}
                                                </Typography>
                                            }
                                        </Stack>
                                    );
                                }}
                            />
                        )
                    }
                </Stack>
                {
                    urlCaptureConfig && (
                        <Controller
                            name="checkType"
                            control={control}
                            render={({field}) => {
                                return (
                                    <URLCaptureDialogForm
                                        {...urlCaptureConfig}
                                        title={(
                                            <Typography gutterBottom variant="body2">
                                                {translate(`project.wizardSteps.target.checkType.groups.${field.value}.ctaLabel`)}
                                            </Typography>
                                        )}
                                        urls={urls}
                                    />
                                );
                            }}
                        />
                    )
                }
            </Wrapper>
        </div>
    );
};

ProjectWizardTarget.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardTarget);
