import {Fragment, useEffect} from 'react';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Controller, useForm} from 'react-hook-form';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Alert,
    FormControlLabel,
    FormGroup,
    Radio,
    Stack,
    Switch,
    Typography,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import {setLinkOptions} from '../../../redux/projectAssistant/store';
import URLFilter from '../forms/URLFilter';
import InfoPopover from '../utils/InfoPopover';
import LabelStyle from '../utils/LabelStyle';

const i18nContext = 'project.wizardSteps.linkOptions.groups.urlFiltering';

const mapStateToProps = ({projectAssistant}) => {
    const {
        linkOptions: {
            filtering,
        },
    } = projectAssistant.configData;

    return {
        filtering,
    };
};

const propTypes = {
    filtering: PropTypes.object,
};

const ProjectWizardLinkOptionsContentUrlFiltering = ({filtering}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: filtering,
    });
    const {
        control,
        watch,
    } = formContext;
    useEffect(() => {
        return () => {
            const filtering = watch();
            dispatch(setLinkOptions({filtering}));
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const useResolvedURLBaseLabel = translate(`${i18nContext}.fields.useResolvedURLBase.label`);

    return (
        <Stack spacing={3}>
            <Stack spacing={1.5}>
                <Stack
                    direction="row"
                    alignItems="center"
                    spacing={.5}>
                    <LabelStyle>
                        {useResolvedURLBaseLabel}
                    </LabelStyle>
                    <InfoPopover
                        isLabel>
                        {translate(`${i18nContext}.fields.useResolvedURLBase.description`)}
                    </InfoPopover>
                </Stack>
                <Controller
                    name="useResolvedURLBase"
                    control={control}
                    render={({field}) => {
                        return (
                            <Stack>
                                <FormControlLabel
                                    control={<Radio
                                        {...field}
                                        checked={field.value}
                                        onChange={() => {
                                            field.onChange(true);
                                        }}
                                    />}
                                    label={translate(`${i18nContext}.fields.useResolvedURLBase.option`)}
                                />
                                <FormControlLabel
                                    control={<Radio
                                        {...field}
                                        checked={!field.value}
                                        onChange={() => {
                                            field.onChange(false);
                                        }}
                                    />}
                                    label={translate(`${i18nContext}.fields.useResolvedURLBase.optionAlt`)}
                                />
                            </Stack>
                        );
                    }}
                />
            </Stack>
            {
                [
                    {item: 'subdomains', name: 'includeSubdomains'},
                    {item: 'externalLinks', name: 'includeExternalLinks'},
                    {item: 'internalLinks', name: 'includeInternalLinks'},
                ].map(({item, name}) => {
                    const description = translate([`${i18nContext}.fields.${item}.description`, 'void']);
                    const notification = translate([`${i18nContext}.fields.${item}.notification`, 'void'], {
                        useResolvedURLBaseLabel,
                    });
                    return (
                        <Fragment key={item}>
                            <Accordion defaultExpanded>
                                <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                                    <LabelStyle>
                                        {translate(`${i18nContext}.fields.${item}.label`)}
                                    </LabelStyle>
                                </AccordionSummary>
                                <AccordionDetails>
                                    {
                                        description &&
                                        <Typography>
                                            {description}
                                        </Typography>
                                    }
                                    {
                                        notification &&
                                        <Alert severity="info" sx={{my: 3}}>{notification}</Alert>
                                    }
                                    <FormGroup row>
                                        <Controller
                                            name={name}
                                            control={control}
                                            render={({field}) => {
                                                return (
                                                    <FormControlLabel
                                                        label={
                                                            field.value ?
                                                                translate(`${i18nContext}.fields.${item}.option`) :
                                                                translate(`${i18nContext}.fields.${item}.optionAlt`)
                                                        }
                                                        control={
                                                            <Switch
                                                                onChange={(_, value) => {
                                                                    field.onChange(value);
                                                                }}
                                                                checked={field.value}
                                                            />
                                                        }
                                                    />
                                                );
                                            }}
                                        />
                                    </FormGroup>
                                </AccordionDetails>
                            </Accordion>
                        </Fragment>
                    );
                })
            }
            <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                    <LabelStyle>
                        {translate(`${i18nContext}.fields.urlMatch.label`)}
                    </LabelStyle>
                </AccordionSummary>
                <AccordionDetails>
                    <Typography component="p" variant="heading" sx={{mb: 2}}>
                        {translate(`${i18nContext}.fields.urlMatch.title`)}
                    </Typography>
                    <Typography component="p" variant="body2" sx={{mb: 2}}>
                        {translate('urlCapture.description')}
                    </Typography>
                    <URLFilter
                        value={filtering.urlFilter}
                        onChange={(value) => formContext.setValue('urlFilter', value)}
                    />
                </AccordionDetails>
            </Accordion>
        </Stack>
    );
};

ProjectWizardLinkOptionsContentUrlFiltering.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectWizardLinkOptionsContentUrlFiltering);
