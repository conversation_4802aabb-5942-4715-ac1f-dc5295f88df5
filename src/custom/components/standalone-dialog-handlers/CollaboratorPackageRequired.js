import PropTypes from 'prop-types';
import {useNavigate} from 'react-router-dom';
import {Alert, AlertTitle, Box, Button, Stack} from '@material-ui/core';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {
    NOTIFICATIONS,
} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';
import {packageTypes} from '../../pages/ShopPage';
import Drawer from '../forms/Drawer';

const propTypes = {
    open: PropTypes.bool,
    handleClose: PropTypes.func,
};

const CollaboratorPackageRequired = ({
    open,
    handleClose,
}) => {
    const {translate} = useLocales();
    const navigate = useNavigate();

    return (
        <Drawer
            open={open}
            title={translate('notice')}
        >
            <Alert severity="info">
                <AlertTitle>{translate('pricing.otp.noActiveLabel')}</AlertTitle>
            </Alert>
            <Stack spacing={3}>
                <Box>
                    {translate(`directives.notifications.${NOTIFICATIONS.collaboratorPackageRequired}`, {
                        package: translate('pricing.otp.collaboratorSeats.label'),
                    })}
                </Box>
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="flex-end"
                    spacing={1}
                    sx={{my: 3}}>
                    <Button
                        variant="text"
                        sx={{textTransform: 'none'}}
                        onClick={() => {
                            handleClose();
                        }}>
                        {translate('cancel')}
                    </Button>
                    <Button
                        variant="contained"
                        sx={{textTransform: 'none'}}
                        onClick={() => {
                            handleClose();
                            navigate(`${PATH_PAGE.pricing}?${urlParameters.pricing.tab}=${packageTypes.otp}`);
                        }}>
                        {translate('pricing.cta')}
                    </Button>
                </Stack>
            </Stack>
        </Drawer>
    )
};

CollaboratorPackageRequired.propTypes = propTypes;

export default CollaboratorPackageRequired;
