import {<PERSON>complete, Button, Checkbox, Grid, Stack, TextField, Typography} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import PubSub from 'pubsub-js';
import * as Yup from 'yup';
import PropTypes from 'prop-types';
import {yupResolver} from '@hookform/resolvers/yup';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {
    roleOptions,
    requestOptions,
    interestOptions,
} from '@w7-3/webeagle-resources/dist/config/contactFormRequests';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {accountStates} from '@w7-3/webeagle-resources/dist/config/account';
import useLocales from '../../../hooks/useLocales';
import Drawer from '../forms/Drawer';
import TextFieldStyle from '../utils/TextFieldStyle';
import SubmitButton from '../utils/SubmitButton';
import useApiCaller from '../../hooks/useApiCaller';
import {getOpenAPIPath} from '../../utils/getPath';
import {getEmailSchema, getFullUrlSchema} from '../../utils/formSchema';
import useAuth from '../../../hooks/useAuth';
import LabelStyle from '../utils/LabelStyle';
import {useSelector} from '../../../redux/store';

const propTypes = {
    open: PropTypes.bool,
    handleClose: PropTypes.func,
    state: PropTypes.object,
};

const ContactForm = ({
    open,
    handleClose,
    state,
}) => {
    const {translate, currentLang,} = useLocales();
    const {isAuthenticated, user} = useAuth();
    const {accountState} = useSelector(({state}) => {
        const {
            accountState,
        } = state;

        return {
            accountState,
        };
    });
    const request = state?.props?.request;
    const {
        watch,
        control,
        trigger,
        formState
    } = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            request: Yup.mixed().test(
                '',
                translate('form.validation.selectionRequired'),
                (value) => {
                    return !!value;
                },
            ),
            name: Yup.string().required(translate('form.validation.fieldRequired')),
            lastName: Yup.string().required(translate('form.validation.fieldRequired')),
            email: getEmailSchema({
                emailRequired: translate('form.validation.emailRequired'),
                emailInvalid: translate('form.validation.emailInvalid'),
            }),
            website: getFullUrlSchema({translate}),
            role: Yup.mixed().test(
                '',
                translate('form.validation.selectionRequired'),
                (value) => {
                    return !!value;
                },
            ),
            interests: Yup.mixed().test(
                '',
                translate('form.validation.selectionRequired'),
                (value) => {
                    return isNonEmptyArray(value);
                },
            ),
            message: Yup.string().required(translate('form.validation.fieldRequired')),
        })),
        defaultValues: {
            request: null,
            name: '',
            lastName: '',
            email: '',
            company: '',
            website: '',
            role: null,
            interests: [],
            ...getOptionalMap(request, {request}),
            ...getOptionalMap(isAuthenticated, {
                email: user?.email,
            }),

        },
    });
    const apiCaller = useApiCaller();
    const getIsValid = () => formState.isDirty && Object.keys(formState.errors).length === 0;
    const handleSubmit = () => {
        if (!getIsValid()) {
            return;
        }

        const data = watch();
        apiCaller({
            uri: getOpenAPIPath(api.sendRequest),
            data: {
                ...data,
                language: currentLang.value,
            },
            successCallback: () => {
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate('contactUs.notifications.success'),
                    variant: 'success',
                });
                PubSub.publish('UPDATE.ACCOUNT.RELATED.DATA');
                handleClose();
            },
        });
    };

    return (
        <Drawer
            open={open}
            onClose={handleClose}
            title={(
                <Typography variant="subtitle1">
                    {
                        request ?
                            translate(`contactUs.contactForm.requestOptions.${request}`) :
                            translate('contactUs.contactForm.label')
                    }
                </Typography>
            )}
        >
            <Grid container spacing={3} sx={{ml: (theme) => `-${theme.spacing(3)} !important`}}>
                {
                    !request && (
                        <Controller
                            name="request"
                            control={control}
                            render={({
                                field,
                                fieldState: {error}
                            }) => {
                                return (
                                    <Grid item xs={12}>
                                        <Stack spacing={1}>
                                            <LabelStyle>{translate('form.request')}</LabelStyle>
                                            <Autocomplete
                                                fullWidth
                                                {...field}
                                                options={Object.values(requestOptions)
                                                    .filter(({isLoginRequired}) => {
                                                        return !isLoginRequired || isAuthenticated;
                                                    })
                                                    .filter(({key}) => {
                                                        if (key !== requestOptions.demo.key) {
                                                            return true;
                                                        }

                                                        return accountState === accountStates.new;
                                                    })
                                                    .map(({key}) => key)}
                                                getOptionLabel={(option) => translate(`contactUs.contactForm.requestOptions.${option}`)}
                                                renderOption={(atts) => {
                                                    return (
                                                        <li {...atts}>
                                                            <Typography
                                                                component="p"
                                                                variant="body2"
                                                                sx={{
                                                                    py: 1,
                                                                    wordBreak: 'break-word',
                                                                }}>
                                                                {atts.key}
                                                            </Typography>
                                                        </li>
                                                    );
                                                }}
                                                renderInput={(params) => {
                                                    return (
                                                        <TextFieldStyle
                                                            {...params}
                                                        />
                                                    );
                                                }}
                                                onChange={(_, newValue) => {
                                                    field.onChange(newValue);
                                                }}
                                                disabled={!!request}
                                            />
                                            <Typography
                                                sx={{color: 'error.main'}}>
                                                {error?.message}
                                            </Typography>
                                        </Stack>
                                    </Grid>
                                );
                            }}
                        />
                    )
                }
                {
                    [
                        ['name', 'text'],
                        ['lastName', 'text'],
                        ['email', 'email', isAuthenticated],
                        ['company', 'text'],
                        ['website', 'url'],
                    ].map(([option, type, disabled]) => (
                        <Controller
                            key={option}
                            name={option}
                            control={control}
                            render={({
                                field,
                                fieldState: {error},
                            }) => {
                                return (
                                    <Grid item xs={12} md={6}>
                                        <Stack spacing={1}>
                                            <LabelStyle>{translate(`form.${option}`)}</LabelStyle>
                                            <TextField
                                                fullWidth
                                                type={type}
                                                disabled={disabled}
                                                {...field}
                                            />
                                            {
                                                error?.message && (
                                                    <Typography
                                                        sx={{color: 'error.main'}}>
                                                        {error?.message}
                                                    </Typography>
                                                )
                                            }
                                        </Stack>
                                    </Grid>
                                );
                            }}
                        />
                    ))
                }
                <Controller
                    name="role"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <Grid item xs={12}>
                                <Stack spacing={1}>
                                    <LabelStyle>{translate('form.role')}</LabelStyle>
                                    <Autocomplete
                                        fullWidth
                                        {...field}
                                        options={Object.keys(roleOptions)}
                                        getOptionLabel={(option) => translate(`contactUs.contactForm.roleOptions.${option}`)}
                                        renderOption={(atts) => {
                                            return (
                                                <li {...atts}>
                                                    <Typography
                                                        component="p"
                                                        variant="body2"
                                                        sx={{
                                                            py: 1,
                                                            wordBreak: 'break-word',
                                                        }}>
                                                        {atts.key}
                                                    </Typography>
                                                </li>
                                            );
                                        }}
                                        renderInput={(params) => {
                                            return (
                                                <TextFieldStyle
                                                    {...params}
                                                />
                                            );
                                        }}
                                        onChange={(_, newValue) => {
                                            field.onChange(newValue);
                                        }}
                                    />
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                </Stack>
                            </Grid>
                        );
                    }}
                />
                <Controller
                    name="interests"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <Grid item xs={12}>
                                <Stack spacing={1}>
                                    <LabelStyle>{translate('form.interests')}</LabelStyle>
                                    <Autocomplete
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        {...field}
                                        options={Object.keys(interestOptions)}
                                        getOptionLabel={(option) => translate(`contactUs.contactForm.interestOptions.${option}`)}
                                        renderOption={(atts, option, {selected}) => {
                                            return (
                                                <li {...atts}>
                                                    <Checkbox checked={selected}/>
                                                    {atts.key}
                                                </li>
                                            );
                                        }}
                                        renderInput={(params) => (
                                            <TextFieldStyle
                                                {...params}
                                            />
                                        )}
                                        onChange={(_, newValue) => {
                                            field.onChange(newValue);
                                        }}
                                    />
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                </Stack>
                            </Grid>
                        );
                    }}
                />
                <Controller
                    name="message"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <Grid item xs={12}>
                                <Stack spacing={1}>
                                    <LabelStyle>{translate('form.message')}</LabelStyle>
                                    <TextField
                                        fullWidth
                                        multiline
                                        rows={5}
                                        {...field}
                                    />
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                </Stack>
                            </Grid>
                        );
                    }}
                />
                <Grid item xs={12}>
                    <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                        <Button
                            color="inherit"
                            onClick={handleClose}>
                            {translate('cancel')}
                        </Button>
                        <SubmitButton
                            onClick={async () => {
                                await trigger();
                                queueCallback(handleSubmit);
                            }}
                            isValid={getIsValid()}
                        />
                    </Stack>
                </Grid>
            </Grid>
        </Drawer>
    )
};

ContactForm.propTypes = propTypes;

export default ContactForm;
