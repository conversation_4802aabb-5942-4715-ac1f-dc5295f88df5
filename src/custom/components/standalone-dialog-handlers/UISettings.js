import {<PERSON><PERSON>, Stack, Typography} from '@material-ui/core';
import PropTypes from 'prop-types';
import useLocales from '../../../hooks/useLocales';
import SettingMode from '../../../components/settings/SettingMode';
import SettingColor from '../../../components/settings/SettingColor';
import SettingFullscreen from '../../../components/settings/SettingFullscreen';
import SettingFontSize from '../../../components/settings/SettingFontSize';
import LanguageChange from '../LanguageChange';
import Drawer from '../forms/Drawer';
import {MHidden} from '../../../components/@material-extend';

const propTypes = {
    open: PropTypes.bool,
    handleClose: PropTypes.func,
};

const UISettings = ({
    open,
    handleClose,
}) => {
    const {
        currentLang,
        translate,
        onChangeLang,
    } = useLocales();

    return (
        <Drawer
            open={open}
            onClose={handleClose}
            title={(
                <Typography variant="subtitle1">
                    {translate('settings.title')}
                </Typography>
            )}
        >
            <Stack
                spacing={3}
                sx={{
                    pt: 3,
                    px: 3,
                    pb: 15,
                }}>
                <Stack spacing={1.5}>
                    <Typography variant="subtitle2">{translate('settings.language')}</Typography>
                    <LanguageChange
                        language={currentLang.value}
                        onChange={onChangeLang}
                    />
                </Stack>
                <Stack spacing={1.5}>
                    <Typography variant="subtitle2">{translate('settings.fontSize.label')}</Typography>
                    <SettingFontSize />
                </Stack>
                <Stack spacing={1.5}>
                    <Typography variant="subtitle2">{translate('settings.appearance')}</Typography>
                    <SettingMode />
                </Stack>
                <Stack spacing={1.5}>
                    <Typography variant="subtitle2">{translate('settings.accent')}</Typography>
                    <SettingColor />
                </Stack>
                <MHidden width="mdDown">
                    <SettingFullscreen />
                </MHidden>
            </Stack>
            <Button onClick={handleClose} variant="contained">
                {translate('close')}
            </Button>
        </Drawer>
    );
};

UISettings.propTypes = propTypes;

export default UISettings;
