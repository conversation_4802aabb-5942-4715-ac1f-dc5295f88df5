import {Box, FormControlLabel, Radio, RadioGroup, Stack, Typography, useMediaQuery} from '@material-ui/core';
import PropTypes from 'prop-types';
import {useTheme} from '@material-ui/core/styles';
import useLocales from '../../hooks/useLocales';

const propTypes = {
    language: PropTypes.string,
    onChange: PropTypes.func,
    showLabel: PropTypes.bool,
};
const defaultProps = {
    showLabel: true,
};

const LanguageChange = ({
    language,
    onChange,
    showLabel,
}) => {
    const {allLang} = useLocales();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

    return (
        <RadioGroup
            row={!isMobile}
            value={language}
            onChange={(_, newLanguage) => {
                onChange(newLanguage);
            }}
        >
            {allLang.map((option) => (
                <FormControlLabel
                    key={option.label}
                    value={option.value}
                    label={(
                        <Stack direction="row" spacing={1}>
                            <Box component="img" alt={option.label} src={option.icon}/>
                            {
                                showLabel && (
                                    <Typography variant="body2" sx={{mr: 1}}>
                                        {option.label}
                                    </Typography>
                                )
                            }
                        </Stack>
                    )}
                    control={<Radio/>}/>
            ))}
        </RadioGroup>
    );
};

LanguageChange.defaultProps = defaultProps;
LanguageChange.propTypes = propTypes;

export default LanguageChange;
