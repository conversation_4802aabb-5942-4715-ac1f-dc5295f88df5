import {useState} from 'react';
import PropTypes from 'prop-types';
import {Box, Button, Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import {useSnackbar} from 'notistack5';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import ResourceInjectionTable from './ResourceInjectionTable';
import ResourceInjectionDialogForm from './ResourceInjectionDialogForm';
import {toggleArrayObjectItem} from '../../utils/toggle';

const initialState = {
    show: false,
    item: null,
    id: undefined,
};
const propTypes = {
    itemList: PropTypes.arrayOf(PropTypes.shape({
        contentType: PropTypes.string,
        injectionType: PropTypes.string,
        source: PropTypes.string,
        content: PropTypes.object,
    })),
    onChange: PropTypes.func,
};

const i18nContext = 'project.wizardSteps.requestOptions.groups.resources.fields.resourcesInjection';

const ResourceInjectionForm = ({onChange, itemList}) => {
    const {translate} = useLocales();
    const {enqueueSnackbar} = useSnackbar();
    const [resourcesInjectionDialogState, setResourcesInjectionDialogState] = useState(initialState);
    const handleUpdate = (id, item) => {
        setResourcesInjectionDialogState(initialState);
        if (!item) {
            return;
        }

        const newList = toggleArrayObjectItem({
            item: {
                ...item,
                id,
            },
            idField: 'id',
            list: itemList,
            add: true,
        });
        onChange(newList);
    };
    const handleClickEdit = (item) => {
        setResourcesInjectionDialogState({
            show: true,
            item,
            id: item.id,
        });
    };
    const handleClickDelete = (item) => {
        const newList = toggleArrayObjectItem({
            item,
            idField: 'id',
            list: itemList,
            add: false,
        });
        onChange(newList);
        enqueueSnackbar(translate(`${i18nContext}.notifications.deleteSuccess`), {variant: 'success'});
    };

    return (
        <Stack
            direction="column"
            spacing={3}>
            {
                itemList?.length > 0 &&
                <ResourceInjectionTable
                    itemList={itemList}
                    handleClickEdit={handleClickEdit}
                    handleClickDelete={handleClickDelete}
                />
            }
            <Box sx={{
                display: 'flex',
                ...getOptionalMap(itemList?.length < 1, {
                    flexDirection: 'column',
                })
            }}>
                <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                    onClick={() => setResourcesInjectionDialogState({
                        show: true,
                    })}
                    sx={{textTransform: 'none'}}>
                    {translate(`${i18nContext}.label`)}
                </Button>
            </Box>
            {
                resourcesInjectionDialogState.show &&
                <ResourceInjectionDialogForm
                    id={resourcesInjectionDialogState.id}
                    item={resourcesInjectionDialogState.item}
                    itemList={itemList}
                    updateDialogState={handleUpdate}
                />
            }
        </Stack>
    );
};

ResourceInjectionForm.propTypes = propTypes;

export default ResourceInjectionForm;
