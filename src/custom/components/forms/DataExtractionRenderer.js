import * as Yup from 'yup';
import PubSub from 'pubsub-js';
import {useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Box, Button, FormControlLabel, Stack, Switch} from '@material-ui/core';
import {yupResolver} from '@hookform/resolvers/yup';
import dataExtractions from '@w7-3/webeagle-resources/dist/config/dataExtractions';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';
import {getLabellingValidation} from '../../utils/formSchema';
import AutomatedDataExtractions from '../services/solutions/dataExtractions/AutomatedDataExtractions';
import SelectorTextField from './SelectorTextField';
import {ItemRendererPropTypes} from '../../../config/prop-types/ProjectSteps';
import ItemLabellingStrategy from '../utils/ItemLabellingStrategy';
import ProjectStepStateForm from './ProjectStepStateForm';
import CustomDataExtractions from '../services/solutions/dataExtractions/CustomDataExtractions';

const renderers = {
    [dataExtractions.custom.js.value]: CustomDataExtractions,
    [dataExtractions.automated.ai.value]: AutomatedDataExtractions,
};

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.dataExtractions';

const DataExtractionRenderer = (props) => {
    const {
        item,
        labelBlacklist,
        handleSave,
        handleCancel,
        labels,
        isPreview,
        showAbortOnError,
        showIgnoreOnError,
    } = props;
    const Renderer = renderers[item?.type];
    const {translate} = useLocales();
    const [isInactive, setIsInactive] = useState(item?.isInactive);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            ...getLabellingValidation({
                label: {
                    error: translate('itemLabelling.mandatoryField'),
                    duplicate: translate('itemLabelling.duplicateGroupName'),
                    allowEmpty: false,
                },
                labelScript: {
                    error: translate('form.validation.script'),
                },
                labelBlacklist,
            }),
        })),
        defaultValues: item,
    });
    const {
        watch,
        control,
        formState,
    } = formContext;

    useEffect(() => {
        if (Renderer) {
            return;
        }

        PubSub.publish(
            'LOG.EVENT',
            {
                uiData: {
                    type: INFO_CODE_LEVELS.ERROR,
                    message: `Cannot find Renderer for WebScraping: ${item?.type}`,
                    data: {
                        item,
                    }
                },
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [Renderer]);

    if (!Renderer) {
        return (
            <Stack spacing={3}>
                <Alert severity="error">
                    {translate(`${i18nContext}.unavailable`)}
                </Alert>
                <Box sx={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center'}}>
                    <Button color="inherit" onClick={handleCancel}>
                        {translate('cancel')}
                    </Button>
                </Box>
            </Stack>
        );
    }

    const renderIgnoreOnError = !isPreview && !showAbortOnError && showIgnoreOnError && Boolean(labels.ignoreActionError);
    const renderWillIgnoreOnError = !isPreview && !showAbortOnError && !renderIgnoreOnError && Boolean(labels.willIgnoreActionError);

    return (
        <>
            <ProjectStepStateForm
                isInactive={isInactive}
                setIsInactive={setIsInactive}
            />
            <Renderer
                {...props}
                parentFormContext={formContext}
                i18nContextRoot={i18nContext}
                handleSave={({value}) => {
                    const formData = watch();

                    handleSave({
                        ...item,
                        ...formData,
                        value,
                        isInactive,
                    });
                }}
                selectorNode={(
                    <SelectorTextField
                        control={control}
                        formState={formState}
                        name="selector"
                        showInitialError
                    />
                )}
                labelNode={(
                    <ItemLabellingStrategy
                        formContext={formContext}
                    />
                )}
                abortOnErrorNode={showAbortOnError && (
                    <Controller
                        name="abortOnError"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={labels.abortion}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                )}
                ignoreOnErrorNode={renderIgnoreOnError && (
                    <Controller
                        name="ignoreOnError"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={labels.ignoreActionError}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                )}
                willIgnoreOnErrorNode={(renderWillIgnoreOnError && (
                    <Alert severity="info">
                        {labels.willIgnoreActionError}
                    </Alert>
                ))}
            />
        </>
    );
};

DataExtractionRenderer.propTypes = ItemRendererPropTypes;

export default DataExtractionRenderer;
