import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {useEffect, useState} from 'react';
import {Box, Button, CardActionArea, List, ListItemButton, Stack, Typography} from '@material-ui/core';
import plusFill from '@iconify/icons-eva/plus-fill';
import closeOutline from '@iconify/icons-eva/close-outline';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {DocIllustration} from '../../../assets';
import useLocales from '../../../hooks/useLocales';
import Drawer from './Drawer';

const propTypes = {
    hasItems: PropTypes.bool,
    configList: PropTypes.arrayOf(PropTypes.shape({
        label: PropTypes.string,
        callback: PropTypes.func,
    })),
    labels: PropTypes.shape({
        startAddingSteps: PropTypes.string,
        addStep: PropTypes.string,
    }),
    callback: PropTypes.func,
};

const ProjectSolutionStepSelectionForm = ({
    hasItems,
    configList,
    labels,
    callback,
}) => {
    const {translate} = useLocales();
    const [state, setState] = useState({});
    const title = hasItems ?
        labels?.addStep || translate('workflowSteps.addStep') :
        labels?.startAddingSteps || translate('workflowSteps.startAddingSteps');

    useEffect(() => {
        const token1 = PubSub.subscribe('OPEN.STEP.SELECTION.FORM', (_, {position}) => {
            setState({
                open: true,
                position,
            });
        });

        const token2 = PubSub.subscribe('CLOSE.STEP.SELECTION.FORM', () => {
            setState({
                ...state,
                open: false,
            });
        });

        return () => {
            PubSub.unsubscribe(token1);
            PubSub.unsubscribe(token2);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (!state.open) {
            return;
        }

        PubSub.publish('HIDE.NOTIFICATIONS.ALL');
    }, [state.open]);

    return (
        <Box sx={{
            bgcolor: 'background.neutral',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flex: '1',
            minWidth: '300px',
            minHeight: '150px',
            position: 'relative',
        }}>
            <CardActionArea
                sx={{
                    color: 'primary.main',
                    display: 'flex',
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    ...getOptionalMap(hasItems, {
                        display: 'block',
                        p: 1,
                        mr: 2,
                    }),
                }}
                onClick={() => {
                    setState({
                        open: true,
                    });
                    callback();
                }}>
                <DocIllustration sx={{width: 120}}/>
                <Typography variant="h6" sx={{
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                }}>
                    <Box
                        component={Icon}
                        icon={plusFill} sx={{
                            mr: 1,
                            width: 24,
                            height: 24,
                        }}/>
                    {title}
                </Typography>
            </CardActionArea>
            <Drawer
                open={state.open}
                title={(
                    <Typography variant="subtitle1">
                        {translate('workflowSteps.step')}
                    </Typography>
                )}
                onClose={() => setState({})}>
                <List component="nav" aria-label={title}>
                    {
                        configList.map((item) => {
                            if (!item?.callback || !item.label) {
                                return null;
                            }

                            return (
                                <ListItemButton
                                    key={item.label}
                                    onClick={() => item.callback(state.position)}>
                                    {item.label}
                                </ListItemButton>
                            );
                        })
                    }
                </List>
                <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                    <Button
                        color="inherit"
                        startIcon={<Icon icon={closeOutline} width={40} height={40}/>}
                        onClick={() => {
                            PubSub.publish('CLOSE.STEP.SELECTION.FORM');
                        }}>
                        {translate('close')}
                    </Button>
                </Stack>
            </Drawer>
        </Box>
    );
};

ProjectSolutionStepSelectionForm.propTypes = propTypes;

export default ProjectSolutionStepSelectionForm;
