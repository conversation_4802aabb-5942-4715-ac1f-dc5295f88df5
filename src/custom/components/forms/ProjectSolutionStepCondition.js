import PropTypes from 'prop-types';
import {ACTIONS} from '@w7-3/webeagle-resources/dist/config/project';
import {confidenceLevelDefault} from '@w7-3/webeagle-resources/dist/config/ai';
import useLocales from '../../../hooks/useLocales';
import ProjectItemLabels from '../project/ProjectItemLabels';
import getProjectSolutionStepItem from '../../utils/getProjectSolutionStepItem';

const i18nContext = 'conditions';

const propTypes = {
    item: PropTypes.object.isRequired,
};

export const ProjectSolutionStepConditionPreviewFooter = ({
    item,
}) => {
    const {translate} = useLocales();

    return (
        <ProjectItemLabels
            item={item}
            i18nContext={i18nContext}
            label={translate(`${i18nContext}.options.${item.type}.label`)}
        />
    );
};

ProjectSolutionStepConditionPreviewFooter.propTypes = propTypes;

export const getSetNewItemProjectSolutionStepCondition = ({translate, setDialogState}) => (
    ({stepType, item: {type}, originalStepIndex}) => {
        const item = getProjectSolutionStepItem({
            originalStepIndex,
            type,
            stepType,
            tempLabel:  translate(`${i18nContext}.options.${type}.namePlaceholder`),
            stepList: [],
            value: {
                code: '',
                confidenceLevel: confidenceLevelDefault,
            },
        });

        setDialogState({
            show: true,
            item,
            action: ACTIONS.add,
        });
    }
);
