import PropTypes from 'prop-types';
import * as w73 from '@w7-3/webeagle-resources/dist/config/w73';
import {getRandomString, libraryKeys} from '@w7-3/webeagle-resources/dist/libs/random';
import {ACTIONS} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../hooks/useLocales';
import ProjectItemLabels from '../project/ProjectItemLabels';
import getProjectSolutionStepItem from '../../utils/getProjectSolutionStepItem';

const propTypes = {
    item: PropTypes.object.isRequired,
};

const i18nContext = 'loops';

export const ProjectSolutionStepLoopPreviewFooter = ({
    item,
}) => {
    const {translate} = useLocales();

    return (
        <ProjectItemLabels
            item={item}
            i18nContext={i18nContext}
            label={translate(`${i18nContext}.options.${item.type}.label`)}
        />
    );
};

ProjectSolutionStepLoopPreviewFooter.propTypes = propTypes;

export const getSetNewItemProjectSolutionStepLoop = ({translate, setDialogState}) => (
    ({stepType, item: {type}, originalStepIndex}) => {
        const item = getProjectSolutionStepItem({
            originalStepIndex,
            type,
            stepType,
            tempLabel:  translate(`${i18nContext}.options.${type}.namePlaceholder`),
            stepList: [],
            breakConditionJSCode: {
                value: '',
                isValid: true,
                isValidated: true,
            },
            indexReference: `${w73.GLOBAL_PARAMETER}_${w73.PARAMETERS}_${getRandomString(8, libraryKeys.alphaUpperCased)}`,
        });

        setDialogState({
            show: true,
            item,
            action: ACTIONS.add,
        });
    }
);
