import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, LinearProgress, Stack, Typography} from '@material-ui/core';
import PubSub from 'pubsub-js';
import CodeMirror from '@uiw/react-codemirror';
import {javascript} from '@codemirror/lang-javascript';
import {useTheme} from '@material-ui/core/styles';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import {maxDimensions} from '@w7-3/webeagle-resources/dist/config/screenshots';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import useLocales from '../../../hooks/useLocales';
import InfoPopover from '../utils/InfoPopover';
import useApiCaller from '../../hooks/useApiCaller';
import {getAssistantAPIPath} from '../../utils/getPath';
import FunctionLabel from './codeTextField/FunctionLabel';
import Scrollbar from '../../../components/Scrollbar';
import useCodeTextFieldExtension from '../../hooks/useCodeTextFieldExtension';

const i18nContext = 'codeField.js';
const placeholders = {
    standard: {
        prePlaceholder: 'function getValue() {',
        placeholder: [
            ' *  var elem = document.querySelector(".target");',
            ' *  if (!elem) {',
            ' *     return "Element mit Selektor ".target" wurde nicht gefunden";',
            ' *  }',
            ' *',
            ' *  return elem.textContent; */',
        ].join('\n'),
        postPlaceholder: '};',
    },
    bool: {
        prePlaceholder: 'function getIsTruthy() {',
        placeholder: [
            ' *  var y = document.querySelector(".target");',
            ' *  if (!y) return false;',
            ' *',
            ' *  return Number.parseInt(y.textContent) === 42; */',
        ].join('\n'),
        postPlaceholder: '};',
    },
    void: {
        prePlaceholder: 'function anonymous() {',
        placeholder: [
            '/*',
            '/* JavaScript-Code hier eingeben oder einfügen */',
            '*/',
        ].join('\n'),
        postPlaceholder: '};',
    },
};

const propTypes = {
    script: PropTypes.shape({
        id: PropTypes.string,
        value: PropTypes.string,
        isValidated: PropTypes.bool,
    }),
    onChange: PropTypes.func,
    showReset: PropTypes.bool,
    label: PropTypes.string,
    isFunctionContext: PropTypes.bool,
    editorProps: PropTypes.object,
    characterLimit: PropTypes.number,
    error: PropTypes.object,
    prePlaceholder: PropTypes.string,
    placeholder: PropTypes.string,
    postPlaceholder: PropTypes.string,
    readOnly: PropTypes.bool,
    modifier: PropTypes.oneOf(['standard', 'bool', 'void']),
    i18n: PropTypes.shape({
        items: PropTypes.string,
        introList: PropTypes.string,
    }),
    paramsDocs: PropTypes.shape({
        fe: PropTypes.string,
        be: PropTypes.string,
        spec: PropTypes.object,
    })
};
const defaultProps = {
    script: {
        id: undefined,
        isValidated: false,
        value: '',
    },
    isFunctionContext: true,
    editorProps: {},
    characterLimit: maxDimensions.scriptLength,
    modifier: 'standard',
};
const initialScriptError = {
    message: undefined,
    hint: '',
};

const CodeTextFieldJS = ({
    script,
    onChange,
    showReset,
    label,
    isFunctionContext,
    editorProps,
    characterLimit,
    error,
    prePlaceholder,
    placeholder,
    postPlaceholder,
    readOnly,
    modifier,
    i18n,
    paramsDocs,
}) => {
    const {translate} = useLocales();
    const [jsScriptError, setJSScriptError] = useState(initialScriptError);
    const [isValidated, setIsValidated] = useState(script?.isValidated);
    const [showWarning, setShowWarning] = useState(true);
    const [value, setValue] = useState(script?.value || '');
    const {extensions} = useCodeTextFieldExtension({
        characterLimit,
        setValue: (newValue) => {
            setJSScriptError(initialScriptError);
            setValue(newValue);
            setIsValidated(false);
            queueCallback(() => {
                saveScript({
                    value: newValue,
                    isValid: false,
                });
            });
        },
    });
    const [initialScript] = useState(script);
    const theme = useTheme();
    const apiCaller = useApiCaller();
    const saveScript = (params) => {
        onChange({
            ...script,
            ...params,
            isValidated,
        });
    };
    const count = script?.value?.length || 0;
    const total = characterLimit;
    const rest = total - count;
    const percentageCount = (count / total) * 100;
    const items = translate(i18n?.items || `${i18nContext}.items`, {
        returnObjects: true,
        webAutomateUtils: translate('webAutomateUtils'),
    });
    const jsSyntaxIsValid = (js) => {
        let message;
        if (!isNonEmptyString(js)) {
            message = translate(`${i18nContext}.notifications.empty`);
            setJSScriptError({message});

            return {isValid: false, message};
        }

        if (modifier !== 'void' && js.indexOf('return ') < 0) {
            message = translate(`${i18nContext}.notifications.incomplete`);
            setJSScriptError({message});

            return {isValid: false, message};
        }

        setJSScriptError(initialScriptError);

        return {isValid: true};
    };
    const validate = (js) => {
        const syntaxData = jsSyntaxIsValid(js);

        if (!syntaxData.isValid) {
            PubSub.publish('SHOW.NOTIFICATION', {
                message: syntaxData.message,
                variant: 'error',
            });
            return;
        }

        apiCaller({
            uri: getAssistantAPIPath(serverlessAPI.assistantApi.uris.validateJSCode),
            data: {
                js: isFunctionContext ? `function x() {${js}}` : js,
            },
            successCallback: () => {
                setIsValidated(true);
                onChange({
                    value: js,
                    isValid: true,
                    isValidated: true,
                });

                setJSScriptError(initialScriptError);
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate(`${i18nContext}.validation.isValid`),
                    variant: 'success',
                });
            },
            failureCallback: ({data}) => {
                setJSScriptError({
                    message: translate(`${i18nContext}.validation.isInvalid`),
                    hint: data,
                });
            },
            errorCallback: () => {
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate(`${i18nContext}.validation.requestError`),
                    variant: 'error',
                });
            },
        });
    }

    useEffect(() => {
        if (!readOnly) {
            return;
        }

        setValue(script?.value);
    }, [readOnly, script?.value]);

    console.log({value});

    return (
        <Stack spacing={3}>
            {
                !readOnly &&
                <LinearProgress variant="determinate" value={percentageCount}/>
            }
            <Stack spacing={1} direction="row" justifyContent="space-between">
                <Stack spacing={1} direction="row">
                    <Typography component="p" variant="body2">
                        {label}
                    </Typography>
                    {
                        !readOnly && modifier !== 'void' && isNonEmptyArray(items) &&
                        <InfoPopover>
                            <>{
                                items.map((item) => {
                                    if (item) {
                                        return (
                                            <Typography key={item}>* {item}</Typography>
                                        );
                                    }

                                    return <br key={item} />;
                                })}
                            </>
                        </InfoPopover>
                    }
                </Stack>
                {
                    !readOnly && (
                        <Typography
                            gutterBottom
                            variant="caption"
                            sx={{
                                pt: .25,
                                ...getOptionalMap(rest < 10, {color: 'error.main'}),
                            }}>
                            {translate(`${i18nContext}.counterLabel`, {rest, total})}
                        </Typography>
                    )
                }
            </Stack>
            <Stack spacing={0}>
                {
                    isFunctionContext &&
                    <FunctionLabel
                        paramsDocs={paramsDocs}
                        label={
                            prePlaceholder ||
                            placeholders[modifier]?.prePlaceholder ||
                            placeholders.standard.prePlaceholder
                        }
                    />
                }
                <Scrollbar>
                    <CodeMirror
                        value={value}
                        minHeight="auto"
                        theme={theme.palette.mode}
                        placeholder={
                            placeholder ||
                            placeholders[modifier]?.placeholder ||
                            placeholders.standard.placeholder
                        }
                        extensions={[
                            javascript({jsx: true}),
                            ...extensions,
                        ]}
                        readOnly={readOnly}
                        {...{
                            maxLength: characterLimit,
                            maxWidth: '100%',
                            ...editorProps,
                        }}
                    />
                </Scrollbar>
                {
                    isFunctionContext &&
                    <FunctionLabel
                        paramsDocs={paramsDocs}
                        label={
                            postPlaceholder ||
                            placeholders[modifier]?.postPlaceholder ||
                            placeholders.standard.postPlaceholder
                        }
                    />
                }
                {
                    error?.message &&
                    <Typography
                        gutterBottom
                        variant="body2"
                        sx={{
                            color: 'error.main',
                            my: 1,
                        }}>
                        {error?.message}
                    </Typography>
                }
            </Stack>
            {
                !readOnly &&
                    <>
                        {
                            jsScriptError?.message && showWarning &&
                            <Alert variant="outlined"
                                   severity="error"
                                   onClose={() => {
                                       setShowWarning(false);
                                   }}>
                                {
                                    jsScriptError?.hint?.message ?
                                        <>
                                            <AlertTitle>{jsScriptError?.hint?.name}</AlertTitle>
                                            {jsScriptError?.hint?.message}
                                        </> : (
                                            jsScriptError?.message
                                        )
                                }
                            </Alert>
                        }
                        {
                            isNonEmptyString(value) && !isValidated &&
                            <Typography
                                sx={{color: 'error.main'}}>
                                {translate('codeField.js.validation.label')}
                            </Typography>
                        }
                        <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                            {
                                showReset && (
                                    <Button
                                        color="inherit"
                                        onClick={() => {
                                            setJSScriptError(initialScriptError);
                                            setValue(initialScript.value);
                                            setIsValidated(initialScriptError.isValidated);
                                            queueCallback(() => {
                                                saveScript({
                                                    value: initialScript.value,
                                                });
                                            });
                                        }}
                                        type="button"
                                        disabled={initialScript.value === value}
                                    >
                                        {translate('resetJS')}
                                    </Button>
                                )
                            }
                            <Button
                                variant="outlined"
                                onClick={() => validate(value)}
                                type="button"
                                disabled={!isNonEmptyString(value) || isValidated}>
                                {translate('validateCode.label')}
                            </Button>
                        </Stack>
                    </>
            }
        </Stack>
    );
};

CodeTextFieldJS.propTypes = propTypes;
CodeTextFieldJS.defaultProps = defaultProps;

export default CodeTextFieldJS;
