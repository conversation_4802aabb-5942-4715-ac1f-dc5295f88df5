import PropTypes from 'prop-types';
import {LinearProgress, Stack, Typography} from '@material-ui/core';
import {
    RESOURCE_CONTENT_TYPES,
} from '@w7-3/webeagle-resources/dist/config/request';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import CodeMirror from '@uiw/react-codemirror';
import {json} from '@codemirror/lang-json';
import {useTheme} from '@material-ui/core/styles';
import {useState} from 'react';
import useLocales from '../../../hooks/useLocales';
import useCodeTextFieldExtension from '../../hooks/useCodeTextFieldExtension';

const propTypes = {
    script: PropTypes.shape({
        id: PropTypes.string,
        value: PropTypes.string,
    }),
    onChange: PropTypes.func.isRequired,
    label: PropTypes.oneOfType([PropTypes.node.isRequired, PropTypes.string.isRequired]),
    characterLimit: PropTypes.number,
};
const defaultProps = {
    script: {
        id: undefined,
        value: '',
    },
    characterLimit: RESOURCE_CONTENT_TYPES.json.characterLimit,
};

const CodeTextFieldJSON = ({script, onChange, label, characterLimit}) => {
    const {translate} = useLocales();
    const theme = useTheme();
    const [value, setValue] = useState(script?.value || '');
    const {extensions} = useCodeTextFieldExtension({
        characterLimit,
        setValue: (newValue) => {
            setValue(newValue);
            onChange({
                ...script,
                value: newValue,
                isValid: true,
                isValidated: true,
            });
        },
    });

    const count = script?.value?.length || 0;
    const total = characterLimit;
    const rest = total - count;
    const percentageCount = (count / total) * 100;

    return (
        <Stack spacing={3}>
            <LinearProgress variant="determinate" value={percentageCount}/>
            <Stack spacing={1} direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{color: 'text.secondary'}}>
                    {label}
                </Typography>
                <Typography
                    gutterBottom
                    variant="caption"
                    sx={{
                        pt: .25,
                        ...getOptionalMap(rest < 10, {color: 'error.main'}),
                    }}>
                    {translate('codeField.json.counterLabel', {rest, total})}
                </Typography>
            </Stack>
            <CodeMirror
                value={value}
                theme={theme.palette.mode}
                placeholder={translate('codeField.json.placeholder')}
                extensions={[
                    json(),
                    ...extensions,
                ]}
            />
        </Stack>
    );
};

CodeTextFieldJSON.propTypes = propTypes;
CodeTextFieldJSON.defaultProps = defaultProps;

export default CodeTextFieldJSON;
