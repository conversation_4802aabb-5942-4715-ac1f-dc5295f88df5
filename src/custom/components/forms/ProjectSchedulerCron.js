import {memo, useEffect, useRef, useState} from 'react';
import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {Box, Button, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import clockFill from '@iconify/icons-eva/clock-fill';
import {BUILD_SCHEDULER_TYPES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import api, {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {Controller, useForm} from 'react-hook-form';
import useLocales from '../../../hooks/useLocales';
import SubmitButton from '../utils/SubmitButton';
import {getAssistantAPIPath, getOpenAPIPath} from '../../utils/getPath';
import useApiCaller from '../../hooks/useApiCaller';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import DefinitionList from '../utils/DefinitionList';
import Drawer from './Drawer';
import CodeTextFieldAI from './CodeTextFieldAI';

const pageSize = 5;
const type = BUILD_SCHEDULER_TYPES.CRON;
const i18nContextRoot = 'project.wizardSteps.summary.fields.scheduler.configurator.types';
const i18nContext = `${i18nContextRoot}.options.${BUILD_SCHEDULER_TYPES.CRON}`;

const propTypes = {
    startDate: PropTypes.number,
    endDate: PropTypes.number,
    onChange: PropTypes.func,
    defaultConfig: PropTypes.shape({
        cron: PropTypes.string,
        cronPrompt: PropTypes.string,
        interval: PropTypes.arrayOf(PropTypes.number),
        frequency: PropTypes.number,
        frequencyQualifier: PropTypes.string,
    }),
};

const defaultProps = {
    defaultConfig: {
        cronPrompt: '',
        value: '',
        isValid: false,
        isValidated: false,
    },
};

const ProjectSchedulerCron = ({defaultConfig, startDate, endDate, onChange}) => {
    const {translate, currentLang} = useLocales();
    const [open, setOpen] = useState(false);
    const [nextBuildList, setNextBuildList] = useState(null);
    const apiCaller = useApiCaller();
    const isBusy = useRef(false);
    const latestResponseData = useRef(null);
    const [isCodeValid, setIsCodeValid] = useState(false);
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: {
            cronPrompt: defaultConfig?.cronPrompt || defaultProps.defaultConfig?.cronPrompt,
            cron: defaultConfig?.cron || defaultProps.defaultConfig?.cron,
        },
    });
    const isValid = startDate && isCodeValid;
    const {cron, cronPrompt} = formContext.watch()
    const onValidate = ({
        value,
        successCallback,
        failureCallback,
        errorCallback,
    }) => {
        if (!isNonEmptyString(value)) {
            return;
        }

        apiCaller({
            uri: getAssistantAPIPath(serverlessAPI.assistantApi.uris.generateProjectSchedulerCron),
            data: {
                cronPrompt: value,
                language: currentLang?.value,
            },
            isAnimated: true,
            successCallback: ({data}) => {
                successCallback({data});
                latestResponseData.current = data?.data;
                formContext.setValue('cron', data?.data?.cron);
                formContext.setValue('cronPrompt', value);
            },
            failureCallback,
            errorCallback,
        });
    }
    const onSave = () => {
        onChange({
            schedulerData: {
                type,
                config: {
                    cronPrompt,
                    cron,
                },
            },
        });
        setOpen(false);
    };

    useEffect(() => {
        if (!isValid || !open || isBusy.current || !isNonEmptyString(cron)) {
            return;
        }

        isBusy.current = true;
        apiCaller({
            uri: getOpenAPIPath(api.nextProjectBuildList),
            data: {
                startDate,
                endDate,
                schedulerData: {
                    type,
                    config: {
                        cron,
                    },
                },
                pageSize,
            },
            successCallback: ({data}) => {
                setNextBuildList(data?.nextBuildList);
            },
            alwaysCallback: () => {
                isBusy.current = false;
            },
            isAnimated: true,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isValid, open, endDate, cron, startDate]);

    useEffect(() => {
        const subscriptionId = PubSub.subscribe(`SHOW_PROJECT_SCHEDULER.${type}`, (_, payload) => {
            formContext.setValue('cron', payload?.config?.cron);
            setOpen(true);
        });

        return () => {
            PubSub.unsubscribe(subscriptionId);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <Box sx={{pl: 0, textAlign: 'left'}}>
                <Button
                    startIcon={<Icon icon={clockFill} width={40} height={40}/>}
                    onClick={() => {
                        setOpen(true);
                    }}
                    sx={{
                        textTransform: 'none',
                    }}>
                    {translate(`${i18nContext}.cta`)}
                </Button>
            </Box>
            <Drawer
                open={open}
                onClose={() => {
                    setOpen(false);
                }}
                title={(
                    <Typography variant="subtitle1">
                        {translate(`${i18nContext}.cta`)}
                    </Typography>
                )}
            >
                <Typography>
                    {translate(`${i18nContext}.question`)}
                </Typography>
                <Controller
                    name="cronPrompt"
                    control={formContext.control}
                    render={({
                        field,
                    }) => (
                        <CodeTextFieldAI
                            defaultCode={{
                                value: field?.value,
                                isValid: isNonEmptyString(defaultConfig?.cronPrompt),
                                isValidated: isNonEmptyString(defaultConfig?.cronPrompt),
                            }}
                            onChange={(newCode) => {
                                if (newCode?.isValidated && newCode?.isValid) {
                                    const suggestedCron = latestResponseData.current?.cronSuggestions?.[newCode?.suggestionIndex];
                                    if (newCode?.isSuggested && suggestedCron) {
                                        newCode.cron = suggestedCron;
                                    }

                                    setIsCodeValid(true);
                                    formContext.setValue('cron', newCode?.cron);
                                    formContext.setValue('cronPrompt', newCode?.value);

                                    return;
                                }

                                setIsCodeValid(false);
                            }}
                            label={translate(`${i18nContext}.cronDescriptionInputNotice`)}
                            placeholder={translate('project.wizardSteps.summary.fields.scheduler.configurator.types.options.cron.cronDescriptionPlaceholder')}
                            validateLabel={translate('project.wizardSteps.summary.fields.scheduler.configurator.types.options.cron.validationLabel')}
                            onValidate={onValidate}
                            revalidateSuggestions
                        />
                    )}
                />
                <Box
                    sx={{
                        '*': {
                            marginRight: '4px',
                        },
                        p: {
                            display: 'inline-flex',
                            alignItems: 'center',
                        },
                        '> div > div': {
                            marginLeft: 0,
                            paddingLeft: 0,
                            '&:nth-last-child(-n+2)': {
                                display: 'none',
                            }
                        },
                        mb: 1,
                    }} />
                {
                    isNonEmptyArray(nextBuildList) && (
                        <DefinitionList
                            dataList={[{
                                key: nextBuildList.length === 1 ?
                                    translate('project.build.schedule.nextBuild') :
                                    translate('project.build.schedule.nextBuildList', {
                                        count: nextBuildList.length,
                                    }),
                                node: (
                                    <Stack spacing={1.5}>
                                        {
                                            nextBuildList.map((nextBuild) => {
                                                return (
                                                    <Box sx={{pl: 1}} key={nextBuild}>
                                                        {fDateTimeHumanReadable(nextBuild)}
                                                    </Box>
                                                );
                                            })
                                        }
                                    </Stack>
                                ),
                            }]}
                        />
                    )
                }
                <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                    <Button color="inherit" onClick={() => {
                        setOpen(false);
                    }}>
                        {translate('cancel')}
                    </Button>
                    <SubmitButton
                        onClick={onSave}
                        disabled={!isNonEmptyArray(nextBuildList)}
                        isValid={isValid}
                    />
                </Stack>
            </Drawer>
        </>
    );
};

ProjectSchedulerCron.propTypes = propTypes;
ProjectSchedulerCron.defaultProps = defaultProps;

export default memo(ProjectSchedulerCron, () => true);
