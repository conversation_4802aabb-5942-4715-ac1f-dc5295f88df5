import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {Box, Button, Typography} from '@material-ui/core';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {useEffect, useState} from 'react';
import {logGlobalError} from '../../../../pages/privacy-policy/PrivacyPolicy';
import Markdown from '../../../../components/Markdown';

const propTypes = {
    paramsDocs: PropTypes.shape({
        fe: PropTypes.string,
        be: PropTypes.string,
        spec: PropTypes.object,
    }),
    label: PropTypes.string,
};

const FunctionLabel = ({
    label,
    paramsDocs,
}) => {
    const [md, setMd] = useState('');
    const showDocs = isNonEmptyObject(paramsDocs);

    useEffect(() => {
        if (!showDocs) {
            return;
        }

        import('@w7-3/webeagle-resources/dist/webautomate/docs/automation-state.spec.md')
            .then(res => {
                fetch(res.default)
                    .then(res => res.text())
                    .then(res => setMd(res))
                    .catch(logGlobalError);
            })
            .catch(logGlobalError);
    }, [showDocs]);

    if (!showDocs) {
        return (
            <Typography
                gutterBottom
                variant="subtitle1"
                sx={{
                    color: 'text.secondary',
                    bgcolor: 'background.neutral',
                    p: 1,
                    m: 0,
                    wordBreak: 'keep-all',
                    wordWrap: 'unset',
                    whiteSpace: 'nowrap',
                    overflow: 'scroll',
                    width: '100%',
                    display: 'inline-block',
                    padding: '1em',
                }}>
                <code>{label}</code>
            </Typography>
        );
    }

    return (
        <Button
            size="large"
            variant="text"
            onClick={() => {
                PubSub.publish('SHOW.DIALOG', {
                    type: 'custom',
                    dialogProps: {
                        isFullScreen: true,
                        title: (
                            <Typography
                                variant="subtitle1"
                                sx={{
                                    wordBreak: 'break-word',
                                    wordWrap: 'break-word',
                                }}>
                                API Documentation
                            </Typography>
                        ),
                    },
                    children: (
                        <Box sx={{
                            '> *': {
                                wordWrap: 'break-word',
                                overflowWrap: 'break-word',
                                whiteSpace: 'pre-wrap',
                            },
                        }}>
                            <Markdown>{md}</Markdown>
                        </Box>
                    ),
                });
            }}
            sx={{
                textTransform: 'none',
                padding: '1em',
                justifyContent: 'flex-start',
                height: 'auto',
                textAlign: 'left',
            }}>
            {label}
        </Button>
    );
};

FunctionLabel.propTypes = propTypes;

export default FunctionLabel;
