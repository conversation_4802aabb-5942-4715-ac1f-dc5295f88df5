import PropTypes from 'prop-types';
import {LinearProgress, Stack, Typography} from '@material-ui/core';
import {
    RESOURCE_CONTENT_TYPES,
} from '@w7-3/webeagle-resources/dist/config/request';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import CodeMirror from '@uiw/react-codemirror';
import {css} from '@codemirror/lang-css';
import {useTheme} from '@material-ui/core/styles';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    script: PropTypes.shape({
        id: PropTypes.string,
        value: PropTypes.string,
    }),
    onChange: PropTypes.func.isRequired,
    label: PropTypes.string.isRequired,
    characterLimit: PropTypes.number,
};
const defaultProps = {
    script: {
        id: undefined,
        value: '',
    },
    characterLimit: RESOURCE_CONTENT_TYPES.css.characterLimit,
};

const CodeTextFieldCSS = ({script, onChange, label, characterLimit}) => {
    const {translate} = useLocales();
    const theme = useTheme();

    const count = script?.value?.length || 0;
    const total = characterLimit;
    const rest = total - count;
    const percentageCount = (count / total) * 100;
    return (
        <Stack spacing={3}>
            <LinearProgress variant="determinate" value={percentageCount}/>
            <Stack spacing={1} direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{color: 'text.secondary'}}>
                    {label}
                </Typography>
                <Typography
                    gutterBottom
                    variant="caption"
                    sx={{
                        pt: .25,
                        ...getOptionalMap(rest < 10, {color: 'error.main'}),
                    }}>
                    {translate('codeField.css.counterLabel', {rest, total})}
                </Typography>
            </Stack>
            <CodeMirror
                value={script?.value}
                theme={theme.palette.mode}
                placeholder={translate('codeField.css.placeholder')}
                extensions={[css()]}
                onChange={(value) => {
                    if (value.length > total) {
                        return;
                    }
                    onChange({
                        ...script,
                        value,
                        isValid: true,
                        isValidated: true,
                    });
                }}
            />
        </Stack>
    );
};

CodeTextFieldCSS.propTypes = propTypes;
CodeTextFieldCSS.defaultProps = defaultProps;

export default CodeTextFieldCSS;
