import {useState} from 'react';
import PubSub from 'pubsub-js';
import {isString} from 'lodash';
import {Autocomplete, Chip, IconButton, InputAdornment, Stack, TextField, Typography} from '@material-ui/core';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import * as filesResources from '@w7-3/webeagle-resources/dist/config/files';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import PropTypes from 'prop-types';
import {Controller, useForm} from 'react-hook-form';
import {Icon} from '@iconify/react';
import useLocales from '../../../hooks/useLocales';
import UploadFile from './UploadFile';
import SubmitButton from '../utils/SubmitButton';
import LabelStyle from '../utils/LabelStyle';

const propTypes = {
    handleSave: PropTypes.func,
    defaultValues: PropTypes.shape({
        name: PropTypes.string,
        tags: PropTypes.arrayOf(PropTypes.string),
    }),
};

const FileUploadForm = ({defaultValues, handleSave}) => {
    const {
        translate,
    } = useLocales();
    const [file, setFile] = useState(null);
    const {
        watch,
        control,
        setValue,
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            name: defaultValues?.name || '',
            tags: defaultValues?.tags || [],
        },
    });
    const handleDropError = () => {
        PubSub.publish('SHOW.NOTIFICATION', {
            message: translate(`directives.notifications.${NOTIFICATIONS.genericErrorWithRetry}`),
            variant: 'error',
        });
    }
    const handleDropSuccess = (acceptedFiles) => {
        try {
            if (!Array.isArray(acceptedFiles)) {
                handleDropError();
                return;
            }

            const blob = acceptedFiles[0];
            const preview = URL.createObjectURL(blob);
            const name = isString(blob) ? blob : blob?.path;
            const extension = (name || '').toLowerCase().split('.').pop();
            const fields = watch('name');

            setFile({
                blob,
                preview,
                name,
                extension,
                isImage: filesResources.imageExtensions.some((item) => {
                    return item.toLowerCase() === extension;
                }),
            });

            if (fields?.name) {
                return;
            }

            setValue('name', name);
        } catch (error) {
            handleDropError();
        }
    };

    const {
        name,
        tags,
    } = watch();

    return (
        <Stack spacing={3}>
            <UploadFile
                files={file ? [file] : []}
                maxSize={filesResources.config.maxFileSize}
                maxFiles={1}
                onDropAccepted={handleDropSuccess}
                onDropRejected={handleDropError}
                removeFile={() => {
                    setFile(null);
                }}
            />
            <Controller
                name="name"
                control={control}
                render={({
                    field,
                }) => {
                    return (
                        <TextField
                            fullWidth
                            {...field}
                            label={translate('form.name')}
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            onClick={() => {
                                                field.onChange('');
                                            }}
                                            edge="end"
                                            color="primary">
                                            <Icon icon="eva:close-outline"/>
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                        />
                    );
                }}
            />
            <Stack spacing={2}>
                <LabelStyle sx={{mt: '0 !important'}}>
                    {translate('templates.files.tags.label')}
                </LabelStyle>
                <Typography sx={{color: 'text.secondary'}}>
                    {translate('templates.files.tags.description')}
                </Typography>
                <Controller
                    name="tags"
                    control={control}
                    render={({
                        field,
                    }) => {
                        return (
                            <Autocomplete
                                value={field.value}
                                fullWidth
                                freeSolo
                                multiple
                                disableClearable
                                options={[]}
                                onInputChange={(_, newValue) => {
                                    if (!isNonEmptyString(newValue) || !newValue.endsWith(',')) {
                                        return;
                                    }

                                    const item = newValue.substring(0, newValue.length - 1)

                                    if (isNonEmptyString(item)) {
                                        field.onChange([...field.value, item]);
                                    }
                                }}
                                renderInput={(params) => (
                                    <TextField {...params} InputProps={{ ...params.InputProps, type: 'search' }} />
                                )}
                                onChange={(_, newValue) => {
                                    field.onChange(newValue);
                                }}
                                renderTags={(value, getTagProps) =>
                                    value.map((option, index) => (
                                        <Chip
                                            key={index}
                                            variant="outlined"
                                            label={option}
                                            {...getTagProps({index})}
                                        />
                                    ))
                                }
                            />
                        );
                    }}
                />
            </Stack>
            <Stack
                spacing={3}
                direction="row"
                justifyContent="flex-end"
                alignItems="center">
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            name,
                            tags,
                            file,
                        });
                    }}
                    isValid={Boolean(name)}
                />
            </Stack>
        </Stack>
    );
};

FileUploadForm.propTypes = propTypes;

export default FileUploadForm;
