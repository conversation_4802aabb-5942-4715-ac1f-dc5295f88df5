import PropTypes from 'prop-types';
import {useRef, useState} from 'react';
import {Icon} from '@iconify/react';
import edit2FillFill from '@iconify/icons-eva/edit-2-fill';
import trash2Outline from '@iconify/icons-eva/trash-2-outline';
import moreVerticalFill from '@iconify/icons-eva/more-vertical-fill';
import {
    Divider,
    IconButton,
    MenuItem,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import Scrollbar from '../../../components/Scrollbar';
import useLocales from '../../../hooks/useLocales';
import MenuPopover from '../../../components/MenuPopover';

const i18nContext = 'project.wizardSteps.requestOptions.groups.resources.fields.responseOverrides';

const MoreMenuButton = ({onEdit, onDelete}) => {
    const {translate} = useLocales();
    const menuRef = useRef(null);
    const [open, setOpen] = useState(false);

    const handleOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <>
            <IconButton ref={menuRef} size="large" onClick={handleOpen}>
                <Icon icon={moreVerticalFill} width={40} height={40}/>
            </IconButton>
            <MenuPopover
                open={open}
                anchorEl={menuRef.current}
                onClose={handleClose}
            >
                <MenuItem onClick={() => {
                    onEdit();
                    handleClose();
                }}>
                    <Icon icon={edit2FillFill} width={40} height={40}/>
                    <Typography variant="body2" sx={{ml: 2}}>
                        {translate('edit')}
                    </Typography>
                </MenuItem>
                <Divider/>
                <MenuItem onClick={() => {
                    onDelete();
                    handleClose();
                }} sx={{color: 'error.main'}}>
                    <Icon icon={trash2Outline} width={40} height={40}/>
                    <Typography variant="body2" sx={{ml: 2}}>
                        {translate('delete')}
                    </Typography>
                </MenuItem>
            </MenuPopover>
        </>
    );
};

MoreMenuButton.propTypes = {
    onDelete: PropTypes.func,
    onEdit: PropTypes.func,
};

const propTypes = {
    itemList: PropTypes.array,
    handleClickEdit: PropTypes.func,
    handleClickDelete: PropTypes.func,
};

const ResponseOverrideTable = ({
    itemList,
    handleClickEdit,
    handleClickDelete,
}) => {
    const {translate} = useLocales();

    return (
        <Scrollbar>
            <TableContainer>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell sx={{
                                width: '80%',
                                minWidth: '250px',
                            }}>{translate(`${i18nContext}.itemListTitle`)}</TableCell>
                            <TableCell sx={{width: '5%', minWidth: '50px'}}/>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {itemList
                            .map((item) => {
                                const method = item?.urlData?.method;
                                const {matcher, value} = item?.urlData?.urlMatch?.[0] || {};

                                return (
                                    <TableRow
                                        key={item.id}
                                        sx={{
                                            cursor: 'pointer',
                                            borderBottom: (theme) => `solid 1px ${theme.palette.grey[500_8]}`,
                                        }}>
                                        <TableCell
                                            onClick={() => {
                                                handleClickEdit(item);
                                            }}>
                                            {translate(`${i18nContext}.preview.standard`, {
                                                method,
                                                matcher,
                                                value,
                                            })}
                                        </TableCell>
                                        <TableCell align="right">
                                            <MoreMenuButton
                                                onEdit={() => handleClickEdit(item)}
                                                onDelete={() => handleClickDelete(item)}
                                            />
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                    </TableBody>
                </Table>
            </TableContainer>
        </Scrollbar>
    );
};

ResponseOverrideTable.propTypes = propTypes;

export default ResponseOverrideTable;

