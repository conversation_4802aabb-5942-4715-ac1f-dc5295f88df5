import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Switch,
    Typo<PERSON>,
} from '@material-ui/core';
import {useState} from 'react';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import ConditionRenderer from './ConditionRenderer';
import SubmitButton from '../utils/SubmitButton';
import SettingItemChange from '../utils/SettingItemChange';
import Drawer from './Drawer';
import {ItemDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';
import ProjectStepState from './ProjectStepState';

const i18nContext = 'conditions';
const STEPS = {
    conditionConfig: 0,
    conditionSettings: 1,
    conditionSteps: 2,
};

const ConditionDialogForm = ({
    state: {
        item,
        action,
    },
    stepList,
    handleUpdate,
    request,
    ProjectSolutionStepBlock,
    labels,
}) => {
    const {
        type,
    } = item;
    const {translate} = useLocales();
    const [state, setState] = useState({
        item,
        activeStep: STEPS.conditionConfig,
    });
    const handleCancel = () => {
        handleUpdate();
    };
    const hasSteps = isNonEmptyArray(state.item.stepList);
    const labelBlacklist = stepList.filter(({id}) => item.id !== id).map(({label}) => label);
    const failStopLabel = translate(`workflowSteps.options.${solutionStepTypes.condition}.labels.abortion`);
    const ignoreLabel = translate(`workflowSteps.options.${solutionStepTypes.condition}.labels.ignore`);
    const willBeIgnoredMessage = translate(`workflowSteps.options.${solutionStepTypes.condition}.notifications.willBeIgnored`);

    return (
        <Drawer
            open
            onClose={handleCancel}
            title={(
                <Stack direction="row" spacing={1}>
                    <Typography variant="subtitle1">
                        {translate(`${i18nContext}.options.${type}.label`)}
                    </Typography>
                    <ProjectStepState item={item} />
                </Stack>
            )}
        >
            <Typography sx={{color: 'text.secondary'}}>
                {translate(`${i18nContext}.options.${type}.description`)}
            </Typography>
            <Stepper
                activeStep={state.activeStep}
                orientation="vertical">
                <Step id={STEPS.conditionConfig}>
                    <StepLabel>
                        {translate(`workflowSteps.options.${solutionStepTypes.condition}.labels.conditionConfig`)}
                        {
                            state.activeStep > STEPS.conditionConfig &&
                            <SettingItemChange
                                onClick={() => {
                                    setState({
                                        ...state,
                                        activeStep: STEPS.conditionConfig,
                                    });
                                }}
                            />
                        }
                    </StepLabel>
                    <StepContent sx={{my: 3}}>
                        <ConditionRenderer
                            item={state.item}
                            labelBlacklist={labelBlacklist}
                            handleCancel={handleCancel}
                            handleSave={(item) => {
                                setState({
                                    ...state,
                                    item,
                                    activeStep: STEPS.conditionSettings,
                                });
                            }}
                            labels={labels}
                        />
                    </StepContent>
                </Step>
                <Step id={STEPS.conditionSettings}>
                    <StepLabel>
                        {translate(`workflowSteps.options.${solutionStepTypes.condition}.labels.conditionSettings`)}
                        {
                            (hasSteps || state.activeStep > STEPS.conditionConfig) &&
                            <SettingItemChange
                                onClick={() => {
                                    setState({
                                        ...state,
                                        activeStep: STEPS.conditionSettings,
                                    });
                                }}
                            />
                        }
                    </StepLabel>
                    <StepContent sx={{my: 3}}>
                        <Stack spacing={3}>
                            <FormControlLabel
                                label={failStopLabel}
                                control={
                                    <Switch
                                        onChange={(_, abortOnError) => {
                                            setState({
                                                ...state,
                                                item: {
                                                    ...state.item,
                                                    abortOnError,
                                                },
                                            });
                                        }}
                                        checked={state.item.abortOnError}
                                    />
                                }
                            />
                            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                                <Button
                                    color="inherit"
                                    onClick={() => {
                                        setState({
                                            ...state,
                                            activeStep: STEPS.conditionConfig,
                                        });
                                    }}
                                    variant="text"
                                    sx={{textTransform: 'none'}}>
                                    {translate('backTo', {label: translate(`workflowSteps.options.${solutionStepTypes.condition}.labels.conditionConfig`),})}
                                </Button>
                                <SubmitButton
                                    onClick={() => {
                                        setState({
                                            ...state,
                                            activeStep: STEPS.conditionSteps,
                                        });
                                    }}
                                    label={translate('continue')}
                                    isValid
                                />
                            </Stack>
                        </Stack>
                    </StepContent>
                </Step>
                <Step id={STEPS.conditionSteps}>
                    <StepLabel>
                        {translate(`workflowSteps.options.${solutionStepTypes.condition}.labels.conditionSteps`)}
                        {
                            hasSteps && state.activeStep !== STEPS.conditionSteps &&
                            <SettingItemChange
                                onClick={() => {
                                    setState({
                                        ...state,
                                        activeStep: STEPS.conditionSteps,
                                    });
                                }}
                            />
                        }
                    </StepLabel>
                    <StepContent sx={{my: 3}}>
                        <ProjectSolutionStepBlock
                            value={state.item.stepList}
                            onChange={({stepList}) => {
                                setState({
                                    ...state,
                                    item: {
                                        ...state.item,
                                        stepList,
                                    },
                                });
                            }}
                            request={request}
                            labels={{
                                ...labels,
                                abortion: failStopLabel,
                                ignoreActionError: ignoreLabel,
                                willIgnoreActionError: willBeIgnoredMessage,
                            }}
                            actionItemRendererProps={{
                                showAbortOnError: false,
                                showIgnoreOnError: state?.item?.abortOnError,
                            }}
                        />
                    </StepContent>
                </Step>
            </Stepper>
            {
                state.activeStep === STEPS.conditionSteps && (
                    <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                        <Button color="inherit"
                                onClick={handleCancel}>
                            {translate('cancel')}
                        </Button>
                        <SubmitButton
                            onClick={() => {
                                handleUpdate({
                                    action,
                                    item: state.item,
                                });
                            }}
                            label={translate('continue')}
                            isValid={hasSteps}
                            errorMessage={translate(`workflowSteps.options.${solutionStepTypes.condition}.saveNotification`)}
                        />
                    </Stack>
                )
            }
        </Drawer>
    );
};

ConditionDialogForm.propTypes = ItemDialogFormPropTypes;

export default ConditionDialogForm;
