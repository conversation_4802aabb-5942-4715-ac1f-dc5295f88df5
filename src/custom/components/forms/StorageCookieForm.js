import {useState} from 'react';
import PropTypes from 'prop-types';
import {Box, Button, Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import {useSnackbar} from 'notistack5';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import {toggleArrayObjectItem} from '../../utils/toggle';
import StorageCookieDialogForm from './StorageCookieDialogForm';
import StorageCookieTable from './StorageCookieTable';

const initialState = {
    show: false,
    item: null,
    id: undefined,
};
const propTypes = {
    itemList: PropTypes.arrayOf(PropTypes.shape({
        contentType: PropTypes.string,
        overrideType: PropTypes.string,
        source: PropTypes.string,
        target: PropTypes.string,
        content: PropTypes.object,
    })),
    onChange: PropTypes.func,
};

const i18nContext = 'project.wizardSteps.requestOptions.groups.storage.fields.requestCookies';

const StorageCookieForm = ({onChange, itemList}) => {
    const {translate} = useLocales();
    const {enqueueSnackbar} = useSnackbar();
    const [state, setState] = useState(initialState);
    const handleUpdate = (id, item) => {
        setState(initialState);
        if (!item) {
            return;
        }

        const newList = toggleArrayObjectItem({
            item: {
                ...item,
                id,
            },
            idField: 'id',
            list: itemList,
            add: true,
        });
        onChange(newList);
    };
    const handleClickEdit = (item) => {
        setState({
            show: true,
            item,
            id: item.id,
        });
    };
    const handleClickDelete = (item) => {
        const newList = toggleArrayObjectItem({
            item,
            idField: 'id',
            list: itemList,
            add: false,
        });
        onChange(newList);
        enqueueSnackbar(translate(`${i18nContext}.notifications.deleteSuccess`), {variant: 'success'});
    };

    return (
        <Stack
            direction="column"
            spacing={3}>
            {
                itemList?.length > 0 &&
                <StorageCookieTable
                    itemList={itemList}
                    handleClickEdit={handleClickEdit}
                    handleClickDelete={handleClickDelete}
                />
            }
            <Box sx={{
                display: 'flex',
                ...getOptionalMap(itemList?.length < 1, {
                    flexDirection: 'column',
                })
            }}>
                <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                    onClick={() => setState({
                        show: true,
                    })}
                    sx={{textTransform: 'none'}}>
                    {translate(`${i18nContext}.label`)}
                </Button>
            </Box>
            {
                state.show &&
                <StorageCookieDialogForm
                    id={state.id}
                    item={state.item}
                    itemList={itemList}
                    updateDialogState={handleUpdate}
                />
            }
        </Stack>
    );
};

StorageCookieForm.propTypes = propTypes;

export default StorageCookieForm;

