import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    FormControl<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>per,
    Switch,
    Typography,
} from '@material-ui/core';
import {useState} from 'react';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {isNonEmptyArray,isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {paramNames} from '@w7-3/webeagle-resources/dist/config/loops';
import useLocales from '../../../hooks/useLocales';
import LoopRenderer from './LoopRenderer';
import SubmitButton from '../utils/SubmitButton';
import SettingItemChange from '../utils/SettingItemChange';
import Drawer from './Drawer';
import {ItemDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';
import CopyClipboard from '../../../components/CopyClipboard';
import CodeTextFieldJS from './CodeTextFieldJS';
import ProjectStepState from './ProjectStepState';

const i18nContext = 'loops';
const STEPS = {
    loopConfig: 0,
    loopSettings: 1,
    loopSteps: 2,
};
const fullScreenItemCategoryTypeList = [
];

const LoopDialogForm = ({
    state: {
        item,
        action,
    },
    stepList,
    handleUpdate,
    request,
    ProjectSolutionStepBlock,
    labels,
}) => {
    const {
        type,
    } = item;
    const {translate} = useLocales();
    const [state, setState] = useState({
        item,
        activeStep: STEPS.loopConfig,
    });
    const [showBreakFunction, setShowBreakFunction] = useState(isNonEmptyString(state.item?.breakConditionJSCode?.value));
    const handleCancel = () => {
        handleUpdate();
    };
    const hasSteps = isNonEmptyArray(state.item.stepList);
    const labelBlacklist = stepList.filter(({id}) => item.id !== id).map(({label}) => label);
    const failStopLabel = translate(`workflowSteps.options.${solutionStepTypes.loop}.labels.abortion`);
    const ignoreLabel = translate(`workflowSteps.options.${solutionStepTypes.loop}.labels.ignore`);
    const willBeIgnoredMessage = translate(`workflowSteps.options.${solutionStepTypes.loop}.notifications.willBeIgnored`);

    return (
        <Drawer
            open
            onClose={handleCancel}
            isFullScreen={fullScreenItemCategoryTypeList.includes(type)}
            title={(
                <Stack direction="row" spacing={1}>
                    <Typography variant="subtitle1">
                        {translate(`${i18nContext}.options.${type}.label`)}
                    </Typography>
                    <ProjectStepState item={item} />
                </Stack>
            )}
        >
            <Typography sx={{color: 'text.secondary'}}>
                {translate(`${i18nContext}.options.${type}.description`)}
            </Typography>
            <Stepper
                activeStep={state.activeStep}
                orientation="vertical">
                <Step id={STEPS.loopConfig}>
                    <StepLabel>
                        {translate(`workflowSteps.options.${solutionStepTypes.loop}.labels.loopConfig`)}
                        {
                            (hasSteps || state.activeStep > STEPS.loopConfig) &&
                            <SettingItemChange
                                onClick={() => {
                                    setState({
                                        ...state,
                                        activeStep: STEPS.loopConfig,
                                    });
                                }}
                            />
                        }
                    </StepLabel>
                    <StepContent sx={{my: 3}}>
                        <LoopRenderer
                            item={state.item}
                            labelBlacklist={labelBlacklist}
                            handleCancel={handleCancel}
                            handleSave={(item) => {
                                setState({
                                    ...state,
                                    item,
                                    activeStep: STEPS.loopSettings,
                                });
                            }}
                        />
                    </StepContent>
                </Step>
                <Step id={STEPS.loopSettings}>
                    <StepLabel>
                        {translate(`workflowSteps.options.${solutionStepTypes.loop}.labels.loopSettings`)}
                        {
                            (hasSteps || state.activeStep > STEPS.loopConfig) &&
                            <SettingItemChange
                                onClick={() => {
                                    setState({
                                        ...state,
                                        activeStep: STEPS.loopSettings,
                                    });
                                }}
                            />
                        }
                    </StepLabel>
                    <StepContent sx={{my: 3}}>
                        <Stack spacing={3}>
                            <div>
                                <FormControlLabel
                                    label={failStopLabel}
                                    control={
                                        <Switch
                                            onChange={(_, abortOnError) => {
                                                setState({
                                                    ...state,
                                                    item: {
                                                        ...state.item,
                                                        abortOnError,
                                                    },
                                                });
                                            }}
                                            checked={state.item.abortOnError}
                                        />
                                    }
                                />
                            </div>
                            <Alert severity="info">
                                {translate(`workflowSteps.options.${solutionStepTypes.loop}.notifications.indexReference.description`)}
                                <CopyClipboard
                                    value={item.indexReference}
                                    title={item.indexReference}
                                    sx={{mt: 1}}
                                    InputProps={{readOnly: true}}
                                />
                                <AlertTitle sx={{mt: 1}}>
                                    {translate(`workflowSteps.options.${solutionStepTypes.loop}.notifications.indexReference.reload`)}
                                </AlertTitle>
                            </Alert>
                            <Alert severity="info" sx={{mb: 3}}>
                                {translate(`workflowSteps.options.${solutionStepTypes.loop}.breakCondition.description.ctaPre`)}
                                <Button onClick={() => setShowBreakFunction(!showBreakFunction)}
                                        sx={{p: 0, m: 0, mb: .25, fontWeight: 'normal'}}>
                                    {translate(`workflowSteps.options.${solutionStepTypes.loop}.breakCondition.label`)}
                                </Button>
                                {translate(`workflowSteps.options.${solutionStepTypes.loop}.breakCondition.description.ctaPost`)}
                            </Alert>
                            {
                                showBreakFunction && (
                                    <CodeTextFieldJS
                                        label={translate(`workflowSteps.options.${solutionStepTypes.loop}.breakCondition.label`)}
                                        script={state.item.breakConditionJSCode}
                                        onChange={(breakConditionJSCode) => {
                                            setState({
                                                ...state,
                                                item: {
                                                    ...state.item,
                                                    breakConditionJSCode,
                                                },
                                            });
                                        }}
                                        prePlaceholder={`function shouldBreakLoop(${paramNames.breakCondition.fe}) {`}
                                        paramsDocs={paramNames.breakCondition}
                                        placeholder={[
                                            `/* // ${translate('exampleCode')}`,
                                            '/* var someElem = document.querySelector("#some-element");',
                                            '/* var someOtherElem = document.querySelector("#other-element");',
                                            ' *',
                                            ' * var elementsCompletelyRendered = Boolean(someElem) && Boolean(someOtherElem);',
                                            ' *',
                                            ' *  return elementsCompletelyRendered; */',
                                        ].join('\n')}
                                        postPlaceholder="}"
                                        modifier="bool"
                                        isFunctionContext
                                        showReset
                                    />
                                )
                            }
                            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                                <Button
                                    color="inherit"
                                    onClick={() => {
                                        setState({
                                            ...state,
                                            activeStep: STEPS.loopConfig,
                                        });
                                    }}
                                    variant="text"
                                    sx={{textTransform: 'none'}}>
                                    {translate('backTo', {label: translate(`workflowSteps.options.${solutionStepTypes.loop}.labels.loopConfig`),})}
                                </Button>
                                <SubmitButton
                                    onClick={() => {
                                        setState({
                                            ...state,
                                            activeStep: STEPS.loopSteps,
                                        });
                                    }}
                                    label={translate('continue')}
                                    isValid={!showBreakFunction || state.item.breakConditionJSCode?.isValidated}
                                />
                            </Stack>
                        </Stack>
                    </StepContent>
                </Step>
                <Step id={STEPS.loopSteps}>
                    <StepLabel>
                        {translate(`workflowSteps.options.${solutionStepTypes.loop}.labels.loopSteps`)}
                        {
                            (hasSteps || state.activeStep > STEPS.loopSettings) &&
                            <SettingItemChange
                                onClick={() => {
                                    setState({
                                        ...state,
                                        activeStep: STEPS.loopSteps,
                                    });
                                }}
                            />
                        }
                    </StepLabel>
                    <StepContent sx={{my: 3}}>
                        <Stack spacing={3}>
                            <ProjectSolutionStepBlock
                                value={state.item.stepList}
                                onChange={({stepList}) => {
                                    setState({
                                        ...state,
                                        item: {
                                            ...state.item,
                                            stepList,
                                        },
                                    });
                                }}
                                request={request}
                                labels={{
                                    ...labels,
                                    abortion: failStopLabel,
                                    ignoreActionError: ignoreLabel,
                                    willIgnoreActionError: willBeIgnoredMessage,
                                }}
                                actionItemRendererProps={{
                                    contextStepType: solutionStepTypes.loop,
                                    showAbortOnError: false,
                                    showIgnoreOnError: state?.item?.abortOnError,
                                }}
                            />
                        </Stack>
                    </StepContent>
                </Step>
            </Stepper>
            {
                state.activeStep === STEPS.loopSteps && (
                    <DialogActions>
                        <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                            <Button
                                color="inherit"
                                onClick={handleCancel}>
                                {translate('cancel')}
                            </Button>
                            <SubmitButton
                                onClick={() => {
                                    handleUpdate({
                                        action,
                                        item: state.item,
                                    });
                                }}
                                isValid={hasSteps}
                                errorMessage={translate(`workflowSteps.options.${solutionStepTypes.loop}.saveNotification`)}
                            />
                        </Stack>
                    </DialogActions>
                )
            }
        </Drawer>
    );
};

LoopDialogForm.propTypes = ItemDialogFormPropTypes;

export default LoopDialogForm;
