import {useState} from 'react';
import PropTypes from 'prop-types';
import {Box, Button, Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import {useSnackbar} from 'notistack5';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import {toggleArrayObjectItem} from '../../utils/toggle';
import KeyValueDialogForm from './KeyValueDialogForm';
import KeyValueTable from './KeyValueTable';

const initialState = {
    show: false,
    item: null,
    id: undefined,
};
const propTypes = {
    itemList: PropTypes.arrayOf(PropTypes.shape({
        contentType: PropTypes.string,
        overrideType: PropTypes.string,
        source: PropTypes.string,
        target: PropTypes.string,
        content: PropTypes.object,
    })),
    onChange: PropTypes.func,
    i18nContext: PropTypes.string,
    optionalValue: PropTypes.bool,
};

const KeyValueForm = ({onChange, itemList, i18nContext, optionalValue}) => {
    const {translate} = useLocales();
    const {enqueueSnackbar} = useSnackbar();
    const [state, setState] = useState(initialState);
    const handleUpdate = (id, item) => {
        setState(initialState);
        if (!item) {
            return;
        }

        const newList = toggleArrayObjectItem({
            item: {
                ...item,
                id,
            },
            idField: 'id',
            list: itemList,
            add: true,
        });
        onChange(newList);
    };
    const handleClickEdit = (item) => {
        setState({
            show: true,
            item,
            id: item.id,
        });
    };
    const handleClickDelete = (item) => {
        const newList = toggleArrayObjectItem({
            item,
            idField: 'id',
            list: itemList,
            add: false,
        });
        onChange(newList);
        enqueueSnackbar(translate(`${i18nContext}.notifications.deleteSuccess`), {variant: 'success'});
    };

    return (
        <Stack
            direction="column"
            spacing={3}>
            {
                itemList?.length > 0 &&
                <KeyValueTable
                    itemList={itemList}
                    handleClickEdit={handleClickEdit}
                    handleClickDelete={handleClickDelete}
                    i18nContext={i18nContext}
                />
            }
            <Box sx={{
                display: 'flex',
                ...getOptionalMap(itemList?.length < 1, {
                    flexDirection: 'column',
                })
            }}>
                <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                    onClick={() => setState({
                        show: true,
                    })}
                    sx={{textTransform: 'none'}}>
                    {translate(`${i18nContext}.label`)}
                </Button>
            </Box>
            {
                state.show &&
                <KeyValueDialogForm
                    id={state.id}
                    item={state.item}
                    itemList={itemList}
                    updateDialogState={handleUpdate}
                    i18nContext={i18nContext}
                    optionalValue={optionalValue}
                />
            }
        </Stack>
    );
};

KeyValueForm.propTypes = propTypes;

export default KeyValueForm;

