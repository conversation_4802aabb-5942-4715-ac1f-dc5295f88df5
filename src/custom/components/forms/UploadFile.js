import PropTypes from 'prop-types';
import {useDropzone} from 'react-dropzone';
import {alpha, styled} from '@material-ui/core/styles';
import {Al<PERSON>, Box, Button, Paper, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import trash2Outline from '@iconify/icons-eva/trash-2-outline';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {UploadIllustration} from '../../../assets';
import {fData} from '../../../utils/formatNumber';
import useLocales from '../../../hooks/useLocales';
import UploadFilePlaceholder from '../UploadFilePlaceholder';

const DropZoneStyle = styled('div')(({theme}) => ({
    outline: 'none',
    display: 'flex',
    overflow: 'hidden',
    textAlign: 'center',
    position: 'relative',
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    padding: theme.spacing(3),
    borderRadius: theme.shape.borderRadius,
    transition: theme.transitions.create('padding'),
    backgroundColor: theme.palette.background.neutral,
    border: `1px dashed ${theme.palette.grey[500_32]}`,
    '&:hover': {
        opacity: 0.72,
        cursor: 'pointer',
    },
    [theme.breakpoints.up('md')]: {textAlign: 'left', flexDirection: 'row'},
}));

const i18nContext = 'templates.files';

const propTypes = {
    error: PropTypes.bool,
    files: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.object])),
    maxSize: PropTypes.number,
    sx: PropTypes.object,
    onDropAccepted: PropTypes.func,
    onDropRejected: PropTypes.func,
    clearFiles: PropTypes.func,
    removeFile: PropTypes.func,
};

export const messageKey = {
    'file-invalid-type': `${i18nContext}.notification.fileInvalid`,
    'file-too-large': `${i18nContext}.notification.fileTooBig`,
};

const UploadFile = ({error, files, maxSize, sx, onDropAccepted, onDropRejected, clearFiles, removeFile, ...other}) => {
    const {translate} = useLocales();
    const {getRootProps, getInputProps, isDragActive, isDragReject, fileRejections} = useDropzone({
        multiple: false,
        onDropAccepted,
        onDropRejected,
        maxSize,
        ...other,
    });
    const ShowRejectionItems = () => (
        <Paper
            variant="outlined"
            sx={{
                width: '100%',
                py: 1,
                px: 2,
                mt: 3,
                borderColor: 'error.light',
                bgcolor: (theme) => alpha(theme.palette.error.main, 0.08),
            }}
        >
            {fileRejections.map(({file, errors}) => {
                const {path, size} = file;
                const fileSize = fData(size);

                return (
                    <Box key={path} sx={{my: 1}}>
                        <Typography variant="subtitle2" noWrap>
                            {path} - {fileSize}
                        </Typography>
                        {errors.map((e) => (
                            <Typography key={e.code} variant="caption" component="p" sx={{mt: 1}}>
                                - {
                                translate(messageKey[e.code] || messageKey['file-invalid-type'], {
                                    maxFileSize: fData(maxSize),
                                    fileSize,
                                    name: file.name,
                                })}
                            </Typography>
                        ))}
                    </Box>
                );
            })}
        </Paper>
    );

    return (
        <Stack justifyContent="flex-start" spacing={3} sx={{width: '100%', ...sx}}>
            {
                isNonEmptyArray(files) && (
                    <Stack spacing={3} direction="row" justifyContent="space-between">
                        {
                            files.map((file) => {
                                return (
                                    <UploadFilePlaceholder
                                        key={file.name}
                                        file={file}
                                        handleDelete={() => {
                                            removeFile(file);
                                        }}
                                    />
                                );
                            })
                        }
                        {
                            files.length > 1 && (
                                <Button
                                    variant="text"
                                    size="large"
                                    startIcon={<Icon icon={trash2Outline} width={40} height={40}/>}
                                    onClick={() => {
                                        clearFiles();
                                    }}>
                                    {translate('clearAll')}
                                </Button>
                            )
                        }
                    </Stack>
                )
            }
            <Alert severity="info" variant="outlined">
                {translate('templates.files.info', {
                    fileSize: fData(maxSize),
                })}
            </Alert>
            <DropZoneStyle
                {...getRootProps()}
                sx={{
                    ...(isDragActive && {opacity: 0.72}),
                    ...((isDragReject || error) && {
                        color: 'error.main',
                        borderColor: 'error.light',
                        bgcolor: 'error.lighter',
                    }),
                }}>
                <input {...getInputProps()} />
                <UploadIllustration sx={{width: 'auto'}}/>
                <Box sx={{p: 3, ml: {md: 2}}}>
                    <Typography gutterBottom variant="h5">
                        {translate(`${i18nContext}.dropOrSelect.label`)}
                    </Typography>
                    <Typography variant="body2"
                                component="span"
                                sx={{color: 'primary.main', textDecoration: 'underline'}}>
                        {translate(`${i18nContext}.dropOrSelect.description`)}
                    </Typography>
                </Box>
            </DropZoneStyle>

            {fileRejections.length > 0 && <ShowRejectionItems/>}
        </Stack>
    );
};

UploadFile.propTypes = propTypes;

export default UploadFile;
