import {useEffect, useState} from 'react';
import {Icon} from '@iconify/react';
import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {Box, Divider, Tooltip, Typography} from '@material-ui/core';
import {ACTIONS, solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getRandomString, libraryKeys} from '@w7-3/webeagle-resources/dist/libs/random';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import * as w73 from '@w7-3/webeagle-resources/dist/config/w73';
import DragDropTableSingleCol from '../services/DragDropTableSingleCol';
import useLocales from '../../../hooks/useLocales';
import DragDropTableItemWrapper from '../services/DragDropTableItemWrapper';
import ProjectSolutionStepSelectionForm from './ProjectSolutionStepSelectionForm';
import ActionDialogForm from './ActionDialogForm';
import ActionSelectionDialogForm from './ActionSelectionDialogForm';
import {
    getSetNewItemProjectSolutionStepAction,
    ProjectSolutionStepActionPreviewFooter,
} from './ProjectSolutionStepAction';
import ConditionDialogForm from './ConditionDialogForm';
import ConditionSelectionDialogForm from './ConditionSelectionDialogForm';
import LoopDialogForm from './LoopDialogForm';
import LoopSelectionDialogForm from './LoopSelectionDialogForm';
import {
    getSetNewItemProjectSolutionStepCondition,
    ProjectSolutionStepConditionPreviewFooter,
} from './ProjectSolutionStepCondition';
import {
    getSetNewItemProjectSolutionStepLoop,
    ProjectSolutionStepLoopPreviewFooter,
} from './ProjectSolutionStepLoop';
import {ItemDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';
import {getProjectSolutionStepItemLabel} from '../../utils/projectSolutionStepItemLabel';

const STEP_CONFIGS = {
    [solutionStepTypes.action]: {
        key: solutionStepTypes.action,
        i18nContext: 'actions',
        PreviewFooter: ProjectSolutionStepActionPreviewFooter,
        SelectionDialogForm: ActionSelectionDialogForm,
        ItemDialogForm: ActionDialogForm,
        getSetNewItem: getSetNewItemProjectSolutionStepAction,
    },
    [solutionStepTypes.condition]: {
        key: solutionStepTypes.condition,
        i18nContext: 'conditions',
        PreviewFooter: ProjectSolutionStepConditionPreviewFooter,
        SelectionDialogForm: ConditionSelectionDialogForm,
        ItemDialogForm: ConditionDialogForm,
        getSetNewItem: getSetNewItemProjectSolutionStepCondition,
    },
    [solutionStepTypes.loop]: {
        key: solutionStepTypes.loop,
        i18nContext: 'loops',
        PreviewFooter: ProjectSolutionStepLoopPreviewFooter,
        SelectionDialogForm: LoopSelectionDialogForm,
        ItemDialogForm: LoopDialogForm,
        getSetNewItem: getSetNewItemProjectSolutionStepLoop,
    },
};

const propTypes = {
    value: PropTypes.array.isRequired,
    onChange: PropTypes.func.isRequired,
    request: ItemDialogFormPropTypes.request,
    labels: ItemDialogFormPropTypes.labels,
    actionItemRendererProps: ItemDialogFormPropTypes.actionItemRendererProps,
};

export const getConfig = ({request, stepType}) => {
    if (!stepType) {
        return {
            SelectionDialogForm: () => null,
            ItemDialogForm: () => null,
            getSetNewItem: () => (() => {}),
        };
    }

    if (stepType === solutionStepTypes.solution) {
        return request.config;
    }

    return STEP_CONFIGS[stepType];
};

export const useProjectSolutionStepBlock = (props) => {
    const {value, onChange, request} = props;
    const {translate} = useLocales();
    const [dialogState, updateDialogState] = useState({});
    const [stepList, updateStepList] = useState(value);
    const [activeStepType, setActiveStepType] = useState();
    const [stepPosition, setStepPosition] = useState(undefined);
    const i18nContext = request?.config?.i18nContext;
    const {
        SelectionDialogForm,
        ItemDialogForm,
        getSetNewItem,
        i18nContext: activeI18nContext,
    } = getConfig({
        request,
        stepType: activeStepType || dialogState?.item?.stepType,
    });
    const setDialogState = (state) => {
        updateDialogState(state);
    };
    const setStepList = (stepList) => {
        updateStepList(stepList);
        onChange({stepList});
    };
    const setNewItem = getSetNewItem({
        translate,
        i18nContext,
        setDialogState,
    });
    const handleUpdate = (data) => {
        setDialogState({});

        if (!data?.item?.id) {
            return;
        }

        const {action, item} = data;
        const list = Array.from(stepList);
        const index = list.findIndex((listItem) => listItem.id === item.id);
        const eventTypes = {
            [ACTIONS.clone]: 'copySuccess',
            [ACTIONS.edit]: 'editSuccess',
        };

        if (ACTIONS.edit && index > -1) {
            list[index] = item;
        }
        else if (Number.isInteger(stepPosition)) {
            list.splice(stepPosition, 0, item);
        }
        else {
            list.push(item);
        }

        setStepList(list);
        if (!eventTypes[action]) {
            return;
        }

        PubSub.publish('SHOW.NOTIFICATION', {
            message: translate([
                `${activeI18nContext}.notifications.${eventTypes[action]}`,
                `${i18nContext}.notifications.${eventTypes[action]}`,
            ], item),
            variant: 'success',
        });
    };
    const handleClickDelete = ({item}) => {
        const list = Array.from(stepList);
        const index = list.findIndex((listItem) => listItem.id === item.id);

        list.splice(index, 1);
        setStepList(list);

        PubSub.publish('SHOW.NOTIFICATION', {
            message: translate(`${i18nContext}.notifications.deleteSuccess`, {
                label: item.label,
            }),
            variant: 'success',
        });
    };
    const handleClickCopy = ({item}) => {
        const id = getRandomString(6);
        setDialogState({
            show: true,
            action: ACTIONS.clone,
            item: {
                ...item,
                id,
                label: `${item.label} - ${translate('copySuffix')}`,
                originalStepIndex: stepList?.length || 0,
                ...getOptionalMap(item?.stepType === solutionStepTypes.loop, {
                    indexReference: `${w73.GLOBAL_PARAMETER}_${w73.PARAMETERS}_${getRandomString(8, libraryKeys.alphaUpperCased)}`,
                }),
            },
        });
    };
    const handleClickEdit = ({item}) => {
        setTimeout(() => {
            setDialogState({
                show: true,
                action: ACTIONS.edit,
                item,
            });
        }, 0);
    };

    return {
        i18nContext,
        stepList,
        setStepList,
        activeStepType,
        setActiveStepType,
        dialogState,
        setNewItem,
        stepPosition,
        setStepPosition,
        handleUpdate,
        handleClickDelete,
        handleClickCopy,
        handleClickEdit,
        setDialogState,
        SelectionDialogForm,
        ItemDialogForm,
    };
}

const DIMENSIONS = {
    title: 240,
    subheader: 200,
    previewContent: 360,
};

const ProjectSolutionStepBlock = (props) => {
    const {request, labels, actionItemRendererProps} = props;
    const {translate} = useLocales();
    const {
        i18nContext,
        stepList,
        setStepList,
        activeStepType,
        setActiveStepType,
        dialogState,
        setNewItem,
        stepPosition,
        setStepPosition,
        handleUpdate,
        handleClickDelete,
        handleClickCopy,
        handleClickEdit,
        SelectionDialogForm,
        ItemDialogForm,
    } = useProjectSolutionStepBlock(props);

    useEffect(() => {
        if (!dialogState.show) {
            return;
        }

        PubSub.publish('HIDE.NOTIFICATIONS.ALL');
    }, [dialogState.show]);

    return (
        <>
            <DragDropTableSingleCol
                itemWidth={DIMENSIONS.previewContent}
                stepPosition={stepPosition}
                itemList={stepList}
                setItemList={setStepList}
                NewItemCTA={({hasItems}) => (
                    <ProjectSolutionStepSelectionForm
                        {...{
                            labels,
                            hasItems,
                        }}
                        callback={() => {
                            setStepPosition(undefined);
                        }}
                        configList={[
                            ... request?.label ? [
                                {
                                    label: request.label,
                                    callback: (position) => {
                                        setStepPosition(position);
                                        setActiveStepType(request.config.key);
                                    },
                                },
                            ] : [],
                            {
                                label: translate(`workflowSteps.options.${solutionStepTypes.action}.ctaLabel`),
                                callback: (position) => {
                                    setStepPosition(position);
                                    setActiveStepType(solutionStepTypes.action);
                                },
                            },
                            {
                                label: translate(`workflowSteps.options.${solutionStepTypes.condition}.ctaLabel`),
                                callback: (position) => {
                                    setStepPosition(position);
                                    setActiveStepType(solutionStepTypes.condition);
                                },
                            },
                            {
                                label: translate(`workflowSteps.options.${solutionStepTypes.loop}.ctaLabel`),
                                callback: (position) => {
                                    setStepPosition(position);
                                    setActiveStepType(solutionStepTypes.loop);
                                },
                            },
                        ]}
                    />
                )}
                ItemRenderer={({item}) => {
                    const {PreviewFooter} = getConfig({
                        request,
                        stepType: item.stepType,
                    });
                    const count = isNonEmptyArray(item?.stepList) ? item.stepList.length : 0;
                    const title = item.stepType === solutionStepTypes.solution ?
                        translate(`project.wizardSteps.solutionsConfigs.groups.${item.solution}.label`) :
                        translate(`workflowSteps.options.${item.stepType}.label`);
                    const titleSuffix = count > 0 ? ` (+ ${item.stepList.length}x)` : '';
                    const titleHint = count > 0 ?
                        translate(`workflowSteps.options.${item.stepType}.subStepLabel`, {count}) :
                        title;

                    return (
                        <DragDropTableItemWrapper
                            {...{
                                item,
                                stepList,
                                handleClickEdit,
                                handleClickDelete,
                            }}
                            handleStateToggle={() => {
                                handleUpdate({
                                    item: {
                                        ...item,
                                        isInactive: !item.isInactive,
                                    },
                                })
                            }}
                            handleClickCopy={handleClickCopy}
                            title={(
                                <Tooltip title={titleHint}>
                                    <Typography
                                        noWrap
                                        sx={{
                                            maxWidth: `${DIMENSIONS.title}px`,
                                        }}>
                                        {`${title}${titleSuffix}`}
                                    </Typography>
                                </Tooltip>
                            )}
                            footer={(
                                <PreviewFooter
                                    item={item}
                                />
                            )}
                            sx={{
                                width: `${DIMENSIONS.previewContent}px`,
                            }}>
                            <Box
                                sx={{color: 'text.secondary'}}>
                                {getProjectSolutionStepItemLabel({item, translate})}
                            </Box>
                        </DragDropTableItemWrapper>
                    );
                }}
                itemSeparator={(
                    <Divider orientation="vertical" flexItem>
                        <Typography variant="body2" sx={{color: 'text.secondary', m: 0}}>
                            <Box component={Icon} icon="eva:arrow-forward-fill" sx={{color: 'text.disabled'}}/>
                        </Typography>
                    </Divider>
                )}
            />
            {
                activeStepType && (
                    <SelectionDialogForm
                        handleSelection={(item) => {
                            setNewItem({stepType: activeStepType, item, originalStepIndex: stepList?.length || 0});
                        }}
                        handleCloseMenu={() => {
                            setActiveStepType(undefined);
                        }}>
                        {translate(`${i18nContext}.newItem`)}
                    </SelectionDialogForm>
                )
            }
            {
                dialogState.show &&
                <ItemDialogForm
                    state={dialogState}
                    stepList={stepList}
                    handleUpdate={handleUpdate}
                    labels={labels}
                    request={request}
                    ProjectSolutionStepBlock={ProjectSolutionStepBlock}
                    actionItemRendererProps={{
                        ...actionItemRendererProps,
                        changeItemType: (item) => {
                            setNewItem({stepType: activeStepType, item});
                        },
                    }}
                />
            }
        </>
    );
};

ProjectSolutionStepBlock.propTypes = propTypes;

export default ProjectSolutionStepBlock;
