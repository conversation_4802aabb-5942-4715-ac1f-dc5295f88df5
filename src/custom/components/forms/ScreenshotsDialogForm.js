import {Stack, Typography} from '@material-ui/core';
import Drawer from './Drawer';
import useLocales from '../../../hooks/useLocales';
import {i18nContext} from '../utils/ScreenshotCapture';
import {ItemDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';
import ScreenshotsRenderer from './ScreenshotsRenderer';

const ScreenshotsDialogForm = ({
    state: {item, action},
    stepList,
    handleUpdate,
    labels,
    actionItemRendererProps,
}) => {
    const {translate} = useLocales();
    const handleCancel = () => {
        handleUpdate();
    };
    const handleSave = (newItem) => {
        handleUpdate({
            action,
            item: {
                ...item,
                ...newItem,
            },
        });
    };
    const labelBlacklist = stepList.filter(({ id }) => item.id !== id).map(({ label }) => label);
    const {categoryId, type} = item;

    return (
        <Drawer
            open
            title={
                <Typography variant="subtitle1">
                    {translate(`${i18nContext}.basicBlock`)} ({translate(action)})
                </Typography>
            }
            onClose={handleCancel}
        >
            <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                {translate(`${i18nContext}.groups.${categoryId}.options.${type}.description`)}
            </Typography>
            <Stack spacing={3} sx={{ width: '100%' }}>
                <ScreenshotsRenderer
                    {...actionItemRendererProps}
                    item={item}
                    list={stepList}
                    handleCancel={handleCancel}
                    handleSave={handleSave}
                    labels={labels}
                    labelBlacklist={labelBlacklist}
                />
            </Stack>
        </Drawer>
    );
};

ScreenshotsDialogForm.propTypes = ItemDialogFormPropTypes;

export default ScreenshotsDialogForm;
