import {Stack, Typography} from '@material-ui/core';
import {actions} from '@w7-3/webeagle-resources/dist/config/actions';
import useLocales from '../../../hooks/useLocales';
import ActionRenderer from './ActionRenderer';
import Drawer from './Drawer';
import {ItemDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';
import ProjectStepState from './ProjectStepState';

const i18nContext = 'actions';
const fullScreenItemCategoryTypeList = [
    actions.keyboardPress.value,
];

const ActionDialogForm = ({
    state: {
        item,
        action,
    },
    stepList,
    handleUpdate,
    labels,
    actionItemRendererProps,
}) => {
    const {
        type,
    } = item;
    const {translate} = useLocales();
    const handleCancel = () => {
        handleUpdate();
    };
    const handleSave = (newItem) => {
        handleUpdate({
            action,
            item: {
                ...item,
                ...newItem,
            },
        });
    };
    const labelBlacklist = stepList.filter(({id}) => item.id !== id).map(({label}) => label);

    return (
        <Drawer
            open
            title={(
                <Stack direction="row" spacing={1}>
                    <Typography variant="subtitle1">
                        {translate(`${i18nContext}.options.${type}.label`)}
                    </Typography>
                    <ProjectStepState item={item} />
                </Stack>
            )}
            onClose={handleCancel}
            isFullScreen={fullScreenItemCategoryTypeList.includes(type)}
        >
            <Typography variant="subtitle2" sx={{color: 'text.secondary'}}>
                {translate(`${i18nContext}.options.${type}.description`)}
            </Typography>
            <ActionRenderer
                {...actionItemRendererProps}
                item={item}
                list={stepList}
                handleCancel={handleCancel}
                handleSave={handleSave}
                labels={labels}
                labelBlacklist={labelBlacklist}
            />
        </Drawer>
    );
};

ActionDialogForm.propTypes = ItemDialogFormPropTypes;

export default ActionDialogForm;
