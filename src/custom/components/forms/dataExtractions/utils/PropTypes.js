import PropTypes from 'prop-types';

const CommonDataExtractionPropTypes = {
    item: PropTypes.shape({
        categoryId: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
    }),
};

export const DataExtractionPropTypes = {
    ...CommonDataExtractionPropTypes,
    i18nContextRoot: PropTypes.string,
    isPreview: PropTypes.bool,
    handleUpdate: PropTypes.func,
    resolvers: PropTypes.shape({
        resolvers: PropTypes.object,
    }),
};
