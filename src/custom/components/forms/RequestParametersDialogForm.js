import PropTypes from 'prop-types';
import {Controller, useForm} from 'react-hook-form';
import {<PERSON><PERSON>, Stack, Typography} from '@material-ui/core';
import {getRandomString} from '@w7-3/webeagle-resources/dist/libs/random';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import pick from 'ramda/src/pick';
import useLocales from '../../../hooks/useLocales';
import SubmitButton from '../utils/SubmitButton';
import NameValueForm from './NameValueForm';
import Drawer from './Drawer';
import UrlRequestForm from './UrlReqestForm';

const defaultProps = {
    get id() {
        return getRandomString(8);
    },
    item: {},
};

const propTypes = {
    id: PropTypes.string,
    item: PropTypes.object,
    updateDialogState: PropTypes.func,
    i18nContext: PropTypes.string,
};

const RequestParametersDialogForm = ({id, item, updateDialogState, i18nContext}) => {
    const {translate} = useLocales();
    const {
        control,
        watch,
        clearErrors,
        setError,
        formState: {
            errors,
        },
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            id: defaultProps.id,
            ...defaultProps.item,
            ...item,
        },
    });
    const handleCancel = () => {
        updateDialogState();
    };
    const handleSave = () => {
        updateDialogState(id, {
            ...pick(['values', 'urlData'], watch()),
            id,
        });
    };

    return (
        <Drawer
            open
            onClose={handleCancel}
            title={(
                <Typography variant="subtitle1">
                    {translate(`${i18nContext}.label`)}
                </Typography>
            )}
        >
            <Controller
                name="urlData"
                control={control}
                render={({field}) => {
                    return (
                        <UrlRequestForm
                            urlData={field.value}
                            onChange={({data, isValid}) => {
                                field.onChange(data);
                                if (!isValid) {
                                    setError(field.name, {
                                        message: translate('form.validation.url'),
                                    });
                                    return;
                                }
                                clearErrors(field.name);
                            }}
                            isGetMethodOnly
                        />
                    );
                }}
            />
            <Controller
                name="values"
                control={control}
                render={({field}) => {
                    return (
                        <NameValueForm
                            values={field.value}
                            onChange={({isValid, values}) => {
                                field.onChange(values);
                                if (isValid) {
                                    clearErrors(field.name);
                                    return;
                                }

                                setError(field.name, {
                                    message: translate('void'),
                                });
                            }}
                            labels={{
                                emptyValueInfo: translate([
                                    `${i18nContext}.emptyValueInfo`,
                                    'void',
                                ]),
                                name: translate(`${i18nContext}.keyLabel`),
                                value: translate(`${i18nContext}.valueLabel`),
                            }}
                            optionalValue
                        />
                    );
                }}
            />
            <Stack
                spacing={3}
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                sx={{pr: 3}}>
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave();
                    }}
                    isValid={!isNonEmptyObject(errors)}
                />
            </Stack>
        </Drawer>
    );
};

RequestParametersDialogForm.propTypes = propTypes;
RequestParametersDialogForm.defaultProps = defaultProps;

export default RequestParametersDialogForm;

