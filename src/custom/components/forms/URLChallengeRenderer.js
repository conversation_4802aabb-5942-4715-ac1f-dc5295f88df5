import {useEffect, useState} from 'react';
import PubSub from 'pubsub-js';
import * as Yup from 'yup';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Box, Button, FormControlLabel, Stack, Switch} from '@material-ui/core';
import {yupResolver} from '@hookform/resolvers/yup';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import {challenges} from '@w7-3/webeagle-resources/dist/config/urlChallenge';
import {UrlChallengeRendererPropTypes} from './urlChallenge/utils/PropTypes';
import useLocales from '../../../hooks/useLocales';
import {getLabellingValidation} from '../../utils/formSchema';
import AutomatedUrlChallenge from '../services/solutions/urlChallenge/AutomatedUrlChallenge';
import ItemLabellingStrategy from '../utils/ItemLabellingStrategy';
import ProjectStepStateForm from './ProjectStepStateForm';
import CustomUrlChallenge from '../services/solutions/urlChallenge/CustomUrlChallenge';

const renderers = {
    [challenges.custom.js.value]: CustomUrlChallenge,
    [challenges.automated.ai.value]: AutomatedUrlChallenge,
};

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.urlChallenge';

const URLChallengeRenderer = (props) => {
    const {
        item,
        labelBlacklist,
        handleSave,
        handleCancel,
        labels,
        isPreview,
        showAbortOnError,
        showIgnoreOnError,
    } = props;
    const Renderer = renderers[item?.type];
    const {translate} = useLocales();
    const [isInactive, setIsInactive] = useState(item?.isInactive);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            ...getLabellingValidation({
                label: {
                    error: translate('itemLabelling.mandatoryField'),
                    duplicate: translate('itemLabelling.duplicateGroupName'),
                    allowEmpty: false,
                },
                labelScript: {
                    error: translate('form.validation.script'),
                },
                labelBlacklist,
            }),
        })),
        defaultValues: item,
    });
    const {
        watch,
        control,
    } = formContext;

    useEffect(() => {
        if (Renderer) {
            return;
        }

        PubSub.publish(
            'LOG.EVENT',
            {
                uiData: {
                    type: INFO_CODE_LEVELS.ERROR,
                    message: `Cannot find Renderer for URL-Challenge: ${item?.type}`,
                    data: {
                        item,
                    }
                },
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [Renderer]);

    if (!Renderer) {
        return (
            <Stack spacing={3}>
                <Alert severity="error">
                    {translate(`${i18nContext}.unavailable`)}
                </Alert>
                <Box sx={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center'}}>
                    <Button color="inherit" onClick={handleCancel}>
                        {translate('cancel')}
                    </Button>
                </Box>
            </Stack>
        );
    }

    const renderIgnoreOnError = !isPreview && !showAbortOnError && showIgnoreOnError && Boolean(labels.ignoreActionError);
    const renderWillIgnoreOnError = !isPreview && !showAbortOnError && !renderIgnoreOnError && Boolean(labels.willIgnoreActionError);

    return (
        <Stack spacing={3}>
            <ProjectStepStateForm
                isInactive={isInactive}
                setIsInactive={setIsInactive}
            />
            <Renderer
                {...props}
                parentFormContext={formContext}
                i18nContextRoot={i18nContext}
                handleSave={({value}) => {
                    const formData = watch();

                    handleSave({
                        ...item,
                        ...formData,
                        value,
                        isInactive,
                    });
                }}
                abortOnErrorNode={showAbortOnError && (
                    <Controller
                        name="abortOnError"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={labels.abortion}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                )}
                ignoreOnErrorNode={renderIgnoreOnError && (
                    <Controller
                        name="ignoreOnError"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={labels.ignoreActionError}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                )}
                willIgnoreOnErrorNode={(renderWillIgnoreOnError && (
                    <Alert severity="info">
                        {labels.willIgnoreActionError}
                    </Alert>
                ))}
                labelNode={(
                    <ItemLabellingStrategy
                        formContext={formContext}
                        notification={(
                            <Alert severity="info">
                                {translate(`${i18nContext}.itemLabelling.distinct`)}
                            </Alert>
                        )}
                    />
                )}
            />
        </Stack>
    );
};

URLChallengeRenderer.propTypes = UrlChallengeRendererPropTypes;

export default URLChallengeRenderer;
