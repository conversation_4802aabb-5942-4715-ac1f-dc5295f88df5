import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, St<PERSON>, <PERSON>Field, Typography} from '@material-ui/core';
import * as Yup from 'yup';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {Controller, useForm} from 'react-hook-form';
import {useEffect} from 'react';
import {yupResolver} from '@hookform/resolvers/yup';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import SubmitButton from '../utils/SubmitButton';
import {getEmailSchema} from '../../utils/formSchema';
import SettingItemChange from '../utils/SettingItemChange';
import Drawer from './Drawer';

const propTypes = {
    handleCancel: PropTypes.func,
    handleSave: PropTypes.func,
    onEdit: PropTypes.func,
    defaultValues: PropTypes.object,
    isPreview: PropTypes.bool,
    emailList: PropTypes.arrayOf(PropTypes.string)
};

const mapStateToProps = ({state}) => {
    const {
        collaboratorList,
    } = state;
    const emailList = (
        isNonEmptyArray(collaboratorList)? collaboratorList.map(({email}) => (email)) : []
    ).filter(Boolean);

    return {
        emailList,
    };
};

const ProjectNotificationEmailDialogForm = ({
    handleCancel,
    handleSave,
    onEdit,
    defaultValues,
    isPreview,
    emailList,
}) => {
    const {translate} = useLocales();
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            main: getEmailSchema({
                emailRequired: translate('form.validation.emailRequired'),
                emailInvalid: translate('form.validation.emailInvalid'),
            }),
        })),
        defaultValues: {
            main: defaultValues?.main || '',
        },
    });
    const {
        watch,
        control,
        trigger,
        setError,
        formState: {
            errors,
        },
    } = formContext;

    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (isPreview) {
        if (!defaultValues?.main) {
            return null;
        }

        return (
            <Stack direction="row" alignItems="center" flexWrap="wrap" spacing={1}>
                {defaultValues?.main}
                <SettingItemChange
                    onClick={onEdit}
                />
            </Stack>
        );
    }

    return (
        <Drawer
            open
            title={(
                <Typography variant="subtitle1">
                    {translate('products.notificationProducts.items.emailNotification.label')}
                </Typography>
            )}
        >
            <Typography variant="subtitle1" sx={{color: 'text.secondary', py: 1}}>
                {translate('products.notificationProducts.items.emailNotification.info')}
            </Typography>

            <Controller
                name="main"
                control={control}
                render={({
                    field,
                    fieldState: {error},
                }) => {
                    return (
                        <Autocomplete
                            fullWidth
                            freeSolo
                            defaultValue={field.value}
                            options={emailList.map((option) => option)}
                            renderInput={(params) => {
                                return (
                                    <Stack spacing={1}>
                                        <TextField
                                            {...params}
                                            label={translate('form.email')}
                                        />
                                        {Boolean(error?.message) && (
                                            <Typography
                                                sx={{color: 'error.main'}}>
                                                {error?.message}
                                            </Typography>
                                        )}
                                    </Stack>
                                );
                            }}
                            onChange={(_, newValue) => {
                                if (!newValue) {
                                    setError(field.name, {
                                        message: translate('form.validation.emailRequired'),
                                    });
                                    return;
                                }

                                field.onChange(newValue);
                            }}
                        />
                    );
                }}
            />
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        const content = watch();
                        handleSave(content);
                    }}
                    isValid={!errors?.main}
                />
            </Stack>
        </Drawer>
    );
};

ProjectNotificationEmailDialogForm.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectNotificationEmailDialogForm);
