import {Fragment} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button, CardActionArea, Di<PERSON><PERSON>, Stack, Typography} from '@material-ui/core';
import {challenges} from '@w7-3/webeagle-resources/dist/config/urlChallenge';
import closeOutline from '@iconify/icons-eva/close-outline';
import {Icon} from '@iconify/react';
import useLocales from '../../../hooks/useLocales';
import Drawer from './Drawer';
import {SelectionDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.urlChallenge';
const fullList = [];
const automatedCategoryName = 'automated';
[
    automatedCategoryName,
    ...Object.keys(challenges).filter((categoryId) => categoryId !== automatedCategoryName),
].forEach((categoryId) => {
    fullList.push({
        categoryId,
        list: Object.values(challenges[categoryId]),
    });
});

const URLChallengeSelectionDialogForm = ({handleSelection, handleCloseMenu}) => {
    const {translate} = useLocales();
    const title = translate(`${i18nContext}.selectOption`);

    return (
        <Drawer
            open
            title={(
                <Typography variant="subtitle1">
                    {title}
                </Typography>
            )}
            onClose={handleCloseMenu}
        >
            {
                fullList.map(({categoryId, list}) => (
                    <Fragment key={categoryId}>
                        <Typography
                            variant="subtitle1"
                            sx={{
                                textTransform: 'none',
                            }}>
                            {translate(`${i18nContext}.groups.${categoryId}.label`)}
                        </Typography>
                        <Stack sx={{my: 3}}>
                            {
                                list.map((type, index2) => (
                                    <Fragment key={type.value}>
                                        <CardActionArea
                                            key={type.value}
                                            onClick={() => {
                                                handleSelection({
                                                    categoryId,
                                                    type: type.value,
                                                });
                                            }}
                                            sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'flex-start',
                                                p: 1,
                                            }}>
                                            <Typography variant="subtitle1" sx={{textTransform: 'none', mb: 1}}>
                                                {translate(`${i18nContext}.groups.${categoryId}.options.${type.value}.label`)}
                                            </Typography>
                                            <Typography sx={{color: 'text.secondary'}}>
                                                {translate(`${i18nContext}.groups.${categoryId}.options.${type.value}.description`)}
                                            </Typography>
                                            {
                                                type.value === challenges.automated.ai.value && (
                                                    <Alert severity="warning" sx={{my: 3}}>
                                                        <AlertTitle>
                                                            {translate(`${i18nContext}.groups.${categoryId}.options.${type.value}.disclaimer.label`)}
                                                        </AlertTitle>
                                                        {translate(`${i18nContext}.groups.${categoryId}.options.${type.value}.disclaimer.description`)}

                                                    </Alert>
                                                )
                                            }
                                            <Divider />
                                        </CardActionArea>
                                        {
                                            (index2 + 1) < list.length &&
                                            <Divider />
                                        }
                                    </Fragment>
                                ))
                            }
                        </Stack>
                    </Fragment>
                ))
            }
            <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                <Button
                    color="inherit"
                    startIcon={<Icon icon={closeOutline} width={40} height={40} />}
                    onClick={handleCloseMenu}>
                    {translate('close')}
                </Button>
            </Stack>
        </Drawer>
    );
};

URLChallengeSelectionDialogForm.propTypes = SelectionDialogFormPropTypes;

export default URLChallengeSelectionDialogForm;
