import {useCallback, useEffect, useRef, useState} from 'react';
import {Alert, Box, Button, Grid, MenuItem, <PERSON>lide<PERSON>, Stack, TextField, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import alarmOn from '@iconify/icons-ic/alarm-on';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {
    BUILD_SCHEDULER_TYPE_RANDOM_FREQUENCY,
    BUILD_SCHEDULER_TYPES,
} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import PubSub from 'pubsub-js';
import {debounce} from 'throttle-debounce';
import SubmitButton from '../utils/SubmitButton';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import DefinitionList from '../utils/DefinitionList';
import LabelStyle from '../utils/LabelStyle';
import {getOpenAPIPath} from '../../utils/getPath';
import useApiCaller from '../../hooks/useApiCaller';
import Drawer from './Drawer';

const i18nContextRoot = 'project.wizardSteps.summary.fields.scheduler.configurator.types';
const i18nContext = `${i18nContextRoot}.options.${BUILD_SCHEDULER_TYPES.RANDOM}`;

const times = [
    {value: 0, label: '00:00'},
    {value: 4, label: '04:00'},
    {value: 9, label: '09:00'},
    {value: 17, label: '15:00'},
    {value: 23, label: '23:00'},
];

const pageSize = 1;
const type = BUILD_SCHEDULER_TYPES.RANDOM;
const valueLabelFormatPrice = (value) => `${value}:00`;
const minHr = 0;
const maxHr = 23;

const propTypes = {
    startDate: PropTypes.number,
    endDate: PropTypes.number,
    onChange: PropTypes.func,
    defaultConfig: PropTypes.shape({
        interval: PropTypes.arrayOf(PropTypes.number),
        frequency: PropTypes.number,
        frequencyQualifier: PropTypes.string,
    }),
};

const defaultProps = {
    defaultConfig: {
        interval: [9, 21],
        frequency: 1,
        frequencyQualifier: BUILD_SCHEDULER_TYPE_RANDOM_FREQUENCY.WEEKLY,
    },
};

const ProjectSchedulerRandom = ({defaultConfig, startDate, endDate, onChange}) => {
    const {translate} = useLocales();
    const [open, setOpen] = useState(false);
    const [interval, setInterval] = useState(defaultConfig?.interval || defaultProps.defaultConfig.interval);
    const [nextBuildList, setNextBuildList] = useState(null);
    const [frequency, setFrequency] = useState(defaultConfig?.frequency || defaultProps.defaultConfig.frequency);
    const [frequencyQualifier, setFrequencyQualifier] = useState(defaultConfig?.frequencyQualifier || defaultProps.defaultConfig.frequencyQualifier);
    const apiCaller = useApiCaller();
    const isBusy = useRef(false);
    const handleApply = debounce(1000,false, useCallback( () => {
        if (!open || isBusy.current) {
            return;
        }

        isBusy.current = true;
        apiCaller({
            uri: getOpenAPIPath(api.nextProjectBuildList),
            data: {
                startDate,
                endDate,
                schedulerData: {
                    type,
                    config: {
                        interval,
                        frequency,
                        frequencyQualifier,
                    },
                },
                pageSize,
            },
            successCallback: ({data}) => {
                setNextBuildList(data?.nextBuildList);
            },
            alwaysCallback: () => {
                isBusy.current = false;
            },
            isAnimated: true,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open, endDate, interval, frequency, frequencyQualifier, startDate]));
    const isValid = Boolean(startDate && interval && frequency);
    const onSave = () => {
        onChange({
            schedulerData: {
                type,
                config: {
                    interval,
                    frequency,
                    frequencyQualifier,
                },
            },
        });
        setOpen(false);
    };

    useEffect(() => {
        if (!isValid) {
            return;
        }

        handleApply();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isValid, startDate, interval, frequency]);

    useEffect(() => {
        const subscriptionId = PubSub.subscribe(`SHOW_PROJECT_SCHEDULER.${type}`, (_, payload) => {
            setInterval(payload?.config?.interval);
            setFrequency(payload?.config?.frequency);
            setFrequencyQualifier(payload?.config?.frequencyQualifier);
            setOpen(true);
        });

        return () => {
            PubSub.unsubscribe(subscriptionId);
        };
    }, []);

    return (
        <>
            <Box sx={{pl: 0, textAlign: 'left'}}>
                <Button
                    startIcon={<Icon icon={alarmOn} width={40} height={40}/>}
                    onClick={() => {
                        setOpen(true);
                    }}
                    sx={{
                        textTransform: 'none',
                    }}>
                    {translate(`${i18nContext}.cta`)}
                </Button>
            </Box>
            <Drawer
                open={open}
                onClose={() => {
                    setOpen(false);
                }}
                title={(
                    <Typography variant="subtitle1">
                        {translate(`${i18nContext}.label`)}
                    </Typography>
                )}
            >
                <Alert severity="info" sx={{my: 2}}>
                    <Typography component="p" variant="subtitle2">
                        {translate(`${i18nContext}.questionInfo`)}
                    </Typography>
                </Alert>
                <Stack
                    spacing={3}
                >
                    <LabelStyle>
                        {translate(`${i18nContext}.question`)}
                    </LabelStyle>
                    <Box sx={{width: '100%', px: 3}}>
                        <Slider
                            min={minHr}
                            max={maxHr}
                            marks={times}
                            value={interval}
                            onChange={(_, value) => {
                                if (value[0] === value[1] || Math.abs(value[0] - value[1]) >= maxHr) {
                                    return;
                                }
                                setInterval(value);
                            }}
                            valueLabelDisplay="auto"
                            valueLabelFormat={valueLabelFormatPrice}
                        />
                    </Box>
                    <Grid container gap={3}>
                        <Grid item xs={12} md={3}>
                            <TextField
                                fullWidth
                                type="number"
                                label={translate(`${i18nContext}.frequency.label`)}
                                value={frequency}
                                InputProps={{
                                    step: 1,
                                    min: 1,
                                    max: 100,
                                    inputProps: {min: 1, max: 100},
                                }}
                                onChange={(event) => setFrequency(event.target.value)} />
                        </Grid>
                        <Grid item xs={12} md={9}>
                            <TextField
                                select
                                fullWidth
                                label={translate(`${i18nContext}.frequency.qualifierLabel`)}
                                value={frequencyQualifier}
                                InputProps={{
                                    step: 1,
                                    min: 0,
                                    inputProps: {min: 0},
                                }}
                                onChange={(event) => setFrequencyQualifier(event.target.value)}>
                                {Object.keys(BUILD_SCHEDULER_TYPE_RANDOM_FREQUENCY).map((key) => {
                                    const value = BUILD_SCHEDULER_TYPE_RANDOM_FREQUENCY[key];

                                    return (
                                        <MenuItem key={key} value={value}>
                                            {translate(`${i18nContext}.frequency.qualifierOptions.${value}`)}
                                        </MenuItem>
                                    );
                                })}
                            </TextField>
                        </Grid>
                    </Grid>
                    {
                        isNonEmptyArray(nextBuildList) && (
                            <DefinitionList
                                dataList={[{
                                    key: nextBuildList?.length === 1 ?
                                        translate('project.build.schedule.nextBuild') :
                                        translate('project.build.schedule.nextBuildList', {
                                            count: nextBuildList.length,
                                        }),
                                    node: (
                                        <Stack spacing={1.5}>
                                            {
                                                nextBuildList.map((nextBuild) => {
                                                    return (
                                                        <Box sx={{pl: 1}} key={nextBuild}>
                                                            {fDateTimeHumanReadable(nextBuild)}
                                                        </Box>
                                                    );
                                                })
                                            }
                                        </Stack>
                                    ),
                                }]}
                            />
                        )
                    }
                </Stack>
                <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                    <Button color="inherit" onClick={() => {
                        setOpen(false);
                    }}>
                        {translate('cancel')}
                    </Button>
                    <SubmitButton
                        onClick={onSave}
                        disabled={!isNonEmptyArray(nextBuildList)}
                        isValid={isValid}
                    />
                </Stack>
            </Drawer>
        </>
    );
};

ProjectSchedulerRandom.propTypes = propTypes;
ProjectSchedulerRandom.defaultProps = defaultProps;

export default ProjectSchedulerRandom;
