import {useState} from 'react';
import PropTypes from 'prop-types';
import {Alert, Box, Button, Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import {useSnackbar} from 'notistack5';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import ResponseOverrideTable from './ResponseOverrideTable';
import ResponseOverrideDialogForm from './ResponseOverrideDialogForm';
import {toggleArrayObjectItem} from '../../utils/toggle';

const initialState = {
    show: false,
    item: null,
    id: undefined,
};
const propTypes = {
    itemList: PropTypes.arrayOf(PropTypes.shape({
        contentType: PropTypes.string,
        overrideType: PropTypes.string,
        source: PropTypes.string,
        target: PropTypes.string,
        content: PropTypes.object,
    })),
    onChange: PropTypes.func,
};

const i18nContext = 'project.wizardSteps.requestOptions.groups.resources.fields.responseOverrides';

const ResponseOverrideForm = ({onChange, itemList}) => {
    const {translate} = useLocales();
    const {enqueueSnackbar} = useSnackbar();
    const [resourceOverrideDialogState, setResourceOverrideDialogState] = useState(initialState);
    const handleUpdate = (id, item) => {
        setResourceOverrideDialogState(initialState);
        if (!item) {
            return;
        }

        const newList = toggleArrayObjectItem({
            item: {
                ...item,
                id,
            },
            idField: 'id',
            list: itemList,
            add: true,
        });
        onChange(newList);
    };
    const handleClickEdit = (item) => {
        setResourceOverrideDialogState({
            show: true,
            item,
            id: item.id,
        });
    };
    const handleClickDelete = (item) => {
        const newList = toggleArrayObjectItem({
            item,
            idField: 'id',
            list: itemList,
            add: false,
        });
        onChange(newList);
        enqueueSnackbar(translate(`${i18nContext}.notifications.deleteSuccess`), {variant: 'success'});
    };

    return (
        <Stack
            direction="column"
            spacing={3}>
            <Alert severity="info" sx={{mb: 3}}>
                {translate(`${i18nContext}.firstMatchInfo`)}
            </Alert>
            {
                itemList?.length > 0 &&
                <ResponseOverrideTable
                    itemList={itemList}
                    handleClickEdit={handleClickEdit}
                    handleClickDelete={handleClickDelete}
                />
            }
            <Box sx={{
                display: 'flex',
                ...getOptionalMap(itemList?.length < 1, {
                    flexDirection: 'column',
                })
            }}>
                <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                    onClick={() => setResourceOverrideDialogState({
                        show: true,
                    })}
                    sx={{textTransform: 'none'}}>
                    {translate(`${i18nContext}.label`)}
                </Button>
            </Box>
            {
                resourceOverrideDialogState.show &&
                <ResponseOverrideDialogForm
                    id={resourceOverrideDialogState.id}
                    item={resourceOverrideDialogState.item}
                    itemList={itemList}
                    updateDialogState={handleUpdate}
                />
            }
        </Stack>
    );
};

ResponseOverrideForm.propTypes = propTypes;

export default ResponseOverrideForm;
