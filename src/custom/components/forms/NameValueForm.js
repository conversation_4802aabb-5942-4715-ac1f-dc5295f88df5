import * as Yup from 'yup';
import {useEffect} from 'react';
import PropTypes from 'prop-types';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {<PERSON><PERSON>, Grid, Stack, TextField, Typography} from '@material-ui/core';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import {getParameterKeySchema} from '../../utils/formSchema';

const propTypes = {
    values: PropTypes.shape({
        name: PropTypes.string,
        value: PropTypes.string,
        data: PropTypes.shape({
            name: PropTypes.string,
            value: PropTypes.string,
        }),
    }),
    labels: PropTypes.shape({
        emptyValueInfo: PropTypes.string,
        name: PropTypes.string,
        value: PropTypes.string,
    }),
    errorLabels: PropTypes.shape({
        name: PropTypes.string,
        value: PropTypes.string,
    }),
    optionalValue: PropTypes.bool,
    onChange: PropTypes.func.isRequired,
    buttons: PropTypes.node,
};

const i18nContext = 'nameValue';

const NameValueForm = ({values, labels, errorLabels, optionalValue, onChange, buttons}) => {
    const {translate} = useLocales();
    const Schema = Yup.object().shape({
        name: getParameterKeySchema({
            mandatoryField: errorLabels?.name || translate('mandatoryField', {field: translate('name')}),
            invalidField: translate('invalidField', {field: translate('name')}),
        }),
        value: Yup
            .string()
            .test('', errorLabels?.value || translate('mandatoryField', {field: translate('value')}), (value) => {
                if (optionalValue) {
                    return true;
                }

                return isNonEmptyString(value);
            }),
    });
    const {
        watch,
        control,
        trigger,
        formState: {
            isValid,
            isDirty,
        },
    } = useForm({
        mode: 'onChange',
        resolver: yupResolver(Schema),
        defaultValues: {
            name: values?.name || '',
            value: values?.value || '',
        },
    });

    const data = watch();

    useEffect(() => {
        trigger();
        onChange({isValid, values: data});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (!isDirty || !data || (data?.name === values?.name && data?.value === values?.value)) {
            return;
        }

        onChange({isValid, values: data});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data, isDirty, values]);

    return (
        <Grid container
              spacing={3}
              sx={{
                  borderRadius: 1,
                  width: '100%',
                  pr: 3,
                  pb: 3,
                  position: 'relative',
                  border: (theme) => `1px solid ${theme.palette.grey[500_32]}`,
              }}>
            {
                labels?.emptyValueInfo && (
                    <Grid item xs={12}>
                        <Alert severity="info">
                            {labels?.emptyValueInfo}
                        </Alert>
                    </Grid>
                )
            }
            <Grid item xs={12} md={6}>
                <Controller
                    name="name"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <Stack spacing={1}>
                                <TextField
                                    fullWidth
                                    {...field}
                                    label={labels?.name || translate(`${i18nContext}.fields.name`)}
                                />
                                {Boolean(error?.message) && (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                )}
                            </Stack>
                        );
                    }}
                />
            </Grid>
            <Grid item xs={12} md={6}>
                <Controller
                    name="value"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <Stack spacing={1}>
                                <TextField
                                    fullWidth
                                    {...field}
                                    label={labels?.value || translate(`${i18nContext}.fields.value`)}
                                />
                                {!optionalValue && Boolean(error?.message) && (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                )}
                            </Stack>
                        );
                    }}
                />
            </Grid>
            <Grid item xs={12} sx={{
                display: 'flex',
                justifyContent: 'flex-end',
            }}>
                {buttons}
            </Grid>
        </Grid>
    );
};

NameValueForm.propTypes = propTypes;

export default NameValueForm;
