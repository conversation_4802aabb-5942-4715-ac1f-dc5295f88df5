import {Fragment} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button, CardActionArea, Di<PERSON><PERSON>, Stack, Typography} from '@material-ui/core';
import conditions from '@w7-3/webeagle-resources/dist/config/conditions';
import closeOutline from '@iconify/icons-eva/close-outline';
import {Icon} from '@iconify/react';
import useLocales from '../../../hooks/useLocales';
import Drawer from './Drawer';
import {SelectionDialogFormPropTypes} from '../../../config/prop-types/ProjectSteps';

const i18nContext = 'conditions';
const conditionList = [
    conditions.ai,
    ...Object.values(conditions).filter((value) => conditions.ai !== value)
];
const ConditionSelectionDialogForm = ({handleSelection, handleCloseMenu}) => {
    const {translate} = useLocales();
    const title = translate(`${i18nContext}.selectOption`);

    return (
        <Drawer
            open
            title={(
                <Typography variant="subtitle1">
                    {title}
                </Typography>
            )}
            onClose={handleCloseMenu}
        >
            {
                conditionList.map((type, index) => {
                    return (
                        <Fragment key={type.value}>
                            <CardActionArea
                                onClick={() => {
                                    handleSelection({type: type.value});
                                }}
                                sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'flex-start',
                                    p: 1,
                                }}>
                                <Typography variant="subtitle1" sx={{textTransform: 'none', mb: 1}}>
                                    {translate(`${i18nContext}.options.${type.value}.label`)}
                                </Typography>
                                <Typography sx={{color: 'text.secondary'}}>
                                    {translate(`${i18nContext}.options.${type.value}.description`)}
                                </Typography>
                                {
                                    type.value === conditions.ai.value && (
                                        <Alert severity="warning" sx={{my: 3}}>
                                            <AlertTitle>
                                                {translate(`${i18nContext}.options.${type.value}.disclaimer.label`)}
                                            </AlertTitle>
                                            {translate(`${i18nContext}.options.${type.value}.disclaimer.description`)}

                                        </Alert>
                                    )
                                }
                            </CardActionArea>
                            {
                                (index + 1) < conditionList.length &&
                                <Divider />
                            }
                        </Fragment>
                    );
                })
            }
            <Stack direction="row" alignItems="center" justifyContent="flex-end" sx={{p: 3}}>
                <Button
                    color="inherit"
                    startIcon={<Icon icon={closeOutline} width={40} height={40} />}
                    onClick={handleCloseMenu}>
                    {translate('close')}
                </Button>
            </Stack>
        </Drawer>
    );
};

ConditionSelectionDialogForm.propTypes = SelectionDialogFormPropTypes;

export default ConditionSelectionDialogForm;
