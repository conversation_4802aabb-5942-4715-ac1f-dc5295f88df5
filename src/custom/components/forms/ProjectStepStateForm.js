import {FormControl<PERSON>abel, Stack, Switch} from '@material-ui/core';
import PropTypes from 'prop-types';
import useLocales from '../../../hooks/useLocales';
import LabelStyle from '../utils/LabelStyle';

const propTypes = {
    isInactive: PropTypes.bool,
    setIsInactive: PropTypes.func,
};

const ProjectStepStateForm = ({isInactive, setIsInactive}) => {
    const {translate} = useLocales();
    const state = translate(`workflowSteps.state.${isInactive ? 'inactive' : 'active'}`);

    return (
        <Stack spacing={1} direction="row" alignItems="center">
            <div>
                <LabelStyle>
                    {translate('workflowSteps.state.label')}
                </LabelStyle>
            </div>
            <FormControlLabel
                label={state}
                control={
                    <Switch
                        onChange={(_, isChecked) => {
                            setIsInactive(!isChecked);
                        }}
                        checked={!isInactive}
                    />
                }
            />
        </Stack>
    );
};

ProjectStepStateForm.propTypes = propTypes;

export default ProjectStepStateForm;
