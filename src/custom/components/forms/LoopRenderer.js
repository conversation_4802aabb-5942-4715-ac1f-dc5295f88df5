import * as Yup from 'yup';
import {useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {Alert, Box, Button, Stack} from '@material-ui/core';
import loops from '@w7-3/webeagle-resources/dist/config/loops';
import {useState} from 'react';
import SelectorLoop from '../services/loops/SelectorLoop';
import RangeLoop from '../services/loops/RangeLoop';
import JSLoop from '../services/loops/JSLoop';
import JSListLoop from '../services/loops/JSListLoop';
import CustomListLoop from '../services/loops/CustomListLoop';
import {getLabellingValidation, getSimpleQuerySelectorValidation} from '../../utils/formSchema';
import useLocales from '../../../hooks/useLocales';
import SelectorTextField from './SelectorTextField';
import {ItemRendererPropTypes} from '../../../config/prop-types/ProjectSteps';
import ItemLabellingStrategy from '../utils/ItemLabellingStrategy';
import ProjectStepStateForm from './ProjectStepStateForm';

const renderers = {
    [loops.selector.value]: SelectorLoop,
    [loops.range.value]: RangeLoop,
    [loops.js.value]: JSLoop,
    [loops.jsList.value]: JSListLoop,
    [loops.customList.value]: CustomListLoop,
};

const LoopRenderer = (props) => {
    const {item, labelBlacklist, handleSave, handleCancel} = props;
    const Renderer = renderers[item?.type];
    const {translate} = useLocales();
    const [isInactive, setIsInactive] = useState(item?.isInactive);
    const disallowEmpty = [
        loops.selector.value,
    ].includes(item?.type);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            ...getLabellingValidation({
                label: {
                    error: translate('itemLabelling.mandatoryField'),
                    duplicate: translate('itemLabelling.duplicateGroupName'),
                    allowEmpty: false,
                },
                labelScript: {
                    error: translate('form.validation.script'),
                },
                labelBlacklist,
            }),
            selector: getSimpleQuerySelectorValidation(translate('selector.invalid'), !disallowEmpty),
        })),
        defaultValues: item,
    });

    if (!Renderer) {
        return (
            <Stack spacing={3}>
                <Alert severity="error">
                    {translate('conditions.unavailable')}
                </Alert>
                <Box sx={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center'}}>
                    <Button color="inherit" onClick={handleCancel}>
                        {translate('cancel')}
                    </Button>
                </Box>
            </Stack>
        );
    }

    const {
        watch,
        control,
        formState,
    } = formContext;

    return (
        <>
            <ProjectStepStateForm
                isInactive={isInactive}
                setIsInactive={setIsInactive}
            />
            <Renderer
                {...props}
                parentFormContext={formContext}
                labelNode={(
                    <ItemLabellingStrategy
                        formContext={formContext}
                    />
                )}
                selectorNode={(
                    <SelectorTextField
                        control={control}
                        formState={formState}
                        name="selector"
                        showInitialError
                        hideReadMore
                    />
                )}
                handleSave={({value}) => {
                    const formData = watch();

                    handleSave({
                        ...item,
                        ...formData,
                        value,
                        isInactive,
                    });
                }}
            />
        </>
    );
};

LoopRenderer.propTypes = ItemRendererPropTypes;

export default LoopRenderer;
