import {useState} from 'react';
import pick from 'ramda/src/pick';
import PropTypes from 'prop-types';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography} from '@material-ui/core';
import ReactDOMServer from 'react-dom/server';
import {OVERRIDES} from '@w7-3/webeagle-resources/dist/config/code';
import {getRandomString} from '@w7-3/webeagle-resources/dist/libs/random';
import useLocales from '../../../hooks/useLocales';
import UrlRequestForm from './UrlReqestForm';
import {FORM_FIELDS} from '../../../config/formFields';
import SubmitButton from '../utils/SubmitButton';
import CodeTextFieldJS from './CodeTextFieldJS';
import Drawer from './Drawer';


const defaultProps = {
    get id() {
        return getRandomString(8);
    },
    item: {},
};

const propTypes = {
    id: PropTypes.string,
    item: PropTypes.object,
    updateDialogState: PropTypes.func,
};

const i18nContext = 'project.wizardSteps.requestOptions.groups.resources.fields.responseOverrides';
const defaultJsCode = {
    value: '',
    isValid: false,
    isValidated: false,
};

const ResponseOverrideDialogForm = ({id, item, updateDialogState}) => {
    const initialJsCode = item?.jsCode || defaultJsCode;
    const {translate} = useLocales();
    const [urlData, setUrlData] = useState(item?.urlData || {});
    const [urlDataIsValid, setUrlDataIsValid] = useState(false);
    const [jsCode, setJsCode] = useState(initialJsCode);
    const isValid = urlDataIsValid && jsCode.isValidated && jsCode.isValid;
    const handleCancel = () => {
        updateDialogState();
    };
    const handleSave = () => {
        updateDialogState(id, {
            id,
            urlData,
            jsCode,
        });
    };
    const linkedResources = translate('linkedResources', {
        returnObjects: true,
    });
    const stringResource = ReactDOMServer.renderToStaticMarkup(
        <a href={linkedResources.string.url}
           data-static-link=""
           target="_blank"
           rel="nofollow noreferrer noopener">
            {linkedResources.string.label}
        </a>,
    );
    const objectResource = ReactDOMServer.renderToStaticMarkup(
        <a href={linkedResources.object.url}
           data-static-link=""
           target="_blank"
           rel="nofollow noreferrer noopener">
            {linkedResources.object.label}
        </a>,
    );

    return (
        <Drawer
            open
            onClose={handleCancel}
            title={(
                <Typography variant="subtitle1">
                    {translate(`${i18nContext}.label`)}
                </Typography>
            )}
        >
            <Stack spacing={3}
                   sx={{width: '100%'}}>
                <UrlRequestForm
                    urlData={urlData}
                    onChange={({data, isValid}) => {
                        setUrlData(data);
                        setUrlDataIsValid(isValid);
                    }}
                />
                <Alert severity="info" sx={{mb: 3}}>
                    <span dangerouslySetInnerHTML={{
                        __html: translate(`${i18nContext}.ctaLabel`, {
                            objectResource,
                            stringResource,
                        }),
                    }}/>
                </Alert>
                <CodeTextFieldJS
                    script={jsCode}
                    onChange={setJsCode}
                    label={translate(`${i18nContext}.reducer`)}
                    prePlaceholder={`function reducer(${OVERRIDES.response.fe}) {`}
                    paramsDocs={OVERRIDES.response}
                    placeholder={[
                        `/* // ${translate('pasteJSCodeHere')}`,
                        ` * return ${JSON.stringify(OVERRIDES.response.be, null, 2)};`,
                        ' */',
                    ].join('\n')}
                    postPlaceholder="}"
                    modifier="standard"
                    showReset
                />
                <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                    <Button color="inherit" onClick={handleCancel}>
                        {translate('cancel')}
                    </Button>
                    <SubmitButton
                        onClick={() => {
                            handleSave({
                                value: {
                                    ...pick([
                                        FORM_FIELDS.urlMatch,
                                        'method',
                                        'context',
                                    ], urlData),
                                    jsCode,
                                },
                            });
                        }}
                        isValid={isValid}
                    />
                </Stack>
            </Stack>
        </Drawer>
    );
};

ResponseOverrideDialogForm.propTypes = propTypes;
ResponseOverrideDialogForm.defaultProps = defaultProps;

export default ResponseOverrideDialogForm;
