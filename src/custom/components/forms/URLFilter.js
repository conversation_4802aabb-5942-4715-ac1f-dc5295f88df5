import {Fragment, memo, useEffect} from 'react';
import {<PERSON><PERSON>, Divider, FormControlLabel, Radio, RadioGroup, Stack} from '@material-ui/core';
import {Controller, useFieldArray, useForm} from 'react-hook-form';
import {URL_MATCHER} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import getUrlMatch from '@w7-3/webeagle-resources/dist/libs/getUrlMatch';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import * as Yup from 'yup';
import {useSnackbar} from 'notistack5';
import {yupResolver} from '@hookform/resolvers/yup';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import {getSimpleUrlSchema, getUrlMatcherSchema} from '../../utils/formSchema';
import URLFilterItem from './URLFilterItem';
import {FORM_FIELDS} from '../../../config/formFields';
import URLFilterTester from './URLFilterTester';

const i18nContext = 'urlCapture';
export const propTypes = {
    value: PropTypes.object.isRequired,
    onChange: PropTypes.func.isRequired,
    testUrlSuccessKey: PropTypes.string,
    testUrlFailureKey: PropTypes.string,
    compareWithGlobal: PropTypes.bool,
    filtering: PropTypes.object,
};

const mapStateToProps = ({projectAssistant}) => {
    const {
        linkOptions: {
            filtering,
        },
    } = projectAssistant.configData;

    return {
        filtering,
    };
};

const URLFilter = ({
    value,
    onChange,
    testUrlSuccessKey,
    testUrlFailureKey,
    compareWithGlobal,
    filtering,
}) => {
    const {translate} = useLocales();
    const {enqueueSnackbar} = useSnackbar();
    const FormSchema = Yup.object().shape({
        [FORM_FIELDS.urlMatch]: Yup.array()
            .of(
                Yup.object().shape({
                    value: getUrlMatcherSchema(),
                }),
            ),
        [FORM_FIELDS.testFieldName]: getSimpleUrlSchema({translate}),
    });
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(FormSchema),
        defaultValues: {
            [FORM_FIELDS.urlMatch]: [],
            ...value,
            [FORM_FIELDS.testFieldName]: '',
        },
    });
    const {
        control,
        watch,
        formState: {
            errors,
        },
        trigger,
    } = formContext;

    const {remove, append} = useFieldArray({
        control,
        name: FORM_FIELDS.urlMatch,
    });
    const urlMatchAny = watch(FORM_FIELDS.urlMatchAny);
    const urlMatch = watch(FORM_FIELDS.urlMatch);
    const checkUrl = (value) => {
        const isGlobalMatch = getUrlMatch(value, filtering.urlMatch, filtering.urlMatchAny);
        const isMatch = isGlobalMatch && getUrlMatch(value, urlMatch, urlMatchAny);

        return {
            isGlobalMatch,
            isMatch,
        };
    };

    useEffect(() => {
        if (isNonEmptyObject(errors)) {
            return;
        }
        onChange({urlMatchAny, urlMatch});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [urlMatchAny, urlMatch]);

    useEffect(() => {
        return () => {
            if (!isNonEmptyObject(errors)) {
                return;
            }
            enqueueSnackbar(translate('notSavedDueToErrors'), {variant: 'info'});
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [errors]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <RadioGroup row>
                <Controller
                    name={FORM_FIELDS.urlMatchAny}
                    control={control}
                    render={({field}) => {
                        return (
                            <>
                                <FormControlLabel
                                    control={<Radio
                                        {...field}
                                        checked={field.value}
                                        onChange={() => {
                                            field.onChange(true);
                                        }}
                                    />}
                                    label={translate(`${i18nContext}.criteriaAny`)}
                                />
                                <FormControlLabel
                                    control={<Radio
                                        {...field}
                                        checked={!field.value}
                                        onChange={() => {
                                            field.onChange(false);
                                        }}
                                    />}
                                    label={translate(`${i18nContext}.criteriaAll`)}
                                />
                            </>
                        );
                    }}
                />
            </RadioGroup>
            {urlMatch.map((item, index) => {
                return (
                    <Fragment key={`${item.id}`}>
                        <URLFilterItem {
                            ...{
                                control,
                                remove,
                                trigger,
                                item,
                                index,
                            }
                        }/>
                        <Divider>
                            {
                                urlMatchAny ?
                                    translate('anyLabel') :
                                    translate('allLabel')
                            }
                        </Divider>
                    </Fragment>
                );
            })}
            <Button variant="outlined"
                    size="large"
                    startIcon={<Icon icon={plusFill} width={40} height={40}/>}
                    onClick={() =>
                        append({
                            id: Date.now(),
                            value: '',
                            matcher: URL_MATCHER.regex,
                        })
                    }
                    disabled={urlMatch.length > 0 && Boolean(errors[FORM_FIELDS.urlMatch])}
            >
                {translate(`${i18nContext}.moreLabel`)}
            </Button>
            <URLFilterTester {
                 ...{
                     control,
                     checkUrl,
                     testUrlSuccessKey,
                     testUrlFailureKey,
                     compareWithGlobal,
                 }
             } />
        </Stack>
    );
};

URLFilter.propTypes = propTypes;

export default memo(connect(mapStateToProps)(URLFilter), () => true);
