import {Controller} from 'react-hook-form';
import PropTypes from 'prop-types';
import {URL_MATCHER} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {InputAdornment, MenuItem, Select, Stack, TextField, Typography} from '@material-ui/core';
import {URL_MATCHER_REGEX_FLAGS} from '@w7-3/webeagle-resources/dist/config/misc';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import DeleteButton from '../utils/DeleteButton';
import useLocales from '../../../hooks/useLocales';
import {FORM_FIELDS} from '../../../config/formFields';

export const propTypes = {
    control: PropTypes.object,
    remove: PropTypes.func,
    trigger: PropTypes.func,
    item: PropTypes.shape({
        matcher: PropTypes.string,
    }),
    index: PropTypes.number,
};

const defaultProps = {
};

const URLFilterItem = ({control, remove, trigger, item, index}) => {
    const {translate} = useLocales();
    return (
        <Stack
            spacing={{
                xs: 1,
                md: 3,
            }}
            sx={{my: 3}}
            direction={{xs: 'column', md: 'row'}}>
            <Controller
                name={`${FORM_FIELDS.urlMatch}.${index}.matcher`}
                control={control}
                defaultValue={URL_MATCHER.exact}
                render={({
                    field,
                }) => {
                    return (
                        <Select
                            key={field.value}
                            {...field}
                            onChange={(...props) => {
                                field.onChange(...props);
                                trigger();
                            }}>
                            {
                                Object.values(URL_MATCHER)
                                    .map((matchItem) => (
                                        <MenuItem key={matchItem} value={matchItem}>
                                            {translate(`urlCapture.options.${matchItem}.label`)}
                                        </MenuItem>
                                    ))
                            }
                        </Select>
                    );
                }}
            />
            <Stack spacing={1} direction="row" sx={{flexGrow: 1}}>
                <Controller
                    name={`${FORM_FIELDS.urlMatch}.${index}.value`}
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        const hasError = Boolean(error?.message);
                        const helperText = hasError ?
                            translate(`urlCapture.options.${item.matcher}.validationError`) :
                            '';

                        return (
                            <Stack spacing={1}>
                                <TextField
                                    fullWidth
                                    {...field}
                                    onChange={(event) => {
                                        if (!event.target.value) {
                                            field.onChange('');
                                            return;
                                        }

                                        field.onChange(event.target.value);
                                    }}
                                    label={translate('ruleIndex', {
                                        index: index + 1,
                                        type: translate(`urlCapture.options.${item.matcher}.label`),
                                    })}
                                    sx={{color: 'grey.500', fontSize: '1em'}}
                                    InputProps={{
                                        ...getOptionalMap(
                                            item.matcher === URL_MATCHER.regex, {
                                                startAdornment: (
                                                    <InputAdornment position="start"
                                                                    sx={{mr: 0}}>/</InputAdornment>
                                                ),
                                                endAdornment: (
                                                    <InputAdornment position="end"
                                                                    sx={{ml: 0}}>/{URL_MATCHER_REGEX_FLAGS}</InputAdornment>
                                                ),
                                            },
                                        ),
                                    }}
                                />
                                {Boolean(hasError) && (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {helperText}
                                    </Typography>
                                )}
                            </Stack>
                        );
                    }}
                />
                {
                    remove &&
                    <DeleteButton
                        onClick={() => remove(index)}
                    />
                }
            </Stack>
        </Stack>
    );
};

URLFilterItem.propTypes = propTypes;
URLFilterItem.defaultProps = defaultProps;

export default URLFilterItem;
