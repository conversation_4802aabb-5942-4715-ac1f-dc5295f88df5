import {useState} from 'react';
import {
    <PERSON>ert,
    Alert<PERSON><PERSON>le,
    Button,
    FormControlLabel,
    Radio,
    RadioGroup,
    Stack,
    Typography,
} from '@material-ui/core';
import PropTypes from 'prop-types';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import SubmitButton from '../utils/SubmitButton';
import useLocales from '../../../hooks/useLocales';

const DeleteAllOptions = {
    deleteAll: 'deleteAll',
    deleteSolution: 'deleteSolution',
};

const propTypes = {
    currentSolution: PropTypes.shape({
        appName: PropTypes.string,
    }),
    targetSolution: PropTypes.shape({
        appName: PropTypes.string,
    }),
    onCancel: PropTypes.func,
    onSubmit: PropTypes.func,
};

const i18nContext = 'project.wizardSteps.solutionsConfigs';

const ProjectSolutionChangeForm = ({
    currentSolution,
    targetSolution,
    onCancel,
    onSubmit,
}) => {
    const {translate} = useLocales();
    const [deleteAllOption, setDeleteAllOptionOption] = useState(DeleteAllOptions.deleteAll);
    const solution1 = translate(`${i18nContext}.groups.${currentSolution.appName}.label`);
    const solution2 = translate(`${i18nContext}.groups.${targetSolution.appName}.label`);

    return (
        <Alert
            severity="info"
            sx={{p: 3}}>
            {
                [
                    solutions.linkChecker.appName,
                    solutions.lighthouse.appName,
                ].includes(targetSolution.appName) ? (
                    <Typography component="p" variant="heading" sx={{mb: 2}}>
                        {translate('project.wizardSteps.solutionSelection.change.stepsNotRequired', {
                            solution2,
                        })}
                    </Typography>
                ) : (
                    <>
                        <AlertTitle>{translate('attentionRequired.label')}</AlertTitle>
                        <Typography component="p" variant="heading" sx={{mb: 2}}>
                            {translate('project.wizardSteps.solutionSelection.change.label', {
                                solution1,
                                solution2,
                            })}
                        </Typography>
                        <RadioGroup
                            value={deleteAllOption}
                            onChange={(_, newValue) => {
                                setDeleteAllOptionOption(newValue);
                            }}>
                            {
                                [
                                    DeleteAllOptions.deleteAll,
                                    DeleteAllOptions.deleteSolution,
                                ].map((option) => (
                                    <FormControlLabel
                                        key={option}
                                        value={option}
                                        label={translate(
                                            `project.wizardSteps.solutionSelection.change.${option}`,
                                            {solution1},
                                        )}
                                        control={<Radio checked={deleteAllOption === option} />} />
                                ))
                            }
                        </RadioGroup>
                    </>
                )
            }
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={onCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    label={translate('continue')}
                    onClick={() => {
                        onSubmit(deleteAllOption === DeleteAllOptions.deleteAll);
                    }}
                    isValid
                />
            </Stack>
        </Alert>
    );
};

ProjectSolutionChangeForm.propTypes = propTypes;

export default ProjectSolutionChangeForm;
