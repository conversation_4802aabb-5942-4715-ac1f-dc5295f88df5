import {useC<PERSON>back, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
    <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ton,
    LinearProgress,
    Stack, TextField,
    Typography,
} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {
    getRandomString,
    libraryKeys,
} from '@w7-3/webeagle-resources/dist/libs/random';
import {
    RESOURCE_CONTENT_TYPES,
} from '@w7-3/webeagle-resources/dist/config/request';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyString, isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {NOTIFICATIONS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    defaultCode: PropTypes.shape({
        id: PropTypes.string,
        value: PropTypes.string,
    }),
    placeholder: PropTypes.string,
    onChange: PropTypes.func,
    label: PropTypes.oneOfType([PropTypes.node.isRequired, PropTypes.string.isRequired]),
    characterLimit: PropTypes.number,
    onValidate: PropTypes.func,
    textFieldProps: PropTypes.object,
    validateLabel: PropTypes.string,
    readOnly: PropTypes.bool,
};
const defaultProps = {
    defaultCode: {
        id: getRandomString(8, libraryKeys.alphaNumericLowerCased),
        value: '',
    },
    characterLimit: RESOURCE_CONTENT_TYPES.aiText.characterLimit,
};

const CodeTextFieldAI = ({
    defaultCode,
    placeholder,
    onChange,
    label,
    characterLimit,
    onValidate,
    textFieldProps,
    validateLabel,
    readOnly
}) => {
    const {translate} = useLocales();
    const [code, setCode] = useState(defaultCode);
    const [validationData, setValidationData] = useState(null);
    const count = code?.value?.length || 0;
    const total = characterLimit;
    const rest = total - count;
    const percentageCount = (count / total) * 100;
    const validate = useCallback(() => {
        if (!isNonEmptyString(code?.value)) {
            return;
        }

        onValidate({
            value: code?.value,
            successCallback: ({data}) => {
                setValidationData(data?.data);
                const newCode = {
                    ...code,
                    isValid: true,
                    isValidated: true,
                };

                setCode(newCode);
                onChange(newCode);
            },
            failureCallback: ({data}) => {
                setValidationData(data?.data);
                const newCode = {
                    ...code,
                    isValid: false,
                    isValidated: true,
                };

                setCode(newCode);
                onChange(newCode);
                if (!isNonEmptyString(data?.data?.reason)) {
                    return;
                }

                PubSub.publish('SHOW.NOTIFICATION', {
                    message: data?.data?.reason,
                    variant: 'error',
                });
            },
            errorCallback: () => {
                setCode({
                    ...code,
                    isValid: false,
                    isValidated: true,
                });
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate(`directives.notifications.${NOTIFICATIONS.genericErrorWithRetry}`),
                    variant: 'error',
                });
            },
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [code?.value]);

    useEffect(() => {
        if (!isNonEmptyString(validationData?.verdict)) {
            return;
        }

        const variant = {
            yes: 'success',
            unknown: 'warning'
        }[validationData?.verdict] || 'error'
        PubSub.publish('SHOW.NOTIFICATION', {
            message: translate(`codeField.aiText.validation.${validationData?.verdict}`),
            variant,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validationData?.verdict]);

    if (readOnly) {
        return (
            <Stack spacing={3}>
                <Typography variant="body2" sx={{color: 'text.secondary'}}>
                    {label}
                </Typography>
                <TextField
                    fullWidth
                    multiline
                    rows={5}
                    {...textFieldProps}
                    value={code.value}
                    sx={{
                        '& .MuiInputBase-root': {
                            minHeight: '75px',
                        },
                    }}
                    inputProps={{
                        maxLength: characterLimit,
                    }}
                    readOnly
                />
            </Stack>
        );
    }

    return (
        <Stack spacing={3}>
            <LinearProgress variant="determinate" value={percentageCount}/>
            <Stack spacing={1} direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{color: 'text.secondary'}}>
                    {label}
                </Typography>
                <Typography
                    gutterBottom
                    variant="caption"
                    sx={{
                        pt: .25,
                        ...getOptionalMap(rest < 10, {color: 'error.main'}),
                    }}>
                    {translate('codeField.aiText.counterLabel', {rest, total})}
                </Typography>
            </Stack>
            <TextField
                fullWidth
                multiline
                rows={5}
                {...textFieldProps}
                value={code.value}
                placeholder={
                    placeholder ||
                    translate('codeField.aiText.placeholder')
                }
                onChange={(event) => {
                    const newCode = {
                        value: event.target.value,
                        isValid: false,
                        isValidated: false,
                    };

                    setCode(newCode);
                    onChange(newCode);
                    setValidationData(null);
                }}
                sx={{
                    '& .MuiInputBase-root': {
                        minHeight: '75px',
                    },
                }}
                inputProps={{
                    maxLength: characterLimit,
                }}
            />
            {
                !code.isValid && isNonEmptyArray(validationData?.impossibleActions) && (
                    <Alert severity="warning">
                        <AlertTitle>{translate('codeField.aiText.validation.impossibleActions')}</AlertTitle>
                        {validationData?.impossibleActions.map((item, index) => (
                            <Typography key={index}>
                                {item}
                            </Typography>
                        ))}
                    </Alert>
                )
            }
            {
                isNonEmptyArray(validationData?.suggestions) && (
                    <Stack spacing={1}>
                        <Typography sx={{pb: 2}}>
                            {translate('codeField.aiText.validation.suggestions')}
                        </Typography>
                        {
                            validationData?.suggestions?.map((suggestion, index) => (
                                <Button
                                    key={index}
                                    variant="outlined"
                                    onClick={() => {
                                        const newCode = {
                                            ...code,
                                            value: suggestion,
                                            isValid: true,
                                            isValidated: true,
                                            isSuggested: true,
                                            suggestionIndex: index,
                                        };

                                        setCode(newCode);
                                        onChange(newCode);
                                    }}
                                    sx={{
                                        justifyContent: 'flex-start',
                                        textTransform: 'none',
                                    }}
                                >
                                    {suggestion}
                                </Button>
                            ))
                        }
                    </Stack>
                )
            }
            {
                !code?.isValidated && !code?.isValid &&
                <Typography
                    sx={{color: 'error.main'}}>
                    {translate('validateAiText.isRequiredForSave')}
                </Typography>
            }
            <Button
                variant="contained"
                onClick={validate}
                type="button"
                disabled={!isNonEmptyString(code?.value) || (code?.isValid && code?.isValidated)}
                sx={{
                    textTransform: 'none',
                }}
            >
                {validateLabel || translate('validateAiText.label')}
            </Button>
        </Stack>
    );
};

CodeTextFieldAI.propTypes = propTypes;
CodeTextFieldAI.defaultProps = defaultProps;

export default CodeTextFieldAI;
