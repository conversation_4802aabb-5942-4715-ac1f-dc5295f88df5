import * as Yup from 'yup';
import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import equals from 'ramda/src/equals';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {FormControlLabel, Grid, MenuItem, Stack, Switch, TextField, Typography} from '@material-ui/core';
import {COMPARATOR_OPTIONS, DEFAULT_CONFIG} from '@w7-3/webeagle-resources/dist/config/cookies';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import {getDomainSchema, getParameterKeySchema, getSimpleUrlSchema} from '../../utils/formSchema';

const sameSiteItems = [
    'Strict',
    'Lax',
    'None',
];

const propTypes = {
    values: PropTypes.shape({
        isValid: PropTypes.bool,
        maxAgeType: PropTypes.string,
        data: PropTypes.shape({
            name: PropTypes.string,
            value: PropTypes.string,
            domain: PropTypes.string,
            path: PropTypes.string,
            maxAge: PropTypes.number,
            httpOnly: PropTypes.bool,
            secure: PropTypes.bool,
            sameSite: PropTypes.oneOf(sameSiteItems),
        }),
    }),
    isCompare: PropTypes.bool,
    isEmptyValueAllowed: PropTypes.bool,
    onChange: PropTypes.func.isRequired,
    buttons: PropTypes.node,
};

const i18nContext = 'cookies';

const  CookieForm = ({values, isCompare, isEmptyValueAllowed, onChange, buttons}) => {
    const {translate} = useLocales();
    const [showExtendedFields, setShowExtendedFields] = useState(!isCompare);
    const Schema = Yup.object().shape({
        data: Yup.object().shape({
            name: getParameterKeySchema({
                mandatoryField: translate('mandatoryField', {field: translate('name')}),
                invalidField: translate('invalidField', {field: translate('name')}),
            }),
            ...getOptionalMap(!isEmptyValueAllowed, {
                value: Yup
                    .string()
                    .required(translate('mandatoryField', {field: translate('value')})),
            }),
            domain: getDomainSchema({invalidField: translate('form.validation.domain')}),
            path: getSimpleUrlSchema({translate}, {field: translate('path')}),
            maxAge: Yup.number().required(translate('form.validation.min', {min: 0})),
        }),
    });
    const {
        watch,
        control,
        trigger,
        formState: {
            isValid,
        },
    } = useForm({
        mode: 'onChange',
        resolver: yupResolver(Schema),
        defaultValues: {
            maxAgeType: COMPARATOR_OPTIONS.greaterThan,
            ...values,
            data: {
                ...DEFAULT_CONFIG,
                name: '',
                value: '',
                ...values?.data,
            },
        },
    });
    const newValues = watch();

    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (!newValues || equals(newValues.data, values?.data)) {
            return;
        }

        onChange({isValid, values: newValues});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [newValues]);

    return (
        <Grid container
              spacing={3}
              sx={{
                  borderRadius: 1,
                  width: '100%',
                  pr: 3,
                  pb: 3,
                  position: 'relative',
                  border: (theme) => `1px solid ${theme.palette.grey[500_32]}`,
              }}>
            <Grid item xs={12} md={6}>
                <Controller
                    name="data.name"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <>
                                <TextField
                                    fullWidth
                                    {...field}
                                    label={translate(`${i18nContext}.fields.name`)}
                                />
                                {Boolean(error?.message) && (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                )}
                            </>
                        );
                    }}
                />
            </Grid>
            <Grid item xs={12} md={6}>
                <Controller
                    name="data.value"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => {
                        return (
                            <>
                                <TextField
                                    fullWidth
                                    {...field}
                                    label={translate(`${i18nContext}.fields.value`)}
                                />
                                {Boolean(error?.message) && (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                )}
                            </>
                        );
                    }}
                />
            </Grid>
            {
                showExtendedFields && (
                    <>
                        <Grid item xs={12} md={6}>
                            <Controller
                                name="data.domain"
                                control={control}
                                render={({
                                    field,
                                    fieldState: {error},
                                }) => {
                                    return (
                                        <Stack spacing={1}>
                                            <TextField
                                                fullWidth
                                                {...field}
                                                label={translate(`${i18nContext}.fields.domain`)}
                                            />
                                            {Boolean(error?.message) && (
                                                <Typography
                                                    sx={{color: 'error.main'}}>
                                                    {error?.message}
                                                </Typography>
                                            )}
                                        </Stack>
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Controller
                                name="data.path"
                                control={control}
                                render={({
                                    field,
                                    fieldState: {error},
                                }) => {
                                    return (
                                        <Stack spacing={1}>
                                            <TextField
                                                fullWidth
                                                {...field}
                                                label={translate(`${i18nContext}.fields.path`)}
                                            />
                                            {Boolean(error?.message) && (
                                                <Typography
                                                    sx={{color: 'error.main'}}>
                                                    {error?.message}
                                                </Typography>
                                            )}
                                        </Stack>
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Controller
                                name="data.maxAge"
                                control={control}
                                render={({
                                    field,
                                    fieldState: {error},
                                }) => {
                                    return (
                                        <Stack spacing={1}>
                                            <TextField
                                                fullWidth
                                                {...field}
                                                sx={{
                                                    '& select': {
                                                        px: 1,
                                                        py: 0.5,
                                                        border: 'none',
                                                        height: '100%',
                                                        typography: 'subtitle2',
                                                        backgroundColor: 'background.neutral',
                                                        borderRadius: 0.75,
                                                    },
                                                    '& input': {
                                                        pl: 1,
                                                    },
                                                }}
                                                InputProps={{
                                                    inputProps: {
                                                        min: 0,
                                                    },
                                                    ...getOptionalMap(isCompare, {
                                                        startAdornment: (
                                                            <Controller
                                                                name="maxAgeType"
                                                                control={control}
                                                                render={({
                                                                    field,
                                                                }) => {
                                                                    return (
                                                                        <select
                                                                            {...field}>
                                                                            {Object.values(COMPARATOR_OPTIONS).map((option) => (
                                                                                <option key={option} value={option}>
                                                                                    {option}
                                                                                </option>
                                                                            ))}
                                                                        </select>
                                                                    );
                                                                }}
                                                            />
                                                        ),
                                                    }),
                                                }}
                                                type="number"
                                                label={translate(`${i18nContext}.fields.maxAge`)}
                                            />
                                            {Boolean(error?.message) && (
                                                <Typography
                                                    sx={{color: 'error.main'}}>
                                                    {error?.message}
                                                </Typography>
                                            )}
                                        </Stack>
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Controller
                                name="data.httpOnly"
                                control={control}
                                render={({
                                    field,
                                }) => {
                                    return (
                                        <FormControlLabel
                                            control={
                                                <Switch
                                                    {...field}
                                                    checked={field.value}/>
                                            }
                                            label={translate(`${i18nContext}.fields.httpOnly`)}
                                            labelPlacement="end"
                                        />
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Controller
                                name="data.secure"
                                control={control}
                                render={({
                                    field,
                                }) => {
                                    return (
                                        <FormControlLabel
                                            control={
                                                <Switch
                                                    {...field}
                                                    checked={field.value}/>
                                            }
                                            label={translate(`${i18nContext}.fields.secure`)}
                                            labelPlacement="end"
                                        />
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Controller
                                name="data.sameSite"
                                control={control}
                                render={({
                                    field,
                                }) => {
                                    return (
                                        <TextField
                                            select
                                            fullWidth
                                            {...field}
                                            variant="standard"
                                            label={translate(`${i18nContext}.fields.sameSite`)}>
                                            {[
                                                'Strict',
                                                'Lax',
                                                'None',
                                            ].map((option) => (
                                                <MenuItem key={option} value={option}>
                                                    {option}
                                                </MenuItem>
                                            ))}
                                        </TextField>
                                    );
                                }}
                            />
                        </Grid>
                    </>
                )
            }
            {
                isCompare && (
                    <Grid item xs={12}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={showExtendedFields}
                                    onChange={() => setShowExtendedFields(!showExtendedFields)}/>
                            }
                            label={translate(`${i18nContext}.comparing.showExtendedFields`)}
                            labelPlacement="end"
                        />
                    </Grid>
                )
            }
            <Grid item xs={12}>
                {buttons}
            </Grid>
        </Grid>
    );
};

CookieForm.propTypes = propTypes;

export default CookieForm;
