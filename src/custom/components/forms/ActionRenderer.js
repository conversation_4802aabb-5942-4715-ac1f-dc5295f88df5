import * as Yup from 'yup';
import {useEffect, useState} from 'react';
import PubSub from 'pubsub-js';
import {yupResolver} from '@hookform/resolvers/yup';
import {Controller, useForm} from 'react-hook-form';
import {connect} from 'react-redux';
import {Alert, Box, Button, FormControlLabel, Stack, Switch} from '@material-ui/core';
import {actions} from '@w7-3/webeagle-resources/dist/config/actions';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';
import {getLabellingValidation, getSimpleQuerySelectorValidation} from '../../utils/formSchema';
import SelectorTextField from './SelectorTextField';
import ActionabilityNotification from '../services/actions/utils/ActionabilityNotification';
import {ItemRendererPropTypes} from '../../../config/prop-types/ProjectSteps';
import ItemLabellingStrategy from '../utils/ItemLabellingStrategy';
import MouseInteraction from '../services/actions/MouseInteraction';
import MouseElementHoverInteraction from '../services/actions/MouseElementHoverInteraction';
import MouseElementClickInteraction from '../services/actions/MouseElementClickInteraction';
import ElementDragAndDropInteraction from '../services/actions/ElementDragAndDropInteraction';
import KeyboardAction from '../services/actions/KeyboardAction';
import TouchScreenInteraction from '../services/actions/TouchScreenInteraction';
import TouchScreenElementTapInteraction from '../services/actions/TouchScreenElementTapInteraction';
import ElementFillInputOrTextareaInteraction from '../services/actions/ElementFillInputOrTextareaInteraction';
import ElementSelectOptionInteraction from '../services/actions/ElementSelectOptionInteraction';
import ElementFocusInteraction from '../services/actions/ElementFocusInteraction';
import ElementScrollIntoViewInteraction from '../services/actions/ElementScrollIntoViewInteraction';
import ElementUploadFileInteraction from '../services/actions/ElementUploadFileInteraction';
import WaitingForElementInteraction from '../services/actions/WaitingForElementInteraction';
import WaitingForBooleanAction from '../services/actions/WaitingForBooleanAction';
import WaitingForNetworkRequestAction from '../services/actions/WaitingForNetworkRequestAction';
import WaitingForNetworkResponseAction from '../services/actions/WaitingForNetworkResponseAction';
import WaitingForTimeoutAction from '../services/actions/WaitingForTimeoutAction';
import CustomReloadAction from '../services/actions/CustomReloadAction';
import CustomGoBack from '../services/actions/CustomGoBack';
import CustomGoForward from '../services/actions/CustomGoForward';
import CustomGoTo from '../services/actions/CustomGoTo';
import CustomScroll from '../services/actions/CustomScroll';
import CustomJSAction from '../services/actions/CustomJSAction';
import CustomClearAllCookies from '../services/actions/CustomClearAllCookies';
import CustomClearStorage from '../services/actions/CustomClearStorage';
import SettingsDialogHandling from '../services/actions/SettingsDialogHandling';
import ProjectStepStateForm from './ProjectStepStateForm';

const renderers = {
    [actions.booleanFunction.value]: WaitingForBooleanAction,
    [actions.clearAllCookies.value]: CustomClearAllCookies,
    [actions.clearLocalStorage.value]: CustomClearStorage,
    [actions.clearSessionStorage.value]: CustomClearStorage,
    [actions.dialogHandling.value]: SettingsDialogHandling,
    [actions.elementClick.value]: MouseElementClickInteraction,
    [actions.elementDragAndDrop.value]: ElementDragAndDropInteraction,
    [actions.elementFocus.value]: ElementFocusInteraction,
    [actions.elementHover.value]: MouseElementHoverInteraction,
    [actions.elementTap.value]: TouchScreenElementTapInteraction,
    [actions.fillInputOrTextarea.value]: ElementFillInputOrTextareaInteraction,
    [actions.goBack.value]: CustomGoBack,
    [actions.goForward.value]: CustomGoForward,
    [actions.goTo.value]: CustomGoTo,
    [actions.js.value]: CustomJSAction,
    [actions.keyboardPress.value]: KeyboardAction,
    [actions.mouseInteraction.value]: MouseInteraction,
    [actions.reloadPage.value]: CustomReloadAction,
    [actions.request.value]: WaitingForNetworkRequestAction,
    [actions.response.value]: WaitingForNetworkResponseAction,
    [actions.scroll.value]: CustomScroll,
    [actions.scrollIntoView.value]: ElementScrollIntoViewInteraction,
    [actions.selectOption.value]: ElementSelectOptionInteraction,
    [actions.tap.value]: TouchScreenInteraction,
    [actions.timeout.value]: WaitingForTimeoutAction,
    [actions.uploadFile.value]: ElementUploadFileInteraction,
    [actions.waitForSelector.value]: WaitingForElementInteraction,
};

const actionabilityNotificationList = [
    actions.elementHover.value,
    actions.elementClick.value,
    actions.elementFocus.value,
    actions.elementTap.value,
    actions.selectOption.value,
    actions.fillInputOrTextarea.value,
    actions.scrollIntoView.value,
    actions.waitForSelector.value,
];

const actionListWithSelector = [
    actions.elementHover.value,
    actions.elementClick.value,
    actions.elementFocus.value,
    actions.elementTap.value,
    actions.selectOption.value,
    actions.fillInputOrTextarea.value,
    actions.scrollIntoView.value,
    actions.waitForSelector.value,
    actions.uploadFile.value,
];

const mapStateToProps = ({projectAssistant}) => {
    const {
        target,
    } = projectAssistant.configData;

    return {
        isMobile: target?.device?.viewport?.isMobile,
    };
};

const i18nContextRoot = 'actions';
const defaultProps = {
    showAbortOnError: true,
    showIgnoreOnError: false,
};

const ActionRenderer = (props) => {
    const {
        item,
        labelBlacklist,
        handleSave,
        handleCancel,
        labels,
        showAbortOnError,
        showIgnoreOnError,
        changeItemType,
    } = props;
    const Renderer = renderers[item?.type];
    const {translate} = useLocales();
    const [isInactive, setIsInactive] = useState(item?.isInactive);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            ...getLabellingValidation({
                label: {
                    error: translate('itemLabelling.mandatoryField'),
                    duplicate: translate('itemLabelling.duplicateGroupName'),
                    allowEmpty: false,
                },
                labelScript: {
                    error: translate('form.validation.script'),
                },
                labelBlacklist,
            }),
            selector: getSimpleQuerySelectorValidation(
                translate('selector.invalid'),
                !actionListWithSelector.includes(item?.type)),
        })),
        defaultValues: item,
    });
    const {
        watch,
        control,
        formState,
    } = formContext;

    useEffect(() => {
        if (Renderer) {
            return;
        }

        PubSub.publish(
            'LOG.EVENT',
            {
                uiData: {
                    type: INFO_CODE_LEVELS.ERROR,
                    message: `Cannot find Renderer for ACTION: ${item?.type}`,
                    data: {
                        item,
                    }
                },
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [Renderer]);

    if (!Renderer) {
        return (
            <Stack spacing={3}>
                <Alert severity="error">
                    {translate('actions.unavailable')}
                </Alert>
                <Box sx={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center'}}>
                    <Button color="inherit" onClick={handleCancel}>
                        {translate('cancel')}
                    </Button>
                </Box>
            </Stack>
        );
    }

    const renderIgnoreOnError = !showAbortOnError && showIgnoreOnError && Boolean(labels.ignoreActionError);
    const renderWillIgnoreOnError = !showAbortOnError && !renderIgnoreOnError && Boolean(labels.willIgnoreActionError);

    return (
        <>
            <ProjectStepStateForm
                isInactive={isInactive}
                setIsInactive={setIsInactive}
            />
            {
                actionabilityNotificationList.includes(item?.type) &&
                <ActionabilityNotification
                    i18nContextRoot={i18nContextRoot}
                    changeItemType={changeItemType}
                />
            }
            <Renderer
                {...props}
                action={actions[item?.type]}
                parentFormContext={formContext}
                i18nContextRoot={i18nContextRoot}
                handleSave={({value}) => {
                    const formData = watch();

                    handleSave({
                        ...item,
                        ...formData,
                        value,
                        isInactive,
                    });
                }}
                selectorNode={(
                    <SelectorTextField
                        control={control}
                        formState={formState}
                        name="selector"
                        showInitialError
                        isSingleSelector
                        hideReadMore
                    />
                )}
                labelNode={(
                    <ItemLabellingStrategy
                        formContext={formContext}
                    />
                )}
                abortOnErrorNode={showAbortOnError && (
                    <Controller
                        name="abortOnError"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={labels.abortion}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                )}
                ignoreOnErrorNode={renderIgnoreOnError && (
                    <Controller
                        name="ignoreOnError"
                        control={control}
                        render={({field}) => {
                            return (
                                <FormControlLabel
                                    label={labels.ignoreActionError}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                            );
                        }}
                    />
                )}
                willIgnoreOnErrorNode={(renderWillIgnoreOnError && (
                    <Alert severity="info">
                        {labels.willIgnoreActionError}
                    </Alert>
                ))}
            />
        </>
    );
};

ActionRenderer.propTypes = ItemRendererPropTypes;
ActionRenderer.defaultProps = defaultProps;

export default connect(mapStateToProps)(ActionRenderer);
