import {<PERSON><PERSON>, FormControlLabel, IconButton, InputAdornment, Stack, Switch, Typography} from '@material-ui/core';
import isEmpty from 'ramda/src/isEmpty';
import * as Yup from 'yup';
import PropTypes from 'prop-types';
import {Controller, useFieldArray, useForm} from 'react-hook-form';
import {useEffect, useRef, useState} from 'react';
import {Icon} from '@iconify/react';
import {yupResolver} from '@hookform/resolvers/yup';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import useLocales from '../../../hooks/useLocales';
import SubmitButton from '../utils/SubmitButton';
import {getFullUrlSchema, getURLAuthorizationValidation} from '../../utils/formSchema';
import TextFieldStyle from '../utils/TextFieldStyle';
import SettingItemChange from '../utils/SettingItemChange';
import SettingItemDelete from '../utils/SettingItemDelete';
import DeleteButton from '../utils/DeleteButton';
import NameValueForm from './NameValueForm';
import Drawer from './Drawer';

const propTypes = {
    title: PropTypes.node,
    handleCancel: PropTypes.func,
    handleSave: PropTypes.func,
    onEdit: PropTypes.func,
    onDelete: PropTypes.func,
    defaultValues: PropTypes.object,
    urls: PropTypes.array,
    disableUrlField: PropTypes.bool,
    isPreview: PropTypes.bool,
    iconOnly: PropTypes.bool,
};

const i18nContext = 'urlCapture.externals';
const getHeaderEntry = () => ({
    id: Date.now(),
    value: '',
    name: '',
});

const URLCaptureDialogForm = ({
    title,
    handleCancel,
    handleSave,
    onEdit,
    onDelete,
    defaultValues,
    urls,
    disableUrlField,
    isPreview,
    iconOnly,
}) => {
    const inputRef = useRef(null);
    const blackList = isNonEmptyArray(urls) ? urls.map(({url}) => url).filter((item) => item !== defaultValues?.url) : [];
    const {translate} = useLocales();
    const [headerErrors, setHeaderErrors] = useState({});
    const [showPassword, setShowPassword] = useState(false);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            url: getFullUrlSchema({translate, blackList}),
            auth: getURLAuthorizationValidation({
                userNameInvalid: translate('form.validation.usernameInvalid'),
                passwordInvalid: translate('form.validation.passwordRequired'),
            }),
        })),
        defaultValues: {
            url: defaultValues?.url || 'https://',
            headers: {
                active: defaultValues?.headers?.active || false,
                items: defaultValues?.headers?.items || [
                    getHeaderEntry(),
                ],
            },
            auth: {
                active: defaultValues?.auth?.active || false,
                username: defaultValues?.auth?.username || '',
                password: defaultValues?.auth?.password || '',
            },
        },
    });

    const {
        watch,
        trigger,
        control,
        formState: {
            errors,
        },
    } = formContext;
    const {fields, remove, append} = useFieldArray({
        control,
        name: 'headers.items',
    });

    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (isPreview) {
        return (
            <Stack direction={{xs: 'column', md: 'row'}} alignItems="center" flexWrap="wrap" spacing={1}>
                <Typography variant="body2" sx={{
                    wordBreak: 'break-all',
                }}>
                    {defaultValues?.url}
                    {
                        defaultValues?.auth?.active && (
                            <Typography variant="caption" sx={{display: 'block'}}>
                                ({defaultValues?.auth?.username}@******)
                            </Typography>
                        )
                    }
                </Typography>
                {
                    onEdit && (
                        <SettingItemChange
                            onClick={onEdit}
                            iconOnly={iconOnly}
                        />
                    )
                }
                {
                    onDelete && (
                        <SettingItemDelete
                            onClick={onDelete}
                            iconOnly={iconOnly}
                        />
                    )
                }
            </Stack>
        );
    }

    const content = watch();
    const hasHeaderErrors = content?.headers?.active && (
        isEmpty(headerErrors) || Object.values(headerErrors).some((item) => item));

    return (
        <Drawer
            open
            onClose={handleCancel}
            title={title}
        >
            <Stack
                direction="column"
                spacing={3}>
                <Controller
                    name="url"
                    control={control}
                    render={({
                        field,
                        fieldState: {error},
                    }) => (
                        <Stack spacing={1}>
                            <TextFieldStyle
                                {...field}
                                inputRef={inputRef}
                                disabled={disableUrlField}
                                label={translate(`${i18nContext}.url`)}
                                type="url"
                                {...getOptionalMap(isNonEmptyString(field.value), {
                                    InputProps: {
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() => {
                                                        field.onChange('');
                                                        queueCallback(() => {
                                                            inputRef.current?.focus();
                                                        });
                                                    }}
                                                    edge="end"
                                                    color="primary">
                                                    <Icon icon="eva:close-outline"/>
                                                </IconButton>
                                            </InputAdornment>
                                        ),
                                    },
                                })}
                                sx={{
                                    width: '100%',
                                    '> .MuiInputAdornment-root': {}
                                }}
                                fullWidth
                            />
                            {Boolean(error?.message) && (
                                <Typography
                                    sx={{color: 'error.main'}}>
                                    {error?.message}
                                </Typography>
                            )}
                        </Stack>
                    )}
                />
                <Controller
                    name="auth.active"
                    control={control}
                    render={({field}) => {
                        return (
                            <>
                                <FormControlLabel
                                    label={translate(`${i18nContext}.authorization.label`)}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                field.onChange(value);
                                                trigger();
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                                {
                                    field.value && (
                                        [
                                            'username',
                                            'password',
                                        ].map((key) => (
                                            <Controller
                                                key={key}
                                                name={`auth.${key}`}
                                                control={control}
                                                render={({
                                                    field: authField,
                                                    fieldState: {error: authError},
                                                }) => {
                                                    return (
                                                        <Stack spacing={1}>
                                                            <TextFieldStyle
                                                                {...authField}
                                                                type={key === 'password' && !showPassword ? 'password' : 'text'}
                                                                label={translate(`${i18nContext}.authorization.options.${key}`)}
                                                                {...getOptionalMap(key === 'password', {
                                                                    InputProps: {
                                                                        endAdornment: (
                                                                            <InputAdornment position="end">
                                                                                <IconButton
                                                                                    onClick={() => setShowPassword(!showPassword)}
                                                                                    edge="end"
                                                                                    color="primary"
                                                                                >
                                                                                    <Icon
                                                                                        icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'}/>
                                                                                </IconButton>
                                                                            </InputAdornment>
                                                                        ),
                                                                    },
                                                                })}
                                                            />
                                                            {Boolean(authError) && (
                                                                <Typography
                                                                    sx={{color: 'error.main'}}>
                                                                    {authError?.message}
                                                                </Typography>
                                                            )}
                                                        </Stack>
                                                    );
                                                }}
                                            />
                                        ))
                                    )
                                }
                            </>
                        );
                    }}
                />
                <Controller
                    name="headers.active"
                    control={control}
                    render={({field}) => {
                        return (
                            <>
                                <FormControlLabel
                                    label={translate(`${i18nContext}.headers.label`)}
                                    control={
                                        <Switch
                                            onChange={(_, value) => {
                                                trigger();
                                                field.onChange(value);
                                            }}
                                            checked={field.value}
                                        />
                                    }
                                />
                                {
                                    field.value && (
                                        <>
                                            {fields.map((item, index) => {
                                                return (
                                                    <Controller
                                                        key={item.id}
                                                        name={`headers.items.${index}`}
                                                        control={control}
                                                        render={({field: headerField}) => {
                                                            return (
                                                                <NameValueForm
                                                                    values={headerField.value}
                                                                    buttons={(
                                                                        index > 0 && (
                                                                            <DeleteButton
                                                                                onClick={() => {
                                                                                    remove(index);
                                                                                    const newHeaderErrors = {...headerErrors};
                                                                                    delete newHeaderErrors[headerField.name];

                                                                                    setHeaderErrors(newHeaderErrors);
                                                                                }}
                                                                            />
                                                                        )
                                                                    )}
                                                                    onChange={({
                                                                        isValid,
                                                                        values,
                                                                    }) => {
                                                                        headerField.onChange(values);
                                                                        setHeaderErrors({
                                                                            ...headerErrors,
                                                                            [headerField.name]: !isValid,
                                                                        });
                                                                    }}
                                                                />
                                                            );
                                                        }}
                                                    />
                                                );
                                            })}
                                            <Button
                                                variant="outlined"
                                                size="large"
                                                startIcon={<Icon icon="eva:plus-fill" width={40} height={40}/>}
                                                onClick={() => {
                                                    append(getHeaderEntry());
                                                    setHeaderErrors({
                                                        ...headerErrors,
                                                        [`headers.items.${fields.length}`]: true,
                                                    });
                                                }}>
                                                {translate('entry')}
                                            </Button>
                                        </>
                                    )
                                }
                            </>
                        );
                    }}
                />
            </Stack>
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave(content);
                    }}
                    isValid={isEmpty(errors) && !hasHeaderErrors}
                />
            </Stack>
        </Drawer>
    );
};

URLCaptureDialogForm.propTypes = propTypes;

export default URLCaptureDialogForm;
