import {useTheme} from '@material-ui/core/styles';
import {Icon} from '@iconify/react';
import {snakeCase} from 'change-case';
import {Link as RouterLink} from 'react-router-dom';
import PubSub from 'pubsub-js';
import {Button, Stack, Typography, useMediaQuery} from '@material-ui/core';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {globalConfig} from '../../../config/setup';
import useLocales from '../../../hooks/useLocales';

const availableSolutions = Object.values(solutions).filter(({configurable}) => configurable);

const OverviewTeaser = () => {
    const {translate} = useLocales();
    const theme = useTheme();
    const deviceIsSM = useMediaQuery(theme.breakpoints.down('lg'));
    const allFeaturesContent = availableSolutions.map((solution) => {
        const {appName} = solution;
        return (
            <Button
                key={appName}
                variant="text"
                to={`${PATH_PAGE.features}/${snakeCase(appName)}`}
                component={RouterLink}
                startIcon={<Icon icon="eva:chevron-right-outline" width={40} height={40}/>}
                sx={{
                    color: 'common.white',
                    fontSize: '1.25em',
                }}
            >
                {translate(`project.wizardSteps.solutionsConfigs.groups.${appName}.label`)}
            </Button>
        );
    });

    if (deviceIsSM) {
        return (
            <Button
                variant="outlined"
                onClick={() => {
                    PubSub.publish('SHOW.DIALOG', {
                        type: 'custom',
                        dialogProps: {
                            title: (
                                <Typography
                                    variant="subtitle1"
                                    sx={{
                                        wordBreak: 'break-word',
                                        wordWrap: 'break-word',
                                    }}>
                                    {translate('solutionsLabel', {
                                        app: globalConfig.domain,
                                    })}
                                </Typography>
                            ),
                        },
                        children: (
                            <Stack
                                spacing={2}
                                alignItems="flex-start"
                                sx={{mt: 3}}
                            >
                                {allFeaturesContent}
                            </Stack>
                        ),
                    });
                }}
                sx={{
                    mt: (theme) => `${theme.spacing(6)} !important;`,
                }}
            >
                {translate('solutionsLabel', {
                    app: globalConfig.domain,
                })}
            </Button>
        );
    }

    return (
        <Stack
            sx={{
                height: 'fit-content',
                minWidth: '33%',
                mt: -6,
                ml: 9,
                p: 3,
                boxShadow: (theme) => theme.customShadows.z24,
                borderRadius: 1,
            }}
            justifyContent="space-between"
            alignItems="flex-start"
            spacing={3}
        >
            <Typography
                component="h5"
                variant="h5"
                sx={{
                    color: 'primary.main',
                    lineHeight: '150%',
                    fontSize: '2rem !important',
                    textDecoration: 'underline',
                }}
                align="center"
            >
                {translate('solutionsLabel', {
                    app: globalConfig.domain,
                })}
            </Typography>
            <Stack
                spacing={1}
                alignItems="flex-start"
            >
                {allFeaturesContent}
            </Stack>
        </Stack>
    );
};

export default OverviewTeaser;
