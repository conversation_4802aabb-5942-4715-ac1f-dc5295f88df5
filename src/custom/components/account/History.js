import PubSub from 'pubsub-js';
import {useEffect} from 'react';
import {connect, useDispatch} from 'react-redux';
import PropTypes from 'prop-types';
import {Card, Typography} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import {setAccountEventList} from '../../../redux/slices/state';
import useLocales from '../../../hooks/useLocales';
import EventsTimeline from '../utils/EventsTimeline';

const mapStateToProps = ({state}) => {
    const {
        accountEventList,
    } = state;

    return {
        accountEventList,
    };
};

const propTypes = {
    accountEventList: PropTypes.array,
};

const History = ({accountEventList}) => {
    const apiCaller = useApiCaller();
    const dispatch = useDispatch();
    const {translate} = useLocales();

    useEffect(() => {
        if (isNonEmptyArray(accountEventList)) {
            return;
        }

        apiCaller({
            uri: getAPIPath(api.accountEvents),
            successCallback: ({data}) => {
                if (!isNonEmptyArray(data.accountEventList)) {
                    return;
                }

                data.accountEventList.sort((a, b) => {
                    return b.timestamp - a.timestamp;
                });

                dispatch(setAccountEventList(data.accountEventList))
            },
        });
        // eslint-disable-next-line
    }, [accountEventList]);

    useEffect(() => {
        if (!accountEventList) {
            PubSub.publish('SHOW.BUSY');
            return;
        }

        PubSub.publish('HIDE.BUSY');
    }, [accountEventList]);

    if (!accountEventList) {
        return null;
    }

    return (
        <Card sx={{p: 3}}>
            <Typography variant="subtitle1" sx={{my: 3}}>
                {translate('account.history.projectEvents', {count: accountEventList.length})}</Typography>
            <EventsTimeline
                eventList={accountEventList}
            />
        </Card>
    );
};

History.propTypes = propTypes;

export default connect(mapStateToProps)(History);
