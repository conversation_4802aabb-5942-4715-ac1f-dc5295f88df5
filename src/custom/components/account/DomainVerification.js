import PropTypes from 'prop-types';
import {
    Al<PERSON>,
    <PERSON>ton,
    FormControlLabel,
    Radio,
    RadioGroup,
    Stack,
    TextField,
    Typography,
} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {
    VERIFICATION_ACTION,
    VERIFICATION_TYPE,
    VERIFICATION_SNIPPETS,
} from '@w7-3/webeagle-resources/dist/config/domains';
import {useEffect} from 'react';
import {yupResolver} from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
    isNonEmptyString,
    isNonEmptyObject,
} from '@w7-3/webeagle-resources/dist/libs/validators';
import {connect} from 'react-redux';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import {Icon} from '@iconify/react';
import useLocales from '../../../hooks/useLocales';
import {getDomainSchema} from '../../utils/formSchema';
import LabelStyle from '../utils/LabelStyle';
import SubmitButton from '../utils/SubmitButton';
import CodeTextFieldHTML from '../forms/CodeTextFieldHTML';
import {globalConfig} from '../../../config/setup';

const propTypes = {
    url: PropTypes.string,
    method: PropTypes.string,
    accountId: PropTypes.string,
    handleSave: PropTypes.func,
    handleClose: PropTypes.func,
};

const defaultProps = {
    url: '',
}

const mapStateToProps = ({state}) => {
    const {
        accountId,
    } = state;

    return {
        accountId,
    };
};

const DomainVerification = ({url, method, accountId, handleSave, handleClose}) => {
    const {translate} = useLocales();
    const {
        watch,
        control,
        formState: {errors},
        trigger,
    } = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            url: getDomainSchema({invalidField: translate('form.validation.url'), isRequired: true}),
        })),
        defaultValues: {
            url,
            method: method || VERIFICATION_TYPE.COLLABORATOR,
        },
    });

    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);


    return (
        <Stack spacing={3}>
            <Controller
                name="url"
                control={control}
                render={({
                    field,
                    fieldState: {error},
                }) => (
                    <Stack spacing={1}>
                        <LabelStyle>{translate('account.domains.verification.domainToVerify')}</LabelStyle>
                        <TextField
                            {...field}
                            fullWidth
                            type="url"
                        />
                        {Boolean(error?.message) && (
                            <Typography
                                sx={{color: 'error.main'}}>
                                {error?.message}
                            </Typography>
                        )}
                    </Stack>
                )}
            />
            {
                !errors?.url && (
                    <>
                        <Controller
                            name="method"
                            control={control}
                            render={({
                                field,
                            }) => {
                                const snippet = VERIFICATION_SNIPPETS[field.value]?.snippet;
                                const value = snippet?.replace('{{code}}', accountId);

                                return (
                                    <Stack spacing={3}>
                                        <LabelStyle>{translate('account.domains.verification.methods.label')}</LabelStyle>
                                        <RadioGroup
                                            row
                                            {...field}
                                        >
                                            {
                                                Object.values(VERIFICATION_TYPE).map((method) => (
                                                    <FormControlLabel
                                                        key={method}
                                                        value={method}
                                                        control={<Radio checked={field.value === method} />}
                                                        label={translate(`account.domains.verification.methods.options.${method}.label`)}
                                                    />
                                                ))
                                            }
                                        </RadioGroup>
                                        <Alert severity="info">
                                            {translate(`account.domains.verification.methods.options.${field.value}.description`, {
                                                app: globalConfig.domain,
                                            })}
                                        </Alert>
                                        {
                                            isNonEmptyString(snippet) && (
                                                <>
                                                    <Stack
                                                        direction="row"
                                                        alignItems="center"
                                                        justifyContent="flex-end">
                                                        <CopyToClipboard text={value}>
                                                            <Button
                                                                variant="text"
                                                                startIcon={<Icon icon="eva:copy-fill" width={40} height={40}/>}
                                                                sx={{
                                                                    mt: 3,
                                                                    display: 'flex',
                                                                    textTransform: 'none',
                                                                }}
                                                            >
                                                                {translate(`account.domains.verification.methods.options.${field.value}.copy`)}
                                                            </Button>
                                                        </CopyToClipboard>
                                                    </Stack>
                                                    <CodeTextFieldHTML
                                                        script={{
                                                            value,
                                                        }}
                                                        label={translate('account.domains.verification.methods.options.HTML.label')}
                                                        readOnly
                                                    />
                                                </>
                                            )
                                        }
                                    </Stack>
                                );
                            }}
                        />
                        <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                            <Button
                                color="inherit"
                                onClick={handleClose}
                                variant="text"
                                sx={{textTransform: 'none'}}>
                                {translate('cancel')}
                            </Button>
                            <SubmitButton
                                onClick={() => {
                                    handleSave(watch());
                                }}
                                isValid={!isNonEmptyObject(errors)}
                                label={translate(`account.domains.verification.${VERIFICATION_ACTION.VERIFY}`)}
                            />
                        </Stack>
                    </>
                )
            }
        </Stack>
    );
};

DomainVerification.propTypes = propTypes;
DomainVerification.defaultProps = defaultProps;

export default connect(mapStateToProps)(DomainVerification);
