import {connect} from 'react-redux';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Card,
    Divider,
    FormControlLabel,
    Radio,
    RadioGroup, Skeleton,
    Stack,
    Typography,
} from '@material-ui/core';
import {useEffect, useState} from 'react';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import {projectBuildTimeoutHandling} from '@w7-3/webeagle-resources/dist/config/project';
import api from '@w7-3/webeagle-resources/dist/config/api';
import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import useLocales from '../../../hooks/useLocales';
import Timezone from './utils/Timezone';
import DefinitionList from '../utils/DefinitionList';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import LanguageChange from '../LanguageChange';

const mapStateToProps = ({state}) => {
    const {
        timeoutHandling,
        preferredLanguage,
    } = state;

    return {
        timeoutHandling,
        preferredLanguage,
    };
};

const propTypes = {
    timeoutHandling: PropTypes.string,
    preferredLanguage: PropTypes.string,
};

const Settings = (props) => {
    const {translate} = useLocales();
    const [preferredLanguage, setPreferredLanguage] = useState();
    const [timeoutHandling, setTimeoutHandling] = useState(
        props?.timeoutHandling || projectBuildTimeoutHandling.noop,
    );
    const [isBusy, setIsBusy] = useState(false);
    const apiCaller = useApiCaller();
    const updateErrorHandling = (value) => {
        setIsBusy(true);

        apiCaller({
            uri: getAPIPath(api.updateAccountData),
            data: {
                timeoutHandling: value,
            },
            successCallback: () => {
                setTimeoutHandling(value);
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate('saved'),
                    variant: 'success',
                });
            },
            alwaysCallback: () => {
                setIsBusy(false);
            },
        });
    };
    const updatePreferredLanguage = (value) => {
        setIsBusy(true);

        apiCaller({
            uri: getAPIPath(api.updateAccountData),
            data: {
                preferredLanguage: value,
            },
            successCallback: () => {
                setPreferredLanguage(value);
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate('saved'),
                    variant: 'success',
                });
            },
            alwaysCallback: () => {
                setIsBusy(false);
            },
        });
    };

    useEffect(() => {
        if (!props?.preferredLanguage) {
            return;
        }

        setPreferredLanguage(props?.preferredLanguage);
    }, [props?.preferredLanguage]);

    return (
        <>
            <Card sx={{p: 3}}>
                <DefinitionList
                    dataList={[{
                        key: translate('account.settings.timeZone.label'),
                        node: (
                            <Timezone />
                        ),
                    },]}
                />
            </Card>
            <Divider sx={{my: 3}} />
            <Typography variant="subtitle" sx={{my: 3}} component="div">
                {translate('account.settings.language.label')}
            </Typography>
            <Accordion
                defaultExpanded>
                <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                    {translate('account.settings.language.description')}
                </AccordionSummary>
                <AccordionDetails>
                    {
                        preferredLanguage ? (
                            <LanguageChange
                                language={preferredLanguage}
                                onChange={updatePreferredLanguage}
                            />
                        ) : (
                            <Skeleton width="100%" height={40} variant="rectangular" />
                        )
                    }
                </AccordionDetails>
            </Accordion>
            <Divider sx={{my: 3}} />
            <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                    {translate('account.settings.builds.timeoutHandling.label')}
                </AccordionSummary>
                <AccordionDetails>
                    <Stack spacing={1}>
                        <Typography variant="subtitle1">
                            {translate('account.settings.builds.timeoutHandling.description')}
                        </Typography>
                        <RadioGroup
                            value={timeoutHandling}
                            onChange={(event) => {
                                updateErrorHandling(event.target.value);
                            }}
                        >
                            {Object.values(projectBuildTimeoutHandling).map((item) => (
                                <FormControlLabel
                                    key={item}
                                    value={item}
                                    label={translate(`account.settings.builds.timeoutHandling.options.${item}`)}
                                    control={<Radio disabled={isBusy} />} />
                            ))}
                        </RadioGroup>
                    </Stack>
                </AccordionDetails>
            </Accordion>
        </>
    );
};

Settings.propTypes = propTypes;

export default connect(mapStateToProps)(Settings);
