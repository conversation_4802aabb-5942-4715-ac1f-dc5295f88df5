import {useCallback, useEffect, useState} from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {Autocomplete, Button, Stack, TextField, Typography} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import PubSub from 'pubsub-js';
import ModalContainer from '../../utils/ModalContainer';
import {getTimeZone, getTimeZoneList} from '../../../utils/timezone';
import useLocales from '../../../../hooks/useLocales';
import {getAPIPath} from '../../../utils/getPath';
import useApiCaller from '../../../hooks/useApiCaller';

const mapStateToProps = ({state}) => {
    const {timeZone} = state;

    return {
        timeZone
    };
};

const propTypes = {
    timeZone: PropTypes.string,
};

const getIsValidTimezone = (timeZone) => {
    return typeof timeZone === 'string' && timeZone.trim() !== '';
};

const Timezone = (props) => {
    const [timeZoneList] = useState(getTimeZoneList());
    const [timeZone, setTimeZone] = useState(props.timeZone);
    const {
        translate,
    } = useLocales();
    const apiCaller = useApiCaller();

    const updateTimeZone = useCallback((handleClose) => {
        if (!getIsValidTimezone(timeZone)) {
            return;
        }

        apiCaller({
            uri: getAPIPath(api.updateAccountData),
            data: {timeZone},
            successCallback: () => {
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate('saved'),
                    variant: 'success',
                });
                handleClose();
            },
            errorCallback: () => {
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate('itemLabelling.mandatoryField'),
                    variant: 'error',
                });
            },
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [timeZone]);
    const autoDetectTimeZone = () => {
        const detectedTZ = getTimeZone();

        if (!detectedTZ) {
            PubSub.publish('SHOW.NOTIFICATION', {
                message: translate('itemLabelling.mandatoryField'),
                variant: 'error',
            });
            return;
        }

        setTimeZone(detectedTZ);
    };

    useEffect(() => {
        if (!props.timeZone) {
            return;
        }

        setTimeZone(props.timeZone);

        // eslint-disable-next-line consistent-return
        return () => {
            PubSub.publish('UPDATE.ACCOUNT.RELATED.DATA', {
                data: {
                    timeZone,
                },
            });
        };

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.timeZone]);

    return (
        <Stack direction="row" alignItems="center">
            <Typography>
                {props.timeZone}
            </Typography>
            <ModalContainer
                triggerLabel={`(${translate('change')})`}
                title={translate('account.settings.timeZone.label')}
                buttonProps={{
                    startIcon: null,
                    variant: 'text',
                    sx: {
                        textTransform: 'lowercase',
                    },
                }}
                CtaButton={({handleClose}) => {
                    return (
                        <Button
                            variant="contained"
                            sx={{textTransform: 'none'}}
                            onClick={async () => {
                                updateTimeZone(handleClose);
                            }}
                            disabled={!getIsValidTimezone(timeZone) || timeZone === props.timeZone}
                        >
                            {translate('save')}
                        </Button>
                    );
                }}>
                <Autocomplete
                    freeSolo
                    fullWidth
                    value={timeZone}
                    options={timeZoneList.map((option) => option)}
                    onChange={(_, newValue) => {
                        setTimeZone(newValue);
                    }}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            label={timeZone ? '' : translate('account.settings.timeZone.filter')}
                        />
                    )}
                />
                <Button
                    size="large"
                    variant="text"
                    sx={{textTransform: 'none'}}
                    onClick={autoDetectTimeZone}
                >
                    {translate('account.settings.timeZone.autoDetect')}
                </Button>
            </ModalContainer>
        </Stack>
    );
};

Timezone.propTypes = propTypes;

export default connect(mapStateToProps)(Timezone);
