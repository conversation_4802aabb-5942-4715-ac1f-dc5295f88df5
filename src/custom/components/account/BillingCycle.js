import {connect} from 'react-redux';
import useLocales from '../../../hooks/useLocales';
import Label from '../../../components/Label';
import {BillingCyclePropType} from '../../../config/prop-types/ts/global.d';

const mapStateToProps = ({state}) => {
    const {
        billingCycle,
    } = state;

    return {
        billingCycle,
    };
};

const propTypes = {
    billingCycle: BillingCyclePropType,
};

const BillingCycle = ({billingCycle}) => {
    const {translate} = useLocales();

    if (!billingCycle) {
        return null;
    }

    return (
        <Label sx={{
            fontSize: {
                xs: '0.75rem',
                md: '1rem',
            },
            p: 2,
        }}>
            {translate('transactions.billingCycle.current')}: {billingCycle}
        </Label>
    );
};

BillingCycle.propTypes = propTypes;

export default connect(mapStateToProps)(BillingCycle);
