import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TextField, Tooltip} from '@material-ui/core';
import {useEffect} from 'react';
import {useForm} from 'react-hook-form';
import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../hooks/useLocales';

const propTypes = {
    showDeleteButton: PropTypes.bool,
    quantity: PropTypes.number,
    setQuantity: PropTypes.func,
    step: PropTypes.number,
    sxSettings: PropTypes.shape({
        root: PropTypes.object,
        input: PropTypes.object,
    }),
    isCompact: PropTypes.bool,
    isDisabled: PropTypes.bool,
};

const defaultProps = {
    showDeleteButton: false,
    step: 1,
};

const QuantityStepper = ({
    showDeleteButton,
    quantity,
    setQuantity,
    step,
    sxSettings,
    isCompact,
    isDisabled,
}) => {
    const {
        watch,
        setValue,
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            item: quantity
        },
    });
    const item = watch('item');
    const {translate} = useLocales();

    useEffect(() => {
        setQuantity({quantity: item});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [item]);

    useEffect(() => {
        setValue('item', quantity);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [quantity]);

    return (
        <Stack
            direction="row"
            alignItems="center"
            justifyContent="center"
            spacing={.5}
            sx={{
                width: 200,
                ...sxSettings?.root,
            }}
        >
            {
                showDeleteButton && (
                    <Tooltip title={translate('delete')}>
                        <IconButton
                            onClick={() => {
                                setValue('item', 0);
                            }}
                        >
                            <Icon
                                icon="eva:close-circle-fill"
                                width={40}
                                height={40}
                            />
                        </IconButton>
                    </Tooltip>
                )
            }
            <IconButton
                onClick={() => {
                    setValue('item', item - step);
                }}
                disabled={isDisabled}
            >
                <Icon
                    icon="eva:minus-outline"
                    width={40}
                    height={40}
                />
            </IconButton>
            <TextField
                value={item}
                onChange={(event) => {
                    if (event.target.value < 1) {
                        setValue('item', 1);
                        return;
                    }

                    setValue('item', event.target.value);
                }}
                onBlur={(event) => {
                    event.target.value = item;
                }}
                type="number"
                min={1}
                sx={{
                    width: 60,
                    ...sxSettings?.input,
                }}
                inputProps={{
                    style: {
                        textAlign: 'center',
                        ...getOptionalMap(isCompact, {
                            height: 16,
                            padding: 4,
                        }),
                    },
                }}
                disabled={isDisabled}
            />
            <IconButton
                onClick={() => {
                    setValue('item', item + step);
                }}
                disabled={isDisabled}
            >
                <Icon
                    icon="eva:plus-outline"
                    width={40}
                    height={40}
                />
            </IconButton>
        </Stack>
    );
};

QuantityStepper.defaultProps = defaultProps;
QuantityStepper.propTypes = propTypes;

export default QuantityStepper;
