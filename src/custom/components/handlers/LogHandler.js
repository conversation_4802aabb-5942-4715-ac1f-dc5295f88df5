import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {INFO_CODE_LEVELS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useApiCaller from '../../hooks/useApiCaller';
import {getOpenAPIPath} from '../../utils/getPath';

const LogHandler = () => {
    const apiCaller = useApiCaller();
    useEffect(() => {
        PubSub.subscribe('LOG.EVENT', (_, {uiData}) => {
            if (
                !INFO_CODE_LEVELS[uiData?.type] ||
                !uiData?.message ||
                !uiData?.data ||
                !window.webAutomate.token
            ) {
                return;
            }

            apiCaller({
                uri: getOpenAPIPath(api.logUIEvent),
                data: {uiData},
            });
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return null;
};

export default LogHandler;
