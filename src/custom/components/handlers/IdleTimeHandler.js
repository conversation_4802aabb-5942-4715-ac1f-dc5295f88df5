import {useEffect, useRef} from 'react';
import PubSub from 'pubsub-js';
import {AlertTitle, Button, Stack, Typography} from '@material-ui/core';
import {IDLE_USER_TIMEOUT} from '../../../config/base';
import useLocales from '../../../hooks/useLocales';
import {setLameDuckState} from '../../utils/appState';

const NotificationHandler = () => {
    const {translate} = useLocales();
    const handleClose = () => {
        window.location.reload();
    };
    const lastHeartbeat = useRef(Date.now());

    useEffect(() => {
        const handleIdleTime = (force = false) => {
            if (force || Date.now() > (lastHeartbeat.current + IDLE_USER_TIMEOUT)) {
                setLameDuckState();
                clearInterval(interval);
                PubSub.publish('HIDE.NOTIFICATIONS.ALL');
                PubSub.publish('SHOW.MODAL', {
                    type: 'custom',
                    handleClose,
                    dialog: {
                        actions: {
                            hide: true,
                        },
                    },
                    dialogProps: {
                        maxWidth: 'xs',
                    },
                    children: (
                        <Stack spacing={3} sx={{p: 3}}>
                            <AlertTitle>
                                {translate('idleTime.title')}
                            </AlertTitle>
                            <Typography align="center">
                                {translate('idleTime.description')}
                            </Typography>
                            <Button
                                size="large"
                                onClick={handleClose}
                                sx={{
                                    alignSelf: 'center',
                                    textTransform:'none',
                                }}
                            >
                                {translate('idleTime.ctaLabel')}
                            </Button>
                        </Stack>
                    ),
                });
            }
        };

        PubSub.subscribe('API.CALLED', () => {
            lastHeartbeat.current = Date.now();
        });

        window.document.body.addEventListener('click', () => {
            lastHeartbeat.current = Date.now();
        }, {
            capture: true,
        });

        PubSub.subscribe('SHOW.IDLE-TIME-NOTIFICATION', () => {
            handleIdleTime(true);
        });

        PubSub.subscribe('LOCATION.CHANGED', () => {
            lastHeartbeat.current = Date.now();
        });

        const interval = setInterval(handleIdleTime, IDLE_USER_TIMEOUT);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return null;
};

export default NotificationHandler;
