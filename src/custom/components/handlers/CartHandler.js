import {connect} from 'react-redux';
import {useEffect, useState} from 'react';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {Link as RouterLink, useLocation} from 'react-router-dom';
import {Badge, Box, Button, IconButton, Stack} from '@material-ui/core';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import callValidFunction from '@w7-3/webeagle-resources/dist/libs/callValidFunction';
import {PATH_HOME, PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import useNotification from '../../hooks/useNotification';
import useLocales from '../../../hooks/useLocales';
import Cart from '../Cart';
import {pricingItemGroupConfig} from '../../../hooks/usePricing';
import {deleteFromCart} from '../../../redux/slices/pricing';
import {useDispatch} from '../../../redux/store';

const mapStateToProps = ({pricing}) => {
    return {
        pricing,
    };
};

const CartContainer = ({setIsMinified}) => {
    const {translate} = useLocales();

    return (
        <Stack
            spacing={1}
            sx={{
                position: 'relative',
            }}
        >
            <IconButton
                onClick={() => {
                    setIsMinified();
                }}
                edge="end"
                sx={{
                    position: 'absolute',
                    top: '-.25em',
                    right: '0.25em',
                }}
                color="primary"
            >
                <Icon icon="eva:close-outline"/>
            </IconButton>
            <Cart
                isCompact
            />
            <Button
                variant="contained"
                to={PATH_PAGE.payment}
                component={RouterLink}
                sx={{mb: 1}}
            >
                {translate('payment.cta')}
            </Button>
            <IconButton
                color="primary"
                sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    width: '100%',
                    lineHeight: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transform: 'translateY(110%)',
                }}
                onClick={() => {
                setIsMinified();
            }}>
                <Icon
                    icon="eva:arrowhead-right-outline"
                    width={32}
                    height={32}
                />
            </IconButton>
        </Stack>
    );
};

CartContainer.propTypes = {
    setIsMinified: PropTypes.func,
};

const CartHandler = ({pricing}) => {
    const dispatch = useDispatch();
    const notify = useNotification();
    const {pathname} = useLocation();
    const [isMinified, setIsMinified] = useState(true);
    const [closeCartNotification, setCloseCartNotification] = useState({
        close: () => {},
    });
    const count = pricingItemGroupConfig.reduce((tempCount, {listType}) => {
        if (!isNonEmptyArray(pricing[listType])) {
            return tempCount;
        }

        return pricing[listType].reduce((tempCount2, {quantity}) => {
            return tempCount2 + quantity;
        }, tempCount);
    }, 0);
    const hideCart = count < 1 || ![
        PATH_HOME,
        PATH_PAGE.pricing,
        PATH_PAGE.projectWizard,
    ].includes(pathname);

    useEffect(() => {
        callValidFunction(closeCartNotification.close);
        if (hideCart || isMinified) {
            return;
        }

        setCloseCartNotification({
            close: notify({
                message: (
                    <CartContainer
                        setIsMinified={() => {
                            callValidFunction(closeCartNotification.close);
                            setIsMinified(true);
                        }}
                    />
                ),
                variant: 'info',
                autoHideDuration: null,
                action: null,
                className: 'cartNotificationContainer',
                anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'right',
                },
            }),
        });
        // eslint-disable-next-line
    }, [hideCart, isMinified]);

    useEffect(() => {
        if (count < 1) {
            return;
        }

        setIsMinified(false);
    }, [count]);

    useEffect(() => {
        let isMounted = true;
        queueCallback(() => {
            PubSub.subscribe('HANDLE.CLEAR.CART', (_, cart) => {
                if (Array.isArray(cart?.removeItems) && cart.removeItems.length > 0) {
                    cart.removeItems.forEach((item) => {
                        dispatch(deleteFromCart({
                            priceId: item?.price,
                        }));
                    });
                }
            });

            PubSub.subscribe('LOCATION.CHANGED', () => {
                if (!isMounted) return;

                setIsMinified(true);
            });

            PubSub.subscribe('SHOW.DIALOG', () => {
                if (!isMounted) return;

                setIsMinified(true);
            });

            return () => {
                isMounted = false;
            };
        }, 3000);
        // eslint-disable-next-line
    }, []);

    if (!hideCart && isMinified) {
        return (
            <Box
                sx={{
                    bottom: 0,
                    right: 0,
                    lineHeight: 0,
                    position: 'fixed',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0 1em !important',
                    zIndex: 999,
                }}>
                <Badge
                    badgeContent={count}
                    color="error"
                    children={
                        <IconButton
                            onClick={() => {
                                setIsMinified(false);
                            }}
                            color="primary">
                            <Icon
                                icon="eva:shopping-cart-outline"
                                width={32}
                                height={32}
                            />
                        </IconButton>
                    } />
            </Box>
        );
    }

    return null;
};

CartHandler.propTypes = {
    pricing: PropTypes.object,
};

export default connect(mapStateToProps)(CartHandler);
