import {useEffect, useRef, useState} from 'react';
import PubSub from 'pubsub-js';
import {<PERSON><PERSON>, <PERSON>ertTitle, Button, Dialog, DialogActions, DialogTitle, IconButton, Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import callValidFunction from '@w7-3/webeagle-resources/dist/libs/callValidFunction';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';

const ModalHandler = () => {
    const {translate} = useLocales();
    const [open, setOpen] = useState(false);
    const [state, setState] = useState({});
    const id = useRef();
    const handleClose = () => {
        setOpen(false);
        callValidFunction(state?.handleClose);
        setState({});
    };

    useEffect(() => {
        PubSub.subscribe('SHOW.MODAL', (_, newState) => {
            setState(newState);
            setOpen(true);
            id.current = newState?.id;
        });

        PubSub.subscribe('HIDE.MODAL', (_, params) => {
            if (params?.id !== id.current) {
                return;
            }

            setOpen(false);
        });

        PubSub.subscribe('HIDE.MODAL.ALL', () => {
            setOpen(false);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (!open) {
        return null;
    }

    return (
        <Dialog
            maxWidth="lg"
            open={open}
            {...state?.dialogProps}
            {...getOptionalMap(!state?.isBlocking, {
                onClose: handleClose,
            })}
        >
            {
                !state?.isBlocking && (
                    <IconButton
                        onClick={handleClose}
                        edge="end"
                        sx={{
                            position: 'fixed',
                            top: 8,
                            right: 16,
                        }}
                        color="primary"
                    >
                        <Icon icon="eva:close-outline"/>
                    </IconButton>
                )
            }
            {
                state.title && (
                    <DialogTitle>
                        <Alert severity={state.title.severity}>
                            <AlertTitle>{state.title.label}</AlertTitle>
                        </Alert>
                    </DialogTitle>
                )
            }
            {state?.children}
            {
                !state?.dialog?.actions?.hide && (
                    <DialogActions>
                        <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                            <Button onClick={handleClose}>
                                {state?.closeText || translate('close')}
                            </Button>
                            {state.commit}
                        </Stack>
                    </DialogActions>
                )
            }
        </Dialog>
    );
};

export default ModalHandler;
