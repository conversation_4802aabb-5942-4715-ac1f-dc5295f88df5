import {useEffect, useRef} from 'react';
import PubSub from 'pubsub-js';
import {Stack} from '@material-ui/core';
import {getProjectSolutionStepItemLabel} from '../../utils/projectSolutionStepItemLabel';
import useLocales from '../../../hooks/useLocales';

const ProjectStepViewer = () => {
    const items = useRef([]);
    const {translate} = useLocales();
    const hideItem = (item) => {
        items.current = items.current.filter((item2) => {
            return item?.id !== item2?.id;
        });
        PubSub.publish('HIDE.DIALOG', {
            id: item?.id,
        });
    };
    const showItem = (item) => {
        hideItem(item);
        items.current.push(item);
        PubSub.publish('SHOW.DIALOG', {
            type: 'custom',
            dialogProps: {
                title: getProjectSolutionStepItemLabel({
                    item,
                    translate,
                }),
            },
            handleClose: () => {
                PubSub.publish('HIDE.PROJECT-STEP', item);
            },
            children: (
                <Stack
                    spacing={2}
                    alignItems="flex-start"
                    sx={{mt: 3}}
                >
                    <pre>{JSON.stringify(item, null, 4)}</pre>
                </Stack>
            ),
        });
    };

    useEffect(() => {
        PubSub.subscribe('SHOW.PROJECT-STEP', (_, payload) => {
            showItem(payload);
        });

        PubSub.subscribe('HIDE.PROJECT-STEP', (_, payload) => {
            hideItem(payload);
        });

        PubSub.subscribe('HIDE.PROJECT-STEP.ALL', () => {
            items.current = items.current.forEach(hideItem);
            items.current = [];
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return null;
};

export default ProjectStepViewer;
