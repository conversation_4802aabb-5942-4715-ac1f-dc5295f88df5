import {useEffect, useRef, useState} from 'react';
import PubSub from 'pubsub-js';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {Alert, Typography} from '@material-ui/core';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {urlParameters, browserApiKeys} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import useLocales from '../../../hooks/useLocales';
import {hideSolutionReviewer, showSolutionReviewer} from '../../../redux/slices/state';
import {useDispatch} from '../../../redux/store';
import Drawer from '../forms/Drawer';
import BuildSelector from '../utils/BuildSelector';
import ProjectBuildURLChallengeRankings from '../project/ProjectBuildURLChallengeRankings';
import LoadingScreen from '../../../components/LoadingScreen';

const solution = solutions.urlChallenge.key;
const i18nContext = `project.wizardSteps.solutionsConfigs.groups.${solution}.reviewer`;

export const mapStateToProps = ({state}) => {
    const {
        projects: allProjects,
        solutionReviewer,
    } = state;

    const projects = {};
    Object.values(allProjects || {}).forEach((project) => {
        if (project?.configData?.solution?.appName !== solution) {
            return;
        }

        projects[project.id] = project;
    });

    return {
        projects,
        solutionReviewer,
    };
};

const propTypes = {
    projects: PropTypes.object,
    solutionReviewer: PropTypes.object,
};

const ScreenshotDocumentationManager = ({projects, solutionReviewer}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const [searchParams, updateSearchParams] = useSearchParams();
    const isLoading = useRef(false);
    const resetState = useRef(null);
    const [show, setShow] = useState(false);
    const [isBusy, setIsBusy] = useState(true);
    const navigate = useNavigate();
    const state = {
        projectId: searchParams.get(urlParameters.solutionHandler.projectId),
        buildId: searchParams.get(urlParameters.solutionHandler.buildId),
    };
    const project = projects?.[state?.projectId];
    const build = project?.results?.[state?.buildId];

    const setState = (newParams) => {
        if (!isNonEmptyObject(newParams)) {
            return;
        }

        Object.entries(newParams).forEach(([keyRaw, valueRaw]) => {
            const key = urlParameters.solutionHandler[keyRaw] || urlParameters[keyRaw];
            if (!key) {
                return;
            }

            if (!valueRaw) {
                searchParams.delete(key);
                return;
            }

            const value = typeof valueRaw === 'string' ? valueRaw : JSON.stringify(valueRaw);

            searchParams.set(key, value);
        });

        updateSearchParams(searchParams);
    };

    const reset = () => {
        const {
            projectId,
            buildId,
        } = state;
        navigate(`${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`);
        dispatch(hideSolutionReviewer());
    };

    const activateBusyState = () => {
        isLoading.current = true;
        PubSub.publish('SHOW.BUSY');
    };

    const deactivateBusyState = () => {
        isLoading.current = false;
        queueCallback(() => {
            PubSub.publish('HIDE.BUSY');
        });
    };

    useEffect(() => {
        PubSub.subscribe('SOLUTION-MANAGER', (_, {
            appName,
            config,
        }) => {
            if (appName !== solution) {
                return;
            }

            dispatch(showSolutionReviewer());
            queueCallback(() => {
                resetState.current = {};
                searchParams.forEach((value, key) => {
                    resetState.current[key] = value;
                });
                delete resetState.current[browserApiKeys.reviewer];
                setState({
                    ...config,
                    [browserApiKeys.reviewer]: 1,
                });
                setShow(true);
            });
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setIsBusy(!build?.data);
    }, [build?.data]);

    if (!solutionReviewer.show || !show) {
        return null;
    }

    return (
        <Drawer
            open
            onClose={reset}
            isFullScreen
            title={(
                <Typography variant="subtitle1">
                    {translate(`${i18nContext}.label`)}
                </Typography>
            )}
        >
            <BuildSelector
                {...{
                    activateBusyState,
                    deactivateBusyState,
                    state,
                    callbacks: {
                        handleProjectIdChange: ({projectId}) => {
                            activateBusyState();
                            queueCallback(() => {
                                setState({
                                    projectId,
                                    buildId: projects?.[projectId]?.latestBuild?.index,
                                    resultIds: [],
                                });
                            });
                        },
                        handleBuildIdChange: ({buildId, state}) => {
                            activateBusyState();
                            queueCallback(() => {
                                setState({
                                    projectId: state.projectId,
                                    buildId,
                                    resultIds: state.resultIds,
                                });
                            });
                        },
                    },
                    projects,
                }}
            />
            {
                isBusy ? (
                    <LoadingScreen
                        sx={{
                            py: 6,
                        }}
                    />
                ) : (
                    <>
                        {
                            [
                                BUILD_STATES.success,
                                BUILD_STATES.cancel,
                                BUILD_STATES.mixed,
                            ].includes(build?.data?.state) ? (
                                <ProjectBuildURLChallengeRankings
                                    project={project}
                                    build={build}
                                />
                            ) : (
                                <Alert severity="error">
                                    {translate(`project.build.states.${build?.data?.state}.description`)}
                                </Alert>
                            )
                        }
                    </>
                )
            }
        </Drawer>
    );
};

ScreenshotDocumentationManager.propTypes = propTypes;

export default connect(mapStateToProps)(ScreenshotDocumentationManager);
