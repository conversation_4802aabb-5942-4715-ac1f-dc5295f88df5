import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import {Alert, AlertTitle, Box, Button, Divider, Grid, IconButton, Stack, Typography} from '@material-ui/core';
import {Link as RouterLink, useLocation} from 'react-router-dom';
import {Icon} from '@iconify/react';
import chevronRightOutline from '@iconify/icons-eva/chevron-right-outline';
import ReactDOMServer from 'react-dom/server';
import closeOutline from '@iconify/icons-eva/close-outline';
import {accountStates} from '@w7-3/webeagle-resources/dist/config/account';
import {PATH_AUTH, PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {requestOptions} from '@w7-3/webeagle-resources/dist/config/contactFormRequests';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {getI18nDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import useLocales from '../../../hooks/useLocales';
import {globalConfig} from '../../../config/setup';
import useAuth from '../../../hooks/useAuth';
import LocationChangeHandler from './LocationChangeHandler';
import {UIStatePropType} from '../../../config/prop-types/ts/ui.d';

export const handleNewAccount = ({translate, accountState}) => {
    const star = ReactDOMServer.renderToStaticMarkup(
        <Box
            component="img"
            src="/static/v1/icons/shinyStar.svg"
            alt="Star"
            sx={{
                display: 'inline-block',
                width: 28,
                height: 28,
            }} />,
    );
    const rocket = ReactDOMServer.renderToStaticMarkup(
        <Box
            component="img"
            src="/static/v1/icons/rocket-space-icon.svg"
            alt="Star"
            sx={{
                display: 'inline-block',
                width: 28,
                height: 28,
                transform: 'translateY(10px)',
            }} />,
    );

    PubSub.publish('SHOW.MODAL', {
        type: 'custom',
        dialog: {
            actions: {
                hide: true,
            },
        },
        children: (
            <Alert
                severity="info"
                icon={false}
                sx={{px: 3}}
                action={
                    <IconButton
                        onClick={() => {
                            PubSub.publish('HIDE.MODAL.ALL');
                        }}
                        sx={{ml: 1}}>
                        <Icon icon={closeOutline}/>
                    </IconButton>
                }>
                <AlertTitle
                    dangerouslySetInnerHTML={{__html: translate(`account.states.${accountState}.label`, {
                            app: globalConfig.domain,
                            star,
                        })}}
                />
                <Typography sx={{pt: 2,}}>
                    {translate(`account.states.${accountState}.description`, {
                        app: globalConfig.domain,
                    })}
                </Typography>
                {
                    translate(`account.states.${accountState}.usps`, {
                        returnObjects: true,
                        app: globalConfig.domain,
                    }).map((usp, index) => (
                        <Typography key={index} sx={{pt: 2,}}>
                            {usp}
                        </Typography>
                    ))
                }

                <Typography
                    sx={{pt: 2}}
                    component="div"
                    dangerouslySetInnerHTML={{__html: translate(`account.states.${accountState}.footer`, {
                            rocket,
                        })}}
                />
                <Divider sx={{my: 3}} />
                <Typography variant="subtitle2" sx={{fontWeight: 'bold'}}>
                    {translate('yourOptions')}
                </Typography>
                <Grid container spacing={3} sx={{my: 1}}>
                    <Grid item xs={12} md={4}>
                        <Button
                            variant="text"
                            startIcon={<Icon icon={chevronRightOutline} width={40} height={40}/>}
                            component={RouterLink}
                            to={PATH_PAGE.faqs}
                        >
                            {translate('faqs.title')}
                        </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <Button
                            variant="text"
                            startIcon={<Icon icon={chevronRightOutline} width={40} height={40}/>}
                            component={RouterLink}
                            to={PATH_PAGE.pricing}
                        >
                            {translate('subscribe')}
                        </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <Button
                            variant="text"
                            startIcon={<Icon icon={chevronRightOutline} width={40} height={40}/>}
                            onClick={() => {
                                PubSub.publish('HIDE.MODAL.ALL');
                                PubSub.publish('SHOW.DIALOG', {
                                    type: 'contactForm',
                                    props: {request: requestOptions.demo.key},
                                });
                            }}
                        >
                            {translate('requestDemo')}
                        </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <Button
                            variant="text"
                            startIcon={<Icon icon={chevronRightOutline} width={40} height={40}/>}
                            component={RouterLink}
                            to={PATH_PAGE.apiDocumentation}
                        >
                            {translate('api.resources')}
                        </Button>
                    </Grid>
                </Grid>
            </Alert>
        ),
    });
};

export const handleSuspendedAccount = ({translate, accountState}) => {
    const handleClose = () => {
        PubSub.publish('AUTHENTICATION.LOGOUT');
    };
    PubSub.publish('SHOW.MODAL', {
        type: 'custom',
        dialog: {
            actions: {
                hide: true,
            },
        },
        dialogProps: {
            maxWidth: 'sm',
        },
        children: (
            <Alert severity="error"
                   sx={{py: 3, pl: 5, pr: 8}}>
                <p>
                    {translate(`account.states.${accountState}.label`, {
                        app: globalConfig.domain,
                    })}
                </p>

                <p>
                    {translate(`account.states.${accountState}.description`, {
                        app: globalConfig.domain,
                    })}
                </p>

                <Button
                    variant="contained"
                    color="inherit"
                    onClick={() => {
                        handleClose();
                        queueCallback(() => {
                            window.location.href = PATH_PAGE.contact;
                        });
                    }}
                    sx={{
                        mt: 3,
                        textTransform: 'none',
                        float: 'right'
                    }}
                >
                    {translate('contactUs.label')}
                </Button>
            </Alert>
        ),
    });
};

export const handleGracePeriod = ({
    translate,
    accountAudit,
    accountState,
    locale,
}) => {
    PubSub.publish('SHOW.MODAL', {
        type: 'custom',
        dialog: {
            actions: {
                hide: true,
            },
        },
        dialogProps: {
            maxWidth: 'sm',
        },
        children: (
            <Alert severity="warning"
                   sx={{py: 3, pl: 5, pr: 8}}>
                <Stack spacing={3}>
                    <p>
                        {translate(`account.states.${accountState}.label`, {
                            app: globalConfig.domain,
                        })}
                    </p>

                    <p>
                        {translate(`account.states.${accountState}.description`, {
                            numberOfDaysLeft: getI18nDuration((accountAudit.next - Date.now()), {
                                locale,
                            }),
                        })}
                    </p>
                    <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                        <Button
                            to={PATH_PAGE.contact}
                            component={RouterLink}
                            sx={{textTransform: 'none'}}>
                            {translate('contactUs.label')}
                        </Button>
                        <Button
                            variant="text"
                            component={RouterLink}
                            to={PATH_PAGE.pricing}
                        >
                            {translate('subscribe')}
                        </Button>
                    </Stack>
                </Stack>
            </Alert>
        ),
    });
};

export const handleInactive = ({translate, accountState}) => {
    const handleClose = () => {
        PubSub.publish('AUTHENTICATION.LOGOUT');
    };
    PubSub.publish('SHOW.MODAL', {
        type: 'custom',
        handleClose,
        dialog: {
            actions: {
                hide: true,
            },
        },
        dialogProps: {
            maxWidth: 'sm',
        },
        children: (
            <Alert severity="error"
                   sx={{py: 3, pl: 5, pr: 8}}>
                <p>
                    {translate(`account.states.${accountState}.label`, {
                        app: globalConfig.domain,
                    })}
                </p>

                <p>
                    {translate(`account.states.${accountState}.description`, {
                        app: globalConfig.domain,
                    })}
                </p>

                <Button
                    variant="contained"
                    color="inherit"
                    onClick={() => {
                        handleClose();
                        queueCallback(() => {
                            window.location.href = PATH_PAGE.contact;
                        });
                    }}
                    sx={{
                        mt: 3,
                        textTransform: 'none',
                        float: 'right'
                    }}
                >
                    {translate('contactUs.label')}
                </Button>
            </Alert>
        ),
    });
};

const mapStateToProps = ({state}) => {
    const {
        accountAudit,
        accountState,
        demoRequest,
        ts: stateLastUpdated,
        automationCredits,
    } = state;

    return {
        accountAudit,
        accountState,
        automationCredits,
        demoRequest,
        stateLastUpdated,
    };
};

const AccountStateHandler = ({
    accountAudit,
    accountState,
    stateLastUpdated,
}) => {
    const {translate, currentLang} = useLocales();
    const location = useLocation();
    const {signOut} = useAuth();

    const handleState = ![
        PATH_PAGE.contact,
        PATH_PAGE.payment,
        PATH_PAGE.pricing,
    ].includes(location?.pathname);

    useEffect(() => {
        if (!handleState) {
            return;
        }

        const fields = {};
        window.location.search.substr(1)
            .split('&')
            .forEach((keyValue) => {
                const [key, value] = keyValue.split('=');
                fields[key] = value;
            });

        if (fields[urlParameters.account.verification]) {
            const handleClose = async () => {
                signOut?.();
                window.location.href = `${PATH_AUTH.login}?${window.location.search}`;
            };

            PubSub.publish('SHOW.MODAL', {
                type: 'custom',
                handleClose,
                dialog: {
                    actions: {
                        hide: true,
                    },
                },
                dialogProps: {
                    maxWidth: 'sm',
                },
                children: (
                    <Alert severity="success"
                           sx={{py: 3, pl: 5, pr: 8}}>
                        <AlertTitle>
                            {translate('collaborators.confirmation.notification.success')}
                        </AlertTitle>
                        <Typography component="p" variant="heading" sx={{mb: 2}}>
                            {translate('collaborators.confirmation.doneDescription')}
                        </Typography>
                        <Stack direction="row"
                               alignItems="center"
                               justifyContent="flex-end"
                               spacing={3}>
                            <Button
                                color="success"
                                variant="contained"
                                onClick={handleClose}
                                sx={{textTransform: 'none'}}>
                                {translate('continue')}
                            </Button>
                        </Stack>
                    </Alert>
                ),
            });

            return;
        }

        if (
            accountState === accountStates.new
            /*
            && automationCredits === 0
            && !demoRequest
            && [ROOTS_DASHBOARD].some((path) => {
                return location?.pathname?.includes?.(path);
            })
            */
        ) {
            handleNewAccount({translate, accountState});
        }

        if (accountState === accountStates.suspended) {
            handleSuspendedAccount({translate, accountState});
        }

        if (accountState === accountStates.gracePeriod) {
            handleGracePeriod({
                translate,
                accountAudit,
                accountState,
                locale: currentLang.value,
            });
        }

        if (accountState === accountStates.inactive) {
            handleInactive({translate, accountState});
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [stateLastUpdated, accountState, handleState]);

    if ([
        accountStates.suspended,
        accountStates.inactive,
    ].includes(accountState)) {
        return null;
    }

    return (
        <LocationChangeHandler />
    );
};

AccountStateHandler.propTypes = {
    accountAudit: UIStatePropType.accountAudit,
    accountState: PropTypes.string,
    stateLastUpdated: PropTypes.number,
};

export default connect(mapStateToProps)(AccountStateHandler);
