import {useEffect, useState} from 'react';
import PubSub from 'pubsub-js';
import {Button, Dialog, Stack} from '@material-ui/core';
import {accountNotificationTypes} from '@w7-3/webeagle-resources/dist/config/account';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {useNavigate} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import useLocales from '../../../../../hooks/useLocales';
import {EVENTS} from './config';
import InfoCode from '../../../utils/InfoCode';
import ExternalLink from '../../../utils/ExternalLink';
import {domainsTabs} from '../../../../pages/DomainsPage';

const ModalHandler = () => {
    const {translate} = useLocales();
    const [state, setState] = useState();
    const navigate = useNavigate();

    useEffect(() => {
        PubSub.subscribe(`${EVENTS.SHOW}.${accountNotificationTypes.blacklistedDomainsInProject}`, (_, custom) => {
            setState(custom);
        });

        PubSub.subscribe(`${EVENTS.HIDE}.${accountNotificationTypes.blacklistedDomainsInProject}`, () => {
            setState(null);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (!state) {
        return null;
    }

    return (
        <Dialog
            {...state?.dialogProps}
            maxWidth="lg"
            open>
            {
                isNonEmptyArray(state?.infoCodes) && state?.infoCodes.map((infoCode) => (
                    <InfoCode
                        key={infoCode?.code}
                        infoCode={infoCode}
                        isDismissible={false}
                    >
                        {
                            isNonEmptyArray(infoCode?.data?.domainList) && (
                                <Stack spacing={3} sx={{p: 3}}>
                                    {
                                        infoCode.data.domainList.map((domain) => (
                                            <ExternalLink
                                                key={domain}
                                                label={domain}
                                                url={`https://${domain}`}
                                                allowCopy={false}
                                            />
                                        ))
                                    }
                                    <Stack direction="row" spacing={3} justifyContent="flex-end">
                                        <Button
                                            color="inherit"
                                            variant="contained"
                                            onClick={() => setState(null)}>
                                            {translate('close')}
                                        </Button>
                                        <Button
                                            color="inherit"
                                            variant="contained"
                                            sx={{textTransform: 'none'}}
                                            onClick={() => {
                                                setState(null);
                                                navigate(`${PATH_DASHBOARD.management.domains}?${urlParameters.miscellaneous.tab}=${domainsTabs.blacklist}`);
                                            }}>
                                            {translate('account.domains.goto')}
                                        </Button>
                                    </Stack>
                                </Stack>
                            )
                        }
                    </InfoCode>
                ))
            }
        </Dialog>
    );
};

export default ModalHandler;
