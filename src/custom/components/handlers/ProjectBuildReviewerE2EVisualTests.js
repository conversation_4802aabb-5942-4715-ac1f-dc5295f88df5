import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {useEffect, useRef, useState} from 'react';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {
    Alert,
    Box,
    Breadcrumbs,
    Button,
    Checkbox,
    FormControlLabel,
    Grid,
    IconButton,
    MenuItem,
    Stack,
    Switch,
    Typography,
} from '@material-ui/core';
import {useSearchParams} from 'react-router-dom';
import mapObjIndexed from 'ramda/src/mapObjIndexed';
import moreVerticalFill from '@iconify/icons-eva/more-vertical-fill';
import {visualTestStates} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyArray, isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import getParsedJSON from '@w7-3/webeagle-resources/dist/libs/getParsedJSON';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {browserApiKeys, urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import useLocales from '../../../hooks/useLocales';
import {useDispatch} from '../../../redux/store';
import BuildSelector from '../utils/BuildSelector';
import Label from '../../../components/Label';
import Drawer from '../forms/Drawer';
import MenuPopover from '../../../components/MenuPopover';
import useUpdateScreenshotState from './project-build-reviewer/e2e-visual-tests/hooks/useUpdateScreenshotState';
import useVisualDiffHandler from './project-build-reviewer/e2e-visual-tests/hooks/useVisualDiffHandler';
import VisualDiffHeader from './project-build-reviewer/e2e-visual-tests/VisualDiffHeader';
import VisualDiffActiveItem from './project-build-reviewer/e2e-visual-tests/VisualDiffActiveItem';
import VisualDiffItem from './project-build-reviewer/e2e-visual-tests/VisualDiffItem';
import {hideSolutionReviewer, showSolutionReviewer} from '../../../redux/slices/state';
import LoadingScreen from '../../../components/LoadingScreen';
import {e2eVisualTestsBuildStates} from '../../../config/base';

const solution = solutions.e2eVisualTests.key;
const i18nContext = `project.wizardSteps.solutionsConfigs.groups.${solution}.reviewer`;
const noHitsClass = 'visual-diff-manager-empty';

const propTypes = {
    projects: PropTypes.object,
    solutionReviewer: PropTypes.object,
};


export const ITEM_VIEWS = {
    sideBySide: 'sideBySide',
    overlappedX: 'overlappedX',
    overlappedY: 'overlappedY',
    overlappedZ: 'overlappedZ',
};

const mapStateToProps = ({state}) => {
    const {
        projects: allProjects,
        solutionReviewer,
    } = state;

    const projects = {};
    Object.values(allProjects || {}).forEach((project) => {
        if (project?.configData?.solution?.appName !== solution) {
            return;
        }

        projects[project.id] = project;
    });


    return {
        projects,
        solutionReviewer,
    };
};

const ProjectBuildReviewerE2EVisualTests = ({projects, solutionReviewer}) => {
    const dispatch = useDispatch();
    const {translate} = useLocales();
    const [itemStateList, updateItemStateList] = useState([]);
    const [selectedData, updateSelectedData] = useState({});
    const menuRef = useRef(null);
    const [openMenu, setOpenMenu] = useState(false);
    const [view, setView] = useState(ITEM_VIEWS.sideBySide);
    const [searchParams, updateSearchParams] = useSearchParams();
    const [showColorDiffs, setShowColorDiffs] = useState(true);
    const [showMarkerDiffs, setShowMarkerDiffs] = useState(false);
    const [markerColor, setMarkerColor] = useState('red');
    const [markerPadding, setMarkerPadding] = useState(4);
    const [markerThickness, setMarkerThickness] = useState(2);
    const [showToolbox, setShowToolbox] = useState(true);
    const [visualDiffBaselines, setVisualDiffBaselines] = useState(null);
    const [show, setShow] = useState(false);
    const [isBusy, setIsBusy] = useState(true);
    const isLoading = useRef(false);
    const resetState = useRef(null);
    const state = {
        projectId: searchParams.get(urlParameters.solutionHandler.projectId),
        buildId: searchParams.get(urlParameters.solutionHandler.buildId),
        resultIds: getParsedJSON(
            searchParams.get(urlParameters.resultIds) || '[]',
        ),
        itemState: getParsedJSON(
            searchParams.get(urlParameters.solutionHandler.itemState) || '[]',
        ),
        activeItemId: searchParams.get(urlParameters.solutionHandler.activeItemId),
    };
    const project = projects?.[state?.projectId];
    const build = project?.results?.[state?.buildId];
    const rootData = build?.solutionResults?.[state?.resultIds?.[0]];
    const subData = rootData?.solutionResults?.[state?.resultIds?.[1]];
    const data = state?.resultIds?.length > 1 ? subData : rootData;
    const count = Object.values(build?.data?.solutionSummary?.e2eVisualTests?.results || {}).reduce((acc, current) => acc + current, 0);
    const {
        groupItems,
        visualDiffItemList,
        summaryData,
        activeItem,
    } = useVisualDiffHandler({
        data,
        visualDiffBaselines,
        state,
    });
    const {
        needsReload,
        updateScreenshotState,
    } = useUpdateScreenshotState({
        state,
        setVisualDiffBaselines,
    });

    const setState = (newParams) => {
        if (!isNonEmptyObject(newParams)) {
            return;
        }

        Object.entries(newParams).forEach(([keyRaw, valueRaw]) => {
            const key = urlParameters.solutionHandler[keyRaw] || urlParameters[keyRaw];
            if (!key) {
                return;
            }

            if (!valueRaw) {
                searchParams.delete(key);
                return;
            }

            const value = typeof valueRaw === 'string' ? valueRaw : JSON.stringify(valueRaw);

            searchParams.set(key, value);
        });

        updateSearchParams(searchParams);
    };
    const setSelectedData = (state) => {
        updateSelectedData(state);
    };
    const setItemStateList = (value) => {
        updateItemStateList(value);
        setSelectedData({});
        setState({
            itemState: encodeURIComponent(JSON.stringify(value)),
            ...getOptionalMap(value.length < 1, {
                activeItemId: '',
            }),
        });
    };
    const setActiveItem = (value) => {
        if (!value) {
            setState({
                activeItemId: '',
            });
            setSelectedData({});
            return;
        }

        const {
            item,
            groupId,
        } = value;

        setSelectedData({
            [groupId]: [item.data.result.id],
        });
        setState({
            activeItemId: item.data.result.id,
        });
    };
    const reset = () => {
        const projectId = state?.projectId;
        setState({
            ...mapObjIndexed(() => null, state),
            [browserApiKeys.reviewer]: null,
        });

        updateSearchParams(resetState.current);
        setActiveItem(null);
        setOpenMenu(false);
        updateItemStateList([]);
        setShow(false);
        dispatch(hideSolutionReviewer());

        if (!needsReload) {
            return;
        }

        PubSub.publish('PROJECT_LOAD', {
            filterIdList: [projectId],
            includeDetails: true,
        });
    };
    const handleMenuOpen = () => {
        setOpenMenu(true);
    };
    const handleMenuClose = () => {
        setOpenMenu(false);
    };
    const activateBusyState = () => {
        isLoading.current = true;
        PubSub.publish('SHOW.BUSY');
    };
    const deactivateBusyState = () => {
        isLoading.current = false;
        queueCallback(() => {
            PubSub.publish('HIDE.BUSY');
        });
    };

    useEffect(() => {
        setVisualDiffBaselines(project?.visualDiffBaselines);
    }, [project?.visualDiffBaselines]);

    useEffect(() => {
        PubSub.subscribe('SOLUTION-MANAGER', (_, {
            appName,
            config,
        }) => {
            if (appName !== solution) {
                return;
            }

            dispatch(showSolutionReviewer());
            queueCallback(() => {
                resetState.current = {};
                searchParams.forEach((value, key) => {
                    resetState.current[key] = value;
                });
                setState({
                    ...config,
                    [browserApiKeys.reviewer]: 1,
                });
                setShow(true);
            });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setIsBusy(!build?.data);
    }, [build?.data]);

    if (!solutionReviewer.show || !show) {
        return null;
    }

    const summaryCount = Object.values(summaryData?.summary || {}).reduce((acc, current) => acc + current, 0);
    const isAllStateActive = summaryCount === 0 || itemStateList.length === 0 || itemStateList.length === Object.keys(visualTestStates).length;
    const isEditable = state?.buildId === `${project?.latestBuild?.index}`;

    return (
        <>
            <Drawer
                open
                onClose={reset}
                actions={(
                    <>
                        {
                            activeItem && (
                                <IconButton ref={menuRef} size="large" onClick={handleMenuOpen}>
                                    <Icon icon={moreVerticalFill} width={40} height={40}/>
                                </IconButton>
                            )
                        }
                    </>
                )}
                isFullScreen
                title={translate(`${i18nContext}.label`)}
            >
                <BuildSelector
                    {...{
                        activateBusyState,
                        deactivateBusyState,
                        state,
                        callbacks: {
                            handleProjectIdChange: ({projectId}) => {
                                queueCallback(() => {
                                    setState({
                                        projectId,
                                        buildId: projects?.[projectId]?.latestBuild?.index,
                                        resultIds: [],
                                    });
                                });
                            },
                            handleBuildIdChange: ({buildId, state}) => {
                                queueCallback(() => {
                                    setState({
                                        projectId: state.projectId,
                                        buildId,
                                        resultIds: state.resultIds,
                                    });
                                });
                            },
                            handleURLChange: ({resultIds, state}) => {
                                setState({
                                    projectId: state.projectId,
                                    buildId: state.buildId,
                                    resultIds,
                                });
                            },
                        },
                        projects,
                        isDisabled: Boolean(activeItem),
                    }}
                />
                {
                    !isEditable && (
                        <Alert severity="warning" sx={{mb: 3, width: '100%'}} action={(
                            <Button
                                color="inherit"
                                variant="text"
                                size="large"
                                onClick={() => {
                                    setState({
                                        buildId: project?.latestBuild?.index,
                                    });
                                }}
                            >
                                {translate('project.build.latestBuild')}
                            </Button>
                        )}>
                            {translate('project.wizardSteps.solutionsConfigs.groups.e2eVisualTests.reviewer.expiredBuild')}
                        </Alert>
                    )
                }
                {
                    isBusy ? (
                        <LoadingScreen
                            sx={{
                                py: 6,
                            }}
                        />
                    ) : (
                        <>
                            {
                                [
                                    BUILD_STATES.success,
                                    BUILD_STATES.cancel,
                                    BUILD_STATES.mixed,
                                ].includes(build?.data?.state) ? (
                                    <>
                                        {
                                            isNonEmptyArray(visualDiffItemList) && visualDiffItemList.length !== count && !activeItem && (
                                                <Alert
                                                    severity="info"
                                                    action={(
                                                        <Button
                                                            variant="outlined"
                                                            size="large"
                                                            onClick={() => {
                                                                setItemStateList([]);
                                                                setState({
                                                                    itemState: null,
                                                                    resultIds: state?.resultIds?.length > 1 ? [state?.resultIds?.[0]] : state?.resultIds,
                                                                });
                                                            }}>
                                                            {translate('resetFilters')}
                                                        </Button>
                                                    )}>
                                                    {translate('project.wizardSteps.solutionsConfigs.groups.e2eVisualTests.captured', {
                                                        count,
                                                    })}
                                                </Alert>
                                            )
                                        }
                                        <VisualDiffHeader
                                            {...{
                                                activeItem, setActiveItem,
                                                view, setView,
                                                itemStateList, setItemStateList,
                                                selectedData, setSelectedData,
                                                summaryData,
                                                itemList: visualDiffItemList,
                                                updateScreenshotState,
                                                showColorDiffs, setShowColorDiffs,
                                                showMarkerDiffs, setShowMarkerDiffs,
                                                markerColor, setMarkerColor,
                                                markerPadding, setMarkerPadding,
                                                markerThickness, setMarkerThickness,
                                                groupItems,
                                                showToolbox,
                                                isAllStateActive,
                                                isEditable,
                                            }} />
                                        {
                                            activeItem && (
                                                <Stack
                                                    direction="row"
                                                    flexWrap="wrap"
                                                    alignItems="center"
                                                    spacing={1}
                                                    sx={{
                                                        pb: 3,
                                                    }}
                                                >
                                                    <Breadcrumbs>
                                                        {
                                                            [...activeItem?.context, activeItem?.item?.labellingData?.label].map((item) => (
                                                                <Box component="span" sx={{textDecoration: 'underline'}} key={item}>{item}</Box>
                                                            ))
                                                        }
                                                        <Label color={e2eVisualTestsBuildStates[activeItem?.currentState]?.color}>
                                                            {translate(`project.wizardSteps.solutionsConfigs.groups.e2eVisualTests.labels.states.${activeItem?.currentState}.label`)}
                                                        </Label>
                                                    </Breadcrumbs>
                                                </Stack>
                                            )
                                        }
                                        {
                                            !isLoading.current && !state?.resultIds?.[0] && (
                                                <Alert severity="warning" sx={{mb: 3}}>
                                                    {isLoading.current}
                                                    {translate('project.build.result.noUrlSelectedInfo')}
                                                </Alert>
                                            )
                                        }
                                        {
                                            data && (
                                                <>
                                                    {
                                                        visualDiffItemList.length > 0 ? (
                                                            <>
                                                                {
                                                                    activeItem ? (
                                                                        <VisualDiffActiveItem
                                                                            {...{
                                                                                buildId: state.buildId,
                                                                                activeItem,
                                                                                view,
                                                                                showColorDiffs,
                                                                                showMarkerDiffs,
                                                                                markerColor,
                                                                                markerPadding,
                                                                                markerThickness,
                                                                            }}
                                                                        />
                                                                    ) : (
                                                                        <>
                                                                            {
                                                                                groupItems
                                                                                    .map(({groupId, context, offset, size}, index) => {
                                                                                        const unfilteredVisualDiffItemList = [...visualDiffItemList].splice(offset, size);
                                                                                        const itemList = isNonEmptyArray(itemStateList) ?
                                                                                            unfilteredVisualDiffItemList.filter(({currentState}) => {
                                                                                                return isAllStateActive || itemStateList.includes(currentState);
                                                                                            }) : unfilteredVisualDiffItemList;

                                                                                        if (itemList.length < 1) {
                                                                                            return null;
                                                                                        }

                                                                                        const selectedItems = itemList.filter(({item}) => {
                                                                                            return (selectedData[groupId] || []).includes(item.data.result.id);
                                                                                        });
                                                                                        const isChecked = selectedItems.length === itemList.length;
                                                                                        const isIndeterminate = !isChecked && selectedItems.length > 0;
                                                                                        const hasSelectableItem = itemList.some(({item}) => {
                                                                                            return item.data.success;
                                                                                        });

                                                                                        return (
                                                                                            <Stack
                                                                                                key={`${groupId}-${index}`}
                                                                                                spacing={2}
                                                                                                sx={{
                                                                                                    [`& ~ .${noHitsClass}`]: {
                                                                                                        display: 'none',
                                                                                                    },
                                                                                                }}>
                                                                                                <Stack direction="row" alignItems="center"
                                                                                                       justifyContent="space-between">
                                                                                                    <Breadcrumbs>
                                                                                                        {
                                                                                                            context.map((item) => (
                                                                                                                <Box component="span" sx={{textDecoration: 'underline'}} key={item}>{item}</Box>
                                                                                                            ))
                                                                                                        }
                                                                                                    </Breadcrumbs>
                                                                                                    {
                                                                                                        isEditable && hasSelectableItem &&
                                                                                                        <FormControlLabel
                                                                                                            sx={{minWidth: '130px'}}
                                                                                                            control={
                                                                                                                <Checkbox
                                                                                                                    indeterminate={isIndeterminate}
                                                                                                                    checked={isChecked}
                                                                                                                    onChange={(event) => {
                                                                                                                        const newList = event.target.checked ?
                                                                                                                            itemList.map((visualDiffItem) => visualDiffItem.item.data.result.id) :
                                                                                                                            [];
                                                                                                                        setSelectedData({
                                                                                                                            ...selectedData,
                                                                                                                            [groupId]: newList,
                                                                                                                        });
                                                                                                                    }}
                                                                                                                    size="large"
                                                                                                                />
                                                                                                            }
                                                                                                            label={translate(`${i18nContext}.marked`, {
                                                                                                                count: selectedItems.length,
                                                                                                            })}
                                                                                                        />
                                                                                                    }
                                                                                                </Stack>
                                                                                                <Grid container spacing={3} sx={{pr: 3}}>
                                                                                                    {
                                                                                                        itemList.map((visualDiffItem) => {
                                                                                                            return (
                                                                                                                <Grid item
                                                                                                                      xs={12}
                                                                                                                      sm={6}
                                                                                                                      md={4}
                                                                                                                      key={visualDiffItem.id}>
                                                                                                                    <VisualDiffItem
                                                                                                                        {...{
                                                                                                                            visualDiffItem,
                                                                                                                            setActiveItem,
                                                                                                                            selectedData,
                                                                                                                            setSelectedData,
                                                                                                                            updateScreenshotState,
                                                                                                                            isEditable,
                                                                                                                        }}
                                                                                                                    />
                                                                                                                </Grid>
                                                                                                            );
                                                                                                        })
                                                                                                    }
                                                                                                </Grid>
                                                                                            </Stack>
                                                                                        );
                                                                                    })
                                                                            }
                                                                            <Typography
                                                                                className={noHitsClass}
                                                                                component="div"
                                                                                variant="subtitle2"
                                                                                sx={{
                                                                                    color: 'text.secondary',
                                                                                    height: '10rem',
                                                                                    display: 'flex',
                                                                                    textAlign: 'center',
                                                                                    alignItems: 'center',
                                                                                    flexDirection: 'column',
                                                                                    justifyContent: 'center',
                                                                                }}
                                                                                paragraph>
                                                                                {translate(`${i18nContext}.activeFilterNoHits`)}
                                                                                <div>
                                                                                    <Button
                                                                                        sx={{
                                                                                            color: 'grey.800',
                                                                                            display: 'flex',
                                                                                            alignItems: 'center',
                                                                                        }}
                                                                                        size="large"
                                                                                        onClick={() => {
                                                                                            setItemStateList([]);
                                                                                        }}>
                                                                                        {translate(`${i18nContext}.resetFilter`)}
                                                                                    </Button>
                                                                                </div>
                                                                            </Typography>
                                                                        </>
                                                                    )
                                                                }
                                                            </>
                                                        ) : (
                                                            <Alert severity="warning" sx={{mb: 3}}>
                                                                {translate(`project.wizardSteps.solutionsConfigs.groups.${solutions.e2eVisualTests.key}.captured`, {count: 0})}
                                                            </Alert>
                                                        )
                                                    }
                                                </>
                                            )
                                        }
                                    </>
                                ) : (
                                    <Alert severity="error">
                                        {translate(`project.build.states.${build?.data?.state}.description`)}
                                    </Alert>
                                )
                            }
                        </>
                    )
                }
            </Drawer>
            <MenuPopover
                open={openMenu}
                onClose={handleMenuClose}
                anchorEl={menuRef.current}
                sx={{width: 250}}>
                <MenuItem>
                    <FormControlLabel
                        control={<Switch
                            checked={showToolbox}
                            onChange={(event) => setShowToolbox(event.target.checked)}
                        />}
                        label={translate(`${i18nContext}.itemView.options.labelCTA`)}/>
                </MenuItem>
            </MenuPopover>
        </>
    );
};

ProjectBuildReviewerE2EVisualTests.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildReviewerE2EVisualTests);
