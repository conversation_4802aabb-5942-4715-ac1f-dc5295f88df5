import {useEffect, useRef, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>alogActions, DialogContent, DialogContentText, DialogTitle} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import PubSub from 'pubsub-js';
import {DialogAnimate} from '../../../components/animate';
import useLocales from '../../../hooks/useLocales';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import useAuth from '../../../hooks/useAuth';

const LanguageChangeHandler = () => {
    const {translate, currentLang} = useLocales();
    const [show, setShow] = useState(false);
    const langRef = useRef();
    const apiCaller = useApiCaller();
    const {user, updateProfile} = useAuth();

    const updateUserLanguage = () => {
        apiCaller({
            uri: getAPIPath(api.updateUserData),
            data: {
                preferredLanguage: currentLang.value,
            },
            successCallback: () => {
                updateProfile({preferredLanguage: currentLang.value});
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate('saved'),
                    variant: 'success',
                });
            },
            alwaysCallback: () => {
                setShow(false);
            },
        });
    };

    useEffect(() => {
        if (!langRef.current || user.preferredLanguage === currentLang.value) {
            langRef.current = currentLang.value;
            return;
        }

        PubSub.publish('HIDE.NOTIFICATIONS.ALL');
        PubSub.publish('HIDE.DIALOG.ALL');
        PubSub.publish('HIDE.MODAL.ALL');
        setShow(true);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentLang.value, user.preferredLanguage]);

    return (
        <DialogAnimate
            fullWidth
            maxWidth="xs"
            open={show}
            onClose={() => {
                setShow(false);
            }}>
            <DialogTitle>{translate('users.profile.languageChange.label')}</DialogTitle>
            <DialogContent sx={{mt: 3}}>
                <DialogContentText>
                    {translate('users.profile.languageChange.description')}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={() => {
                    setShow(false);
                }} color="inherit">
                    {translate('no')}
                </Button>
                <Button onClick={updateUserLanguage} variant="contained">
                    {translate('yes')}
                </Button>
            </DialogActions>

        </DialogAnimate>
    );
};

export default LanguageChangeHandler;
