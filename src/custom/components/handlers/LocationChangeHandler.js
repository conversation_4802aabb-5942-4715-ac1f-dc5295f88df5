import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import {useLocation} from 'react-router-dom';

const LocationChangeHandler = () => {
    const location = useLocation();

    useEffect(() => {
        PubSub.publish('LOCATION.CHANGED', {location});
        PubSub.publish('HIDE.MODAL.ALL');
        PubSub.publish('HIDE.DIALOG.ALL');
        PubSub.publish('HIDE.NOTIFICATIONS.ALL');
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location?.pathname, location.search, location.hash]);

    return null;
};

export default LocationChangeHandler;
