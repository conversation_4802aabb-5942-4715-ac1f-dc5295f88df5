import {useEffect} from 'react';
import PubSub from 'pubsub-js';

const ScrollHandler = () => {
    useEffect(() => {
        PubSub.subscribe('SCROLL.TO.ELEMENT', (_, data) => {
            if (!data?.selector) {
                return;
            }

            const element = document.querySelector(data?.selector);
            if (!element) {
                return;
            }

            element.scrollIntoView({block: 'start', behavior: 'smooth'});
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return null;
};

export default ScrollHandler;
