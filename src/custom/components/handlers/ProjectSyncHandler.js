import PubSub from 'pubsub-js';
import {useEffect, useRef} from 'react';
import {useSnackbar} from 'notistack5';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {STATES as userStates} from '@w7-3/webeagle-resources/dist/config/users';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import callValidFunction from '@w7-3/webeagle-resources/dist/libs/callValidFunction';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import useAuth from '../../../hooks/useAuth';
import {updateProjectResult, updateProjects} from '../../../redux/slices/state';
import {useDispatch} from '../../../redux/store';
import useLocales from '../../../hooks/useLocales';

const ProjectSyncHandler = () => {
    const apiCaller = useApiCaller();
    const {user} = useAuth();
    const dispatch = useDispatch();
    const {enqueueSnackbar} = useSnackbar();
    const {translate} = useLocales();
    const isLoaded = useRef(false);
    const isVerifiedUser = user?.state === userStates.VERIFIED;
    const fetchProjects = ({
        includeDetails,
        filterIdList = [],
        filterStateList,
        callback,
        isAnimated,
    }) => {
        apiCaller({
            uri: getAPIPath(api.projects),
            data: {
                includeDetails,
                filterIdList,
                filterStateList,
            },
            successCallback: (result) => {
                const projects = result.data?.projects || {};

                if (typeof callback === 'function') {
                    queueCallback(() => {
                        callback(result?.data);
                    });
                }

                dispatch(updateProjects(projects));
            },
            isAnimated,
        });
    };

    useEffect(() => {
        if (!isVerifiedUser || isLoaded.current) {
            return;
        }

        isLoaded.current = true;
        window.webAutomate.busyEvents = {};
        /* @todo implement project build cancel
        PubSub.subscribe('PROJECT_CANCEL', (EVENT, {
            projectId,
            buildId,
        }) => {
            window.webAutomate.busyEvents[EVENT] = true;
            apiCaller({
                uri: getAPIPath(api.projectBuildCancel),
                data: {
                    projectId,
                    buildId,
                },
                alwaysCallback: () => {
                    fetchProjects({
                        includeDetails: true,
                        filterIdList: [projectId],
                    });
                    window.webAutomate.busyEvents[EVENT] = false;
                },
            });
        });
        */

        PubSub.subscribe('PROJECT_LOAD', (EVENT, {includeDetails, filterIdList, filterStateList, preRequest, callback, isAnimated}) => {
            callValidFunction(preRequest);

            fetchProjects({
                includeDetails,
                filterIdList,
                filterStateList,
                callback,
                isAnimated,
            });
        });

        PubSub.subscribe('PROJECT_CHANGE_STATE', (EVENT, {project, state}) => {
            window.webAutomate.busyEvents[EVENT] = true;
            window.webAutomate.pendingEvent = {
                EVENT,
                payload: {project, state},
            };
            apiCaller({
                uri: getAPIPath(api.projectStateChange),
                data: {
                    projectId: project.id,
                    state,
                },
                successCallback: () => {
                    enqueueSnackbar(
                        translate('project.wizardSteps.summary.notifications.updateSuccess', {
                            name: project?.configData?.projectName,
                        }), {
                            variant: 'success',
                        });
                    fetchProjects({
                        includeDetails: true,
                        filterIdList: [project.id],
                    });
                },
                alwaysCallback: () => {
                    window.webAutomate.busyEvents[EVENT] = false;
                },
            });
        });

        PubSub.subscribe('PROJECT_FETCH_BUILD_RESULT_BY_ID', (EVENT, {projectId, buildId, resultIds, callback}) => {
            window.webAutomate.busyEvents[EVENT] = true;
            apiCaller({
                uri: getAPIPath(api.projectBuildResult),
                data: {
                    projectId,
                    buildId,
                    resultIds,
                },
                successCallback: (result) => {
                    dispatch(updateProjectResult({
                        projectId,
                        buildId,
                        resultIds,
                        data: result?.data?.data,
                    }));
                    if (typeof callback !== 'function') {
                        return;
                    }

                    queueCallback(() => {
                        callback(result?.data);
                    });
                },
                alwaysCallback: () => {
                    window.webAutomate.busyEvents[EVENT] = false;
                },
            });
        });

        const subPageResultAllCache = {};

        PubSub.subscribe('PROJECT_FETCH_BUILD_SUB-PAGE-RESULT_ALL', (EVENT, {projectId, buildId, preRequest, callback}) => {
            callValidFunction(preRequest);
            const requestKey = `${projectId}.${buildId}`;
            if (isNonEmptyArray(subPageResultAllCache[requestKey])) {
                subPageResultAllCache[requestKey].forEach(({resultIds, data}) => {
                    dispatch(updateProjectResult({
                        projectId,
                        buildId,
                        resultIds,
                        data,
                    }));
                });
                callValidFunction(callback, subPageResultAllCache[requestKey]);

                return;
            }

            window.webAutomate.busyEvents[EVENT] = true;
            apiCaller({
                uri: getAPIPath(api.projectBuildSubPageResultAll),
                data: {
                    projectId,
                    buildId,
                },
                successCallback: (result) => {
                    subPageResultAllCache[requestKey] = result?.data?.list;
                    callValidFunction(callback, subPageResultAllCache[requestKey]);

                    if (!isNonEmptyArray(subPageResultAllCache[requestKey])) {
                        return;
                    }

                    subPageResultAllCache[requestKey].forEach(({resultIds, data}) => {
                        dispatch(updateProjectResult({
                            projectId,
                            buildId,
                            resultIds,
                            data,
                        }));
                    });
                },
                alwaysCallback: () => {
                    window.webAutomate.busyEvents[EVENT] = false;
                },
            });
        });

        // eslint-disable-next-line
    }, [isVerifiedUser, isLoaded]);

    return null;
};

export default ProjectSyncHandler;
