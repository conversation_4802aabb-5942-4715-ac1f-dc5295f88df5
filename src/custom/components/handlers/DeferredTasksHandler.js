/* eslint-disable no-continue */
import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {sessionStorageKeys} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {getSessionStorageItem} from '../../utils/storage';
import useAuth from '../../../hooks/useAuth';

export const TASK_TYPES = {
    notification: 'notification',
    notifyUnauthenticated: 'notifyUnauthenticated',
};

export const getDeferredTasks = (type) => {
    const messages = {};
    for (let i = 0; i < sessionStorage.length; i += 1) {
        const key = sessionStorage.key(i);

        if (key.indexOf(`${sessionStorageKeys.deferredTasks}_`) !== 0) {
            continue;
        }

        const value = getSessionStorageItem(key);

        if (!value || (type && value.type !== type)) {
            continue;
        }

        messages[key] = value;
    }

    return messages;
}

export const hasDeferredTask = (task) => {
    return Object.values(getDeferredTasks(task)).some((item) => {
        return (
            task.id === item.id &&
            task.type === item.type
        );
    });
};

export const setDeferredTask = ({
    type,
    options,
}) => {
    const id = `${sessionStorageKeys.deferredTasks}_${Date.now()}`;
    window.sessionStorage?.setItem(id, JSON.stringify({
        id,
        type,
        options,
    }));

    return id;
};

export const consumeDeferredTask = (handler, type) => {
    Object.entries(getDeferredTasks(type)).forEach(([key, data]) => {
        queueCallback(() => {
            handler(data);
        }, 1000);
        sessionStorage.removeItem(key);
    });
};

const DeferredTasksHandler = () => {
    const {isAuthenticated} = useAuth();

    useEffect(() => {
        consumeDeferredTask((item) => {
            PubSub.publish('SHOW.NOTIFICATION', item.options);
        }, TASK_TYPES.notification);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        consumeDeferredTask((item) => {
            if (isAuthenticated) {
                return;
            }

            PubSub.publish('SHOW.NOTIFICATION', item.options);
        }, TASK_TYPES.notifyUnauthenticated);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated]);

    return null;
};

export default DeferredTasksHandler;
