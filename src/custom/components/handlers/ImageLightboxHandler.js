import {useEffect, useState} from 'react';
import {findIndex} from 'lodash';
import PubSub from 'pubsub-js';
import LightboxModal from '../../../components/LightboxModal';
import useLocales from '../../../hooks/useLocales';

const ImageLightboxHandler = () => {
    const {translate} = useLocales();
    const [openLightbox, setOpenLightbox] = useState(false);
    const [photoIndex, setPhotoIndex] = useState(null);
    const [imageList, setImageList] = useState([]);
    const [captionList, setCaptionList] = useState([]);

    useEffect(() => {
        PubSub.subscribe('SHOW.LIGHTBOX.IMAGES', (_, data) => {
            const index = findIndex(data.imageList, (url) => url === data.initialUrl);
            setPhotoIndex(index);
            setImageList(data.imageList);
            setCaptionList(data.captionList);
            setOpenLightbox(true);
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <LightboxModal
            images={imageList}
            photoIndex={photoIndex}
            setPhotoIndex={setPhotoIndex}
            isOpen={openLightbox}
            onClose={() => setOpenLightbox(false)}
            imageLoadErrorMessage={translate('imageLoadErrorMessage')}
            imageCaption={captionList?.[photoIndex]}
        />
    );
};

export default ImageLightboxHandler;
