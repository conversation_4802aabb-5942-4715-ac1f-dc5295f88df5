import {useEffect, useRef, useState, Fragment} from 'react';
import PubSub from 'pubsub-js';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import getParsedJSON from '@w7-3/webeagle-resources/dist/libs/getParsedJSON';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {Alert, Box, Breadcrumbs, CardActionArea, Skeleton, Stack, Typography} from '@material-ui/core';
import Slider from 'react-slick';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {alpha} from '@material-ui/core/styles';
import {LazyLoadImage} from 'react-lazy-load-image-component';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {browserApiKeys, urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../hooks/useLocales';
import {hideSolutionReviewer, showSolutionReviewer} from '../../../redux/slices/state';
import {useDispatch} from '../../../redux/store';
import {CarouselControlsArrowsIndex} from '../../../components/carousel';
import Drawer from '../forms/Drawer';
import BuildSelector from '../utils/BuildSelector';
import useSolutionHandler from './project-build-reviewer/screenshots/hooks/useScreenshotsHandler';
import {getCDNPath} from '../../utils/getPath';
import ExternalLink from '../utils/ExternalLink';
import LoadingScreen from '../../../components/LoadingScreen';

const solution = solutions.screenshots.key;
const i18nContext = `project.wizardSteps.solutionsConfigs.groups.${solution}.reviewer`;

const mapStateToProps = ({state}) => {
    const {
        projects: allProjects,
        solutionReviewer,
    } = state;

    const projects = {};
    Object.values(allProjects || {}).forEach((project) => {
        if (project?.configData?.solution?.appName !== solution) {
            return;
        }

        projects[project.id] = project;
    });

    return {
        projects,
        solutionReviewer,
    };
};

const propTypes = {
    projects: PropTypes.object,
    solutionReviewer: PropTypes.object,
};

const ProjectBuildReviewerScreenshots = ({projects, solutionReviewer}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const [searchParams, updateSearchParams] = useSearchParams();
    const [currentIndex, setCurrentIndex] = useState(0);
    const [show, setShow] = useState(false);
    const slider = useRef(null);
    const isLoading = useRef(false);
    const resetState = useRef(null);
    const navigate = useNavigate();
    const [isBusy, setIsBusy] = useState(true);
    const state = {
        projectId: searchParams.get(urlParameters.solutionHandler.projectId),
        buildId: searchParams.get(urlParameters.solutionHandler.buildId),
        resultIds: getParsedJSON(searchParams.get(urlParameters.resultIds) || '[]'),
    };
    const project = projects?.[state?.projectId];
    const build = project?.results?.[state?.buildId];
    const rootData = build?.solutionResults?.[state?.resultIds?.[0]];
    const subData = rootData?.solutionResults?.[state?.resultIds?.[1]];
    const data = state?.resultIds?.length > 1 ? subData : rootData;

    const {
        solutionItemList,
    } = useSolutionHandler({
        data,
        state,
    });

    const setState = (newParams) => {
        if (!isNonEmptyObject(newParams)) {
            return;
        }

        Object.entries(newParams).forEach(([keyRaw, valueRaw]) => {
            const key = urlParameters.solutionHandler[keyRaw] || urlParameters[keyRaw];
            if (!key) {
                return;
            }

            if (!valueRaw) {
                searchParams.delete(key);
                return;
            }

            const value = typeof valueRaw === 'string' ? valueRaw : JSON.stringify(valueRaw);

            searchParams.set(key, value);
        });

        updateSearchParams(searchParams);
    };

    const handlePrevious = () => {
        slider.current.slickPrev();
    };

    const handleNext = () => {
        slider.current.slickNext();
    };

    const reset = () => {
        const {
            projectId,
            buildId,
        } = state;
        navigate(`${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`);
        dispatch(hideSolutionReviewer());
    };

    const activateBusyState = () => {
        isLoading.current = true;
        PubSub.publish('SHOW.BUSY');
    };
    const deactivateBusyState = () => {
        isLoading.current = false;
        queueCallback(() => {
            PubSub.publish('HIDE.BUSY');
        });
    };

    useEffect(() => {
        PubSub.subscribe('SOLUTION-MANAGER', (_, {
            appName,
            config,
        }) => {
            if (appName !== solution) {
                return;
            }

            dispatch(showSolutionReviewer());
            queueCallback(() => {
                resetState.current = {};
                searchParams.forEach((value, key) => {
                    resetState.current[key] = value;
                });
                setState({
                    ...config,
                    [browserApiKeys.reviewer]: 1,
                });
                setShow(true);
            });
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setIsBusy(!build?.data);
        setCurrentIndex(0);
    }, [build?.data]);

    if (!solutionReviewer.show || !show) {
        return null;
    }

    return (
        <Drawer
            open
            onClose={reset}
            isFullScreen
            title={(
                <Typography variant="subtitle1">
                    {translate(`${i18nContext}.label`)}
                </Typography>
            )}
        >
            <BuildSelector
                {...{
                    activateBusyState,
                    deactivateBusyState,
                    state,
                    callbacks: {
                        handleProjectIdChange: ({projectId}) => {
                            queueCallback(() => {
                                setState({
                                    projectId,
                                    buildId: projects?.[projectId]?.latestBuild?.index,
                                    resultIds: [],
                                });
                            });
                        },
                        handleBuildIdChange: ({buildId, state}) => {
                            queueCallback(() => {
                                setState({
                                    projectId: state.projectId,
                                    buildId,
                                    resultIds: state.resultIds,
                                });
                            });
                        },
                        handleURLChange: ({resultIds, state}) => {
                            setState({
                                projectId: state.projectId,
                                buildId: state.buildId,
                                resultIds,
                            });
                        },
                    },
                    projects,
                }}
            />
            {
                isBusy ? (
                    <LoadingScreen
                        sx={{
                            py: 6,
                        }}
                    />
                ) : (
                    <>
                        {
                            [
                                BUILD_STATES.success,
                                BUILD_STATES.cancel,
                                BUILD_STATES.mixed,
                            ].includes(build?.data?.state) ? (
                                <>
                                    {
                                        !state?.resultIds?.[0] && (
                                            <Alert severity="warning" sx={{mb: 3}}>
                                                {translate('project.build.result.noUrlSelectedInfo')}
                                            </Alert>
                                        )
                                    }
                                    {
                                        state?.resultIds?.[0] && (
                                            <>
                                                {
                                                    solutionItemList.length === 0 ? (
                                                        <Alert severity="warning">
                                                            {translate(`project.wizardSteps.solutionsConfigs.groups.${solution}.captured`, {
                                                                count: 0,
                                                            })}
                                                        </Alert>
                                                    ) : (
                                                        <Box sx={{
                                                            '& .slick-slide': {
                                                                float: (theme) => theme.direction === 'rtl' ? 'right' : 'left'
                                                            }
                                                        }}>
                                                            <Box
                                                                sx={{
                                                                    zIndex: 0,
                                                                    borderRadius: 2,
                                                                    overflow: 'hidden',
                                                                    position: 'relative'
                                                                }}
                                                            >
                                                                <Slider
                                                                    {...{
                                                                        dots: false,
                                                                        arrows: false,
                                                                        slidesToShow: 1,
                                                                        draggable: false,
                                                                        slidesToScroll: 1,
                                                                        adaptiveHeight: true,
                                                                        beforeChange: (current, next) => setCurrentIndex(next)
                                                                    }}
                                                                    ref={slider}>
                                                                    {solutionItemList.map(({item, context}) => {
                                                                        if (!item?.success) {
                                                                            return null;
                                                                        }

                                                                        const src = getCDNPath(item?.data?.destination)
                                                                        return (
                                                                            <Fragment key={src}>
                                                                                <Breadcrumbs>
                                                                                    {
                                                                                        context.map((label) => (
                                                                                            <Box component="span" sx={{
                                                                                                textDecoration: 'underline',
                                                                                                display: 'block',
                                                                                                my: 1,
                                                                                            }} key={label}>{label}</Box>
                                                                                        ))
                                                                                    }
                                                                                </Breadcrumbs>
                                                                                <CardActionArea
                                                                                    sx={{
                                                                                        p: 3,
                                                                                        borderRadius: 1,
                                                                                        height: '60vh',
                                                                                        color: 'primary.main',
                                                                                        bgcolor: 'background.neutral',
                                                                                        border: (theme) => `solid 3px ${alpha(theme.palette.primary.dark, 0.24)}`,
                                                                                    }}
                                                                                    onClick={() => {
                                                                                        window.open(src, '_blank');
                                                                                    }}
                                                                                >
                                                                                    <div>
                                                                                        <LazyLoadImage
                                                                                            alt={src}
                                                                                            src={src}
                                                                                            effect="blur"
                                                                                            width="100%"
                                                                                            height="100%"
                                                                                            placeholder={(
                                                                                                <Skeleton
                                                                                                    width="100%"
                                                                                                    height="60vh"
                                                                                                    variant="rectangular" sx={{
                                                                                                    borderRadius: 2,
                                                                                                    mb: 5,
                                                                                                }}/>
                                                                                            )}
                                                                                            style={{
                                                                                                margin: 'auto',
                                                                                                width: 'auto',
                                                                                            }} />
                                                                                    </div>
                                                                                </CardActionArea>
                                                                            </Fragment>
                                                                        );
                                                                    })}
                                                                </Slider>
                                                                <CarouselControlsArrowsIndex
                                                                    index={currentIndex}
                                                                    total={solutionItemList.length}
                                                                    onNext={handleNext}
                                                                    onPrevious={handlePrevious}
                                                                />
                                                            </Box>
                                                            <Stack direction="row" alignItems="center">
                                                                <ExternalLink
                                                                    url={getCDNPath(solutionItemList?.[currentIndex]?.item?.data?.destination)}
                                                                    rootProps={{
                                                                        sx: {
                                                                            maxWidth: '100%',
                                                                        },
                                                                    }}
                                                                />
                                                            </Stack>
                                                        </Box>
                                                    )
                                                }
                                            </>
                                        )
                                    }
                                </>
                            ) : (
                                <Alert severity="error">
                                    {translate(`project.build.states.${build?.data?.state}.description`)}
                                </Alert>
                            )
                        }
                    </>
                )
            }
        </Drawer>
    );
};

ProjectBuildReviewerScreenshots.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildReviewerScreenshots);
