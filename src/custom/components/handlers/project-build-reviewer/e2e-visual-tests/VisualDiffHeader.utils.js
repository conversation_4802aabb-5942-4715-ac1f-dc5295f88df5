import PropTypes from 'prop-types';
import {visualTestStates} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import useLocales from '../../../../../hooks/useLocales';
import MoreMenuButton from '../../../forms/MoreMenuButton';

const i18nContextE2eVisualTests = 'project.wizardSteps.solutionsConfigs.groups.e2eVisualTests';
const i18nContext = `${i18nContextE2eVisualTests}.reviewer`;

export const VisualDiffHeaderOptions = ({
    selectedData,
    selectedItems,
    groupItems,
    itemList,
    setSelectedData,
    allNewOrChangedItems,
}) => {
    const {translate} = useLocales();
    const isAllNewOrChangedSelected = allNewOrChangedItems.length > 0 && allNewOrChangedItems.every(({item}) => {
        return Object.values(selectedData).some((list) => {
            return (list || []).includes(item.data.result.id);
        });
    });

    return (
        <MoreMenuButton additionalItems={[
            {
                label: translate(`${i18nContext}.selectAll`),
                callback: () => {
                    const newSelectedData = {};
                    groupItems
                        .forEach(({groupId, offset, size}) => {
                            const list = [...itemList || []].splice(offset, size).map((visualDiffItem) => visualDiffItem.item.data.result.id);

                            if (list.length < 1) {
                                return;
                            }

                            newSelectedData[groupId] = list;
                        });

                    setSelectedData(newSelectedData);
                },
                disabled: selectedItems.length === itemList.length,
            },
            {
                label: translate(`${i18nContext}.selectAllNewOrChanged`),
                callback: () => {
                    const newSelectedData = {};
                    groupItems
                        .forEach(({groupId, offset, size}) => {
                            const list = [...itemList || []].splice(offset, size)
                                .filter(({currentState}) => {
                                    return ([
                                        visualTestStates.new,
                                        visualTestStates.changed,
                                    ].includes(currentState));
                                })
                                .map((visualDiffItem) => visualDiffItem.item.data.result.id);

                            if (list.length < 1) {
                                return;
                            }

                            newSelectedData[groupId] = list;
                        });
                    setSelectedData(newSelectedData);
                },
                disabled: isAllNewOrChangedSelected,
            },
            {
                label: translate(`${i18nContext}.deselectAll`),
                callback: () => {
                    setSelectedData({});
                },
            }
        ]}/>
    );
};

VisualDiffHeaderOptions.propTypes = {
    selectedData: PropTypes.object,
    setSelectedData: PropTypes.func,
    selectedItems: PropTypes.array,
    groupItems: PropTypes.array,
    itemList: PropTypes.array,
    allNewOrChangedItems: PropTypes.array,
}
