import {useMemo} from 'react';
import {visualTestStates} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {isNonEmptyArray, isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../../../hooks/useLocales';
import {getLabel} from '../../../../../utils/projectSolutionStepItemLabel';

export default ({
    data,
    visualDiffBaselines,
    state,
}) => {
    const {translate} = useLocales();
    const {
        groupItems,
        visualDiffItemList,
        summaryData,
        activeItem,
    } = useMemo(() => {
        const groupItems = [];
        const visualDiffItemList = [];
        const summaryData = {
            count: 0,
            summary: {},
        };

        if (isNonEmptyObject(data)) {
            Object.values(visualTestStates).forEach((state) => {
                summaryData.summary[state] = 0;
            });

            const todoList = [];
            let offset = 0;
            const processItem = (todoItem) => {
                const {solutionData} = todoItem;

                if (!solutionData) {
                    return;
                }

                const itemList = [];
                const parents = todoItem?.parents || [];
                const context = todoItem?.context || [];
                const groupId = [...parents.map(({id}) => id), solutionData.id].join('');

                if (solutionData.stepType !== solutionStepTypes.root) {
                    context.push(getLabel({translate, itemResult: solutionData}));
                }

                if (!isNonEmptyArray(solutionData?.data?.stepListResults)) {
                    return;
                }

                solutionData.data.stepListResults.forEach((itemRaw) => {
                    const item = JSON.parse(JSON.stringify(itemRaw));
                    if (item?.stepType === solutionStepTypes.solution) {
                        if (!item?.data?.result?.id) {
                            return;
                        }

                        const baselineRecords = visualDiffBaselines?.[item?.data?.result?.id] || {};
                        const itemBaseline = baselineRecords?.[state?.buildId];
                        let buildIndex = state?.buildId - 1;
                        let currentBaseline = null;

                        while (buildIndex > -1 && !currentBaseline) {
                            if (baselineRecords[buildIndex]?.data?.state === visualTestStates.accepted) {
                                currentBaseline = baselineRecords[buildIndex];
                            }

                            buildIndex -= 1;
                        }

                        const currentState = itemBaseline?.data?.state || item?.data?.result?.state;
                        itemList.push({item, context: [...context], itemBaseline, currentBaseline, currentState});
                        summaryData.summary[currentState] += 1;
                        summaryData.count += 1;
                    }

                    if (!isNonEmptyArray(item?.data?.stepListResults)) {
                        return;
                    }

                    todoList.push({
                        solutionData: item,
                        context: [...context],
                        parents: [
                            ...parents,
                            {
                                id: solutionData.id,
                            }
                        ],
                    });
                });
                const size = itemList.length;

                groupItems.push({
                    groupId,
                    context: [...context],
                    offset,
                    size,
                });

                visualDiffItemList.push(...itemList.map(({item, context, itemBaseline, currentBaseline, currentState}, index) => {
                    return {
                        id: item.id,
                        itemIndex: offset + index,
                        item,
                        itemBaseline,
                        currentBaseline,
                        currentState,
                        context,
                        groupId,
                    };
                }));

                offset += size;
            };

            const {solutionData, requestedUrl, solutionResults} = data;

            todoList.push({
                solutionData,
                context: [requestedUrl],
                parents: [],
            });

            if (isNonEmptyObject(solutionResults)) {
                Object.values(solutionResults).forEach((item) => {
                    if (!item?.solutionData) {
                        return;
                    }

                    todoList.push({
                        solutionData: item.solutionData,
                        context: [requestedUrl, item.requestedUrl],
                        parents: [solutionData.id],
                    });
                });
            }

            let todoItem = todoList.shift();
            do {
                processItem(todoItem);
                todoItem = todoList.shift();
            } while (todoList.length > 0);
        }

        const activeItem = visualDiffItemList
            .find((visualDiffItem) => visualDiffItem?.item?.data?.result?.id === state?.activeItemId);

        return {
            groupItems,
            visualDiffItemList,
            summaryData,
            activeItem,
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state?.activeItemId, data, visualDiffBaselines, state?.buildId]);

    return {
        groupItems,
        visualDiffItemList,
        summaryData,
        activeItem,
    };
}
