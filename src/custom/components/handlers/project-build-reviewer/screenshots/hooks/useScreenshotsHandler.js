import {useMemo} from 'react';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import {isNonEmptyArray, isNonEmptyObject, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../../../hooks/useLocales';
import {getLabel} from '../../../../../utils/projectSolutionStepItemLabel';

export default ({
    data,
    state,
}) => {
    const {translate} = useLocales();
    const {
        solutionItemList,
    } = useMemo(() => {
        const solutionItemList = [];

        if (isNonEmptyObject(data)) {
            const todoList = [];
            let offset = 0;
            const processItem = (todoItem) => {
                const solutionData = todoItem?.solutionData;
                const itemList = [];
                const context = todoItem?.context || [];

                if (solutionData?.stepType !== solutionStepTypes.root) {
                    context.push(getLabel({translate, itemResult: solutionData}));
                }

                if (isNonEmptyArray(solutionData?.data?.stepListResults)) {
                    solutionData.data.stepListResults.forEach((itemRaw) => {
                        if (itemRaw?.stepType === solutionStepTypes.solution && isNonEmptyArray(itemRaw?.data?.result)) {
                            itemRaw.data.result.forEach((item, index) => {
                                if (item.success && isNonEmptyString(item?.data?.destination)) {
                                    itemList.push({
                                        itemRaw,
                                        item,
                                        context: [
                                            ...context,
                                            itemRaw?.labellingData?.label,
                                            `${index + 1} / ${itemRaw.data.result.length}`,
                                    ]});
                                }
                            });

                        }

                        if (!isNonEmptyArray(itemRaw?.data?.stepListResults)) {
                            return;
                        }

                        todoList.push({
                            solutionData: itemRaw,
                            context: [...context],
                        });
                    });
                }
                const size = itemList.length;

                solutionItemList.push(...itemList.map(({itemRaw, item, context}, index) => {
                    return {
                        item,
                        itemRaw,
                        itemIndex: offset + index,
                        context,
                    };
                }));

                offset += size;
            };

            const {solutionData, requestedUrl, solutionResults} = data;
            todoList.push({
                solutionData,
                context: [requestedUrl],
                parents: [],
            });

            if (isNonEmptyObject(solutionResults)) {
                Object.values(solutionResults).forEach((item) => {
                    if (!item?.solutionData || isNonEmptyArray(state?.resultIds) && state?.resultIds?.[1] !== item?.id) {
                        return;
                    }

                    todoList.push({
                        solutionData: item.solutionData,
                        context: [requestedUrl, item.requestedUrl],
                        parents: [solutionData.id],
                    });
                });
            }

            while (todoList.length > 0) {
                const todoItem = todoList.shift();
                processItem(todoItem);
            }
        }

        return {
            solutionItemList,
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data, state?.buildId]);

    return {
        solutionItemList,
    };
}
