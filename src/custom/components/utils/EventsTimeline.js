import PropTypes from 'prop-types';
import {Al<PERSON>, Box, Button, Stack, Typography} from '@material-ui/core';
import {
    Timeline,
    TimelineConnector,
    TimelineContent,
    TimelineDot,
    TimelineItem,
    TimelineOppositeContent,
    TimelineSeparator,
} from '@material-ui/lab';
import {useEffect, useRef} from 'react';
import {Icon} from '@iconify/react';
import arrowDownwardOutline from '@iconify/icons-eva/arrow-downward-outline';
import arrowUpwardOutline from '@iconify/icons-eva/arrow-upward-outline';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import {InfoCodeColors} from '../../utils/infoCodes';
import ImagePreview from './ImagePreview';
import InfoCode from './InfoCode';

const propTypes = {
    eventList: PropTypes.arrayOf(PropTypes.object),
};

const EventsTimeline = ({
    eventList,
}) => {
    const {translate} = useLocales();
    const ref = useRef();
    useEffect(() => {
        if (!ref.current?.scrollTop) {
            return;
        }

        ref.current.scrollTop = ref.current.scrollHeight;
    }, [eventList.length]);

    if (eventList.length < 1) {
        return (
            <Alert severity="info">
                {translate('logs.noEvents')}
            </Alert>
        );
    }

    return (
        <>
            {
                eventList.length > 1 && (
                    <Stack
                        direction="row"
                        alignItems="center"
                        justifyContent="flex-end"
                        spacing={3}>
                        <Button
                            color="inherit"
                            startIcon={<Icon icon={arrowUpwardOutline} width={40} height={40} />}
                            onClick={() => {
                                ref.current.scrollTop = 0;
                            }}>
                            {translate('scroll.top')}
                        </Button>
                        <Button
                            color="inherit"
                            startIcon={<Icon icon={arrowDownwardOutline} width={40} height={40} />}
                            onClick={() => {
                                ref.current.scrollTop = ref.current.scrollHeight;
                            }}>
                            {translate('scroll.bottom')}
                        </Button>
                    </Stack>
                )
            }
            <Box
                ref={ref}
                sx={{
                    maxHeight: '40vh',
                    overflow: 'scroll',
                }}>
                <Timeline
                    position="right">
                    {eventList.map((infoCode, index) => {
                        const {level, timestamp, data} = infoCode;
                        const extension = (data?.url || '').split('.').pop();

                        return (
                            <TimelineItem key={`${timestamp}-${index}`}>
                                <TimelineOppositeContent sx={{display: 'none'}} />
                                <TimelineSeparator>
                                    <TimelineDot color={InfoCodeColors[level]}/>
                                    {index < (eventList.length - 1) && <TimelineConnector />}
                                </TimelineSeparator>
                                <TimelineContent sx={{maxWidth: '100%', mb: 3}}>
                                    <Typography sx={{color: 'text.secondary', mb: 1}}>
                                        {fDateTimeHumanReadable(timestamp)}
                                    </Typography>
                                    <InfoCode
                                        infoCode={infoCode}
                                        modalContent={
                                            [
                                                'png',
                                            ].includes(extension.toLowerCase()) &&
                                            <ImagePreview
                                                initialUrl={data?.url}
                                                sxConfig={{
                                                    img: {
                                                        maxHeight: 50,
                                                    },
                                                }}
                                                hideTitle
                                            />
                                        }
                                        isDismissible={false}
                                    />
                                </TimelineContent>
                            </TimelineItem>
                        );
                    })}
                </Timeline>
            </Box>
        </>
    )
};

EventsTimeline.propTypes = propTypes;

export default EventsTimeline;
