import {Button, IconButton, Tooltip, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    label: PropTypes.string,
    onClick: PropTypes.func,
    iconOnly: PropTypes.bool,
    iconProps: PropTypes.object,
};

const SettingItemChange = ({label, onClick, iconOnly, iconProps, ...rest}) => {
    const {translate} = useLocales();

    if (iconOnly) {
        return (
            <Tooltip title={translate('change')}>
                <IconButton
                    onClick={onClick}
                    color="primary"
                >
                    <Icon icon="eva:edit-fill" width={40} height={40} />
                </IconButton>
            </Tooltip>
        );
    }

    return (
        <Button
            sx={{
                display: 'inline-flex',
                alignItems: 'center',
                py: 0,
                mx: 0,
                ml: 1,
                my: 'auto',
                '& .MuiButton-startIcon': {
                    mr: 0,
                },
                ...rest.sx,
            }}
            onClick={onClick}
            startIcon={(
                <Icon
                    icon="eva:edit-fill"
                    width={40}
                    height={40}
                    {...iconProps}
                />
            )}
            {...rest}
        >
            <Typography sx={{textTransform: 'lowercase',}}>
                {label || translate('change')}
            </Typography>
        </Button>
    );
};

SettingItemChange.propTypes = propTypes;

export default SettingItemChange;
