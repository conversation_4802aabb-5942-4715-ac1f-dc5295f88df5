import {styled} from '@material-ui/core/styles';
import {Link} from '@material-ui/core';

const LinkStyle = styled(Link)(({theme}) => ({
    ...theme.typography.subtitle2,
    color: theme.palette.text.primary,
    marginRight: theme.spacing(5),
    transition: theme.transitions.create('opacity', {
        duration: theme.transitions.duration.shortest,
    }),
    '&:hover': {
        opacity: 0.48,
        textDecoration: 'none',
    },
}));

export default LinkStyle;
