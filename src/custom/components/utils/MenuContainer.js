import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {useEffect, useRef, useState} from 'react';
import {Button, List, ListItemButton, ListItemIcon, ListItemText, MenuItem} from '@material-ui/core';
import arrowIosDownwardOutline from '@iconify/icons-eva/arrow-ios-downward-outline';
import arrowIosUpwardOutline from '@iconify/icons-eva/arrow-ios-upward-outline';
import MenuPopover from '../../../components/MenuPopover';

const propTypes = {
    defaultValue: PropTypes.string,
    disabled: PropTypes.bool,
    optionList: PropTypes.arrayOf(PropTypes.shape({
        content: PropTypes.node,
        value: PropTypes.string,
        isDisabled: PropTypes.bool,
    })),
    onChange: PropTypes.func,
    isSelectMode: PropTypes.bool,
    disableSelection: PropTypes.bool,
    label: PropTypes.string,
    sx: PropTypes.object,
};

const defaultProps = {
    isSelectMode: true,
    disableSelection: true,
}

const MenuContainer = ({
    defaultValue,
    disabled,
    optionList,
    onChange,
    isSelectMode,
    disableSelection,
    label,
    sx,
}) => {
    const menuRef = useRef(null);
    const [open, setOpen] = useState(false);
    const [value, setValue] = useState(defaultValue);
    const handleOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };
    const handleMenuItemClick = (option) => {
        setValue(option.value);
        onChange(option.value);
        handleClose();
    };
    const activeOption = optionList.find((option) => option.value === value);

    useEffect(() => {
        setValue(defaultValue);
    }, [defaultValue]);

    return (
        <>
            {
                isSelectMode ? (
                    <List component="nav">
                        <ListItemButton
                            aria-haspopup="true"
                            ref={menuRef}
                            size="large"
                            onClick={handleOpen}
                            disabled={disabled}
                            sx={sx}
                        >
                            <ListItemText primary={activeOption?.content} />
                            <ListItemIcon>
                                <Icon icon={open ? arrowIosUpwardOutline : arrowIosDownwardOutline} width={40} height={40} />
                            </ListItemIcon>
                        </ListItemButton>
                    </List>
                ) : (
                    <Button
                        fullWidth
                        size="large"
                        onClick={handleOpen}
                        ref={menuRef}
                        variant="outlined"
                        color="primary">
                        {label}
                    </Button>
                )
            }
            <MenuPopover
                open={open}
                anchorEl={menuRef.current}
                onClose={handleClose}
                sx={{ py: 1, width: 'auto'}}>
                {optionList.map((option) => {
                    return (
                        <MenuItem
                            key={option.value}
                            disabled={option.isDisabled || disableSelection && option.value === value}
                            selected={option.value === value}
                            onClick={() => handleMenuItemClick(option)}>
                            {option?.content}
                        </MenuItem>
                    );
                })}
            </MenuPopover>
        </>
    );
};

MenuContainer.propTypes = propTypes;
MenuContainer.defaultProps = defaultProps;

export default MenuContainer;
