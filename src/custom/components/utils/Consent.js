import {useState} from 'react';
import {<PERSON><PERSON>, Container, Stack} from '@material-ui/core';
import {CONSENT_COOKIE} from '@w7-3/webeagle-resources/dist/config/w73';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {hasCookie, setCookie} from '@w7-3/webeagle-resources/dist/libs/cookies';
import useLocales from '../../../hooks/useLocales';
import Link from './Link';
import {globalConfig} from '../../../config/setup';

const Consent = () => {
    const {translate} = useLocales();
    const [hide, setHide] = useState(hasCookie(CONSENT_COOKIE));

    if (hide) {
        return null;
    }

    return (
        <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{
                position: 'fixed',
                bottom: 0,
                left: 0,
                width: '100%',
                zIndex: 1000,
                py: 3,
                backgroundColor: 'rg<PERSON>(0, 0, 0, 0.95)',
            }}
        >
            <Container
                maxWidth="xl"
                sx={{
                    color: 'white',
                }}>
                <Stack direction="row" alignItems="center" flexWrap="wrap">
                    {translate('cookieConsent.message', {
                        app: globalConfig.domain,
                    })}
                    <Link href={PATH_PAGE.privacyPolicy} sx={{
                        textDecoration: 'underline',
                    }}>
                        {translate('cookieConsent.moreInfo')}
                    </Link>
                </Stack>
                <Button
                    size="large"
                    variant="outlined"
                    onClick={() => {
                        setCookie({
                            name: CONSENT_COOKIE,
                            value: 1,
                            path: '/',
                            maxAge: 315360000,
                        });
                        setHide(true);
                    }}
                    sx={{textTransform: 'none', mt: 3, px: 3,}}>
                    {translate('close')}
                </Button>
            </Container>
        </Stack>
    );
};

export default Consent;
