import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    Dialog,
    <PERSON>alogActions,
    DialogContent,
    DialogContentText,
    Stack,
    TextField,
    Typography,
} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import useLocales from '../../../hooks/useLocales';
import {fNumber} from '../../../utils/formatNumber';

const propTypes = {
    disabled: PropTypes.bool,
    title: PropTypes.string,
    description: PropTypes.string,
    onChange: PropTypes.func,
    min: PropTypes.number,
    max: PropTypes.number,
    step: PropTypes.number,
    value: PropTypes.number,
    suffix: PropTypes.string,
    buttonProps: PropTypes.object,
    inputProps: PropTypes.object,
};
const defaultProps = {
    suffix: '',
}

const ContingentInputDialog = ({
    disabled,
    title,
    description,
    onChange,
    min,
    max,
    step,
    value,
    suffix,
    buttonProps,
    inputProps,
}) => {
    const [open, setOpen] = useState(false);
    const [confirmRoundUp, setConfirmRoundUp] = useState(false);
    const {translate} = useLocales();
    const {
        control,
        watch,
        setValue,
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            value,
        },
    });
    const denominator = Number.isInteger(step) ? step : 1;
    const handleClose = () => {
        setOpen(false);
    };

    const handleSave = () => {
        const value = watch('value');
        const isValid = Number.isInteger(value / denominator);

        if (!isValid) {
            setValue('value', Math.ceil(value / denominator) * step);
            setConfirmRoundUp(true);
            return;
        }

        setOpen(false);
        onChange(value);
    };

    useEffect(() => {
        setValue('value', value);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    return (
        <>
            <Button
                size="large"
                variant="outlined"
                onClick={() => setOpen(true)}
                disabled={disabled}
                {...buttonProps}
            >{fNumber(value)}{suffix}</Button>
            <Controller
                name="value"
                control={control}
                render={({
                    field,
                }) => {
                    const isValid = field.value && field.value >= min && field.value <= max;

                    return (
                        <Dialog open={open} onClose={handleClose}>
                            <DialogContent>
                                <DialogContentText>
                                    {description}
                                </DialogContentText>
                                <Stack
                                    spacing={3}
                                    alignItems="center"
                                    sx={{mt: 3}}>
                                    {
                                        confirmRoundUp &&
                                        <Alert severity="info" sx={{width: '100%'}}>
                                            {translate('roundUpInfo', {step})}
                                        </Alert>
                                    }
                                    <TextField
                                        {...field}
                                        autoFocus
                                        fullWidth
                                        type="number"
                                        margin="dense"
                                        variant="outlined"
                                        label={title}
                                        InputProps={{
                                            step,
                                            min,
                                            max,
                                            inputProps: {min, max},
                                        }}
                                        {...inputProps}
                                    />
                                    {Boolean(!isValid) && (
                                        <Typography
                                            sx={{color: 'error.main'}}>
                                            {
                                                denominator > 1 ?
                                                    translate('form.validation.minMaxStep', {
                                                        min: fNumber(min),
                                                        max: fNumber(max),
                                                        step,
                                                    }) :
                                                    translate('form.validation.minMax', {
                                                        min: fNumber(min),
                                                        max: fNumber(max),
                                                    })
                                            }
                                        </Typography>
                                    )}
                                </Stack>
                            </DialogContent>
                            <DialogActions>
                                <Button onClick={handleClose} color="inherit">
                                    {translate('cancel')}
                                </Button>
                                <Button
                                    size="large"
                                    onClick={handleSave}
                                    variant="contained"
                                    disabled={!isValid}>
                                    {translate('save')}
                                </Button>
                            </DialogActions>
                        </Dialog>
                    );
                }}/>
        </>
    );
};

ContingentInputDialog.propTypes = propTypes;
ContingentInputDialog.defaultProps = defaultProps;

export default ContingentInputDialog;
