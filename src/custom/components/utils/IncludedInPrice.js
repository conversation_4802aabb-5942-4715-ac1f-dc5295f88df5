import {Icon} from '@iconify/react';
import {Typography} from '@material-ui/core';
import briefcaseOutline from '@iconify/icons-eva/briefcase-outline';
import InfoPopover from './InfoPopover';
import useLocales from '../../../hooks/useLocales';

const IncludedInPrice = () => {
    const {translate} = useLocales();

    return (
        <InfoPopover
            label={<Icon icon={briefcaseOutline} width={40} height={40} />}
            hideIcon>
            <Typography variant="caption">
                {translate('pricing.details.contained')}
            </Typography>
        </InfoPopover>
    );
};

export default IncludedInPrice;
