import PropTypes from 'prop-types';
import {animated, useSpring} from 'react-spring';
import {Collapse} from '@material-ui/core';
import {createStyles, withStyles} from '@material-ui/styles';
import {alpha, styled} from '@material-ui/core/styles';
import {TreeItem, TreeView} from '@material-ui/lab';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';

const TransitionComponent = (props) => {
    const style = useSpring({
        from: {
            opacity: 0,
            transform: 'translate3d(20px,0,0)'
        },
        to: {
            opacity: props.in ? 1 : 0,
            transform: `translate3d(${props.in ? 0 : 20}px,0,0)`
        }
    });
    return (
        <animated.div style={style}>
            <Collapse {...props} />
        </animated.div>
    );
}

TransitionComponent.propTypes = {
    in: PropTypes.bool
};

export const RootStyle = styled(TreeView)(({theme}) => ({
    '.MuiTreeItem-content': {
        paddingTop: theme.spacing(.75),
        paddingBottom: theme.spacing(.75),
        marginBottom: theme.spacing(1),
        borderTop: `1px solid ${alpha(theme.palette.grey[500_32], 0.4)}`,
    },
}));

export const StyledTreeItem = withStyles((theme) =>
    createStyles({
        iconContainer: {
            '& .close': {
                opacity: 0.3
            }
        },
        group: {
            marginLeft: 15,
            paddingLeft: 18,
            borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.5)}`
        }
    })
)((props) => <TreeItem {...props} TransitionComponent={TransitionComponent} />);

const NodeItemProp = PropTypes.shape({
    id: PropTypes.string,
    label: PropTypes.node,
    content: PropTypes.node,
    children: PropTypes.array,
});

const propTypes = {
    defaultExpanded: PropTypes.arrayOf(PropTypes.string),
    nodeList: PropTypes.arrayOf(NodeItemProp),
    sx: PropTypes.object,
};

const defaultProps = {
    defaultExpanded: [],
    sx: {},
};

const Content = (item) => {
    if (!item?.id) {
        return null;
    }

    const {id, label, nodeList: subNodeList, content} = item;

    return (
        <StyledTreeItem
            key={id}
            nodeId={id}
            label={label}
            TransitionComponent={TransitionComponent}>
            {content}
            {
                isNonEmptyArray(subNodeList) &&
                    subNodeList.map((item) => (
                        <Content
                            key={item.id}
                            {...item} />
                    ))
            }
        </StyledTreeItem>
    );
};

const TreeViewStyle = ({defaultExpanded, nodeList, sx}) => {
    if (!isNonEmptyArray(nodeList)) {
        return null;
    }

    return (
        <RootStyle defaultExpanded={defaultExpanded} sx={sx}>
            {
                nodeList.map((item) => {
                    return (
                        <Content
                            key={item?.id}
                            {...item}
                        />
                    );
                })
            }
        </RootStyle>
    );
};

TreeViewStyle.propTypes = propTypes;
TreeViewStyle.defaultProps = defaultProps;

export default TreeViewStyle;
