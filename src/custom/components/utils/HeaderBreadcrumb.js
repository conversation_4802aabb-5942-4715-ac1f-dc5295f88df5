import PropTypes from 'prop-types';
import {Box, Stack, Typography} from '@material-ui/core';

const HeaderBreadcrumb = ({label, hideSeparator, children}) => {
    return (
        <Stack direction="row" alignItems="center" spacing={1}>
            {
                !hideSeparator && (
                    <Box
                        sx={{
                            width: 8,
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: (theme) => theme.palette.grey[500_16],
                        }}
                    />
                )
            }
            {
                label && (
                    <Typography>
                        {label}
                    </Typography>
                )
            }
            {children}
        </Stack>
    );
}

HeaderBreadcrumb.propTypes = {
    label: PropTypes.string,
    hideSeparator: PropTypes.bool,
    children: PropTypes.node,
};

export default HeaderBreadcrumb;
