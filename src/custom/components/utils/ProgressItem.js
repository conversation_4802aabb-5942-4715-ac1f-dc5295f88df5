import PropTypes from 'prop-types';
import {LinearProgress, Stack, Typography} from '@material-ui/core';
import {styled} from '@material-ui/core/styles';
import {fPercent} from '../../../utils/formatNumber';

const LinearProgressStyle = styled(LinearProgress)(({theme}) => {
    return {
        backgroundColor: theme.palette.primary.lighter,
        height: 6,
    };
});

const propTypes = {
    label: PropTypes.string,
    totalValue: PropTypes.number,
    restValue: PropTypes.number,
    unit: PropTypes.string,
};

const ProgressItem = ({label, totalValue, restValue, unit}) => {
    const usedValue = totalValue - restValue;
    const usedValueFraction = Math.ceil((usedValue / totalValue) * 100 * 10) / 10;

    return (
        <Stack spacing={1}>
            <Typography variant="subtitle2" sx={{flexGrow: 1}}>
                {label} {unit && `${usedValue} ${unit} (${fPercent(usedValueFraction)})`}
            </Typography>
            <LinearProgressStyle
                variant="determinate"
                value={usedValueFraction}
                color="primary"
            />
            <Stack direction="row" alignItems="center" justifyContent="flex-end" spacing={1}>
                <Typography variant="subtitle2">{unit && `${totalValue} ${unit}`}</Typography>
            </Stack>
        </Stack>
    );
};

ProgressItem.propTypes = propTypes;

export default ProgressItem;
