import PropTypes from 'prop-types';
import {styled} from '@material-ui/core/styles';

const RootStyle = styled('div')(({theme}) => ({
    outline: 'none',
    padding: theme.spacing(0, 3, 3),
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.neutral,
    border: `1px dashed ${theme.palette.grey[500_32]}`,
    '> *': {
        marginTop: theme.spacing(3),
    }
}));

// ----------------------------------------------------------------------

CodeZone.propTypes = {
    children: PropTypes.node,
};

export default function CodeZone({children}) {
    return (
        <RootStyle>
            {children}
        </RootStyle>
    );
}
