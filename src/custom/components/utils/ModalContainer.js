import {useState} from 'react';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import {Box, Button, Dialog, DialogActions, DialogContent, DialogTitle} from '@material-ui/core';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    triggerLabel: PropTypes.string,
    buttonProps: PropTypes.object,
    CtaButton: PropTypes.func,
    title: PropTypes.node,
    children: PropTypes.node,
};
export const defaultProps = {
    buttonProps: {},
};

const ModalContainer = ({triggerLabel, buttonProps, CtaButton, title, children}) => {
    const [open, setOpen] = useState(false);
    const {translate} = useLocales();
    const handleClickOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };

    return (
        <>
            <Button
                variant="outlined"
                startIcon={<Icon
                    icon="eva:options-2-outline"
                    width={40}
                    height={40}
                />}
                {...buttonProps}
                sx={{
                    textTransform: 'none',
                    ...buttonProps.sx,
                }}
                onClick={handleClickOpen}>
                {triggerLabel}
            </Button>

            <Dialog
                fullWidth
                keepMounted
                maxWidth="sm"
                open={open}
            >
                {
                    title && (
                        <DialogTitle>
                            {title}
                        </DialogTitle>
                    )
                }
                <DialogContent>
                    <Box sx={{pt: 3}}>
                        {children}
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="outlined"
                        color="inherit"
                        onClick={handleClose}>
                        {translate('close')}
                    </Button>
                    {CtaButton && (
                        <CtaButton handleClose={handleClose} />
                    )}
                </DialogActions>
            </Dialog>
        </>
    );
};

ModalContainer.propTypes = propTypes;
ModalContainer.defaultProps = defaultProps;

export default ModalContainer;

