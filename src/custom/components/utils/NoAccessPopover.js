import {useRef, useState} from 'react';
import {Icon} from '@iconify/react';
import infoFill from '@iconify/icons-eva/info-fill';
import {Box, Button, IconButton, Typography} from '@material-ui/core';
import arrowUpOutline from '@iconify/icons-eva/arrow-up-outline';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {Link as RouterLink} from 'react-router-dom';
import MenuPopover from '../../../components/MenuPopover';
import useLocales from '../../../hooks/useLocales';

const NoAccessPopover = () => {
    const anchorRef = useRef(null);
    const [open, setOpen] = useState(false);
    const {translate} = useLocales();

    const handleOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <>
            <IconButton
                ref={anchorRef}
                size="large"
                sx={{p: 0, m: 0, ml: 1}}
                onMouseEnter={handleOpen}>
                <Icon icon={infoFill} width={40} height={40}/>
            </IconButton>

            <MenuPopover
                open={open}
                onClose={handleClose}
                anchorEl={anchorRef.current}
                sx={{p: 3, mt: 1, width: 'auto'}}>
                <Typography variant="body2">{translate('noAccessToFunction')}</Typography>
                <Box sx={{display: 'flex', justifyContent: 'flex-end', mt: 3}}>
                    <Button
                        href={PATH_PAGE.pricing}
                        variant="contained"
                        sx={{color: 'common.white'}}
                        size="large"
                        component={RouterLink}
                        endIcon={<Icon icon={arrowUpOutline} width={40} height={40}/>}
                    >
                        {translate('upgradePlan')}
                    </Button>
                </Box>
            </MenuPopover>
        </>
    );
};

export default NoAccessPopover;
