import {Box} from '@material-ui/core';
import {Icon} from '@iconify/react';
import Label from '../../../components/Label';
import InfoPopover from './InfoPopover';
import useLocales from '../../../hooks/useLocales';

const InfoPopoverAI = () => {
    const {translate} = useLocales();

    return (
        <InfoPopover
            label={(
                <Label
                    color="primary"
                    sx={{alignSelf: 'flex-start', py: 3, px: 1}}
                >
                    <Box
                        sx={{mr: 1}}
                        component={Icon}
                        icon="eva:message-square-outline"
                        width={40} height={40}
                    />
                    AI
                </Label>
            )}
            hideIcon
            isLabel
        >
            {translate('ai.step.label')}
        </InfoPopover>
    );
};

export default InfoPopoverAI;
