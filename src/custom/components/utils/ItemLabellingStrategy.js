import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {Controller} from 'react-hook-form';
import {
    FormControlLabel,
    InputAdornment,
    Radio,
    RadioGroup,
    Stack,
    TextField,
    Typography,
} from '@material-ui/core';
import {ITEM_LABELLING_STRATEGY} from '@w7-3/webeagle-resources/dist/config/misc';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import CodeTextFieldJS from '../forms/CodeTextFieldJS';
import LabelStyle from './LabelStyle';
import useLocales from '../../../hooks/useLocales';
import InfoPopover from './InfoPopover';

const propTypes = {
    formContext: PropTypes.object,
    notification: PropTypes.node,
    config: PropTypes.shape({
        endAdornment: PropTypes.string,
    }),
    fixedLabel: PropTypes.bool,
};

const ItemLabellingStrategy = ({
    formContext: {
        control,
        getValues,
        setError,
        clearErrors,
        trigger,
    },
    notification,
    config,
    fixedLabel,
}) => {
    const {translate} = useLocales();
    const [fallbackLabel] = useState(getValues('fallbackLabel'));

    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Stack spacing={2}>
            <Stack
                direction="row"
                alignItems="center"
            >
                <LabelStyle>{translate('itemLabelling.label')}</LabelStyle>
                <InfoPopover
                    isLabel>
                    {notification || translate('itemLabelling.labelInfo')}
                </InfoPopover>
            </Stack>
            <Controller
                name="labelStrategy"
                control={control}
                render={({
                    field,
                }) => {
                    return (
                        <>
                            {
                                !fixedLabel && (
                                    <>
                                        <RadioGroup
                                            value={field.value}
                                            onChange={(_, value) => {
                                                const newValue = value || field.value;
                                                field.onChange(newValue);

                                                if (newValue === ITEM_LABELLING_STRATEGY.JS) {
                                                    clearErrors('label');
                                                    trigger('labelScript');
                                                }

                                                if (newValue === ITEM_LABELLING_STRATEGY.LABEL) {
                                                    trigger('label');
                                                    clearErrors('labelScript');
                                                }
                                            }}
                                            row>
                                            {Object.values(ITEM_LABELLING_STRATEGY).map((value) => (
                                                <FormControlLabel
                                                    key={value}
                                                    value={value}
                                                    control={<Radio />}
                                                    label={translate(`itemLabelling.strategies.${value}.label`)} />
                                            ))}
                                        </RadioGroup>
                                        {
                                            field.value === ITEM_LABELLING_STRATEGY.JS && (
                                                <Controller
                                                    name="labelScript"
                                                    control={control}
                                                    render={(labelScriptRenderProps) => {
                                                        return (
                                                            <CodeTextFieldJS
                                                                label={translate(`itemLabelling.strategies.${ITEM_LABELLING_STRATEGY.JS}.description`, {
                                                                    fallbackLabel,
                                                                })}
                                                                script={labelScriptRenderProps.field.value}
                                                                onChange={(jsCode) => {
                                                                    if (!jsCode?.isValid) {
                                                                        setError(labelScriptRenderProps.field.name, {
                                                                            message: translate('form.validation.script'),
                                                                        });
                                                                        return;
                                                                    }

                                                                    labelScriptRenderProps.field.onChange(jsCode);
                                                                }}
                                                                prePlaceholder="function getItemName() {"
                                                                placeholder={[
                                                                    `/* // ${translate('exampleCode')}`,
                                                                    '/* var elem = document.querySelector(".my-target");',
                                                                    ' *',
                                                                    ' *  return elem.getAttribute("data-name"); */',
                                                                    ' */',
                                                                ].join('\n')}
                                                                postPlaceholder="}"
                                                                error={labelScriptRenderProps.fieldState.error}
                                                                isFunctionContext
                                                                showReset
                                                            />
                                                        );
                                                    }}
                                                />
                                            )
                                        }
                                    </>
                                )
                            }
                            {
                                field.value === ITEM_LABELLING_STRATEGY.LABEL && (
                                    <Controller
                                        name="label"
                                        control={control}
                                        render={(labelRenderProps) => (
                                            <>
                                                <TextField
                                                    fullWidth
                                                    {...labelRenderProps.field}
                                                    {...getOptionalMap(config?.endAdornment, {
                                                        InputProps: {
                                                            endAdornment: (
                                                                <InputAdornment
                                                                    position="end"
                                                                >.{config?.endAdornment}</InputAdornment>
                                                            ),
                                                        },
                                                    })}
                                                />
                                                {Boolean(labelRenderProps.fieldState.error) && (
                                                    <Typography
                                                        sx={{color: 'error.main'}}>
                                                        {labelRenderProps.fieldState.error?.message}
                                                    </Typography>
                                                )}
                                                <Typography sx={{color: 'text.secondary'}}>
                                                    {translate('itemLabelling.labelInfo')}
                                                </Typography>
                                            </>
                                        )}
                                    />
                                )
                            }
                        </>
                    );
                }}
            />
        </Stack>
    );
};

ItemLabellingStrategy.propTypes = propTypes;

export default ItemLabellingStrategy;
