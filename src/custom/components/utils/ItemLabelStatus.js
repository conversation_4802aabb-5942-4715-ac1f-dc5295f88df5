import {Box, Stack, Typography} from '@material-ui/core';
import {alpha} from '@material-ui/core/styles';
import {Icon} from '@iconify/react';
import PropTypes from 'prop-types';
import InfoPopover from './InfoPopover';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    item: PropTypes.shape({
        title: PropTypes.string,
        active: PropTypes.bool,
    }),
    itemLabels: PropTypes.shape({
        active: PropTypes.string,
        inActive: PropTypes.string,
    }),
    sx: PropTypes.object,
};

const Item = ({sx, color, bgColor, icon, title}) => {
    return (
        <Stack
            direction="row"
            spacing={1}
            alignItems="center"
            sx={sx}>
            <Box
                component="span"
                sx={{
                    minWidth: 40,
                    minHeight: 40,
                    display: 'inline-flex',
                    borderRadius: 1.5,
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: `${bgColor}.main`,
                    bgcolor: (theme) => alpha(theme.palette[bgColor].main, 0.16),
                }}>
                <Icon icon={icon} width={40} height={40}/>
            </Box>
            <Typography
                variant="subtitle2"
                align="left"
                sx={{color}}>{title}</Typography>
        </Stack>
    );
};

Item.propTypes = {
    sx: PropTypes.object,
    color: PropTypes.string,
    bgColor: PropTypes.string,
    icon: PropTypes.string,
    title: PropTypes.string,
}

const ItemLabelStatus = ({item, itemLabels, sx}) => {
    const {translate} = useLocales();

    if (!item) {
        return null;
    }

    const color = item?.active ? 'text.primary' : 'text.secondary';
    const bgColor = item?.active ? 'success' : 'error';
    const icon = item?.active ? 'eva:checkmark-circle-2-fill' : 'eva:close-circle-fill';
    const label = item?.active ? itemLabels?.active || translate('activated') : itemLabels?.inActive || translate('notActivated');

    if (!item?.title) {
        return (
            <Item
                sx={sx}
                color={color}
                bgColor={bgColor}
                icon={icon}
                title={label}
            />
        );
    }

    return (
        <InfoPopover
            label={
                <Item
                    sx={sx}
                    color={color}
                    bgColor={bgColor}
                    icon={icon}
                    title={item?.title}
                />
            }
            hideIcon>
            <Box sx={{p: 1}}>
                <Typography>{label}</Typography>
            </Box>
        </InfoPopover>
    );
};

ItemLabelStatus.propTypes = propTypes;

export default ItemLabelStatus;
