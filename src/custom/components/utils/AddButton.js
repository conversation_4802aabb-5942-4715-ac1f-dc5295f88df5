import {Icon} from '@iconify/react';
import plusFill from '@iconify/icons-eva/plus-fill';
import {Tooltip} from '@material-ui/core';
import PropTypes from 'prop-types';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import {MFab} from '../../../components/@material-extend';

const AddButton = ({onClick, label, inlineLabel, sx}) => {
    const {translate} = useLocales();
    return (
        <Tooltip
            title={label || translate('add')}
            onClick={onClick}>
            <MFab sx={{margin: 'auto', ...sx}}>
                <Icon icon={plusFill} width={40} height={40}/>
                {
                    (inlineLabel && isNonEmptyString(label)) &&
                        label
                }
            </MFab>
        </Tooltip>
    );
};

AddButton.propTypes = {
    onClick: PropTypes.func,
    label: PropTypes.string,
    inlineLabel: PropTypes.bool,
    sx: PropTypes.object,
};

export default AddButton;
