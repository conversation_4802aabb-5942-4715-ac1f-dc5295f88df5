import {useState} from 'react';
import PropTypes from 'prop-types';
import {Button, Dialog, DialogActions, DialogContent, DialogTitle} from '@material-ui/core';
import useLocales from '../../../hooks/useLocales';

// ----------------------------------------------------------------------

const propTypes = {
    title: PropTypes.string,
    label: PropTypes.string,
    children: PropTypes.node,
};

const ScrollDialog = ({title, label, children}) => {
    const [open, setOpen] = useState(false);
    const {translate} = useLocales();

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <div>
            <Button
                size="large"
                onClick={() => setOpen(true)}
                sx={{m: 0, p: 0}}>
                {label}
            </Button>
            <Dialog
                open={open}
                onClose={handleClose}
                scroll="paper"
                maxWidth="lg">
                {
                    title &&
                    <DialogTitle sx={{pb: 2}}>{title}</DialogTitle>
                }
                <DialogContent dividers>
                    {children}
                </DialogContent>
                <DialogActions>
                    <Button variant="contained" onClick={handleClose}>
                        {translate('close')}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

ScrollDialog.propTypes = propTypes;

export default ScrollDialog;

