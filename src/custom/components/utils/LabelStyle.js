import {styled} from '@material-ui/core/styles';
import {Typography} from '@material-ui/core';
import PropTypes from 'prop-types';

const RootStyle = styled(Typography)(({theme, ...props}) => {
    return {
        ...theme.typography.subtitle1,
        ...props?.sx,
        color: theme.palette.text.primary,
    };
});

const propTypes = {
    children: PropTypes.node,
    sx: PropTypes.object,
};

const LabelStyle = ({children, sx, ...restProps}) => {
    return (
        <RootStyle
            {...restProps}
            sx={{
                ...sx,
                ...restProps?.sx,
            }}>
            {children}
        </RootStyle>
    )
};

LabelStyle.propTypes = propTypes;

export default LabelStyle;
