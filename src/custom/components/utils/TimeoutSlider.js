import {Slider, Stack} from '@material-ui/core';
import PropTypes from 'prop-types';
import {getDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import {MAX_STEP_TIMEOUT} from '@w7-3/webeagle-resources/dist/config/misc';
import LabelStyle from './LabelStyle';
import InfoPopover from './InfoPopover';

export const propTypes = {
    value: PropTypes.number,
    stepCount: PropTypes.number,
    minValue: PropTypes.number,
    maxValue: PropTypes.number,
    label: PropTypes.string,
    labelInfo: PropTypes.string,
    onChange: PropTypes.func,
};

const getMarks = ({
    stepCount,
    maxValue,
}) => {
    const marks = Array(stepCount + 1).fill()
        .map((_, index) => {
            const value = index * Math.round(maxValue / stepCount);
            return {
                value,
                label: getDuration(value)
            };
        });

    return {
        marks,
    };
};

const defaultProps = {
    stepCount: 5,
    minValue: 0,
    maxValue: MAX_STEP_TIMEOUT,
};

const TimeoutSlider = (props) => {
    const {
        value,
        label,
        labelInfo,
        onChange,
        stepCount,
        minValue,
        maxValue,
    } = props;
    const {
        marks,
    } = getMarks({
        stepCount,
        minValue,
        maxValue,
    });

    return (
        <Stack spacing={1} sx={{mb: '2em !important'}}>
            <Stack
                direction="row"
                alignItems="center"
            >
                <LabelStyle>
                    {label}
                </LabelStyle>
                {
                    labelInfo && (
                        <InfoPopover
                            isLabel>
                            {labelInfo}
                        </InfoPopover>
                    )
                }
            </Stack>
            <Slider
                onChange={(_, x) => {
                    onChange(x);
                }}
                size="medium"
                marks={marks}
                value={value}
                min={minValue}
                step={stepCount}
                max={maxValue}
                valueLabelDisplay="auto"
                getAriaValueText={getDuration}
                valueLabelFormat={getDuration}
                sx={{
                    mx: '1em',
                    width: 'calc(100% - 2em)!important',
                }}
            />
        </Stack>
    );
};

TimeoutSlider.propTypes = propTypes;
TimeoutSlider.defaultProps = defaultProps;

export default TimeoutSlider;
