import PropTypes from 'prop-types';
import {Link as RouterLink} from 'react-router-dom';
import {Link as MuiLink} from '@material-ui/core';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';

const propTypes = {
    name: PropTypes.string,
    href: PropTypes.string,
    sx: PropTypes.object,
    children: PropTypes.node,
};

const Link = ({
    name,
    href,
    sx,
    children,
}) => {
    if (!isNonEmptyString(href)) {
        return null;
    }

    if (href.startsWith('http://') || href.startsWith('https://')) {
        return (
            <MuiLink
                key={name}
                href={href}
                color="inherit"
                variant="body2"
                sx={{...sx}}
            >
                {name}
                {children}
            </MuiLink>
        );
    }

    return (
        <MuiLink
            to={href}
            key={name}
            color="inherit"
            variant="body2"
            component={RouterLink}
            sx={{...sx}}
        >
            {name}
            {children}
        </MuiLink>
    );
};

Link.propTypes = propTypes;

export default Link;
