import {styled} from '@material-ui/core/styles';
import {TextField} from '@material-ui/core';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';

export default styled(TextField)(({theme}) => {
    const isDark = theme.palette.mode === 'dark';
    return {
        'input:-webkit-autofill': getOptionalMap(isDark, {
            boxShadow: '0 0 0 100px #1e2b3e inset',
        }),
        legend: {
            ...theme.typography.overline,
        },
        '& label': {
            pointerEvents: 'none !important',
            button: {
                fontSize: theme.typography.body1.fontSize,
            },
            '& svg': {
                marginLeft: '.25em',
            },
        },
    };
});
