import {Icon} from '@iconify/react';
import trash2Outline from '@iconify/icons-eva/trash-2-outline';
import {Tooltip} from '@material-ui/core';
import PropTypes from 'prop-types';
import useLocales from '../../../hooks/useLocales';
import {MFab} from '../../../components/@material-extend';

const propTypes = {
    onClick: PropTypes.func,
    label: PropTypes.string,
};

const DeleteButton = ({onClick, label}) => {
    const {translate} = useLocales();
    return (
        <Tooltip
            title={label || translate('delete')}
            onClick={onClick}
            sx={{margin: 'auto'}}>
            <MFab color="error">
                <Icon icon={trash2Outline} width={40} height={40}/>
            </MFab>
        </Tooltip>
    );
};

DeleteButton.propTypes = propTypes;

export default DeleteButton;
