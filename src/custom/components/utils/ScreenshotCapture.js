import {Controller} from 'react-hook-form';
import {Fragment, useEffect} from 'react';
import * as Yup from 'yup';
import PropTypes from 'prop-types';
import {Al<PERSON>, FormControlLabel, Radio, RadioGroup, Stack, TextField, Typography} from '@material-ui/core';
import {
    maxDimensions,
    minDimensions,
    SCREENSHOT_EXTENSIONS,
    SCREENSHOT_IMAGE_TYPE,
    SELECTOR_TARGETS,
} from '@w7-3/webeagle-resources/dist/config/screenshots';
import {parsePageString} from '@w7-3/webeagle-resources/dist/libs/parsers';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import LabelStyle from './LabelStyle';
import SelectorTextField from '../forms/SelectorTextField';
import useLocales from '../../../hooks/useLocales';
import {
    getLabellingValidation,
    getQuerySelectorValidation,
    getScreenshotDOMElementCustomValidation,
} from '../../utils/formSchema';
import ItemLabellingStrategy from './ItemLabellingStrategy';

export const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.screenshots';

export const useScreenshotDialog = ({id, stepList, showNamingField}) => {
    const {translate} = useLocales();
    const nameError = translate('customDevice.validation.name', {
        start: minDimensions.nameLength,
        end: maxDimensions.nameLength,
    });
    const duplicateNameError = translate(`${i18nContext}.validation.duplicateName`);
    const selectorError = translate('linkedResources.querySelector.validation');
    const customSelectorTargets = translate(`${i18nContext}.validation.selectorTargets`);
    const labelBlacklist = stepList.filter((item) => item?.id !== id).map(({label}) => label);

    return {
        schema: Yup.object().shape({
            ...getOptionalMap(showNamingField, getLabellingValidation({
                label: {
                    error: nameError,
                    duplicate: duplicateNameError,
                    allowEmpty: false,
                },
                labelScript: {
                    error: translate('form.validation.script'),
                },
                labelBlacklist,
            })),
            selector: getQuerySelectorValidation(selectorError),
            customSelectorTargets: getScreenshotDOMElementCustomValidation(customSelectorTargets),
        }),
    };
};

export const useScreenshotCapture = ({formContext, setIsValid}) => {
    const {
        watch,
        trigger,
        formState: {
            errors,
        },
    } = formContext;
    const content = watch();

    useEffect(() => {
        trigger();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    useEffect(() => {
        // trigger();
        setIsValid(!isNonEmptyObject(errors));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [content]);
};

const propTypes = {
    formContext: PropTypes.object,
    showNamingField: PropTypes.bool,
    isSingleSelector: PropTypes.bool,
};

const ScreenshotCapture = ({
    formContext,
    showNamingField,
    isSingleSelector,
}) => {
    const {
        control,
        trigger,
    } = formContext;
    const {translate} = useLocales();

    return (
        <Stack sx={{minWidth: '100%'}} spacing={1}>
            <LabelStyle>{translate(`${i18nContext}.target.title`)}</LabelStyle>
            <Controller
                name="target"
                control={control}
                render={({
                    field,
                }) => (
                    <>
                        <RadioGroup
                            row
                            {...field}
                            onChange={(_, value) => {
                                field.onChange(value);
                                trigger('selector');
                            }}
                        >
                            {
                                Object.values(SCREENSHOT_IMAGE_TYPE)
                                    .map((type) => (
                                        <FormControlLabel
                                            key={type}
                                            control={<Radio
                                                checked={field.value === type}
                                                value={type}
                                            />}
                                            label={translate(`${i18nContext}.target.${type}.description`)}
                                        />
                                    ))
                            }
                        </RadioGroup>
                        {
                            field.value === SCREENSHOT_IMAGE_TYPE.element &&
                            <Stack spacing={3} sx={{mt: 3, minWidth: '100%'}}>
                                <SelectorTextField
                                    control={control}
                                    label={translate(`${i18nContext}.selector`)}
                                    name="selector"
                                    isSingleSelector={isSingleSelector}
                                />
                                {
                                    !isSingleSelector &&
                                    <Controller
                                        name="selectorTargets"
                                        control={control}
                                        render={(selectorTargets) => (
                                            <>
                                                <RadioGroup
                                                    row
                                                    {...selectorTargets.field}
                                                    onChange={(_, value) => {
                                                        selectorTargets.field.onChange(value);
                                                        trigger('customSelectorTargets');
                                                    }}>
                                                    {
                                                        Object.values(SELECTOR_TARGETS).map((target) => {
                                                            const label = target === SELECTOR_TARGETS.custom ?
                                                                translate(`${i18nContext}.selectorTargets.custom.label`) :
                                                                translate(`${i18nContext}.selectorTargets.${target}`);
                                                            return (
                                                                <FormControlLabel
                                                                    key={target}
                                                                    control={<Radio
                                                                        checked={selectorTargets.field.value === target}
                                                                        value={target}
                                                                    />}
                                                                    label={label}
                                                                />
                                                            );
                                                        })
                                                    }
                                                </RadioGroup>
                                                {
                                                    selectorTargets.field.value === SELECTOR_TARGETS.custom &&
                                                    <Controller
                                                        name="customSelectorTargets"
                                                        control={control}
                                                        render={(customSelectorTargets) => {
                                                            const parsedData = parsePageString(customSelectorTargets.field.value);
                                                            return (
                                                                <Stack sx={{mt: 3, minWidth: '100%'}} spacing={3}>
                                                                    <Alert severity="info">
                                                                        {
                                                                            [
                                                                                '1',
                                                                                '1,4',
                                                                                '2,3_5,7',
                                                                            ].map((item) => (
                                                                                <Typography key={item}>
                                                                                    <b>{item}:</b> {translate(`${i18nContext}.selectorTargets.custom.${item}`)}<br/>
                                                                                </Typography>
                                                                            ))
                                                                        }
                                                                    </Alert>
                                                                    <Stack>
                                                                        <TextField
                                                                            {...customSelectorTargets.field}
                                                                            label={translate(`${i18nContext}.selectorTargets.custom.description`)}
                                                                            inputProps={{
                                                                                form: {
                                                                                    autocomplete: 'off',
                                                                                },
                                                                            }}
                                                                        />
                                                                        {Boolean(customSelectorTargets.fieldState.error) && (
                                                                            <Typography
                                                                                sx={{color: 'error.main'}}>
                                                                                {
                                                                                    customSelectorTargets.fieldState.error?.message ||
                                                                                    parsedData?.isValid && (
                                                                                        JSON.stringify(parsedData.pages)
                                                                                    )
                                                                                }
                                                                            </Typography>
                                                                        )}
                                                                    </Stack>
                                                                </Stack>
                                                            );
                                                        }}
                                                    />
                                                }
                                            </>
                                        )}
                                    />
                                }
                            </Stack>
                        }
                    </>
                )}
            />
            {
                showNamingField && (
                    <ItemLabellingStrategy
                        formContext={formContext}
                        config={{
                            endAdornment: SCREENSHOT_EXTENSIONS.PNG,
                        }}
                    />
                )
            }
        </Stack>
    );
};

ScreenshotCapture.propTypes = propTypes;

export default ScreenshotCapture;
