import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {timeoutConfig} from '@w7-3/webeagle-resources/dist/config/actions';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';
import TimeoutSlider from '../../utils/TimeoutSlider';
import UrlRequestForm from '../../forms/UrlReqestForm';

const propTypes = {
    ...ActionItemPropTypes,
};
const i18nContext = 'actions.options.request';

const WaitingForNetworkRequestAction = ({
    item,
    handleCancel,
    handleSave,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {value} = item;
    const [showInfo, setShowInfo] = useState(true);
    const {translate} = useLocales();
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);
    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);
    const {
        watch,
        formState,
        control,
        clearErrors,
        setError,
        formState: {
            errors,
        },
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            timeout: value?.timeout || timeoutConfig.defaultTimeout,
            urlData: value?.urlData || {},
        },
    });

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            {
                showInfo &&
                <Alert severity="info" onClose={() => {
                    setShowInfo(false);
                }}>
                    <Typography sx={{pr: 1}}>
                        {translate(`${i18nContext}.notification.timeout`, {
                            timeout: getDuration(watch('timeout')),
                        })}
                    </Typography>
                </Alert>
            }
            <Controller
                name="urlData"
                control={control}
                render={({field}) => {
                    return (
                        <UrlRequestForm
                            urlData={field.value}
                            onChange={({data, isValid}) => {
                                field.onChange(data);
                                if (isValid) {
                                    clearErrors(field.name);
                                    return;
                                }
                                setError(field.name, {
                                    message: translate('form.validation.url'),
                                });
                            }}
                        />
                    );
                }}
            />
            <Controller
                name="timeout"
                control={control}
                render={({
                    field,
                }) => (
                    <TimeoutSlider
                        value={field.value}
                        onChange={(x) => {
                            field.onChange(x);
                        }}
                        label={translate(`${i18nContext}.config.timeout`)}
                    />
                )}
            />
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        const {
                            timeout,
                            urlData,
                        } = watch();

                        handleSave({
                            value: {
                                timeout,
                                urlData,
                            },
                        });
                    }}
                    isValid={!isNonEmptyObject(errors) && formState.isValid && isParentValid}
                />
            </Stack>
        </Stack>
    );
};

WaitingForNetworkRequestAction.propTypes = propTypes;

export default WaitingForNetworkRequestAction;
