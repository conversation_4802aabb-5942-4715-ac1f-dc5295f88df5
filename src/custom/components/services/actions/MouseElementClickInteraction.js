import {But<PERSON>, <PERSON>ack} from '@material-ui/core';
import {useEffect, useState} from 'react';
import {
    mouseButtonActions,
    mouseButtonTypes,
} from '@w7-3/webeagle-resources/dist/config/mouseCodes';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import <PERSON><PERSON><PERSON>onHandler from './utils/MouseButtonHandler';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

const propTypes = {
    ...ActionItemPropTypes,
};

const MouseElementClickInteraction = ({
    item,
    handleCancel,
    handleSave,
    selectorNode,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {value} = item;
    const {translate} = useLocales();
    const [buttonType, setButtonType] = useState(value?.buttonType || mouseButtonTypes.left);
    const [clickCount, setClickCount] = useState(value?.count || 1);
    const [delay, setDelay] = useState(value?.delay || 0);
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            {selectorNode}
            <MouseButtonHandler
                {...{
                    buttonType,
                    setButtonType,
                    clickCount,
                    setClickCount,
                    delay,
                    setDelay,
                    buttonAction: mouseButtonActions.click,
                }}
            isElementInteraction
            />
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: {
                                buttonType,
                                clickCount,
                                delay,
                            },
                        });
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

MouseElementClickInteraction.propTypes = propTypes;

export default MouseElementClickInteraction;
