import {useEffect, useState} from 'react';
import {Button, Stack} from '@material-ui/core';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

const propTypes = {
    ...ActionItemPropTypes,
};

const MouseElementHoverInteraction = ({
    handleCancel,
    handleSave,
    selectorNode,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {translate} = useLocales();
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            {selectorNode}
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({});
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

MouseElementHoverInteraction.propTypes = propTypes;

export default MouseElementHoverInteraction;
