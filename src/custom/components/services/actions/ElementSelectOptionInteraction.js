import {useState} from 'react';
import {Button, Chip, Paper, Stack, TextField, ToggleButton, ToggleButtonGroup, Typography} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import clone from 'ramda/src/clone';
import {elementSelectTypes} from '@w7-3/webeagle-resources/dist/config/actions';
import useLocales from '../../../../hooks/useLocales';
import LabelStyle from '../../utils/LabelStyle';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

const propTypes = {
    ...ActionItemPropTypes,
};

const i18nContext = 'actions.options.selectOption';

const style = {
    p: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
    '& > *': {m: '8px !important'},
};

const ElementSelectOptionInteraction = ({
    item,
    handleCancel,
    handleSave,
    isMobile,
    selectorNode,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {value} = item;
    const {translate} = useLocales();
    const [type, setType] = useState(value?.type || elementSelectTypes.value);
    const [labelItems, setLabelItems] = useState(value?.list || []);
    const [valueItems, setValueItems] = useState(value?.list || []);
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: {
            newLabel: '',
            newValue: '',
        },
    });
    const {
        formState,
        control,
    } = formContext;

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <Stack spacing={3}>
                {selectorNode}
                <LabelStyle>
                    {translate(`${i18nContext}.type.label`)}
                </LabelStyle>
                <ToggleButtonGroup
                    exclusive
                    fullWidth
                    value={type}
                    onChange={(_, value) => {
                        setType(value || type);
                    }}>
                    {
                        Object.values(elementSelectTypes).map((value) => (
                            <ToggleButton
                                key={value}
                                value={value}
                                sx={{textTransform: 'none'}}>
                                {translate(`${i18nContext}.type.fields.${value}.label`)}
                            </ToggleButton>
                        ))
                    }
                </ToggleButtonGroup>
            </Stack>
            {
                type === elementSelectTypes.label &&
                <Controller
                    name="newLabel"
                    control={control}
                    render={({
                        field,
                    }) => (
                        <>
                            <Paper variant="outlined" sx={style}>
                                {
                                    labelItems.map((label, index) => {
                                        return (
                                            <Chip
                                                key={`${label}-${index}`}
                                                variant="outlined"
                                                size={isMobile ? 'small' : 'large'}
                                                sx={{p: 1, minHeight: 40}}
                                                label={label}
                                                onDelete={() => {
                                                    const list = clone(labelItems);
                                                    list.splice(index, 1);

                                                    setLabelItems(list);
                                                }}
                                            />
                                        )})
                                }
                            </Paper>
                            {
                                labelItems.length < 1 &&
                                <Typography
                                    sx={{color: 'error.main'}}>
                                    {translate(`${i18nContext}.type.fields.label.notification.empty`)}
                                </Typography>
                            }
                            <Stack direction="row" alignItems="center" spacing={3}>
                                <TextField
                                    {...field}
                                    label={translate(`${i18nContext}.type.fields.label.fields.newItem`)}
                                    fullWidth/>
                                <Button
                                    onClick={() => {
                                        const value = field.value.trim();
                                        if (!labelItems.includes(value)) {
                                            const list = clone(labelItems);
                                            list.push(value);

                                            setLabelItems(list);
                                        }

                                        field.onChange('');
                                    }}
                                    variant="contained"
                                    disabled={!field.value}>
                                    {translate('add')}
                                </Button>
                            </Stack>
                        </>
                    )}
                />
            }
            {
                type === elementSelectTypes.value &&
                <Controller
                    name="newValue"
                    control={control}
                    render={({
                        field,
                    }) => (
                        <>
                            <Paper variant="outlined" sx={style}>
                                {
                                    valueItems.map((label, index) => {
                                        return (
                                            <Chip
                                                key={`${label}-${index}`}
                                                variant="outlined"
                                                size={isMobile ? 'small' : 'large'}
                                                sx={{p: 1, minHeight: 40}}
                                                label={label}
                                                onDelete={() => {
                                                    const list = clone(valueItems);
                                                    list.splice(index, 1);

                                                    setValueItems(list);
                                                    field.onChange('');
                                                }}
                                            />
                                        )})
                                }
                            </Paper>
                            {
                                valueItems.length < 1 &&
                                <Typography
                                    sx={{color: 'error.main'}}>
                                    {translate(`${i18nContext}.type.fields.value.notification.empty`)}
                                </Typography>
                            }
                            <Stack direction="row" alignItems="center" spacing={3}>
                                <TextField
                                    {...field}
                                    label={translate(`${i18nContext}.type.fields.value.fields.newItem`)}
                                    fullWidth/>
                                <Button
                                    onClick={() => {
                                        const value = field.value.trim();
                                        if (!valueItems.includes(value)) {
                                            const list = clone(valueItems);
                                            list.push(value);

                                            setValueItems(list);
                                        }

                                        field.onChange('');
                                    }}
                                    variant="contained"
                                    disabled={!field.value}>
                                    {translate('save')}
                                </Button>
                            </Stack>
                        </>
                    )}
                />
            }
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: {
                                type,
                                list: type === elementSelectTypes.label ? labelItems : valueItems,
                            },
                        });
                    }}
                    isValid={
                        formState.isValid &&
                        (labelItems.length + valueItems.length) > 0 &&
                        parentFormContext.formState.isValid
                    }
                />
            </Stack>
        </Stack>
    );
};

ElementSelectOptionInteraction.propTypes = propTypes;

export default ElementSelectOptionInteraction;
