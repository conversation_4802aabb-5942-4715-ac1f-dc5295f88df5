/* eslint-disable quotes */

import pick from 'ramda/src/pick';
import mapObjIndexed from 'ramda/src/mapObjIndexed';
import keyboardCodes from '@w7-3/webeagle-resources/dist/config/keyboardCodes';

const getRowKeys = (input) => {
    const list = input.split(' ');
    const items = pick(list, keyboardCodes);
    const keys = Object.values(items).map(({key}) => key);

    return keys.join(' ');
};

const getDisplays = ({isMobile} = {isMobile: false}) => mapObjIndexed(({display, displayMobile}) => {
    if (isMobile && displayMobile) {
        return displayMobile;
    }

    return display;
}, keyboardCodes);

export const basicKeysMobile = {
    layout: {
        default: [
            "q w e r t y u i o p {bksp}",
            "a s d f g h j k l {enter}",
            "{shift} z x c v b n m , . {shift}",
            "{alt} {smileys} {space} {altright}"
        ],
        shift: [
            "Q W E R T Y U I O P {bksp}",
            "A S D F G H J K L {enter}",
            "{shiftactivated} Z X C V B N M , . {shiftactivated}",
            "{alt} {smileys} {space} {altright}"
        ],
        alt: [
            "1 2 3 4 5 6 7 8 9 0 {bksp}",
            `@ # $ & * ( ) ' " {enter}`,
            "{shift} % - + = / ; : ! ? {shift}",
            "{default} {smileys} {space} {back}"
        ],
        smileys: [
            "😀 😊 😅 😂 🙂 😉 😍 😛 😠 😎 {bksp}",
            `😏 😬 😭 😓 😱 😪 😬 😴 😯 {enter}`,
            "😐 😇 🤣 😘 😚 😆 😡 😥 😓 🙄 {shift}",
            "{default} {smileys} {space} {altright}"
        ]
    },
    display: getDisplays({isMobile: true}),
};

export const basicKeys = {
    layout: {
        default: [
            getRowKeys("{Escape} {f1} {f2} {f3} {f4} {f5} {f6} {f7} {f8} {f9} {f10} {f11} {f12}"),
            getRowKeys("` 1 2 3 4 5 6 7 8 9 0 - = {backspace}"),
            getRowKeys("{tab} q w e r t y u i o p [ ] \\"),
            getRowKeys("{capslock} a s d f g h j k l ; ' {enter}"),
            getRowKeys("{shiftleft} z x c v b n m , . / {shiftright}"),
            getRowKeys("{controlleft} {altleft} {metaleft} {space} {metaright} {altright}"),
        ],
        shift: [
            getRowKeys("{Escape} {f1} {f2} {f3} {f4} {f5} {f6} {f7} {f8} {f9} {f10} {f11} {f12}"),
            getRowKeys("~ ! @ # $ % ^ & * ( ) _ + {backspace}"),
            getRowKeys("{tab} Q W E R T Y U I O P { } |"),
            getRowKeys('{capslock} A S D F G H J K L : " {enter}'),
            getRowKeys("{shiftleft} Z X C V B N M < > ? {shiftright}"),
            getRowKeys("{controlleft} {altleft} {metaleft} {space} {metaright} {altright}"),
        ],
        caps: [
            getRowKeys("{Escape} {f1} {f2} {f3} {f4} {f5} {f6} {f7} {f8} {f9} {f10} {f11} {f12}"),
            getRowKeys("` 1 2 3 4 5 6 7 8 9 0 - = {backspace}"),
            getRowKeys("{tab} Q W E R T Y U I O P [ ] \\"),
            getRowKeys("{capslock} A S D F G H J K L ; ' {enter}"),
            getRowKeys("{shiftleft} Z X C V B N M , . / {shiftright}"),
            getRowKeys("{controlleft} {altleft} {metaleft} {space} {metaright} {altright}"),
        ],
    },
    display: getDisplays(),
};

export const controlKeys = {
    layout: {
        default: [
            getRowKeys("{prtscr} {scrolllock} {pause}"),
            getRowKeys("{insert} {home} {pageup}"),
            getRowKeys("{delete} {end} {pagedown}"),
        ]
    },
};

export const arrowKeys = {
    layout: {
        default: [
            getRowKeys("{arrowup}"),
            getRowKeys("{arrowleft} {arrowdown} {arrowright}"),
        ]
    },
};

export const numPadStartKeys = {
    layout: {
        default: [
            getRowKeys("{numlock} {numpaddivide} {numpadmultiply}"),
            getRowKeys("{numpad7} {numpad8} {numpad9}"),
            getRowKeys("{numpad4} {numpad5} {numpad6}"),
            getRowKeys("{numpad1} {numpad2} {numpad3}"),
            getRowKeys("{numpad0} {numpaddecimal}"),
        ]
    },
};

export const numPadEndKeys = {
    layout: {
        default: [
            getRowKeys("{numpadsubtract}"),
            getRowKeys("{numpadadd}"),
            getRowKeys("{numpadenter}"),
        ]
    }
};
