import Keyboard from 'react-simple-keyboard';
import PropTypes from 'prop-types';
import {useState} from 'react';
import {Stack} from '@material-ui/core';
import keyboardCodes, {keyActions} from '@w7-3/webeagle-resources/dist/config/keyboardCodes';
import {basicKeysMobile} from './inputKeys';
import './MobileKeyboard.css';
import CustomKeyboardInputModal from './CustomKeyboardInputModal';

export const propTypes = {
    keyConfigList: PropTypes.array,
    setKeyConfigList: PropTypes.func,
};

const MobileKeyboard = ({keyConfigList, setKeyConfigList}) => {
    const [layoutName, setLayoutName] = useState('default');
    const updateLayout = ({key}) => {
        let newLayoutName;

        switch (key) {
            case keyboardCodes['{alt}'].key:
            case keyboardCodes['{altright}'].key:

                newLayoutName = layoutName === 'alt' ? 'default' : 'alt';
                break;

            case keyboardCodes['{smileys}'].key:

                newLayoutName = layoutName === 'smileys' ? 'default' : 'smileys';
                break;

            case keyboardCodes['{shift}'].key:
            case keyboardCodes['{shiftactivated}'].key:
            case keyboardCodes['{default}'].key:

                newLayoutName = layoutName === 'default' ? 'shift' : 'default';
                break;

            default:
                break;
        }

        if (!newLayoutName) {
            return;
        }

        setLayoutName(newLayoutName);
    };

    const updateKeyConfigList = ({key, action, config}) => {
        const data = {
            key,
            action,
            config,
        };
        const newList = [
            ...keyConfigList,
            data,
        ];

        setKeyConfigList(newList);
        updateLayout({key});
    };
    const commonKeyboardOptions = {
        onKeyPress: (key) => updateKeyConfigList({key, action: keyActions.press}),
        theme: 'hg-theme-default hg-theme-ios',
        physicalKeyboardHighlight: false,
        syncInstanceInputs: true,
        mergeDisplay: true,
    };

    const keyboardOptionsMobile = {
        ...commonKeyboardOptions,
        ...basicKeysMobile,
    };

    return (
        <>
            <div className="simple-keyboard">
                <Keyboard
                    baseClass="simple-keyboard-mobile"
                    layoutName={layoutName}
                    {...keyboardOptionsMobile}
                />
            </div>
            <Stack direction="row" justifyContent="flex-end">
                <CustomKeyboardInputModal
                    save={({characters, config}) => {
                        if (characters.length < 1) {
                            return;
                        }

                        updateKeyConfigList({
                            key: characters,
                            action: keyActions.type,
                            config,
                        });
                    }}
                />
            </Stack>
        </>
    );
};

MobileKeyboard.propTypes = propTypes;

export default MobileKeyboard;
