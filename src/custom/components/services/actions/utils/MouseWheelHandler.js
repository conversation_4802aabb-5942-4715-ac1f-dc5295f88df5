import {But<PERSON>, InputAdornment, Stack, TextField} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import useLocales from '../../../../../hooks/useLocales';
import DevicePreview from '../../../forms/DevicePreview';
import LabelStyle from '../../../utils/LabelStyle';
import {MouseHandlerProps} from './PropTypes';

export const i18nContext = 'actions.groups.mouse';

const mapStateToProps = ({projectAssistant}) => {
    const {
        configData: {
            target,
        },
    } = projectAssistant;
    const {device} = target;

    return {
        device,
    };
};

const propTypes = {
    ...MouseHandlerProps,
    device: PropTypes.object,
};

const MouseWheelHandler = ({
    dx,
    setDx,
    dy,
    setDy,
    device,
}) => {
    const {translate} = useLocales();
    const minX = -device?.viewport?.width;
    const maxX = device?.viewport?.width;
    const minY = -device?.viewport?.height;
    const maxY = device?.viewport?.height;

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <Stack direction="row" justifyContent="flex-end">
                <Button
                    size="large"
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'custom',
                            children: (
                                <DevicePreview deviceData={device} />
                            ),
                        });
                    }}
                    sx={{ml: 'auto'}}
                >
                    {device?.name}
                </Button>
            </Stack>
            <LabelStyle>
                {translate(`${i18nContext}.config.dXdY.description`)}
            </LabelStyle>
            <Stack spacing={3}
                   direction={{xs: 'column', sm: 'row'}}
                   alignItems="center">
                <TextField
                    fullWidth
                    value={dx}
                    label={translate(`${i18nContext}.config.dXdY.xAxis`, {
                        min: minX,
                        max: maxX,
                    })}
                    onChange={({target}) => {
                        const value = Number.parseInt(target.value, 10) || minX;
                        setDx(Math.min(value, maxX));
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: minX,
                            max: maxX,
                        },
                        endAdornment: (
                            <InputAdornment position="end">px</InputAdornment>
                        ),
                    }}
                />
                <TextField
                    fullWidth
                    value={dy}
                    label={translate(`${i18nContext}.config.dXdY.yAxis`, {
                        min: minY,
                        max: maxY,
                    })}
                    onChange={({target}) => {
                        const value = Number.parseInt(target.value, 10) || minY;
                        setDy(Math.min(value, maxY));
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: minY,
                            max: maxY,
                        },
                        endAdornment: (
                            <InputAdornment position="end">px</InputAdornment>
                        ),
                    }}
                />
            </Stack>
        </Stack>
    );
};

MouseWheelHandler.propTypes = propTypes;

export default connect(mapStateToProps)(MouseWheelHandler);
