import PubSub from 'pubsub-js';
import {Button, InputAdornment, Stack, TextField} from '@material-ui/core';
import useLocales from '../../../../../hooks/useLocales';
import DevicePreview from '../../../forms/DevicePreview';
import LabelStyle from '../../../utils/LabelStyle';
import {MouseHandlerProps} from './PropTypes';

export const i18nContext = 'actions.groups.mouse';

const propTypes = MouseHandlerProps;

const MouseMoveHandler = ({
    x,
    setX,
    y,
    setY,
    device,
}) => {
    const {translate} = useLocales();
    const minX = 0;
    const maxX = device?.viewport?.width;
    const minY = 0;
    const maxY = device?.viewport?.height;

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <Stack direction="row" justifyContent="flex-end">
                <Button
                    size="large"
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'custom',
                            maxWidth: 'xs',
                            children: (
                                <DevicePreview deviceData={device} />
                            ),
                        });
                    }}
                    sx={{ml: 'auto'}}
                >
                    {device?.name}
                </Button>
            </Stack>
            <LabelStyle>
                {translate(`${i18nContext}.config.xy.description`)}
            </LabelStyle>
            <Stack spacing={3}
                   direction={{xs: 'column', sm: 'row'}}
                   alignItems="center">
                <TextField
                    fullWidth
                    value={x}
                    label={translate(`${i18nContext}.config.xy.xAxis`, {
                        min: minX,
                        max: maxX,
                    })}
                    onChange={({target}) => {
                        const value = Number.parseInt(target.value, 10) || minX;
                        setX(Math.min(value, maxX));
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: minX,
                            max: maxX,
                        },
                        endAdornment: (
                            <InputAdornment position="end">px</InputAdornment>
                        ),
                    }}
                />
                <TextField
                    fullWidth
                    value={y}
                    label={translate(`${i18nContext}.config.xy.yAxis`, {
                        min: minY,
                        max: maxY,
                    })}
                    onChange={({target}) => {
                        const value = Number.parseInt(target.value, 10) || minY;
                        setY(Math.min(value, maxY));
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: minY,
                            max: maxY,
                        },
                        endAdornment: (
                            <InputAdornment position="end">px</InputAdornment>
                        ),
                    }}
                />
            </Stack>
        </Stack>
    );
};

MouseMoveHandler.propTypes = propTypes;

export default MouseMoveHandler;
