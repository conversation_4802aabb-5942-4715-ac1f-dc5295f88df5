import {useState} from 'react';
import {
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    Stack,
    TextField,
} from '@material-ui/core';
import PropTypes from 'prop-types';
import {Controller, useForm} from 'react-hook-form';
import useLocales from '../../../../../hooks/useLocales';
import TimeoutSlider from '../../../utils/TimeoutSlider';

export const i18nContext = 'actions.groups.keyboard.customCharacter';
const FORM_FIELDS = {
    characters: 'characters',
    delay: 'delay',
};
export const propTypes = {
    save: PropTypes.func,
};

const CustomKeyboardInputModal = ({save}) => {
    const {translate} = useLocales();
    const [open, setOpen] = useState(false);
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: {
            [FORM_FIELDS.characters]: '',
            [FORM_FIELDS.delay]: 0,
        },
    });
    const handleClickOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };
    const {
        watch,
        control,
    } = formContext;

    return (
        <>
            <Button variant="outlined"
                    onClick={handleClickOpen}>
                {translate(`${i18nContext}.label`)}
            </Button>

            <Dialog open={open} onClose={handleClose}>
                <DialogTitle>
                    {translate(`${i18nContext}.label`)}
                </DialogTitle>
                <DialogContent>
                    <Stack spacing={3}>
                        <DialogContentText>
                            {translate(`${i18nContext}.title`)}
                        </DialogContentText>
                        <Controller
                            name={FORM_FIELDS.characters}
                            control={control}
                            render={({
                                field,
                            }) => (
                                <TextField
                                    {...field}
                                    autoFocus
                                    fullWidth
                                    margin="dense"
                                    variant="outlined"
                                    label={translate(`${i18nContext}.label`)}/>
                            )}
                        />
                        <Controller
                            name={FORM_FIELDS.delay}
                            control={control}
                            render={({
                                field,
                            }) => (
                                <TimeoutSlider
                                    value={field.value}
                                    onChange={(x) => {
                                        field.onChange(x);
                                    }}
                                    label={translate(`${i18nContext}.delay`)}
                                />
                            )}
                        />
                    </Stack>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose} color="inherit">
                        {translate('cancel')}
                    </Button>
                    <Button onClick={() => {
                        const data = watch();
                        save(data);
                        handleClose();
                    }} variant="contained">
                        {translate('save')}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

CustomKeyboardInputModal.propTypes = propTypes;

export default CustomKeyboardInputModal;
