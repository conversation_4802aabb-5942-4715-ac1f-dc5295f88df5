import PropTypes from 'prop-types';
import {Alert} from '@material-ui/lab';
import {Accordion, AccordionDetails, AccordionSummary, AlertTitle, Box, Button} from '@material-ui/core';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import {actions} from '@w7-3/webeagle-resources/dist/config/actions';
import useLocales from '../../../../../hooks/useLocales';

const propTypes = {
    i18nContextRoot: PropTypes.string,
    changeItemType: PropTypes.func,
};

const jsActions = actions.js;
const {categoryId} = jsActions;
const type = jsActions.value;

const ActionabilityNotification = ({i18nContextRoot, changeItemType}) => {
    const {translate} = useLocales();

    return (
        <Alert severity="info" sx={{
            mb: 3,
            '.MuiAlert-message': {
                flexGrow: 1,
                py: 0,
            },
        }}>
            <Accordion sx={{
                m: '0 !important',
                p: '0 !important',
                boxShadow: 'none !important',
                backgroundColor: 'transparent',
                '.MuiAccordionSummary-content': {
                    m: '0 !important',
                },
            }}>
                <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40} />}>
                    <AlertTitle>{translate('actionability.label')}</AlertTitle>
                </AccordionSummary>
                <AccordionDetails>
                    <div>{translate('actionability.description')}</div>
                    <Box component="ul" sx={{m: 2}}>
                        <Box component="strong" sx={{ml: -2}}>
                            {translate('actionability.itemsLabel')}
                        </Box>
                        {
                            translate('actionability.items', {returnObjects: true})
                                .map((item) => (
                                    <li key={item}>{item}</li>
                                ))
                        }
                    </Box>
                    <Button
                        variant="outlined"
                        onClick={() => {
                            changeItemType(({
                                categoryId,
                                type,
                            }));
                        }}
                        sx={{textTransform: 'none'}}>
                        {translate('actionability.alternative', {
                            javaScript: translate(`${i18nContextRoot}.options.${type}.label`)
                        })}
                    </Button>
                </AccordionDetails>
            </Accordion>
        </Alert>
    );
};

ActionabilityNotification.propTypes = propTypes;

export default ActionabilityNotification;
