import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Stack} from '@material-ui/core';
import {timeoutConfig} from '@w7-3/webeagle-resources/dist/config/actions';
import {getDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';
import TimeoutSlider from '../../utils/TimeoutSlider';

const propTypes = {
    ...ActionItemPropTypes,
};

const i18nContext = 'actions.options.timeout';

const WaitingForTimeoutAction = ({
    item,
    handleCancel,
    handleSave,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {value} = item;
    const [timeout, setTimeout] = useState(value?.timeout || timeoutConfig.defaultTimeout);
    const {translate} = useLocales();
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <TimeoutSlider
                value={timeout}
                onChange={setTimeout}
                label={translate(`${i18nContext}.config.timeout`, {
                    timeout: getDuration(timeout),
                })}
            />
            <br />
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: {
                                timeout,
                            },
                        });
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

WaitingForTimeoutAction.propTypes = propTypes;

export default WaitingForTimeoutAction;
