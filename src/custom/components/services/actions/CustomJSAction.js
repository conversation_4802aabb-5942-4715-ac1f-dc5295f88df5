import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Stack} from '@material-ui/core';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../hooks/useLocales';
import CodeTextFieldJS from '../../forms/CodeTextFieldJS';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

const propTypes = {
    ...ActionItemPropTypes,
};

export const i18nContext = 'actions.options.js';

const CustomJSAction = ({
    item,
    handleCancel,
    handleSave,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {value} = item;
    const {translate} = useLocales();
    const [jsCode, setJsCode] = useState(value?.jsCode || {});
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);
    const isValid = isNonEmptyString(jsCode?.value) && jsCode?.isValidated && jsCode?.isValid;

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <CodeTextFieldJS
                script={jsCode}
                onChange={setJsCode}
                label={translate(`${i18nContext}.label`)}
                modifier="void"
            />
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: {
                                jsCode,
                            },
                        });
                    }}
                    isValid={isValid && isParentValid}
                />
            </Stack>
        </Stack>
    );
};

CustomJSAction.propTypes = propTypes;

export default CustomJSAction;
