import {But<PERSON>, InputAdornment, Stack, TextField} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {useEffect, useState} from 'react';
import useLocales from '../../../../hooks/useLocales';
import DevicePreview from '../../forms/DevicePreview';
import LabelStyle from '../../utils/LabelStyle';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

const propTypes = {
    ...ActionItemPropTypes,
    device: PropTypes.object,
};

const i18nContext = 'actions.groups.touchScreen';
const mapStateToProps = ({projectAssistant}) => {
    const {
        configData: {
            target,
        },
    } = projectAssistant;
    const {device} = target;

    return {
        device,
    };
};

const TouchScreenInteraction = ({
    item,
    handleCancel,
    handleSave,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    device,
    parentFormContext,
}) => {
    const {value} = item;
    const {translate} = useLocales();
    const [x, setX] = useState(value?.x || 0);
    const [y, setY] = useState(value?.y || 0);
    const minX = 0;
    const maxX = device?.viewport?.width;
    const minY = 0;
    const maxY = device?.viewport?.height;
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <Stack direction="row" justifyContent="flex-end">
                <Button
                    size="large"
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'custom',
                            maxWidth: 'xs',
                            children: (
                                <DevicePreview deviceData={device} />
                            ),
                        });
                    }}
                    sx={{ml: 'auto'}}
                >
                    {device?.name}
                </Button>
            </Stack>
            <LabelStyle>
                {translate(`${i18nContext}.label`)}
            </LabelStyle>
            <Stack spacing={3}
                   direction={{xs: 'column', sm: 'row'}}
                   alignItems="center">
                <TextField
                    fullWidth
                    value={x}
                    label={translate(`${i18nContext}.config.xy.xAxis`, {
                        min: minX,
                        max: maxX,
                    })}
                    onChange={({target}) => {
                        const value = Number.parseInt(target.value, 10) || minX;
                        setX(Math.min(value, maxX));
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: minX,
                            max: maxX,
                        },
                        endAdornment: (
                            <InputAdornment position="end">px</InputAdornment>
                        ),
                    }}
                />
                <TextField
                    fullWidth
                    value={y}
                    label={translate(`${i18nContext}.config.xy.yAxis`, {
                        min: minY,
                        max: maxY,
                    })}
                    onChange={({target}) => {
                        const value = Number.parseInt(target.value, 10) || minY;
                        setY(Math.min(value, maxY));
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: minY,
                            max: maxY,
                        },
                        endAdornment: (
                            <InputAdornment position="end">px</InputAdornment>
                        ),
                    }}
                />
            </Stack>
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: {
                                x,
                                y,
                            },
                        });
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

TouchScreenInteraction.propTypes = propTypes;

export default connect(mapStateToProps)(TouchScreenInteraction);
