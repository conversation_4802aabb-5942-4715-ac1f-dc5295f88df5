import {useEffect, useState} from 'react';
import {Button, Stack} from '@material-ui/core';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {ActionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

const propTypes = {
    ...ActionItemPropTypes,
};

export const i18nContext = 'actions.options.reloadPage';

const CustomReloadAction = ({
    handleCancel,
    handleSave,
    labelNode,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    parentFormContext,
}) => {
    const {translate} = useLocales();
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            {labelNode}
            {abortOnErrorNode}
            {ignoreOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({});
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

CustomReloadAction.propTypes = propTypes;

export default CustomReloadAction;
