import {useEffect, useState} from 'react';
import {<PERSON>ton, Chip, Paper, Stack, TextField, Typography} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import clone from 'ramda/src/clone';
import loops from '@w7-3/webeagle-resources/dist/config/loops';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {LoopItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

export const i18nContext = `loops.options.${loops.customList.value}`;

const style = {
    p: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
    '& > *': {m: '8px !important'},
};

const CustomListLoop = ({
    item,
    handleCancel,
    handleSave,
    isMobile,
    labelNode,
    abortOnErrorNode,
    parentFormContext,
}) => {
    const {value} = item;
    const {translate} = useLocales();
    const [list, setList] = useState(value?.list || []);
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: {
            newLabel: '',
            newValue: '',
        },
    });
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    const {
        control,
    } = formContext;

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <Controller
                name="newValue"
                control={control}
                render={({
                    field,
                }) => {
                    const handleAddValue = () => {
                        const value = field.value.trim();
                        if (!list.includes(value)) {
                            const newList = clone(list);
                            newList.push(value);

                            setList(newList);
                        }

                        field.onChange('');
                    };
                    return (
                        <>
                            {
                                list.length > 0 ? (
                                    <Paper variant="outlined" sx={style}>
                                        {
                                            list.map((label, index) => {
                                                return (
                                                    <Chip
                                                        key={`${label}-${index}`}
                                                        variant="outlined"
                                                        size={isMobile ? 'small' : 'large'}
                                                        sx={{p: 1, minHeight: 40}}
                                                        label={label}
                                                        onDelete={() => {
                                                            const list2 = clone(list);
                                                            list2.splice(index, 1);

                                                            setList(list2);
                                                            field.onChange('');
                                                        }}
                                                    />
                                                );
                                            })
                                        }
                                    </Paper>
                                ) : (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {translate(`${i18nContext}.notification.empty`)}
                                    </Typography>
                                )
                            }
                            <Stack direction="row" alignItems="center" spacing={3}>
                                <TextField
                                    {...field}
                                    label={translate(`${i18nContext}.labels.newItem`)}
                                    onKeyUp={(event) => {
                                        if (event.key === 'Enter') {
                                            handleAddValue();
                                        }
                                    }}
                                    fullWidth
                                />
                                <Button
                                    onClick={handleAddValue}
                                    variant="contained"
                                    disabled={!field.value}>
                                    {translate('save')}
                                </Button>
                            </Stack>
                        </>
                    );
                }}
            />
            {labelNode}
            {abortOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    label={translate('continue')}
                    onClick={() => {
                        handleSave({
                            value: {
                                list,
                            },
                        });
                    }}
                    isValid={list.length > 0 && isParentValid}
                />
            </Stack>
        </Stack>
    );
};

CustomListLoop.propTypes = LoopItemPropTypes;

export default CustomListLoop;
