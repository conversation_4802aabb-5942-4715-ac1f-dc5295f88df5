import {useEffect, useState} from 'react';
import {Button, Stack} from '@material-ui/core';
import loops from '@w7-3/webeagle-resources/dist/config/loops';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {LoopItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

export const i18nContext = `loops.options.${loops.selector.value}`;

const SelectorLoop = ({
    handleCancel,
    handleSave,
    selectorNode,
    labelNode,
    parentFormContext,
}) => {
    const {translate} = useLocales();
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}>
            {selectorNode}
            {labelNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    label={translate('continue')}
                    onClick={() => {
                        handleSave({});
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

SelectorLoop.propTypes = LoopItemPropTypes;

export default SelectorLoop;
