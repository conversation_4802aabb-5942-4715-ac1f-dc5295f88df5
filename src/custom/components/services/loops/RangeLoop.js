import {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>uItem, <PERSON>lide<PERSON>, <PERSON>ack, TextField} from '@material-ui/core';
import loops from '@w7-3/webeagle-resources/dist/config/loops';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {LoopItemPropTypes} from '../../../../config/prop-types/ProjectSteps';

export const i18nContext = `loops.options.${loops.range.value}`;

const MIN_VALUE = 1;
const MAX_VALUE = 100;
const DEFAULT_VALUE = 10;
const STEP = 1;
const marks = [
    MIN_VALUE,
    DEFAULT_VALUE,
    Number.parseInt(MAX_VALUE / 2, 10),
].map((value) => ({
    value,
    label: `${value}`,
}));

const RangeLoop = ({
    item,
    handleCancel,
    handleSave,
    labelNode,
    parentFormContext,
}) => {
    const {translate} = useLocales();
    const [value, setValue] = useState(item?.value || {
        start: MIN_VALUE,
        step: STEP,
        end: MAX_VALUE,
    });
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}>
            <Stack
                spacing={3}
                direction={{xs: 'column', sm: 'row'}}
                alignItems="center"
                sx={{mb: 3}}>
                <TextField
                    fullWidth
                    value={value.start}
                    label={translate(`${i18nContext}.start`)}
                    onChange={({target}) => {
                        const start = Number.parseInt(target.value, 10) || MIN_VALUE;
                        setValue({
                            ...value,
                            start,
                        });
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: MIN_VALUE,
                            max: value.end,
                        },
                    }}
                />
                <TextField
                    select
                    sx={{
                        width: '18em',
                    }}
                    value={value.step}
                    label={translate(`${i18nContext}.step`)}
                    onChange={({target}) => {
                        const step = Number.parseInt(target.value, 10) || MIN_VALUE;
                        setValue({
                            ...value,
                            step,
                        });
                    }}
                    InputProps={{
                        type: 'number',
                    }}
                >
                    {Array(MAX_VALUE - MIN_VALUE).fill(0).map((_, index) => (
                        <MenuItem key={index} value={(index + 1)}>
                            {(index + 1)}
                        </MenuItem>
                    ))}
                </TextField>
                <TextField
                    fullWidth
                    value={value.end}
                    label={translate(`${i18nContext}.end`)}
                    onChange={({target}) => {
                        const end = Number.parseInt(target.value, 10) || DEFAULT_VALUE;
                        setValue({
                            ...value,
                            end,
                        });
                    }}
                    InputProps={{
                        type: 'number',
                        inputProps: {
                            min: value.start,
                            max: MAX_VALUE,
                        },
                    }}
                />
            </Stack>
            <Slider
                step={STEP}
                marks={marks}
                value={[
                    value.start,
                    value.end,
                ]}
                onChange={(_, [start, end]) => {
                    setValue({
                        ...value,
                        start,
                        end,
                    });
                }}
                valueLabelDisplay="on"
                sx={{
                    m: {xs: 3, md: 5},
                }}
            />
            {labelNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    label={translate('continue')}
                    onClick={() => {
                        handleSave({
                            value,
                        });
                    }}
                    isValid={isParentValid}
                />
            </Stack>
        </Stack>
    );
};

RangeLoop.propTypes = LoopItemPropTypes;

export default RangeLoop;
