import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {useRef, useState} from 'react';
import editFill from '@iconify/icons-eva/edit-fill';
import trash2Outline from '@iconify/icons-eva/trash-2-outline';
import moreHorizontalFill from '@iconify/icons-eva/more-horizontal-fill';
import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    ListItemText,
    MenuItem,
    Stack,
    TextField,
    Tooltip,
    Typography,
} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import MenuPopover from '../../../components/MenuPopover';
import {MIconButton} from '../../../components/@material-extend';
import useLocales from '../../../hooks/useLocales';
import SubmitButton from '../utils/SubmitButton';
import {getLabelValidation} from '../../utils/formSchema';

const propTypes = {
    label: PropTypes.string,
    onDelete: PropTypes.func,
    onUpdate: PropTypes.func,
    i18nMap: PropTypes.shape({
        rename: PropTypes.string,
        delete: PropTypes.string,
        mandatoryField: PropTypes.string,
        duplicateGroupName: PropTypes.string,
        labelFormTitle: PropTypes.string,
    }),
    labelBlacklist: PropTypes.arrayOf(PropTypes.string),
};

const FORM_FIELDS = {
    label: 'label',
};

const DragDropTableColumnHeader = ({label, labelBlacklist, onDelete, onUpdate, i18nMap}) => {
    const {translate} = useLocales();
    const anchorRef = useRef(null);
    const [openMenu, setOpenMenu] = useState(false);
    const [openRenameForm, setOpenRenameForm] = useState(false);
    const {
        control,
        watch,
        formState: {
            errors,
        },
    } = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            [FORM_FIELDS.label]: getLabelValidation({
                errors: {
                    required: i18nMap.mandatoryField,
                    duplicated: i18nMap.duplicateGroupName,
                },
                labelBlacklist,
            }),
        })),
        defaultValues: {
            [FORM_FIELDS.label]: label,
        },
    });

    const handleOpenMenu = () => {
        setOpenMenu(true);
    };
    const handleCloseMenu = () => {
        setOpenMenu(false);
    };
    const handleOpenRenameForm = () => {
        handleCloseMenu();
        setOpenRenameForm(true);
    };
    const handleCloseRenameForm = () => {
        setOpenRenameForm(false);
    };

    return (
        <>
            <Stack direction="row"
                   justifyContent="space-between"
                   alignItems="center"
                   spacing={1}
                   sx={{
                       pt: 3,
                   }}>
                <Button
                    onClick={handleOpenRenameForm}
                    sx={{
                        textTransform: 'none',
                    }}>
                    <Tooltip title={label}>
                        <ListItemText
                            primary={label}
                            primaryTypographyProps={{
                                noWrap: true,
                                variant: 'subtitle2'
                            }}
                        />
                    </Tooltip>
                </Button>
                <IconButton ref={anchorRef} size="large" onClick={handleOpenMenu}
                             color={openMenu ? 'inherit' : 'default'}>
                    <Icon icon={moreHorizontalFill} width={40} height={40}/>
                </IconButton>
            </Stack>

            <MenuPopover
                open={openMenu} onClose={handleCloseMenu}
                anchorEl={anchorRef.current} sx={{py: 1, width: 'auto'}}>
                <MenuItem onClick={handleOpenRenameForm} sx={{py: 0.75, px: 1.5}}>
                    <Box component={Icon} icon={editFill} sx={{width: 20, height: 20, flexShrink: 0, mr: 1}}/>
                    <Typography variant="body2">{i18nMap.rename}</Typography>
                </MenuItem>
                <MenuItem onClick={onDelete} sx={{py: 0.75, px: 1.5}}>
                    <Box component={Icon} icon={trash2Outline} sx={{width: 20, height: 20, flexShrink: 0, mr: 1}}/>
                    <Typography variant="body2">{i18nMap.delete}</Typography>
                </MenuItem>
            </MenuPopover>
            <Dialog
                fullWidth
                maxWidth="xs"
                open={openRenameForm}
                onClose={handleCloseRenameForm}>
                <DialogTitle sx={{mb: 1}}>{i18nMap.labelFormTitle}</DialogTitle>
                <DialogContent>
                    <Controller
                        name={FORM_FIELDS.label}
                        control={control}
                        render={({
                            field,
                            fieldState: {error},
                        }) => {
                            return (
                                <Stack spacing={1}>
                                    <TextField
                                        fullWidth
                                        {...field}
                                    />
                                    {Boolean(error?.message) && (
                                        <Typography
                                            sx={{color: 'error.main'}}>
                                            {error?.message}
                                        </Typography>
                                    )}
                                </Stack>
                            );
                        }}
                    />
                </DialogContent>
                <DialogActions>
                    <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                        <Button color="inherit"
                                onClick={handleCloseRenameForm}>
                            {translate('cancel')}
                        </Button>
                        <SubmitButton
                            onClick={() => {
                                onUpdate(watch()[FORM_FIELDS.label]);
                                handleCloseRenameForm();
                            }}
                            isValid={!errors[FORM_FIELDS.label]}
                        />
                    </Stack>
                </DialogActions>
            </Dialog>
        </>
    );
};

DragDropTableColumnHeader.propTypes = propTypes;

export default DragDropTableColumnHeader;
