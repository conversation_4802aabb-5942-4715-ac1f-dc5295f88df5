import {useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {But<PERSON>, Stack} from '@material-ui/core';
import {yupResolver} from '@hookform/resolvers/yup';
import {
    SCREENSHOT_IMAGE_TYPE,
    SELECTOR_TARGETS,
} from '@w7-3/webeagle-resources/dist/config/screenshots';
import ScreenshotCapture, {useScreenshotCapture, useScreenshotDialog} from '../../../utils/ScreenshotCapture';
import {SolutionItemPropTypes} from '../../../../../config/prop-types/ProjectSteps';
import SubmitButton from '../../../utils/SubmitButton';
import useLocales from '../../../../../hooks/useLocales';

const propTypes = {
    ...SolutionItemPropTypes,
};

const CustomScreenshots = ({
    item,
    handleCancel,
    handleSave,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    labelNode,
    list,
    parentFormContext,
}) => {
    const {translate} = useLocales();
    const {schema} = useScreenshotDialog({id: item?.id, stepList: list, showNamingField: true});
    const [isValid, setIsValid] = useState(false);
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues: {
            ...item.value,
            target: SCREENSHOT_IMAGE_TYPE.viewport,
            selectorTargets: SELECTOR_TARGETS.all,
            customSelectorTargets: '',
        },
    });

    useScreenshotCapture({formContext, setIsValid});
    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <>
            <ScreenshotCapture
                formContext={formContext}
            />
            {labelNode}
            {ignoreOnErrorNode}
            {abortOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: formContext.watch(),
                        });
                    }}
                    isValid={isParentValid && isValid}
                />
            </Stack>
        </>
    );
};

CustomScreenshots.propTypes = propTypes;

export default CustomScreenshots;
