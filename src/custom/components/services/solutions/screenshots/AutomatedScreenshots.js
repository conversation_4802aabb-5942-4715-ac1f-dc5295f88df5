import {useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Box, Button, FormControlLabel, Slide<PERSON>, Stack, Switch} from '@material-ui/core';
import {yupResolver} from '@hookform/resolvers/yup';
import {confidenceLevelThresholds, confidenceLevelDefault} from '@w7-3/webeagle-resources/dist/config/ai';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import getConfidenceLevelInterpretation from '@w7-3/webeagle-resources/dist/libs/getConfidenceLevelInterpretation';
import {useScreenshotCapture, useScreenshotDialog} from '../../../utils/ScreenshotCapture';
import {SolutionItemPropTypes} from '../../../../../config/prop-types/ProjectSteps';
import LabelStyle from '../../../utils/LabelStyle';
import SubmitButton from '../../../utils/SubmitButton';
import useLocales from '../../../../../hooks/useLocales';
import CodeTextFieldAI from '../../../forms/CodeTextFieldAI';
import {getAssistantAPIPath} from '../../../../utils/getPath';
import useApiCaller from '../../../../hooks/useApiCaller';
import InfoPopover from '../../../utils/InfoPopover';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.screenshots';

const propTypes = {
    ...SolutionItemPropTypes,
};

const AutomatedScreenshots = ({
    item,
    handleCancel,
    handleSave,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    labelNode,
    list,
    parentFormContext,
}) => {
    const {translate, currentLang} = useLocales();
    const {schema} = useScreenshotDialog({id: item?.id, stepList: list, showNamingField: true});
    const [isScreenshotCaptureValid, setIsScreenshotCaptureValid] = useState(false);
    const [isCodeValid, setIsCodeValid] = useState(item?.value?.ai?.instructions?.isValid);
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);

    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues: {
            ...item?.value,
            ai: {
                ...item?.value?.ai,
                confidenceLevel: item?.value?.ai?.confidenceLevel || confidenceLevelDefault,
                instructions: item?.value?.ai?.instructions || {code: '', isValid: false, isValidated: false},
                reuseSelectors: true,
            },
        },
    });
    const apiCaller = useApiCaller();
    const expectationExamples = translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.expectationExamples`, {
        returnObjects: true,
    });
    const onValidate = ({
        value,
        successCallback,
        failureCallback,
        errorCallback,
    }) => {
        if (!isNonEmptyString(value)) {
            return;
        }

        apiCaller({
            uri: getAssistantAPIPath(serverlessAPI.assistantApi.uris.validateSolutionCode),
            data: {
                request: 'ScreenshotElementRetrieval',
                prompt: value,
                language: currentLang?.value,
            },
            isAnimated: true,
            successCallback,
            failureCallback,
            errorCallback,
        });
    }

    useScreenshotCapture({
        formContext,
        setIsValid: setIsScreenshotCaptureValid,
    });

    return (
        <>
            <Controller
                name="ai.instructions"
                control={formContext.control}
                render={({
                    field,
                }) => (
                    <CodeTextFieldAI
                        defaultCode={field?.value}
                        onChange={(newCode) => {
                            if (newCode?.isValidated && newCode?.isValid) {
                                field.onChange(newCode);
                                setIsCodeValid(true);
                                return;
                            }

                            setIsCodeValid(false);
                        }}
                        label={translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.title`)}
                        {...isNonEmptyArray(expectationExamples) && {
                            placeholder: expectationExamples.join(`\n\n-${translate('or')}-\n\n`),
                        }}
                        onValidate={onValidate}
                        textFieldProps={{
                            rows: 7,
                        }}
                    />
                )}
            />
            <Stack
                direction="row"
                alignItems="center"
                spacing={.5}
                sx={{
                    my: 3,
                }}
            >
                <LabelStyle>{translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.confidenceLevel.label`)}</LabelStyle>
                <InfoPopover
                    isLabel>
                    {translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.confidenceLevel.description`)}
                </InfoPopover>
            </Stack>
            <Controller
                name="ai.confidenceLevel"
                control={formContext.control}
                render={({
                    field,
                }) => {
                    const confidenceLevelInterpretation = getConfidenceLevelInterpretation({
                        value: field.value,
                    });
                    return (
                        <Stack
                            alignItems="center"
                            spacing={2}
                            sx={{
                                pb: 3,
                            }}
                        >
                            <Alert severity={confidenceLevelInterpretation.severity} sx={{width: '100%'}}>
                                {translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.confidenceLevel.interpretation.${confidenceLevelInterpretation.code}`, {
                                    value: `${field?.value}%`,
                                })}
                            </Alert>
                            <Box sx={{
                                width: '100%',
                                minWidth: 400,
                                px: 2,
                            }}>
                                <Slider
                                    {...field}
                                    step={1}
                                    marks={Object.values(confidenceLevelThresholds).map((value) => ({
                                        value,
                                        label: `${value}%`,
                                    }))}
                                    valueLabelDisplay="on"
                                    valueLabelFormat={(value) => `${value}%`}
                                    min={1}
                                    max={100}
                                />
                            </Box>
                        </Stack>
                    );
                }}
            />
            <div>
                <Stack spacing={1} direction="row" alignItems="center">
                    <LabelStyle>
                        {translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.cachedSelectors.label`)}
                    </LabelStyle>
                    <InfoPopover
                        isLabel>
                        {translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.cachedSelectors.description`)}
                    </InfoPopover>
                </Stack>
                <Controller
                    name="ai.reuseSelectors"
                    control={formContext.control}
                    render={({field}) => {
                        return (
                            <FormControlLabel
                                label={translate(`${i18nContext}.groups.${item?.categoryId}.options.${item?.type}.cachedSelectors.label`)}
                                control={
                                    <Switch
                                        onChange={(_, value) => {
                                            field.onChange(value);
                                        }}
                                        checked={field.value}
                                    />
                                }
                            />
                        );
                    }}
                />
            </div>
            {labelNode}
            {ignoreOnErrorNode}
            {abortOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: formContext.watch(),
                        });
                    }}
                    isValid={isParentValid && isScreenshotCaptureValid && isCodeValid}
                />
            </Stack>
        </>
    );
};

AutomatedScreenshots.propTypes = propTypes;

export default AutomatedScreenshots;
