import {useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Button, MenuItem, Stack, TextField} from '@material-ui/core';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import useLocales from '../../../../../hooks/useLocales';
import CodeTextFieldAI from '../../../forms/CodeTextFieldAI';
import SubmitButton from '../../../utils/SubmitButton';
import {SolutionItemPropTypes} from '../../../../../config/prop-types/ProjectSteps';
import useApiCaller from '../../../../hooks/useApiCaller';
import {getAssistantAPIPath} from '../../../../utils/getPath';
import {i18nContext} from '../../conditions/AutomatedCondition';

const propTypes = {
    ...SolutionItemPropTypes,
};

export const defaultProps = {
    item: {},
};

const AutomatedUrlChallenge = ({
    item,
    handleCancel,
    handleSave,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    labelNode,
    i18nContextRoot,
    parentFormContext,
}) => {
    const {translate, currentLang} = useLocales();
    const [isCodeValid, setIsCodeValid] = useState(false);
    const [isValidContent, setIsValidContent] = useState(false);
    const apiCaller = useApiCaller();
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: item.value,
    });
    const expectationExamples = translate(`${i18nContextRoot}.groups.${item?.categoryId}.options.${item?.type}.expectationExamples`, {
        returnObjects: true,
    });
    const {control} = parentFormContext;
    const indecisiveCaseOptions = translate(`${i18nContextRoot}.groups.${item?.categoryId}.options.${item?.type}.indecisiveCase.options`, {
        returnObjects: true,
    });
    const onValidate = ({
        value,
        successCallback,
        failureCallback,
        errorCallback,
    }) => {
        if (!isNonEmptyString(value)) {
            return;
        }

        apiCaller({
            uri: getAssistantAPIPath(serverlessAPI.assistantApi.uris.validateSolutionCode),
            data: {
                request: 'URLChallengeEvaluation',
                prompt: value,
                language: currentLang?.value,
            },
            isAnimated: true,
            successCallback,
            failureCallback,
            errorCallback,
        });
    }

    useEffect(() => {
        setIsValidContent(isCodeValid && parentFormContext.formState.isValid);
    }, [isCodeValid, parentFormContext.formState.isValid]);

    return (
        <Stack spacing={3}
               sx={{width: '100%'}}>
            <Controller
                name="code"
                control={formContext.control}
                render={({
                    field,
                }) => (
                    <CodeTextFieldAI
                        defaultCode={field?.value}
                        onChange={(newCode) => {
                            if (newCode?.isValidated && newCode?.isValid) {
                                field.onChange(newCode);
                                setIsCodeValid(true);
                                return;
                            }

                            setIsCodeValid(false);
                        }}
                        label={translate(`${i18nContext}.inputNotice`)}
                        {...isNonEmptyArray(expectationExamples) && {
                            placeholder: expectationExamples.join(`\n\n-${translate('or')}-\n\n`),
                        }}
                        onValidate={onValidate}
                    />
                )}
            />
            <Alert severity="info">
                {translate(`${i18nContextRoot}.groups.${item?.categoryId}.options.${item?.type}.indecisiveCase.description`)}
            </Alert>
            <Controller
                name="indecisiveCase"
                control={control}
                render={({field}) => {
                    return (
                        <TextField
                            select
                            fullWidth
                            {...field}
                            label={translate(`${i18nContextRoot}.groups.${item?.categoryId}.options.${item?.type}.indecisiveCase.label`)}>
                            {Object.keys(indecisiveCaseOptions).map((key) => (
                                <MenuItem key={key} value={key}>
                                    {indecisiveCaseOptions[key]}
                                </MenuItem>
                            ))}
                        </TextField>
                    );
                }}
            />
            {labelNode}
            {ignoreOnErrorNode}
            {abortOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        const {
                            code,
                        } = formContext.watch();
                        handleSave({
                            value: {
                                code,
                            },
                        });
                    }}
                    isValid={isValidContent}
                />
            </Stack>
        </Stack>
    );
};

AutomatedUrlChallenge.propTypes = propTypes;
AutomatedUrlChallenge.defaultProps = defaultProps;

export default AutomatedUrlChallenge;
