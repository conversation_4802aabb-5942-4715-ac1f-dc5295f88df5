import {useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {<PERSON><PERSON>, But<PERSON>, FormControlLabel, Radio, RadioGroup, Stack, Typography} from '@material-ui/core';
import {yupResolver} from '@hookform/resolvers/yup';
import {visualCompareOptions} from '@w7-3/webeagle-resources/dist/config/e2eVisualTests';
import ScreenshotCapture, {
    useScreenshotCapture,
    useScreenshotDialog,
} from '../../../utils/ScreenshotCapture';
import {SolutionItemPropTypes} from '../../../../../config/prop-types/ProjectSteps';
import LabelStyle from '../../../utils/LabelStyle';
import SubmitButton from '../../../utils/SubmitButton';
import useLocales from '../../../../../hooks/useLocales';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.e2eVisualTests';

const propTypes = {
    ...SolutionItemPropTypes,
};

const CustomE2EVisualTests = ({
    item,
    handleCancel,
    handleSave,
    abortOnErrorNode,
    ignoreOnErrorNode,
    willIgnoreOnErrorNode,
    labelNode,
    list,
    parentFormContext,
}) => {
    const {translate} = useLocales();
    const {schema} = useScreenshotDialog({id: item?.id, stepList: list, showNamingField: true});
    const [isValid, setIsValid] = useState(false);
    const [isParentValid, setIsParentValid] = useState(parentFormContext.formState.isValid);
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues: {
            ...item.value,
            strategy: item.value.strategy || visualCompareOptions.defaultValue,
        },
    });

    useScreenshotCapture({formContext, setIsValid});
    useEffect(() => {
        setIsParentValid(parentFormContext.formState.isValid);
    }, [parentFormContext.formState.isValid]);

    return (
        <>
            <ScreenshotCapture
                formContext={formContext}
                isSingleSelector
            />
            <Stack spacing={1}>
                <LabelStyle sx={{mt: '0 !important'}}>
                    {translate(`${i18nContext}.tolerance.label`)}
                </LabelStyle>
                <Controller
                    name="strategy"
                    control={formContext.control}
                    render={({
                        field,
                    }) => {
                        const strategyI18nData = translate(`${i18nContext}.tolerance.strategy`, {returnObjects: true});
                        return (
                            <>
                                <RadioGroup
                                    {...field}
                                    row>
                                    <Stack spacing={1} direction="row">
                                        {Object.keys(visualCompareOptions.strategy).map((strategy) => (
                                            <FormControlLabel
                                                key={strategy}
                                                value={strategy}
                                                control={<Radio />}
                                                label={strategyI18nData[strategy].label} />
                                        ))}
                                    </Stack>
                                </RadioGroup>
                                <Alert severity="info">
                                    {
                                        strategyI18nData[field.value].items.map((value) => (
                                            <Typography
                                                key={value}>{value}</Typography>
                                        ))
                                    }
                                </Alert>
                            </>
                        );
                    }}
                />
            </Stack>
            {labelNode}
            {ignoreOnErrorNode}
            {abortOnErrorNode}
            {willIgnoreOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        handleSave({
                            value: formContext.watch(),
                        });
                    }}
                    isValid={isParentValid && isValid}
                />
            </Stack>
        </>
    );
};

CustomE2EVisualTests.propTypes = propTypes;

export default CustomE2EVisualTests;
