import {allParamNames} from '@w7-3/webeagle-resources/dist/config/specs';
import CustomJS from '../CustomJS';
import useLocales from '../../../../../hooks/useLocales';
import {ItemRendererPropTypes} from '../../../../../config/prop-types/ProjectSteps';

const CustomDataExtractions = (props) => {
    const {i18nContextRoot, item} = props;
    const {translate} = useLocales();

    return (
        <CustomJS
            {...props}
            config={{
                prePlaceholder: `function getData(${allParamNames.state.fe}) {`,
                postPlaceholder: '}',
                modifier: 'standard',
                paramsDocs: allParamNames.state,
                label: translate(`${i18nContextRoot}.groups.${item?.categoryId}.options.${item?.type}.fields.reducer`),
            }}
        />
    );
};

CustomDataExtractions.propTypes = ItemRendererPropTypes;

export default CustomDataExtractions;
