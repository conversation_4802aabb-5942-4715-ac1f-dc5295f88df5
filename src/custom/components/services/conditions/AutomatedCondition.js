import {useEffect, useState} from 'react';
import {Al<PERSON>, Box, Button, Slider, Stack} from '@material-ui/core';
import {Controller, useForm} from 'react-hook-form';
import {isNonEmptyArray, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {serverlessAPI} from '@w7-3/webeagle-resources/dist/config/api';
import conditions from '@w7-3/webeagle-resources/dist/config/conditions';
import getConfidenceLevelInterpretation from '@w7-3/webeagle-resources/dist/libs/getConfidenceLevelInterpretation';
import {confidenceLevelThresholds, confidenceLevelDefault} from '@w7-3/webeagle-resources/dist/config/ai';
import useLocales from '../../../../hooks/useLocales';
import SubmitButton from '../../utils/SubmitButton';
import {ConditionItemPropTypes} from '../../../../config/prop-types/ProjectSteps';
import useApiCaller from '../../../hooks/useApiCaller';
import {getAssistantAPIPath} from '../../../utils/getPath';
import CodeTextFieldAI from '../../forms/CodeTextFieldAI';
import LabelStyle from '../../utils/LabelStyle';
import InfoPopover from '../../utils/InfoPopover';

export const i18nContext = `conditions.options.${conditions.ai.value}`;

const AutomatedCondition = ({
    item,
    handleCancel,
    handleSave,
    labelNode,
    abortOnErrorNode,
    parentFormContext,
}) => {
    const {translate, currentLang} = useLocales();
    const [isCodeValid, setIsCodeValid] = useState(item?.value?.code?.isValid || false);
    const [isValidContent, setIsValidContent] = useState(item?.value?.isValidated || false);
    const apiCaller = useApiCaller();
    const formContext = useForm({
        mode: 'onChange',
        defaultValues: {
            ...item.value,
            confidenceLevel: item?.value?.confidenceLevel || confidenceLevelDefault,
        },
    });
    const expectationExamples = translate(`${i18nContext}.expectationExamples`, {
        returnObjects: true,
    });
    const onValidate = ({
        value,
        successCallback,
        failureCallback,
        errorCallback,
    }) => {
        if (!isNonEmptyString(value)) {
            return;
        }

        apiCaller({
            uri: getAssistantAPIPath(serverlessAPI.assistantApi.uris.validateSolutionCode),
            data: {
                request: 'ConditionEvaluation',
                prompt: value,
                language: currentLang?.value,
            },
            isAnimated: true,
            successCallback,
            failureCallback,
            errorCallback,
        });
    }

    useEffect(() => {
        setIsValidContent(isCodeValid && parentFormContext.formState.isValid);
    }, [isCodeValid, parentFormContext.formState.isValid]);

    return (
        <Stack
            spacing={3}
            sx={{width: '100%'}}>
            <Controller
                name="code"
                control={formContext.control}
                render={({
                    field,
                }) => (
                    <CodeTextFieldAI
                        defaultCode={field?.value}
                        onChange={(newCode) => {
                            if (newCode?.isValidated && newCode?.isValid) {
                                field.onChange(newCode);
                                setIsCodeValid(true);
                                return;
                            }

                            setIsCodeValid(false);
                        }}
                        label={translate(`${i18nContext}.inputNotice`)}
                        {...isNonEmptyArray(expectationExamples) && {
                            placeholder: expectationExamples.join(`\n\n-${translate('or')}-\n\n`),
                        }}
                        onValidate={onValidate}
                    />
                )}
            />
            <Stack
                direction="row"
                alignItems="center"
                spacing={.5}>
                <LabelStyle>{translate(`${i18nContext}.confidenceLevel.label`)}</LabelStyle>
                <InfoPopover
                    isLabel>
                    {translate(`${i18nContext}.confidenceLevel.description`)}
                </InfoPopover>
            </Stack>
            <Controller
                name="confidenceLevel"
                control={formContext.control}
                render={({
                    field,
                }) => {
                    const confidenceLevelInterpretation = getConfidenceLevelInterpretation({
                        value: field.value,
                    });
                    return (
                        <Stack
                            alignItems="center"
                            spacing={2}
                            sx={{
                                pb: 3,
                            }}
                        >
                            <Alert severity={confidenceLevelInterpretation.severity} sx={{width: '100%'}}>
                                {translate(`${i18nContext}.confidenceLevel.interpretation.${confidenceLevelInterpretation.code}`, {
                                    value: `${field?.value}%`,
                                })}
                            </Alert>
                            <Box sx={{
                                width: '100%',
                                minWidth: 400,
                                px: 2,
                            }}>
                                <Slider
                                    {...field}
                                    step={1}
                                    marks={Object.values(confidenceLevelThresholds).map((value) => ({
                                        value,
                                        label: `${value}%`,
                                    }))}
                                    valueLabelDisplay="on"
                                    valueLabelFormat={(value) => `${value}%`}
                                    min={1}
                                    max={100}
                                />
                            </Box>
                        </Stack>
                    );
                }}
            />
            {labelNode}
            {abortOnErrorNode}
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                <Button color="inherit" onClick={handleCancel}>
                    {translate('cancel')}
                </Button>
                <SubmitButton
                    onClick={() => {
                        const {
                            code,
                            confidenceLevel,
                        } = formContext.watch();
                        handleSave({
                            value: {
                                code,
                                confidenceLevel,
                            },
                        });
                    }}
                    isValid={isValidContent}
                />
            </Stack>
        </Stack>
    );
};

AutomatedCondition.propTypes = ConditionItemPropTypes;

export default AutomatedCondition;
