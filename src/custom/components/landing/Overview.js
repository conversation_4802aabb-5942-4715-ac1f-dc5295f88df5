import PubSub from 'pubsub-js';
import {Link as RouterLink} from 'react-router-dom';
import {motion} from 'framer-motion';
import {styled} from '@material-ui/core/styles';
import {Icon} from '@iconify/react';
import {<PERSON>, Button, Chip, Container, Stack, Typography} from '@material-ui/core';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {varFadeIn, varFadeInRight, varWrapEnter} from '../../../components/animate';
import useLocales from '../../../hooks/useLocales';
import {globalConfig} from '../../../config/setup';
import OverviewTeaser from '../features/OverviewTeaser';

const RootStyle = styled(motion.div)(({theme}) => ({
    backgroundColor: theme.palette.grey[400],
    top: 0,
    left: 0,
    width: '100%',
    height: '100vh',
    display: 'flex',
    position: 'fixed',
    alignItems: 'center',
}));

const ContentStyle = styled((props) => <Stack spacing={5} {...props} />)(({theme}) => ({
    zIndex: 10,
    margin: 'auto',
    textAlign: 'center',
    position: 'relative',
    paddingTop: theme.spacing(10),
    height: '90%',
    justifyContent: 'flex-start',
    [theme.breakpoints.up('md')]: {
        paddingTop: theme.spacing(15),
        margin: 'unset',
        textAlign: 'left',
    },
}));


const HeroOverlayStyle = styled(motion.img)({
    zIndex: 9,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    position: 'absolute',
});

const Overview = () => {
    const {translate} = useLocales();

    return (
        <>
            <RootStyle initial="initial" animate="animate" variants={varWrapEnter}>
                <HeroOverlayStyle alt="overlay" src="/static/overlay.svg" variants={varFadeIn}/>
                <Container maxWidth="xl" sx={{position: 'relative', height: '100%'}}>
                    <ContentStyle>
                        <Stack
                            spacing={3}
                            sx={{
                                pb: 3,
                                px: {
                                    xs: 0,
                                    md: 5,
                                },
                            }}>
                            <Stack
                                direction={{
                                    xs: 'column',
                                    lg: 'row',
                                }}
                                spacing={12}
                                justifyContent="space-between"
                                sx={{
                                    my: {
                                        xs: 1,
                                        md: 6,
                                    },
                                    height: {
                                        xs: 'auto',
                                        lg: '60vh',
                                    },
                                }}
                            >
                                <motion.div
                                    variants={varFadeInRight}
                                >
                                    <Typography
                                        component="h1"
                                        variant="h1"
                                        sx={{
                                            color: 'primary.main',
                                            display: 'inline-block',
                                            lineHeight: '120%',
                                            fontSize: {
                                                xs: '2rem !important',
                                                lg: '4rem !important',
                                            },
                                        }}>
                                        {translate('intro.title')}
                                    </Typography>
                                    <Stack
                                        justifyContent="space-around"
                                        alignItems="center"
                                        spacing={3}>
                                        <Button
                                            variant="outlined"
                                            startIcon={(
                                                <Icon
                                                    icon="eva:message-square-outline"
                                                    width={40}
                                                    height={40}
                                                />
                                            )}
                                            onClick={() => {
                                                PubSub.publish('SHOW.DIALOG', {
                                                    type: 'aiAssistant',
                                                    initialPrompt: translate('assistant.prompts.exploreFeatures.description', {
                                                        app: globalConfig.domain,
                                                    }),
                                                });
                                            }}
                                            sx={{
                                                textTransform: 'none',
                                                my: {
                                                    xs: 2,
                                                    lg: 6,
                                                },
                                            }}
                                        >
                                            {translate('assistant.prompts.exploreFeatures.label')}
                                        </Button>
                                        <Button
                                            variant="contained"
                                            size="large"
                                            to={PATH_PAGE.pricing}
                                            sx={{color: 'common.white'}}
                                            component={RouterLink}
                                            endIcon={<Icon icon="eva:round-arrow-right-alt" width={40} height={40}/>}>
                                            {translate('pricing.cta')}
                                        </Button>
                                    </Stack>
                                </motion.div>
                                <OverviewTeaser />
                            </Stack>
                            <Stack
                                justifyContent="space-between"
                                direction={{
                                    xs: 'column',
                                    lg: 'row',
                                }}
                                flexWrap="wrap"
                            >
                                {
                                    [
                                        {
                                            text: translate('intro.noCoding'),
                                            breakpoint: 'xs',
                                        },
                                        {
                                            text: translate('intro.easySetup'),
                                            breakpoint: 'xs',
                                        },
                                        {
                                            text: translate('intro.automated'),
                                            breakpoint: 'xs',
                                        },
                                        {
                                            text: translate('intro.maintenance'),
                                            breakpoint: 'xs',
                                        },
                                    ].map(({text, breakpoint}, index) => (
                                        <Stack key={index} sx={{alignSelf: 'center', my: 1.5}}>
                                            <Chip
                                                icon={<Icon
                                                    color="#fff"
                                                    icon="eva:checkmark-square-outline"
                                                    width={40}
                                                    height={40}
                                                />}
                                                label={text}
                                                sx={{
                                                    py: 3,
                                                    px: 1,
                                                    borderRadius: 1,
                                                    color: 'common.white',
                                                    fontSize: '1em !important',
                                                    display: {
                                                        xs: 'none',
                                                        [breakpoint]: 'inline-flex',
                                                    },
                                                }}
                                            />
                                        </Stack>
                                    ))
                                }
                            </Stack>
                        </Stack>
                    </ContentStyle>
                </Container>
            </RootStyle>
            <Box sx={{height: '100vh'}}/>
        </>
    );
};

export default Overview;
