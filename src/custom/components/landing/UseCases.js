import {Accordion, AccordionDetails, AccordionSummary, Box, Divider, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import chevronRightOutline from '@iconify/icons-eva/chevron-right-outline';
import {BROWSERS} from '@w7-3/webeagle-resources/dist/config/scrapper';
import ReactDOMServer from 'react-dom/server';
import useLocales from '../../../hooks/useLocales';
import {globalConfig} from '../../../config/setup';
import DefinitionList from '../utils/DefinitionList';
import BrowserCard from '../utils/BrowserCard';

export const getRandomArbitrary = (min, max) => {
    return Math.round(Math.random() * (max - min) + min);
}

const numberOfItems = getRandomArbitrary(7, 19);
const durationInMins = Math.round(numberOfItems * 3);

const Features = () => {
    const {translate} = useLocales();
    const browsersHTML = ReactDOMServer.renderToStaticMarkup(
        <Stack direction="row" alignItems="center" spacing={1} sx={{
            display: 'inline-flex',
            px: .5,
        }}>
            {Object.values(BROWSERS).map((item) => {
                return (
                    <BrowserCard
                        key={item?.key}
                        item={item}/>
                );
            })}
        </Stack>
    );
    const realLiveExamples = Object.values(translate('solutionsUsage.examples', {
        returnObjects: true,
        durationInMins,
    }));
    return (
        <Stack spacing={3} sx={{pb: 3}}>
            <Typography variant="h4">
                {translate('solutionsUsage.options.label')}
            </Typography>
            <DefinitionList
                dataList={realLiveExamples.map(({
                    title,
                    label,
                    items,
                    timeSpent,
                }) => ({
                    label: title,
                    node: (
                        <Stack spacing={3}>
                            <Typography component="strong">
                                {label}
                            </Typography>
                            <Stack spacing={1}>
                                {
                                    items.map((step) => (
                                        <Stack key={step} direction="row" alignItems="center" spacing={1} flexWrap="wrap">
                                            <Icon icon={chevronRightOutline} width={40} height={40}/>
                                            <Typography component="div" variant="body2" sx={{color: 'text.secondary'}}>
                                                {step}
                                            </Typography>
                                        </Stack>
                                    ))
                                }
                            </Stack>
                            <Typography component="div" variant="body2">
                                {timeSpent}
                            </Typography>
                        </Stack>
                    )
                }))}
                variant={DefinitionList.VARIANTS.tabs}
            />
            <Typography
                component="strong"
                variant="subtitle2"
                sx={{pt: 1}}
            >
                {translate('solutionsUsage.automatizeIt', {
                    app: globalConfig.domain,
                })}
            </Typography>
            <Typography
                component="div"
                variant="body2"
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    flexWrap: 'wrap',
                }}
                dangerouslySetInnerHTML={{
                    __html: translate('solutionsUsage.executionAnytimeAnyBrowserAnyPlace', {
                        browsers: browsersHTML,
                    })
                }}
            />
        </Stack>
    );
};

export default Features;
