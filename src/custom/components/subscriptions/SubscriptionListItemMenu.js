import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {useRef, useState} from 'react';
import PubSub from 'pubsub-js';
import moreVerticalFill from '@iconify/icons-eva/more-vertical-fill';
import {IconButton, ListItemText, MenuItem} from '@material-ui/core';
import useLocales from '../../../hooks/useLocales';
import MenuPopover from '../../../components/MenuPopover';

const propTypes = {
    item: PropTypes.object,
};

const SubscriptionListItemMenu = ({item}) => {
    const ref = useRef(null);
    const [isOpen, setIsOpen] = useState(false);
    const {translate} = useLocales();

    return (
        <>
            <IconButton
                ref={ref}
                onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    setIsOpen(true);
                }}
                sx={{
                    position: 'relative',
                    zIndex: 1000,
                }}
                color="primary"

            >
                <Icon icon={moreVerticalFill} width={40} height={40}/>
            </IconButton>

            <MenuPopover
                open={isOpen}
                anchorEl={ref.current}
                onClose={() => setIsOpen(false)}>
                <MenuItem
                    onClick={(event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        setIsOpen(false);
                        PubSub.publish('OPEN.PAYMENT.ITEM-DETAILS', {item});
                    }}
                    sx={{color: 'text.secondary'}}>
                    <ListItemText primary={translate('transactions.subscriptions.viewItem')} primaryTypographyProps={{variant: 'body2'}}/>
                </MenuItem>
                <MenuItem
                    component="a"
                    href={item?.checkout?.invoiceUrl}
                    target="_blank"
                    sx={{color: 'text.secondary'}}>
                    <ListItemText
                        primary={translate('transactions.subscriptions.openInvoice')}
                        primaryTypographyProps={{variant: 'body2'}}
                        secondaryTypographyProps={{variant: 'caption'}}
                    />
                </MenuItem>
            </MenuPopover>
        </>
    );
};

SubscriptionListItemMenu.propTypes = propTypes;

export default SubscriptionListItemMenu;
