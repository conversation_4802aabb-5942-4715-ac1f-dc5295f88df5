import {Button, Stack, Typography} from '@material-ui/core';
import {Link as RouterLink} from 'react-router-dom';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../hooks/useLocales';

const MissingTransactionItem = () => {
    const {translate} = useLocales();

    return (
        <>
            <Typography
                variant="body2"
                textAlign="center"
                sx={{color: 'text.secondary'}}>
                {translate('transactions.missingItem')}
            </Typography>
            <Stack
                direction={{
                    xs: 'column',
                    sm: 'row',
                }}
                alignItems="center"
                justifyContent="center"
                spacing={3}>
                <Button
                    to={PATH_PAGE.contact}
                    component={RouterLink}
                    sx={{textTransform: 'none'}}>
                    {translate('contactUs.label')}
                </Button>
                <Button
                    to={PATH_PAGE.pricing}
                    component={RouterLink}
                    sx={{textTransform: 'none'}}>
                    {translate('getStarted')}
                </Button>
            </Stack>
        </>
    );
};

export default MissingTransactionItem;
