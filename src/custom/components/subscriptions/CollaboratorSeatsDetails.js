import PropTypes from 'prop-types';
import {PurchaseType} from '../../../config/prop-types/SubscriptionTypes';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import DefinitionList from '../utils/DefinitionList';
import Label from '../../../components/Label';

const propTypes = {
    item: PurchaseType,
    collaboratorItem: PropTypes.object,
};

const CollaboratorSeatsDetails = ({item, collaboratorItem}) => {
    const {translate} = useLocales();
    const {
        checkout: {
            created,
        },
    } = item;

    return (
        <DefinitionList
            dataList={[
                {
                    key: translate('pricing.otp.collaboratorSeats.label'),
                    node: collaboratorItem.quantity,
                },
                {
                    key: translate('transactions.validity'),
                    node: translate('dateFrom', {
                        startDate: fDateTimeHumanReadable(created),
                    }),
                },
                {
                    key: translate('transactions.automationCreditsTopUp.fields.billingCycle'),
                    node: (
                        <Label
                            color="info"
                            sx={{
                                m: '0 !important',
                                p: 2,
                            }}>
                            {item.checkout.billingCycle}
                        </Label>
                    ),
                },
            ]}
        />
    );
};

CollaboratorSeatsDetails.propTypes = propTypes;

export default CollaboratorSeatsDetails;
