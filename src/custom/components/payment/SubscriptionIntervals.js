import {useEffect} from 'react';
import {useTheme} from '@material-ui/core/styles';
import {Alert, Button, Stack, Typography, useMediaQuery} from '@material-ui/core';
import * as subscriptions from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import useLocales from '../../../hooks/useLocales';
import {updateInterval} from '../../../redux/slices/pricing';
import {useDispatch} from '../../../redux/store';
import usePricing from '../../../hooks/usePricing';

const i18nContext = 'pricing.subscriptions';
const savingInPercent = 25;

const SubscriptionIntervals = () => {
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('sm'));
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const onChangeInterval = (value) => {
        dispatch(updateInterval(value));
    };
    const {pricing, itemGroups} = usePricing();
    const activeInterval = itemGroups?.subscriptionsCartItemList?.list?.[0]?.interval;

    useEffect(() => {
        if (!activeInterval || pricing.interval === activeInterval) {
            return;
        }

        onChangeInterval(activeInterval);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeInterval]);

    return (
        <Stack spacing={3}>
            <Stack
                direction={isDesktop ? 'row' : 'column'}
                justifyContent="center"
                spacing={3}>
                {Object.values(subscriptions.plans).map((item) => {
                    return (
                        <Button
                            key={item.appName}
                            variant={item.appName === pricing.interval ? 'contained' : 'outlined'}
                            size="large"
                            disabled={Boolean(activeInterval) && item.appName !== activeInterval}
                            onClick={() => onChangeInterval(item.appName)}>
                            {translate(`${i18nContext}.intervals.${item.appName}.label`)}
                        </Button>
                    );
                })}
            </Stack>
            {
                activeInterval && (
                    <Alert
                        severity="warning"
                    >
                        {translate(`${i18nContext}.multiPackageInfo`)}
                    </Alert>
                )
            }
            <Typography
                component="p"
                sx={{
                    color: 'text.secondary',
                    opacity: pricing.interval === subscriptions.plans.monthlyX12.appName ? 0 : 1,
                }}>
                {translate(`${i18nContext}.saveInfo`, {savingInPercent})}
            </Typography>
        </Stack>
    );
};

export default SubscriptionIntervals;
