import {useCallback, useState} from 'react';
import {connect} from 'react-redux';
import {Icon} from '@iconify/react';
import {Navigate} from 'react-router-dom';
import ReactDOMServer from 'react-dom/server';
import shieldFill from '@iconify/icons-eva/shield-fill';
import {LoadingButton} from '@material-ui/lab';
import {Alert, Box, Divider, Stack, Typography, useMediaQuery} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {useTheme} from '@material-ui/core/styles';
import useLocales from '../../../hooks/useLocales';
import Price from './Price';
import useAuth from '../../../hooks/useAuth';
import {getCheckoutApiPath} from '../../utils/getPath';
import useApiCaller from '../../hooks/useApiCaller';
import Cart from '../Cart';
import usePricing from '../../../hooks/usePricing';
import {globalConfig} from '../../../config/setup';
import {UIStatePropType} from '../../../config/prop-types/ts/ui.d';

const mapStateToProps = ({state}) => {
    const {
        accountHolder,
        customerData,
    } = state;

    return {
        accountHolder,
        customerData,
    };
};

const propTypes = UIStatePropType;

const PaymentSummary = ({accountHolder}) => {
    const {translate, currentLang} = useLocales();
    const {user} = useAuth();
    const [isLoading, setIsLoading] = useState(false);
    const apiCaller = useApiCaller({
        handleRedirectDirective: true,
        handleNotificationDirective: true,
        handleInfoCodeDirectives: true,
        handleModalDirective: true,
        handleCart: true,
    });
    const {
        pricing,
        totalPrice,
        cartItemList,
    } = usePricing();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
    const handleSubmit = useCallback(() => {
        const {
            origin,
            pathname,
        } = window.location;
        setIsLoading(true);
        apiCaller({
            uri: getCheckoutApiPath(api.checkoutInit),
            data: {
                successUrl: `${origin}${PATH_PAGE.paymentSuccess}?${urlParameters.stripeCheckout.success}={CHECKOUT_SESSION_ID}`,
                cancelUrl: `${origin}${pathname}?${urlParameters.stripeCheckout.failure}={CHECKOUT_SESSION_ID}`,
                locale: currentLang.value,
                cartItemList,
            },
            successCallback: (result) => {
                const paymentGateway = result.data?.url;
                if (isNonEmptyString(paymentGateway)) {
                    window.location.href = paymentGateway;
                    return;
                }

                setIsLoading(false);
            },
            failureCallback: handleGenericError,
            errorCallback: handleGenericError,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cartItemList]);

    if (!Number.isInteger(pricing?.cartItemCount) || pricing?.cartItemCount < 1) {
        return <Navigate to={PATH_PAGE.pricing}/>;
    }

    const paymentProvider = ReactDOMServer.renderToStaticMarkup(
        <a href={globalConfig.paymentProviderLink}
           target="_blank"
           rel="noreferrer">{globalConfig.paymentProviderName}</a>
    );

    const handleGenericError = () => {
        setIsLoading(false);
    };

    return (
        <Stack spacing={3}>
            <Cart
                isCompact={isMobile}
            />
            <Stack
                sx={{mt: 3, p: 3, backgroundColor: (theme) => theme.palette.background.neutral}}
                spacing={3}
            >
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="flex-end"
                    spacing={3}>
                    <Typography variant="h6" component="p">
                        {translate('pricing.totalSum')}
                    </Typography>
                    <Typography
                        variant="h6"
                        component="p"
                        sx={{textTransform: 'uppercase'}}>
                        <Price
                            variant="b"
                            price={totalPrice}
                            currency="CHF"
                        />
                    </Typography>
                </Stack>
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="flex-end">
                    <Typography variant="caption" sx={{color: 'text.secondary', mt: -2, ml: 'auto'}}>
                        {translate('pricing.includesTaxesInfo')}
                    </Typography>
                </Stack>
                <Divider sx={{borderStyle: 'dashed'}}/>
                {
                    [
                        userRoles.ACCOUNT_HOLDER.key,
                        userRoles.ADMIN.key,
                    ].includes(user.role) ? (
                        <>
                            <Stack
                                direction="row"
                                alignItems="center"
                                justifyContent="flex-end">
                                <LoadingButton
                                    loading={isLoading}
                                    size="large"
                                    type="submit"
                                    variant="contained"
                                    onClick={handleSubmit}
                                    sx={{p: 3, ml: 'auto', textTransform: 'none'}}>
                                    {translate('payment.cta')}
                                </LoadingButton>
                            </Stack>
                            <Stack alignItems="center" spacing={1}>
                                <Stack direction="row" alignItems="center" spacing={1.5}>
                                    <Box component={Icon} icon={shieldFill} sx={{width: 20, height: 20, color: 'primary.main'}}/>
                                    <Typography variant="subtitle2">
                                        {translate('pricing.stripeInfo.title')}
                                    </Typography>
                                </Stack>
                                <Typography sx={{textAlign: 'center'}}>
                                    <span
                                        dangerouslySetInnerHTML={{
                                            __html: `${translate('pricing.stripeInfo.description', {paymentProvider})}`
                                        }}
                                    />
                                </Typography>
                                <Typography sx={{color: 'text.secondary', textAlign: 'center'}}>
                                    {translate('pricing.stripeInfo.billViaEmail')}
                                </Typography>
                            </Stack>
                        </>
                    ) : (
                        <Alert
                            severity="error"
                            sx={{my: 3}}
                        >{translate('directives.notifications.noRights', {
                            email: accountHolder,
                        })}</Alert>
                    )
                }
            </Stack>
        </Stack>
    );
};

PaymentSummary.propTypes = propTypes;

export default connect(mapStateToProps)(PaymentSummary);
