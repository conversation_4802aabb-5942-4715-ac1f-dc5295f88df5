import * as Yup from 'yup';
import {useEffect} from 'react';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Al<PERSON>, FormControl, Stack, Typography} from '@material-ui/core';
import {yupResolver} from '@hookform/resolvers/yup';
import {Controller, useFieldArray, useForm} from 'react-hook-form';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import {getSubscriptionPlan} from '../../utils/getSubscriptionPlan';
import {getDomainSchema} from '../../utils/formSchema';
import {setDomains} from '../../../redux/slices/pricing';
import {useDispatch} from '../../../redux/store';
import TextFieldStyle from '../utils/TextFieldStyle';

const mapStateToProps = ({pricing}) => {
    const {packages: {administration}} = getSubscriptionPlan(pricing.package.appName);
    const numberOfDomains = administration.items.domains.count;
    return {
        numberOfDomains,
    };
};

const propTypes = {
    numberOfDomains: PropTypes.number,
};

const SubscriptionDomainsForm = ({numberOfDomains}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const formContext = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({
            domains: Yup.array().of(Yup.object().shape({
                url: getDomainSchema({invalidField: translate('form.validation.domain')}),
            })),
        })),
        defaultValues: {
            domains: Array(numberOfDomains).fill({
                url: '',
            }),
        },
    });
    const {
        watch,
        control,
        formState: {
            errors,
        },
    } = formContext;
    const {fields} = useFieldArray({
        control,
        name: 'domains',
    });
    const domains = watch('domains');

    useEffect(() => {
        dispatch(setDomains({
            domains,
        }));
        // eslint-disable-next-line
    }, [domains]);

    return (
        <FormControl component="fieldset">
            <Stack spacing={3}>
                {
                    isNonEmptyObject(errors) ? (
                        <Alert severity="warning">
                            {translate('transactions.subscriptions.domains.notifications.invalidDomainsWarning')}
                        </Alert>
                    ) : (
                        <Alert severity="info">
                            {translate('transactions.subscriptions.domains.description', {numberOfDomains})}
                        </Alert>
                    )
                }
                {fields.map((item, index) => (
                    <Controller
                        key={item.id}
                        name={`domains.${index}.url`}
                        control={control}
                        render={({
                            field,
                            fieldState: {error},
                        }) => (
                            <Stack spacing={1}>
                                <TextFieldStyle
                                    {...field}
                                    label={translate('transactions.subscriptions.domains.index', {index: index + 1})}
                                    type="url"
                                    fullWidth
                                />
                                {Boolean(error?.message) && (
                                    <Typography
                                        sx={{color: 'error.main'}}>
                                        {error?.message}
                                    </Typography>
                                )}
                            </Stack>
                        )}
                    />
                ))}
            </Stack>
        </FormControl>
    );
};

SubscriptionDomainsForm.propTypes = propTypes;

export default connect(mapStateToProps)(SubscriptionDomainsForm);
