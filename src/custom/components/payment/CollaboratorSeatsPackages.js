import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {Box, Button, Card, Grid, Stack, Typography} from '@material-ui/core';
import collaborators from '@w7-3/webeagle-resources/dist/config/catalog/collaborators';
import {getRandomString} from '@w7-3/webeagle-resources/dist/libs/random';
import useLocales from '../../../hooks/useLocales';
import {MotionInView, varFadeInDown, varFadeInUp} from '../../../components/animate';
import LoadingScreen from '../../../components/LoadingScreen';
import {useDispatch} from '../../../redux/store';
import {addToCart} from '../../../redux/slices/pricing';
import Price from './Price';

const mapStateToProps = ({pricing}) => {
    return {
        pricing,
    };
};

const propTypes = {
    pricing: PropTypes.object,
};

const CollaboratorSeatsPackages = ({pricing}) => {
    const {translate} = useLocales();
    const dispatch = useDispatch();

    const handleSelection = ({cartItem, listType}) => {
        dispatch(addToCart({cartItem, listType}));
    };

    return (
        <Grid container spacing={6} justifyContent="center" sx={{ml: (theme) => `-${theme.spacing(6)} !important`}}>
            {
                Object.values(collaborators.items).map((item, index) => {
                    const priceData = pricing?.prices?.[item?.priceId];
                    const productData = pricing?.products?.[item?.productId];
                    const gridPros = {
                        item: true,
                        xs: 12,
                        sm: 6,
                        lg: 3,
                    };

                    if (!productData) {
                        return (
                            <Grid {...gridPros} key={item?.priceId}>
                                <Box sx={{m: 'auto', textAlign: 'center'}}>
                                    <LoadingScreen isInline/>
                                </Box>
                            </Grid>
                        );
                    }

                    return (
                        <Grid {...gridPros} key={item?.priceId}>
                            <MotionInView variants={index % 2 === 0 ? varFadeInDown : varFadeInUp}>
                                <Card
                                    sx={{
                                        boxShadow: (theme) => theme.customShadows.z8,
                                        p: 3
                                    }}>
                                    <Stack spacing={5}>
                                        <Typography
                                            variant="h6"
                                            align="center"
                                            sx={{
                                                height: 60,
                                            }}>
                                            {productData.name}
                                        </Typography>
                                        <Price
                                            price={priceData.unit_amount}
                                            currency={priceData?.currency}
                                            sx={{
                                                textAlign: 'center',
                                            }}
                                        />
                                        <Button
                                            size="large"
                                            fullWidth
                                            variant="outlined"
                                            onClick={() => {
                                                handleSelection({
                                                    cartItem: {
                                                        item,
                                                        id: getRandomString(8),
                                                        quantity: 1,
                                                        priceData,
                                                        productData,
                                                    },
                                                    listType: 'collaboratorSeats',
                                                });
                                            }}
                                            sx={{textTransform: 'none'}}>
                                            {translate('pricing.planSelection')}
                                        </Button>
                                    </Stack>
                                </Card>
                            </MotionInView>
                        </Grid>
                    );
                })
            }
        </Grid>
    );
};

CollaboratorSeatsPackages.propTypes = propTypes;

export default connect(mapStateToProps)(CollaboratorSeatsPackages);
