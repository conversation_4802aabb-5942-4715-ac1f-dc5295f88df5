import {styled} from '@material-ui/core/styles';
import {Container} from '@material-ui/core';
import PropTypes from 'prop-types';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import Page from '../../components/Page';
import useSettings from '../../hooks/useSettings';

const RootStyle = styled(Page)(({ theme }) => ({
    paddingTop: theme.spacing(8),
    paddingBottom: theme.spacing(15),
    [theme.breakpoints.up('md')]: {
        paddingTop: theme.spacing(11)
    }
}));

const propTypes = {
    id: PropTypes.string,
    title: PropTypes.string,
    hero: PropTypes.node,
    children: PropTypes.node,
    sx: PropTypes.object,
    isCentralized: PropTypes.bool,
    isStretched: PropTypes.bool,
    maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
};

const defaultProps = {
    maxWidth: 'xl',
};

const PageWrapper = ({
    id,
    title,
    hero,
    children,
    sx,
    isCentralized,
    isStretched,
    maxWidth,
}) => {
    const {themeStretch} = useSettings();
    return (
        <RootStyle
            {...getOptionalMap(id, {id})}
            title={title}
            sx={sx}>
            {hero}
            <Container
                maxWidth={isStretched || themeStretch ? false : maxWidth}
                sx={{
                    ...getOptionalMap(isCentralized, {
                        display: 'flex',
                        ml: 'auto',
                        minHeight: '80vh',
                        alignItems: 'center',
                    })
                }}>
                {children}
            </Container>
        </RootStyle>
    );
};

PageWrapper.defaultProps = defaultProps;
PageWrapper.propTypes = propTypes;

export default PageWrapper;


