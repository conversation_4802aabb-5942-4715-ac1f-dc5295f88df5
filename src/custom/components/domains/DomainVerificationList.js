import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {
    <PERSON><PERSON>,
    Button,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {VERIFICATION_ACTION, VERIFICATION_STATE} from '@w7-3/webeagle-resources/dist/config/domains';
import PubSub from 'pubsub-js';
import {fDateTime} from '../../../utils/formatTime';
import useLocales from '../../../hooks/useLocales';
import ExternalLink from '../utils/ExternalLink';
import Label from '../../../components/Label';
import Scrollbar from '../../../components/Scrollbar';
import MoreMenuButton from '../forms/MoreMenuButton';

const mapStateToProps = ({state}) => {
    const {
        domainVerifications,
    } = state;

    return {
        domainVerificationList: Object.values(domainVerifications || {}),
    };
};

const DomainVerification = PropTypes.shape({
    id: PropTypes.string,
    ts: PropTypes.number,
    status: PropTypes.string,
    method: PropTypes.string,
    expiry: PropTypes.number,
    author: PropTypes.shape({
        email: PropTypes.string,
    }),
});

const propTypes = {
    domainVerificationList: PropTypes.arrayOf(DomainVerification),
};

const DomainVerificationList = ({
    domainVerificationList,
}) => {
    const {translate} = useLocales();

    return (
        <Stack spacing={3}>
            <Typography sx={{color: 'text.secondary'}}>
                {translate('account.domains.verification.description', {
                    revoke: translate(`account.domains.verification.${VERIFICATION_ACTION.REVOKE}`),
                })}
            </Typography>
            <Alert severity="info" sx={{my: 3}}>
                {translate('account.domains.verification.info')}
            </Alert>
            <Stack direction="row" justifyContent="flex-end" alignItems="center">
                <Button
                    variant="contained"
                    sx={{textTransform: 'none'}}
                    onClick={() => {
                        PubSub.publish('DOMAIN.VERIFICATION.SHOW');
                    }}>
                    {translate('account.domains.verification.label')}
                </Button>
            </Stack>
            <Typography sx={{color: 'text.secondary'}}>
                {translate('account.domains.verification.items.count', {
                    count: domainVerificationList?.length,
                })}
            </Typography>
            {
                isNonEmptyArray(domainVerificationList) && (
                    <Scrollbar>
                        <TableContainer sx={{minWidth: 1024}}>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{minWidth: 250}}>
                                            {translate('account.domains.verification.domainToVerify')}
                                        </TableCell>
                                        <TableCell>
                                            {translate('account.domains.verification.items.ts')}
                                        </TableCell>
                                        <TableCell>
                                            {translate('account.domains.verification.methods.label')}
                                        </TableCell>
                                        <TableCell>
                                            {translate('state')}
                                        </TableCell>
                                        <TableCell>
                                            {translate('account.domains.verification.items.verifiedBy')}
                                        </TableCell>
                                        <TableCell>
                                            {translate('account.domains.verification.items.verifiedTill')}
                                        </TableCell>
                                        <TableCell sx={{width: 150, p: 1}}/>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {
                                        domainVerificationList.map(({
                                            id,
                                            ts,
                                            state,
                                            method,
                                            expiry,
                                            author,
                                        }) => (
                                            <TableRow key={id}>
                                                <TableCell>
                                                    <ExternalLink label={id} url={id} />
                                                </TableCell>
                                                <TableCell>
                                                    {fDateTime(ts, false)}
                                                </TableCell>
                                                <TableCell>
                                                    {translate(`account.domains.verification.methods.options.${method}.label`)}
                                                </TableCell>
                                                <TableCell>
                                                    <Label variant="filled" color={
                                                        state === VERIFICATION_STATE.VERIFIED ? 'success' : 'warning'
                                                    }>
                                                        {translate(`account.domains.verification.state.${state}`)}
                                                    </Label>
                                                </TableCell>
                                                <TableCell>
                                                    {author?.email || translate('notAvailable')}
                                                </TableCell>
                                                <TableCell>
                                                    {fDateTime(expiry, false) || translate('notAvailable')}
                                                </TableCell>
                                                <TableCell>
                                                    <MoreMenuButton
                                                        additionalItems={[
                                                            {
                                                                label: translate('edit'),
                                                                callback: () => {
                                                                    PubSub.publish('DOMAIN.VERIFICATION.SHOW', {
                                                                        url: id,
                                                                        method,
                                                                    });
                                                                },
                                                            },
                                                            state !== VERIFICATION_STATE.VERIFIED && {
                                                                label: translate(`account.domains.verification.${VERIFICATION_ACTION.VERIFY}`),
                                                                callback: () => {
                                                                    PubSub.publish('DOMAIN.VERIFICATION.UPDATE', {
                                                                        url: id,
                                                                        method,
                                                                        action: VERIFICATION_ACTION.VERIFY,
                                                                    });
                                                                },
                                                            },
                                                            {
                                                                label: translate(`account.domains.verification.${VERIFICATION_ACTION.REVOKE}`),
                                                                callback: () => {
                                                                    PubSub.publish('DOMAIN.VERIFICATION.UPDATE', {
                                                                        url: id,
                                                                        method,
                                                                        action: VERIFICATION_ACTION.REVOKE,
                                                                    });
                                                                },
                                                            },
                                                        ]}
                                                    />
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    }
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Scrollbar>
                )
            }
        </Stack>
    );
};

DomainVerificationList.propTypes = propTypes;

export default connect(mapStateToProps)(DomainVerificationList);
