import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {EVENTS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import InfoCode from '../utils/InfoCode';
import ConditionRenderer from '../forms/ConditionRenderer';
import ModalContainer from '../utils/ModalContainer';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    projectId: PropTypes.string,
    data: PropTypes.object,
    buildId: PropTypes.number,
};

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

export const ModalContent = ({projects, projectId, buildId, infoCodeProps}) => {
    const {translate} = useLocales();
    const project = projects?.[projectId];
    const solutionConfig = project?.results?.[buildId]?.data?.configData?.solutionsConfigs?.[project?.configData?.solution?.key];
    const item = solutionConfig?.conditions?.[infoCodeProps?.data?.groupId]?.list?.find((item) => {
        return infoCodeProps?.data?.id === item.id;
    });

    if ([EVENTS.CONDITION_FAILED, EVENTS.CONDITION_GROUP_FAILED].includes(infoCodeProps?.code) && item) {
        return (
            <ModalContainer
                triggerLabel={translate('conditions.preview')}
                buttonProps={{
                    startIcon: null,
                    sx: {
                        display: 'flex',
                        ml: 'auto !important',
                        mt: 3,
                    },
                }}>
                <ConditionRenderer
                    item={item}
                    isPreview
                />
            </ModalContainer>
        );
    }

    return null;
};

ModalContent.propTypes = {
    ...propTypes,
    projects: PropTypes.object,
    infoCodeProps: PropTypes.object,
};

const ConnectedModalContent = connect(mapStateToProps)(ModalContent);

const ProjectBuildInfoCodeList = ({projectId, data, buildId}) => {
    const infoCodes = data?.solutionData?.infoCodes || [];

    return (
        <>
            {
                infoCodes.map((infoCode, index) => {
                    const modalContent = (
                        <ConnectedModalContent
                            projectId={projectId}
                            buildId={buildId}
                            infoCodeProps={infoCode}
                        />
                    );

                    return (
                        <InfoCode
                            key={index}
                            infoCode={infoCode}
                            modalContent={modalContent}
                        />
                    );
                })
            }
        </>
    );
};

ProjectBuildInfoCodeList.propTypes = propTypes;

export default ProjectBuildInfoCodeList;
