import PropTypes from 'prop-types';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import ExternalLink from '../utils/ExternalLink';
import DefinitionList from '../utils/DefinitionList';

const propTypes = {
    originalUrl: PropTypes.string,
    requestedUrl: PropTypes.string,
    resolvedUrl: PropTypes.string,
};

const ProjectBuildResultInfo = ({originalUrl, requestedUrl, resolvedUrl}) => {
    const {translate} = useLocales();
    const requestedUrlUnchanged = (!originalUrl || originalUrl === requestedUrl) && requestedUrl === resolvedUrl;

    return (
        <DefinitionList
            dataList={[
                getOptionalMap(requestedUrlUnchanged, {
                    key: translate('project.url'),
                    node: <ExternalLink url={requestedUrl}/>,
                }),
                getOptionalMap(!requestedUrlUnchanged && Bo<PERSON>an(requestedUrl), {
                    key: translate('project.requestedURL'),
                    node: <ExternalLink url={requestedUrl}/>
                }),
                getOptionalMap(!requestedUrlUnchanged && Boolean(resolvedUrl), {
                    key: translate('project.resolvedURL'),
                    node: <ExternalLink url={resolvedUrl}/>
                }),
            ]}
        />
    );
};

ProjectBuildResultInfo.propTypes = propTypes;

export default ProjectBuildResultInfo;
