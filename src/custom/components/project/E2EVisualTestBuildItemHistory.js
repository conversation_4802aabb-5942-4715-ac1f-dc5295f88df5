import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography} from '@material-ui/core';
import {LoadingButton} from '@material-ui/lab';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyObject, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import ProjectBuildLink from './ProjectBuildLink';
import Label from '../../../components/Label';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import {Flags} from '../../../config/e2eVisualTests';
import Scrollbar from '../../../components/Scrollbar';

const STATES = {
    INITIAL: 'INITIAL',
    BUSY: 'BUSY',
    LOADED: 'LOADED',
};

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.e2eVisualTests';

const commonPropTypes = {
    projectId: PropTypes.string,
    buildId: PropTypes.number,
    item: PropTypes.shape({
        id: PropTypes.string,
        data: PropTypes.object,
    }),
};

const propTypes = {
    ...commonPropTypes,
    resultIds: PropTypes.arrayOf(PropTypes.string),
    groupId: PropTypes.string,
};

const TestRow = ({
    item,
    projectId,
    buildId,
}) => {
    const {translate} = useLocales();
    const {color} = Flags[item.data.state];

    return (
        <TableRow>
            <TableCell>
                <ProjectBuildLink
                    projectId={projectId}
                    buildId={buildId}
                />
            </TableCell>
            <TableCell align="center">
                <Label
                    color={color}
                    sx={{alignSelf: 'flex-start'}}>
                    {translate(`${i18nContext}.labels.${item.data.state}.label`)}
                </Label>
            </TableCell>
        </TableRow>
    );
};

TestRow.propTypes = {
    ...commonPropTypes,
};

const E2EVisualTestBuildItemHistory = ({projectId, resultIds, buildId, groupId, item}) => {
    const {translate} = useLocales();
    const [state, setState] = useState(STATES.INITIAL);
    const [historyList, setHistoryList] = useState([]);
    const apiCaller = useApiCaller();

    useEffect(() => {
        if (state !== STATES.BUSY) {
            return;
        }

        apiCaller({
            uri: getAPIPath(api.projectBuildItemHistory),
            data: {
                buildId,
                projectId,
                resultIds,
                groupId,
                type: 'e2eVisualTests',
                id: item.id,
            },
            successCallback: (result) => {
                const rawData = result?.data?.data || [];
                const list = rawData.filter(
                    (item) => isNonEmptyString(item?.item?.id) && isNonEmptyObject(item?.item?.data));

                list.sort((a, b) => {
                    if (a.buildId > b.buildId) {
                        return -1;
                    }

                    if (a.buildId < b.buildId) {
                        return 1;
                    }

                    return 0;
                });

                setHistoryList(list);
            },
            alwaysCallback: () => {
                setState(STATES.LOADED);
            },
        });
        // eslint-disable-next-line
    }, [state]);

    if (state === STATES.INITIAL) {
        return (
            <Button
                fullWidth
                variant="contained"
                onClick={() => setState(STATES.BUSY)}
                sx={{textTransform: 'none'}}>
                {translate(`${i18nContext}.items.loadHistory`)}
            </Button>
        );
    }

    if (state === STATES.BUSY) {
        return (
            <LoadingButton
                fullWidth
                loading
                sx={{textTransform: 'none'}}
            />
        );
    }

    return (
        <Scrollbar>
        <TableContainer>
            <Typography
                variant="subtitle1"
                sx={{my: 3}}>
                {translate(`${i18nContext}.items.historyLabel`)}
            </Typography>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell>{translate(`${i18nContext}.items.build`)}</TableCell>
                        <TableCell align="center">{translate(`${i18nContext}.items.currentState`)}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {
                        historyList.map(({buildId, item}) => {
                            return (
                                <TestRow
                                    key={buildId}
                                    item={item}
                                    projectId={projectId}
                                    buildId={buildId}
                                />
                            );
                        })
                    }
                </TableBody>
            </Table>
        </TableContainer>
        </Scrollbar>
    );
};

E2EVisualTestBuildItemHistory.propTypes = propTypes;

export default E2EVisualTestBuildItemHistory;
