import {useEffect} from 'react';
import {connect, useDispatch} from 'react-redux';
import PropTypes from 'prop-types';
import {Box} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import LoadingScreen from '../../../components/LoadingScreen';
import {setProjectEventList} from '../../../redux/slices/state';
import EventsTimeline from '../utils/EventsTimeline';

const mapStateToProps = ({state}) => {
    const {
        projectEventList,
    } = state;

    return {
        projectEventList,
    };
};

const propTypes = {
    projectId: PropTypes.string,
    projectEventList: PropTypes.arrayOf(PropTypes.object),
};

const ProjectHistory = ({projectId, projectEventList}) => {
    const apiCaller = useApiCaller();
    const dispatch = useDispatch();

    useEffect(() => {
        if (isNonEmptyArray(projectEventList)) {
            return;
        }

        apiCaller({
            uri: getAPIPath(api.projectEvents),
            data: {
                projectId,
            },
            successCallback: ({data}) => {
                if (!isNonEmptyArray(data.projectEventList)) {
                    return;
                }

                data.projectEventList.sort((a, b) => b.timestamp - a.timestamp);

                dispatch(setProjectEventList(data.projectEventList))
            },
        });
        // eslint-disable-next-line
    }, [projectEventList]);

    if (!projectEventList) {
        return (
            <Box sx={{m: 'auto', textAlign: 'center'}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    return (
        <EventsTimeline
            eventList={projectEventList}
        />
    );
};

ProjectHistory.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectHistory);
