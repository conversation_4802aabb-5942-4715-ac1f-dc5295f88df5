import PropTypes from 'prop-types';
import Highlight from 'react-highlight';
import {Stack} from '@material-ui/core';
import {Icon} from '@iconify/react';
import cornerDownRightOutline from '@iconify/icons-eva/corner-down-right-outline';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import useLocales from '../../../hooks/useLocales';
import ExternalLink from '../utils/ExternalLink';
import DefinitionList from '../utils/DefinitionList';

const propTypes = {
    url: PropTypes.string,
    normalizedUrl: PropTypes.string,
    exclusionReason: PropTypes.string,
    html: PropTypes.string,
    selector: PropTypes.string,
    parents: PropTypes.arrayOf(PropTypes.shape({
        pageUrl: PropTypes.string,
    })),
};

const iconWidth = 24;

const ProjectBuildResultContextDetails = ({
    url,
    normalizedUrl,
    exclusionReason,
    html,
    selector,
    parents,
}) => {
    const {translate} = useLocales();

    return (
        <DefinitionList dataList={[
            getOptionalMap(Boolean(isNonEmptyArray(parents)), {
                key: translate('project.build.result.html.hierarchy'),
                node: (
                    <Stack>
                        {
                            [
                                ...parents,
                                {pageUrl: url},
                            ].map(({pageUrl}, index) => {
                                const level = index + 1;
                                return (
                                    <Stack key={`${pageUrl}${index}`} direction="row" alignItems="center"
                                           sx={{pl: `${index * iconWidth * .5}px`, pt: 1}}>
                                        {
                                            level > 1 &&
                                            <Icon icon={cornerDownRightOutline} width={iconWidth} height={40}/>
                                        }
                                        <ExternalLink url={pageUrl}/>
                                    </Stack>
                                );
                            })
                        }
                    </Stack>
                ),
            }),
            getOptionalMap(Boolean(normalizedUrl) && `${url}`.toLowerCase() !== `${normalizedUrl}`.toLowerCase(), {
                key: translate('project.build.result.html.normalizedUrl'),
                node: (<ExternalLink url={normalizedUrl}/>),
            }),
            getOptionalMap(Boolean(exclusionReason), {
                key: translate('project.build.result.exclusion.label'),
                node: translate(`project.build.result.exclusion.reasons.${exclusionReason}`),
            }),
            getOptionalMap(Boolean(html), {
                key: translate('project.build.result.html.sourceCode'),
                node: (
                    <Highlight className="language-xml">
                        {html}
                    </Highlight>
                ),
            }),
            getOptionalMap(selector, {
                key: translate('project.build.result.html.htmlSelector'),
                node: (
                    <Highlight className="language-css">
                        {selector}
                    </Highlight>
                ),
            }),
        ]}/>
    );
};

ProjectBuildResultContextDetails.propTypes = propTypes;

export default ProjectBuildResultContextDetails;
