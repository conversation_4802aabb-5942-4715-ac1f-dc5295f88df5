import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Button,
    Collapse,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import {LoadingButton} from '@material-ui/lab';
import arrowIosUpwardFill from '@iconify/icons-eva/arrow-ios-upward-fill';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyObject, isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import DataExtractionBuildItemResult from '../project-build/details/DataExtractionBuildDetails';
import useLocales from '../../../hooks/useLocales';
import ProjectBuildLink from './ProjectBuildLink';
import useApiCaller from '../../hooks/useApiCaller';
import {getAPIPath} from '../../utils/getPath';
import Scrollbar from '../../../components/Scrollbar';

const STATES = {
    INITIAL: 'INITIAL',
    BUSY: 'BUSY',
    LOADED: 'LOADED',
};

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.dataExtractions';

const commonPropTypes = {
    projectId: PropTypes.string,
    buildId: PropTypes.number,
    item: PropTypes.shape({
        id: PropTypes.string,
        data: PropTypes.object,
    }),
    data: PropTypes.object,
};

const propTypes = {
    ...commonPropTypes,
    resultIds: PropTypes.arrayOf(PropTypes.string),
    groupId: PropTypes.string,
};

const TestRow = ({
    item,
    data,
    projectId,
    buildId,
}) => {
    const [open, setOpen] = useState(false);
    return (
        <>
            <TableRow
                sx={{
                    cursor: 'pointer',
                    borderBottom: (theme) => `solid 1px ${theme.palette.grey[500_8]}`,
                }}
                onClick={() => setOpen(!open)}>
                <TableCell>
                    <ProjectBuildLink
                        projectId={projectId}
                        buildId={buildId}
                    />
                </TableCell>
                <TableCell>
                    <Icon icon={open ? arrowIosUpwardFill : arrowIosDownwardFill}/>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell style={{
                    paddingBottom: 0,
                    paddingTop: 0,
                }} colSpan={2}>
                    <Collapse in={open} timeout="auto" unmountOnExit>
                        <Box sx={{margin: 1}}>
                            <DataExtractionBuildItemResult
                                item={item}
                                data={data}
                            />
                        </Box>
                    </Collapse>
                </TableCell>
            </TableRow>
        </>
    );
};

TestRow.propTypes = {
    ...commonPropTypes,
};

const DataExtractionBuildItemHistory = ({projectId, resultIds, buildId, groupId, item}) => {
    const {translate} = useLocales();
    const [state, setState] = useState(STATES.INITIAL);
    const [historyList, setHistoryList] = useState([]);
    const apiCaller = useApiCaller();

    useEffect(() => {
        if (state !== STATES.BUSY) {
            return;
        }

        apiCaller({
            uri: getAPIPath(api.projectBuildItemHistory),
            data: {
                buildId,
                projectId,
                resultIds,
                groupId,
                type: 'dataExtractions',
                id: item.id,
            },
            successCallback: (result) => {
                const rawData = result?.data?.data || [];
                const list = rawData.filter(
                    (item) => isNonEmptyString(item?.item?.id) && isNonEmptyObject(item?.item?.data));

                list.sort((a, b) => {
                    if (a.buildId > b.buildId) {
                        return -1;
                    }

                    if (a.buildId < b.buildId) {
                        return 1;
                    }

                    return 0;
                });

                setHistoryList(list);
            },
            alwaysCallback: () => {
                setState(STATES.LOADED);
            },
        });
        // eslint-disable-next-line
    }, [state]);

    if (state === STATES.INITIAL) {
        return (
            <Button
                fullWidth
                variant="contained"
                onClick={() => setState(STATES.BUSY)}
                sx={{textTransform: 'none'}}>
                {translate(`${i18nContext}.items.loadHistory`)}
            </Button>
        );
    }

    if (state === STATES.BUSY) {
        return (
            <LoadingButton
                fullWidth
                loading
                sx={{textTransform: 'none'}}
            />
        );
    }

    return (
        <Scrollbar>
        <TableContainer>
            <Typography
                variant="subtitle1"
                sx={{my: 3}}>
                {translate(`${i18nContext}.items.historyLabel`)}
            </Typography>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell>{translate(`${i18nContext}.items.build`)}</TableCell>
                        <TableCell sx={{width: 50}}/>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {
                        historyList.map(({buildId, ...rest}) => {
                            return (
                                <TestRow
                                    key={buildId}
                                    item={item}
                                    data={rest.item.data}
                                    projectId={projectId}
                                    buildId={buildId}
                                />
                            );
                        })
                    }
                </TableBody>
            </Table>
        </TableContainer>
        </Scrollbar>
    );
};

DataExtractionBuildItemHistory.propTypes = propTypes;

export default DataExtractionBuildItemHistory;
