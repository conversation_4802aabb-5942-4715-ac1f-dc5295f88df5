import {Box, Stack, Typography} from '@material-ui/core';
import PropTypes from 'prop-types';
import {thresholds} from '../../../config/lighthouse';

const propTypes = {
    score: PropTypes.number,
    label: PropTypes.string,
};

const LighthouseScoreScale = ({score, label}) => {
    const item = Object.values(thresholds).find(({range}) => score < range[1]);
    return (
        <Stack direction="row" alignItems="center" spacing={1}>
            <Box
                sx={{
                    width: 16,
                    height: 16,
                    borderRadius: 0.75,
                    bgcolor: item?.color,
                }}
            />
            <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                {label}
            </Typography>
        </Stack>
    );
};

LighthouseScoreScale.propTypes = propTypes;

export default LighthouseScoreScale;
