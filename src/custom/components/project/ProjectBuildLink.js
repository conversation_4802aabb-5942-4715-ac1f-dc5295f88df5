import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Link as RouterLink} from 'react-router-dom';
import {Link, Stack, Typography} from '@material-ui/core';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import ProjectBuildInfo from './ProjectBuildInfo';

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const propTypes = {
    projects: PropTypes.object,
    projectId: PropTypes.string,
    buildId: PropTypes.number,
    showDate: PropTypes.bool,
    showDuration: PropTypes.bool,
    showState: PropTypes.bool,
};

const ProjectBuildLink = ({projects, projectId, buildId, showDate, showDuration, showState}) => {
    const project = projects?.[projectId];
    const build = project?.results?.[buildId];

    return (
        <Stack direction="row" alignItems="center" spacing={1}>
            <Link
                to={`${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`}
                component={RouterLink}
                underline="none">
                <Typography variant="h6">
                    #{buildId}.
                </Typography>
            </Link>
            <ProjectBuildInfo
                state={build?.data?.state}
                start={build?.data?.start}
                end={build?.data?.end}
                showDate={showDate}
                showDuration={showDuration}
                showState={showState}
            />
        </Stack>
    );
};

ProjectBuildLink.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildLink);
