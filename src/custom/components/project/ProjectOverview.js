import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>rid, Typography} from '@material-ui/core';
import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import {isPositiveInt} from '@w7-3/webeagle-resources/dist/libs/validators';
import ProjectBasicInfo from './ProjectBasicInfo';
import useLocales from '../../../hooks/useLocales';
import ProjectBuild from './ProjectBuild';
import PrivilegedContent from '../../../guards/PrivilegedContent';
import ProjectBuildHistoryTable from './ProjectBuildHistoryTable';

const propTypes = {
    projects: PropTypes.object,
    projectId: PropTypes.string,
};

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const ProjectOverview = ({projects, projectId}) => {
    const {translate} = useLocales();
    const project = projects?.[projectId];
    const buildExists = isPositiveInt(project?.latestBuild?.index);

    return (
        <Grid container spacing={6}>
            <Grid item xs={12} lg={9}>
                {
                    buildExists ? (
                        <ProjectBuildHistoryTable
                            projectId={projectId}
                        />
                    ) : (
                        <Card sx={{p: 3}}>
                            <CardHeader
                                sx={{p: 0, pb: 3}}
                                title={(
                                    <Typography>
                                        {translate('project.build.noData')}
                                    </Typography>
                                )}
                            />
                            <CardContent>
                                <PrivilegedContent
                                    accessibleRoles={[
                                        userRoles.EDITOR.key,
                                    ]}
                                    isCovert>
                                    <Button
                                        size="large"
                                        variant="outlined"
                                        onClick={() => {
                                            PubSub.publish('PROJECT.BUILD', {
                                                projectId: project.id,
                                            });
                                        }}
                                        sx={{textTransform: 'none', mt: 1}}>
                                        {translate('project.run')}
                                    </Button>
                                </PrivilegedContent>
                            </CardContent>
                        </Card>
                    )
                }
            </Grid>
            <Grid item xs={12} lg={3} sx={{
                '> *': {
                    mb: 3,
                },
            }}>
                {
                    project.state === projectStates?.active &&
                    <PrivilegedContent
                        accessibleRoles={[
                            userRoles.EDITOR.key,
                        ]}
                        isCovert>
                        {
                            project?.configData && (
                                <ProjectBuild
                                    project={project}
                                />
                            )
                        }
                    </PrivilegedContent>
                }
                <ProjectBasicInfo
                    projectId={project.id}
                />
            </Grid>
        </Grid>
    );
};

ProjectOverview.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectOverview);
