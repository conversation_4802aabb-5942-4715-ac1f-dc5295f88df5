import {Fragment, useState} from 'react';
import {
    Box,
    Divider,
    IconButton, Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TablePagination,
    TableRow,
    Typography,
} from '@material-ui/core';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getSummaryData} from '@w7-3/webeagle-resources/dist/libs/project-solution/summaries';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {useNavigate} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../hooks/useLocales';
import ProjectBuildLink from './ProjectBuildLink';
import ProjectBuildTeaser from './ProjectBuildTeaser';
import LoadingScreen from '../../../components/LoadingScreen';
import ProjectBuildState from './ProjectBuildState';
import MoreMenuButton from '../forms/MoreMenuButton';
import {getTimeZone} from '../../utils/timezone';
import Scrollbar from '../../../components/Scrollbar';

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const propTypes = {
    projects: PropTypes.object,
    projectId: PropTypes.string,
};

const ProjectBuildHistoryTable = ({projects, projectId}) => {
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [openItems, setOpenItems] = useState({});
    const {
        translate,
        currentLang,
    } = useLocales();
    const navigate = useNavigate();
    const project = projects?.[projectId];
    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };
    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    if (!isNonEmptyObject(project?.results)) {
        return (
            <Box sx={{m: 'auto', textAlign: 'center'}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const isProjectRunning = project?.latestBuild?.state === BUILD_STATES.running;
    const isProjectCancellationInProgress = project?.latestBuild?.cancellation?.isInProgress;
    const buildKeyList = Object.keys(project?.results)
        .map((buildKey) => Number.parseInt(buildKey, 10))
        .filter((buildKey) => {
            return !(isProjectRunning || isProjectCancellationInProgress) || buildKey !== project?.latestBuild?.index;
        });
    buildKeyList.reverse();

    let latestBuildIndex = project?.latestBuild?.index;

    if (isProjectRunning) {
        latestBuildIndex = project?.latestBuild?.index - 1;
    }

    if (latestBuildIndex < 1 || !project?.results?.[latestBuildIndex]?.data?.solutionSummary) {
        return (
            <Box sx={{m: 'auto', textAlign: 'center'}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const linkCheckerSolutionSummaryData = getSummaryData({
        solutionKey: solutions.linkChecker.appName,
        language: currentLang.value,
        timeZone: getTimeZone(),
        projectBuildOverviewData: project?.results?.[latestBuildIndex]?.data,
    });

    return (
        <>
            <Scrollbar>
                <TableContainer sx={{
                    minWidth: 780,
                }}>
                    <Table stickyHeader>
                        <TableHead>
                            <TableRow>
                                <TableCell align="left" sx={{width: 40}}>#</TableCell>
                                <TableCell align="left" sx={{width: 200}}>
                                    {linkCheckerSolutionSummaryData?.items?.start?.label}
                                </TableCell>
                                <TableCell align="left" sx={{width: 80}}>
                                    {linkCheckerSolutionSummaryData?.items?.duration?.label}
                                </TableCell>
                                <TableCell align="left" sx={{width: 100}}>
                                    {translate('state')}
                                </TableCell>
                                <TableCell align="left" sx={{width: 'auto', minWidth: 250}}>
                                    {translate('project.build.report.summary.title')}
                                </TableCell>
                                <TableCell align="left" sx={{width: 80}} />
                                <TableCell align="left" sx={{width: 80}} />
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {
                                buildKeyList
                                    .filter((_, index) => index >= page * rowsPerPage && index < (page + 1) * rowsPerPage)
                                    .map((buildId, index) => {
                                        const build = project?.results?.[buildId]?.data;
                                        const linkCheckerSolutionSummaryData = getSummaryData({
                                            solutionKey: solutions.linkChecker.appName,
                                            language: currentLang.value,
                                            timeZone: getTimeZone(),
                                            projectBuildOverviewData: project?.results?.[buildId]?.data,
                                            previousProjectBuildOverviewData: project?.results?.[buildId - 1]?.data,
                                        });
                                        const projectSolutionSummaryData = getSummaryData({
                                            solutionKey: project?.configData?.solution?.appName,
                                            language: currentLang.value,
                                            timeZone: getTimeZone(),
                                            projectBuildOverviewData: project?.results?.[buildId]?.data,
                                            previousProjectBuildOverviewData: project?.results?.[buildId - 1]?.data,
                                        });
                                        const handleRowClick = () => {
                                            setOpenItems({
                                                ...openItems,
                                                [buildId]: !openItems[buildId],
                                            });
                                        };

                                        return (
                                            <Fragment key={buildId}>
                                                <TableRow>
                                                    <TableCell align="left">
                                                        <ProjectBuildLink
                                                            projectId={projectId}
                                                            buildId={buildId}
                                                        />
                                                    </TableCell>
                                                    <TableCell
                                                        align="left"
                                                        sx={{
                                                            cursor: 'pointer',
                                                        }}
                                                        onClick={handleRowClick}>
                                                        {linkCheckerSolutionSummaryData?.items?.start?.value}
                                                    </TableCell>
                                                    <TableCell align="left">
                                                        {linkCheckerSolutionSummaryData?.items?.duration?.value}
                                                    </TableCell>
                                                    <TableCell align="left">
                                                        <ProjectBuildState
                                                            state={build.state}
                                                            showLabel
                                                        />
                                                    </TableCell>
                                                    <TableCell
                                                        align="left"
                                                        sx={{
                                                            alignItems: 'center',
                                                            cursor: 'pointer',
                                                            ml: -1,
                                                            position: 'relative',
                                                        }}
                                                        onClick={handleRowClick}>
                                                        <Stack direction="row" alignItems="center" justifyContent="space-between">
                                                            <Typography sx={{
                                                                color: projectSolutionSummaryData?.items?.success?.value > 0 ? 'inherit' : 'text.secondary',
                                                            }}>
                                                                {projectSolutionSummaryData?.description}
                                                            </Typography>
                                                        </Stack>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Box
                                                            sx={{
                                                                alignSelf: 'flex-end',
                                                                display: 'inline-flex',
                                                                justifyContent: 'flex-end',
                                                                ml: 'auto',
                                                                '& > *': {
                                                                    mx: 0.5,
                                                                },
                                                            }}
                                                        >
                                                            <IconButton
                                                                size="large"
                                                                onClick={handleRowClick}
                                                                color="primary"
                                                            >
                                                                <Icon
                                                                    icon={openItems[buildId] ? 'eva:chevron-up-fill' : 'eva:chevron-down-fill'}
                                                                />
                                                            </IconButton>

                                                        </Box>
                                                    </TableCell>
                                                    <TableCell>
                                                        <MoreMenuButton additionalItems={[
                                                            {
                                                                label: translate('project.build.ctaLabel'),
                                                                callback: () => {
                                                                    navigate(`${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`);
                                                                },
                                                            },
                                                        ]}/>
                                                    </TableCell>
                                                </TableRow>
                                                {
                                                    Boolean(openItems[buildId]) && (
                                                        <TableRow sx={{m: 0, p: 0}}>
                                                            <TableCell colSpan={7} sx={{p: 0, m: 0, pb: 5}}>
                                                                <ProjectBuildTeaser
                                                                    projectId={projectId}
                                                                    buildId={buildId}
                                                                    title={translate('project.build.latestBuild')}
                                                                    showActions={false}
                                                                    isSummaryOnly
                                                                />
                                                            </TableCell>
                                                        </TableRow>
                                                    )
                                                }
                                                {
                                                    index < buildKeyList.length - 1 && (
                                                        <TableRow>
                                                            <TableCell colSpan={7} sx={{p: 0}}>
                                                                <Divider />
                                                            </TableCell>
                                                        </TableRow>
                                                    )
                                                }
                                            </Fragment>
                                        );
                                    })
                            }
                        </TableBody>
                    </Table>
                </TableContainer>
            </Scrollbar>
            {
                buildKeyList.length > 5 &&
                <TablePagination
                    rowsPerPageOptions={[5, 10, 25, 100]}
                    component="div"
                    count={buildKeyList.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            }
        </>
    );
};

ProjectBuildHistoryTable.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildHistoryTable);
