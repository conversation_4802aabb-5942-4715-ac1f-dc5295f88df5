import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {useRef, useState} from 'react';
import {<PERSON><PERSON>, Box, Button, Card, CardHeader, IconButton, MenuItem, Stack, Typography} from '@material-ui/core';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {steps} from '@w7-3/webeagle-resources/dist/config/project';
import useLocales from '../../../hooks/useLocales';
import MenuPopover from '../../../components/MenuPopover';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import DefinitionList from '../utils/DefinitionList';
import CountDown from '../utils/CountDown';
import {useProject} from '../../hooks/useProject';
import LoadingScreen from '../../../components/LoadingScreen';

const propTypes = {
    project: PropTypes.object,
    isPreview: PropTypes.bool,
};

const ProjectBuild = ({
    project,
    isPreview,
}) => {
    const {translate} = useLocales();
    const optionStatusSelectionRef = useRef(null);
    const [openStatusSelection, setOpenStatusSelection] = useState(false);
    const {goToProjectEdit} = useProject();

    if (!project?.configData) {
        return (
            <Box sx={{m: 'auto', textAlign: 'center'}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const {
        projectId,
        summary: {
            activateScheduler,
            schedulerData,
            schedulerEndDate,
            schedulerStartDate,
        },
    } = project.configData;
    const nextBuild = project?.buildQueue?.auto?.executeAt;
    const Component = isPreview ? Box : Card;

    return (
        <Component sx={{p: 3}}>
            {
                !isPreview && (
                    <CardHeader
                        sx={{p: 0, pb: 3}}
                        title={translate('project.build.schedule.label')}
                        action={(
                            !isPreview && (
                                <IconButton
                                    onClick={() => setOpenStatusSelection( true)}
                                    ref={optionStatusSelectionRef}
                                    sx={{ml: 1}}>
                                    <Icon icon="eva:more-vertical-outline" sx={{width: 40, height: 40}} />
                                </IconButton>
                            )
                        )}
                    />
                )
            }
            {
                activateScheduler ? (
                    <DefinitionList
                        dataList={[{
                            key: translate('project.wizardSteps.summary.fields.scheduler.configurator.types.label'),
                            node: (
                                <Box sx={{pl: .125}}>
                                    {translate(`project.wizardSteps.summary.fields.scheduler.configurator.types.options.${schedulerData?.type}.description`, {
                                        cronPrompt: schedulerData?.config?.cronPrompt,
                                        frequency: schedulerData?.config?.frequency,
                                        frequencyQualifier: translate(`project.wizardSteps.summary.fields.scheduler.configurator.types.options.${schedulerData?.type}.frequency.qualifierOptions.${schedulerData?.config?.frequencyQualifier}`),
                                    })}
                                </Box>
                            ),
                        }, {
                            key: translate('project.wizardSteps.summary.fields.scheduler.startDate'),
                            node: (
                                <Box sx={{pl: .125}}>
                                    <Typography component="p" variant="heading">
                                        {fDateTimeHumanReadable(schedulerStartDate)}
                                    </Typography>
                                </Box>
                            ),
                        }, getOptionalMap(Boolean(schedulerEndDate), {
                            key: translate('project.wizardSteps.summary.fields.scheduler.endDate'),
                            node: (
                                <Box sx={{pl: .125}}>
                                    <Typography component="p" variant="heading">
                                        {fDateTimeHumanReadable(schedulerEndDate)}
                                    </Typography>
                                </Box>
                            ),
                        }), getOptionalMap(Boolean(nextBuild), {
                            key: translate('project.build.schedule.nextBuild'),
                            node: (
                                <Stack spacing={3}>
                                    <Typography component="p" variant="heading">
                                        {fDateTimeHumanReadable(nextBuild)}
                                    </Typography>
                                    <CountDown
                                        ts={nextBuild}
                                        elapsedLabel={translate('project.build.schedule.provisioningServers')}
                                    />
                                </Stack>
                            ),
                        })]}
                        variant={DefinitionList.VARIANTS.vertical}
                    />
                ) : (
                    <Alert severity="info">
                        {translate('project.build.schedule.inactive')}
                    </Alert>
                )
            }
            {
                !isPreview && (
                    <>
                        <Stack direction="row"
                               justifyContent="flex-end"
                               alignItems="center"
                               sx={{pt: 3}}>
                            <Button
                                size="large"
                                variant="outlined"
                                startIcon={<Icon icon="eva:play-circle-outline" width={40} height={40} />}
                                onClick={() => {
                                    PubSub.publish('PROJECT.BUILD', {
                                        projectId,
                                    });
                                }}
                                sx={{textTransform: 'none'}}>
                                {translate('project.run')}
                            </Button>
                        </Stack>
                        <MenuPopover
                            sx={{width: 'auto'}}
                            open={openStatusSelection}
                            onClose={() => setOpenStatusSelection(false)}
                            anchorEl={optionStatusSelectionRef.current}>
                            {
                                [
                                    {
                                        label:translate('project.build.schedule.edit'),
                                        onClick: () => {
                                            goToProjectEdit({projectId, step: steps.summary.key});
                                        },
                                    },
                                ]
                                .map(({label, onClick, ...rest}, index) => (
                                    <MenuItem
                                        key={`${label}-${index}`}
                                        onClick={() => {
                                            setOpenStatusSelection(false);
                                            onClick?.();
                                        }}
                                        sx={{typography: 'body2', pb: 1, px: 2.5}}
                                        {...rest}>
                                        {label}
                                    </MenuItem>
                                ))}
                        </MenuPopover>
                    </>
                )
            }
        </Component>
    );
};

ProjectBuild.propTypes = propTypes;

export default ProjectBuild;
