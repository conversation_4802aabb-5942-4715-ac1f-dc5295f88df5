import PropTypes from 'prop-types';
import {EVENTS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import InfoCode from '../utils/InfoCode';
import ConditionRenderer from '../forms/ConditionRenderer';
import ModalContainer from '../utils/ModalContainer';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    infoCodes: PropTypes.array,
    modalContent: PropTypes.node,
};

export const ModalContent = ({project, buildId, infoCodeProps}) => {
    const {translate} = useLocales();
    const solutionConfig = project?.results?.[buildId]?.data?.configData?.solutionsConfigs?.[project?.configData?.solution?.key];
    const item = solutionConfig?.conditions?.[infoCodeProps?.data?.groupId]?.list?.find((item) => {
        return infoCodeProps?.data?.id === item.id;
    });

    if ([EVENTS.CONDITION_FAILED, EVENTS.CONDITION_GROUP_FAILED].includes(infoCodeProps?.code) && item) {
        return (
            <ModalContainer
                triggerLabel={translate('conditions.preview')}
                buttonProps={{
                    startIcon: null,
                    sx: {
                        display: 'flex',
                        ml: 'auto !important',
                        mt: 3,
                    },
                }}>
                <ConditionRenderer
                    item={item}
                    isPreview
                />
            </ModalContainer>
        );
    }

    return null;
};

ModalContent.propTypes = {
    ...propTypes,
};

const ProjectBuildItemInfoCodeList = ({infoCodes, modalContent}) => {
    return (infoCodes || []).map((infoCode, index) => {
        return (
            <InfoCode
                key={index}
                infoCode={infoCode}
                modalContent={modalContent}
                isDismissible={false}
            />
        );
    });
};

ProjectBuildItemInfoCodeList.propTypes = propTypes;

export default ProjectBuildItemInfoCodeList;
