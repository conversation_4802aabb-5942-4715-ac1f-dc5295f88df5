import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {Box, Tooltip} from '@material-ui/core';
import {styled} from '@material-ui/core/styles';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import useLocales from '../../../hooks/useLocales';
import Label from '../../../components/Label';

const IconRotating = styled(Icon)(({theme}) => ({
    animation: theme.animations.rotation.r2,
    transformOrigin: 'center center',
}));

export const CONFIG = {
    [BUILD_STATES.cancel]: {
        color: 'warning.main',
        severity: 'warning',
        icon: 'eva:alert-circle-outline',
        Component: Icon,
    },
    [BUILD_STATES.failure]: {
        color: 'error.main',
        severity: 'error',
        icon: 'eva:alert-triangle-outline',
        Component: Icon,
    },
    [BUILD_STATES.mixed]: {
        color: 'error.main',
        severity: 'error',
        icon: 'eva:link-2-outline',
        Component: Icon,
    },
    [BUILD_STATES.queue]: {
        color: 'info.main',
        severity: 'info',
        icon: 'eva:loader-outline',
        Component: IconRotating,
    },
    [BUILD_STATES.running]: {
        color: 'info.main',
        severity: 'info',
        icon: 'eva:loader-outline',
        Component: IconRotating,
    },
    [BUILD_STATES.success]: {
        color: 'success.main',
        severity: 'success',
        icon: 'eva:checkmark-circle-2-outline',
        Component: Icon,
    },
    [BUILD_STATES.timeout]: {
        color: 'error.main',
        severity: 'error',
        icon: 'eva:clock-outline',
        Component: Icon,
    },
    [BUILD_STATES.terminated]: {
        color: 'error.main',
        severity: 'error',
        icon: 'eva:alert-triangle-outline',
        Component: Icon,
    },
};

const propTypes = {
    state: PropTypes.string,
    showLabel: PropTypes.bool,
    sx: PropTypes.object,
};

const ProjectBuildState = ({state, showLabel, sx}) => {
    const item = CONFIG[state];
    const {translate} = useLocales();

    if (!item) {
        return null;
    }

    const {color, icon, Component} = item;
    const label = translate(`project.build.states.${state}.label`);
    const description = translate(`project.build.states.${state}.description`);

    return (
        <Tooltip title={description}>
            <Box
                component="span"
                sx={{
                    display: 'flex',
                    ...sx,
                }}>
                {
                    showLabel ? (
                        <Label
                            sx={{
                                color,
                                height: 40,
                            }}>
                            <Component
                                icon={icon}
                                width={40}
                                height={40}
                            />
                            <Box
                                component="span"
                                sx={{ml: .5}}>
                                {label}
                            </Box>
                        </Label>
                    ) : (
                        <Box
                            component={Component}
                            icon={icon}
                            color={color}
                            width={40}
                            height={40}
                        />
                    )
                }
            </Box>
        </Tooltip>
    );
};

ProjectBuildState.propTypes = propTypes;

export default ProjectBuildState;
