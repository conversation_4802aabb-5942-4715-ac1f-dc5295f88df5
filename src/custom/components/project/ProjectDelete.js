import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {useState} from 'react';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Alert,
    AlertTitle,
    Button,
    Checkbox,
    FormControlLabel,
    FormGroup,
    Stack,
    Typography,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import arrowIosDownwardFill from '@iconify/icons-eva/arrow-ios-downward-fill';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../hooks/useLocales';
import {getAPIPath} from '../../utils/getPath';
import useApiCaller from '../../hooks/useApiCaller';
import {setDeferredTask, TASK_TYPES} from '../handlers/DeferredTasksHandler';

const propTypes = {
    project: PropTypes.object,
    projectId: PropTypes.string,
};

const mapStateToProps = ({state}, {projectId}) => {
    const {
        projects,
    } = state;

    const project = projects?.[projectId];

    return {
        project,
    };
};

const i18nContext = 'project.delete';

const ProjectDelete = ({project, projectId}) => {
    const {translate} = useLocales();
    const apiCaller = useApiCaller();
    const [impacts, setImpacts] = useState({
        irreversible: false, relatedData: false,
    });

    if (!project) {
        return null;
    }

    const handleProjectDelete = () => {
        apiCaller({
            uri: getAPIPath(api.projectDelete),
            data: {
                projectId,
            },
            isAnimated: true,
            successCallback: () => {
                setDeferredTask({
                    type: TASK_TYPES.notification,
                    options: {
                        message: translate(`${i18nContext}.notifications.deleteSuccess`),
                        variant: 'success',
                    }
                });
                window.location.href = `${PATH_DASHBOARD.general.projects.root}`;
            },
            failureCallback: () => {
                PubSub.publish('SHOW.NOTIFICATION', {
                    message: translate(`${i18nContext}.notifications.deleteFailure`),
                    variant: 'error',
                });
            },
        });
    };

    return (
        <Accordion>
            <AccordionSummary expandIcon={<Icon icon={arrowIosDownwardFill} width={40} height={40}/>}>
                <Typography gutterBottom variant="h5" sx={{color: 'error.main'}}>
                    {translate('dangerZone.label')}: {translate(`${i18nContext}.label`)}
                </Typography>
            </AccordionSummary>
            <AccordionDetails>
                <Alert severity="error" sx={{my: 5}}>
                    <AlertTitle>{translate('project.delete.label')}</AlertTitle>
                    <FormGroup>
                        {Object.keys(impacts).map((item) => (
                            <FormControlLabel
                                key={item}
                                control={<Checkbox color="error" />}
                                label={translate(`project.delete.impact.${item}`)}
                                onChange={() => setImpacts({...impacts, [item]: !impacts[item]})}
                            />
                        ))}
                    </FormGroup>
                </Alert>
                <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                    <Button
                        size="large"
                        color="error"
                        variant="contained"
                        onClick={handleProjectDelete}
                        disabled={Object.values(impacts).some((item) => !item)}
                    >
                        {translate('project.delete.label')}
                    </Button>
                </Stack>
            </AccordionDetails>
        </Accordion>
    );
};

ProjectDelete.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectDelete);
