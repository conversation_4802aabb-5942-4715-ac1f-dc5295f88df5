import {useMemo} from 'react';
import PropTypes from 'prop-types';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {
    Alert,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import useLocales from '../../../hooks/useLocales';
import ExternalLink from '../utils/ExternalLink';
import {
    getItemSummaryFlag,
    Content as URLChallengeBuildDetailsContent,
} from '../project-build/details/URLChallengeBuildDetails';
import Scrollbar from '../../../components/Scrollbar';
import InfoCode from '../utils/InfoCode';

const solution = solutions.urlChallenge.key;
const i18nContext = `project.wizardSteps.solutionsConfigs.groups.${solution}`;

const propTypes = {
    project: PropTypes.object,
    build: PropTypes.object,
};

export const getChallengeData = ({item, list, points}) => {
    item?.data?.stepListResults?.forEach((subItem) => {
        if (subItem.stepType === 'solution') {
            points[getItemSummaryFlag(subItem)] += 1;
            list.push(subItem);
        }

        if (!isNonEmptyArray(subItem?.data?.stepListResults)) {
            return;
        }

        getChallengeData({item: subItem, list, points});
    });

    return {points, list};
};

const ProjectBuildURLChallengeRankings = ({project, build}) => {
    const {translate} = useLocales();
    const {
        participantList,
        disqualifiedList,
    } = useMemo(() => {
        const participantList = [];
        const disqualifiedList = [];

        Object.values(build?.solutionResults || {}).forEach((result) => {
            if (result?.responseSummary?.broken) {
                disqualifiedList.push(result);
                return;
            }

            const {points, list} = getChallengeData({
                item: result?.solutionData,
                list: [],
                points: {
                    passed: 0,
                    failed: 0,
                    notPassed: 0,
                    voided: 0,
                    aborted: 0,
                },
            });

            participantList.push({
                result,
                points,
                list,
            });
        });

        participantList.sort((a, b) => {
            return b.points.passed - a.points.passed;
        })

        return {participantList, disqualifiedList};
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [project?.id, project?.configData?.solution?.config?.stepList?.length, build?.data?.buildId]);

    return (
        <Scrollbar>
            <Stack spacing={3}>
                <Typography variant="subtitle1">{translate(`${i18nContext}.reviewer.rankingTable`)}</Typography>
                {
                    participantList.length > 0 ? (
                        <TableContainer sx={{
                            minWidth: 960,
                            mt: 3,
                        }}>
                            <Table stickyHeader size="medium">
                                <TableHead>
                                    <TableRow>
                                        <TableCell align="center" width="100px">
                                            {translate(`${i18nContext}.reviewer.position`)}
                                        </TableCell>
                                        <TableCell align="center" width="220px" sx={{minWidth: '220px'}}>
                                            {translate(`${i18nContext}.labels.totalChallenges.label`)}
                                        </TableCell>
                                        <TableCell align="left" width="400px" sx={{minWidth: '400px'}}>
                                            {translate(`${i18nContext}.reviewer.participants`)}
                                        </TableCell>
                                        <TableCell align="left" width="50px" sx={{minWidth: '120px'}}>
                                            {translate(`${i18nContext}.labels.passed.label`)}
                                        </TableCell>
                                        <TableCell align="left" width="50px" sx={{minWidth: '180px'}}>
                                            {translate(`${i18nContext}.labels.notPassed.label`)}
                                        </TableCell>
                                        {participantList?.[0]?.list?.map((challenge, index) => {
                                            return (
                                                <TableCell align="left" key={index} width="200px" sx={{minWidth: '200px'}}>
                                                    {challenge?.labellingData?.label}
                                                </TableCell>
                                            );
                                        })}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {participantList.map((participant, index) => {
                                        const position = [...participantList].splice(0, index).findIndex((item, index2) => {
                                            return index2 < index && item.points.passed === participant.points.passed;
                                        });

                                        if (!isNonEmptyArray(participant?.list)) {
                                            return null;
                                        }

                                        const participatedChallenges = participant?.list?.length;
                                        const inapplicableChallenges = participantList[0].length - participatedChallenges;

                                        return (
                                            <TableRow
                                                key={participant?.result?.id}
                                                sx={{
                                                    ...getOptionalMap(index % 2 === 1, {
                                                        backgroundColor: (theme) => theme.palette.action.hover,
                                                    }),
                                                }}
                                            >
                                                <TableCell align="center">
                                                    {(position > -1 ? position : index) + 1}
                                                </TableCell>
                                                <TableCell align="center">
                                                    {participatedChallenges}
                                                </TableCell>
                                                <TableCell align="left">
                                                    <ExternalLink url={participant?.result?.url}/>
                                                </TableCell>
                                                <TableCell align="left">
                                                    {participant.points.passed}
                                                </TableCell>
                                                <TableCell align="left">
                                                    {participant.points.notPassed}
                                                </TableCell>
                                                {participant?.list?.map((result) => {
                                                    return (
                                                        <TableCell align="left" key={result?.id} sx={{pl: '0 !important'}} width="200px">
                                                            <URLChallengeBuildDetailsContent item={result} />
                                                        </TableCell>
                                                    );
                                                })}
                                                {Array.from({length: inapplicableChallenges}).map((_, index) => {
                                                    return (
                                                        <TableCell key={index} sx={{pl: '0 !important'}} width="200px">-</TableCell>
                                                    );
                                                })}
                                            </TableRow>
                                        );
                                    })}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    ) : (
                        <Alert severity="warning" sx={{mb: 3}}>
                            {translate(`project.wizardSteps.solutionsConfigs.groups.${solution}.captured`, {count: 0})}
                        </Alert>
                    )
                }
                {
                    isNonEmptyArray(disqualifiedList) && (
                        <>
                            <Typography variant="subtitle1">{translate(`${i18nContext}.reviewer.disqualified.label`)}</Typography>
                            <Table stickyHeader size="medium" sx={{width: '100%'}}>
                                <TableHead>
                                    <TableRow>
                                        <TableCell align="left" width="350px" sx={{maxWidth: '50%'}}>
                                            {translate(`${i18nContext}.reviewer.participants`)}
                                        </TableCell>
                                        <TableCell align="left" width="100px">
                                            {translate(`${i18nContext}.reviewer.disqualified.reason`)}
                                        </TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {
                                        disqualifiedList.map((result) => {
                                            return (
                                                <TableRow key={result?.id}>
                                                    <TableCell align="left">
                                                        <ExternalLink url={result?.url}/>
                                                    </TableCell>
                                                    <TableCell align="left">
                                                        {
                                                            result?.responseSummary?.infoCodes?.map((infoCode) => {
                                                                return (
                                                                    <InfoCode
                                                                        key={infoCode.code}
                                                                        infoCode={infoCode}
                                                                        showDetails={false}
                                                                    />
                                                                );
                                                            })
                                                        }
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })
                                    }
                                </TableBody>
                            </Table>
                        </>
                    )
                }
            </Stack>
        </Scrollbar>
    );
};

ProjectBuildURLChallengeRankings.propTypes = propTypes;

export default ProjectBuildURLChallengeRankings;
