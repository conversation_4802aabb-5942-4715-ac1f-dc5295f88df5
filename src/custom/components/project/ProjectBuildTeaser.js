import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, CardHeader, <PERSON>, <PERSON>ack, Typography} from '@material-ui/core';
import {useEffect} from 'react';
import {connect} from 'react-redux';
import {Link as RouterLink} from 'react-router-dom';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../hooks/useLocales';
import ProjectBuildWrapper from './ProjectBuildWrapper';
import {MHidden} from '../../../components/@material-extend';
import ProjectBuildPagination from './ProjectBuildPagination';
import ProjectSolutionBuildSummary from '../project-build/ProjectSolutionBuildSummary';
import ProjectBuildEvents from './ProjectBuildEvents';
import ProjectBuildQuotaUsage from './ProjectBuildQuotaUsage';
import {CONFIG as ProjectBuildStateConfig} from './ProjectBuildState';
import ProjectBuildInfo from './ProjectBuildInfo';
import getProjectFieldDisplayManager from '../../utils/getProjectFieldDisplayManager';

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const propTypes = {
    projects: PropTypes.object,
    projectId: PropTypes.string,
    isSummaryOnly: PropTypes.bool,
    showActions: PropTypes.bool,
    buildId: PropTypes.number,
};

const defaultProps = {
    isSummaryOnly: false,
    showActions: true,
};

const ProjectBuildTeaser = ({projects, projectId, buildId, isSummaryOnly, showActions}) => {
    const {translate} = useLocales();
    const project = projects?.[projectId];
    const build = project?.results?.[buildId];
    const projectFieldsManager= getProjectFieldDisplayManager({translate});

    useEffect(() => {
        if (!build?.data) {
            PubSub.publish('SHOW.BUSY');
            return;
        }

        PubSub.publish('HIDE.BUSY');
    }, [build?.data]);

    if (!build?.data) {
        return null;
    }

    const buildStateConfig = ProjectBuildStateConfig[build?.data?.state];
    const projectBuildPage = `${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`;

    return (
        <Card sx={{p: 3, m: 0}}>
            {
                showActions &&
                <CardHeader
                    sx={{p: 0, mb: 3}}
                    title={(
                        <Box sx={{pb: 1}}>
                            {projectFieldsManager.buildVendor?.q(project, true)}
                        </Box>
                    )}
                    subheader={(
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <MHidden width="mdDown">
                                <Link
                                    to={`${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`}
                                    component={RouterLink}
                                    underline="none">
                                    <Typography variant="h6">
                                        #{buildId}.
                                    </Typography>
                                </Link>
                            </MHidden>
                            <ProjectBuildInfo
                                state={build?.data?.state}
                                start={build?.data?.start}
                                end={build?.data?.end}
                                showDate
                                showDuration
                                showState
                            />
                        </Stack>
                    )}
                    action={(
                        <Stack
                            spacing={1}
                            direction="row"
                            alignItems="center"
                            justifyContent="flex-end">
                            <MHidden width="mdDown">
                                {
                                    !isSummaryOnly && (
                                        <ProjectBuildPagination
                                            projectId={projectId}
                                            buildId={buildId}
                                        />
                                    )
                                }
                            </MHidden>
                        </Stack>
                    )}
                />
            }
            <Stack
                direction="row"
                alignItems="center"
                justifyContent="flex-end"
                spacing={3}
                sx={{mb: 3}}>
                {
                    !showActions && (
                        <Box sx={{pb: 1}}>
                            {projectFieldsManager.buildVendor?.q(project, true)}
                        </Box>
                    )
                }
                <Button
                    size="large"
                    variant="outlined"
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'custom',
                            dialogProps: {
                                title: translate('checkout.metrics.label'),
                            },
                            children: (
                                <ProjectBuildQuotaUsage
                                    buildSummary={build?.data?.buildSummary}
                                />
                            ),
                        });
                    }}>
                    {translate('checkout.metrics.label')}
                </Button>
                {
                    Object.keys(build.events).length > 0 && (
                        <Button
                            size="large"
                            variant="outlined"
                            onClick={() => {
                                PubSub.publish('SHOW.DIALOG', {
                                    type: 'custom',
                                    children: (
                                        <ProjectBuildEvents
                                            projectId={projectId}
                                            buildId={buildId}
                                        />
                                    ),
                                });
                            }}>
                            {translate('project.build.timeline', {count: Object.values(build.events).length})}
                        </Button>
                    )
                }
            </Stack>
            <Stack spacing={3}>
                <Alert severity={buildStateConfig?.severity}>
                    {translate(`project.build.states.${build?.data?.state}.description`)}
                </Alert>
                {
                    isSummaryOnly ? (
                        <Stack spacing={3}>
                            <ProjectSolutionBuildSummary
                                projectId={projectId}
                                buildId={buildId}
                                solution={solutions.linkChecker.appName}
                            />
                            {
                                project?.configData?.solution?.key !== solutions.linkChecker.appName && (
                                    <ProjectSolutionBuildSummary
                                        projectId={projectId}
                                        buildId={buildId}
                                        solution={project?.configData?.solution?.key}
                                    />
                                )
                            }
                            <Stack direction="row" alignItems="center" justifyContent="flex-end">
                                <Button
                                    variant="contained"
                                    size="large"
                                    to={projectBuildPage}
                                    component={RouterLink}>
                                    {translate('project.build.ctaLabel')}
                                </Button>
                            </Stack>
                        </Stack>
                    ) : (
                        <ProjectBuildWrapper
                            projectId={projectId}
                            buildId={buildId}
                        />
                    )
                }
            </Stack>
        </Card>
    );
};

ProjectBuildTeaser.propTypes = propTypes;
ProjectBuildTeaser.defaultProps = defaultProps;

export default connect(mapStateToProps)(ProjectBuildTeaser);
