import PropTypes from 'prop-types';
import {useEffect, useRef, useState} from 'react';
import {Alert, LinearProgress, Stack, Typography} from '@material-ui/core';
import {connect} from 'react-redux';
import PubSub from 'pubsub-js';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import useLocales from '../../../hooks/useLocales';
import {fDuration} from '../../../utils/formatTime';
import MoreMenuButton from '../forms/MoreMenuButton';

const propTypes = {
    buildId: PropTypes.number,
    projectId: PropTypes.string,
    projects: PropTypes.object,
};

const mapStateToProps = ({state}) => {
    const {
        activeProjectData,
        projects,
    } = state;

    return {
        activeProjectData,
        projects,
    };
};

const ProjectRunInProgress = ({
    projectId,
    buildId,
    projects,
}) => {
    const project = projects?.[projectId];
    const build = project?.results?.[buildId];
    const {translate} = useLocales();
    const [duration, setDuration] = useState();
    const intervalsRef = useRef({});

    useEffect(() => {
        PubSub.publish('HIDE.NOTIFICATIONS.ALL');
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        if (!build?.data?.start) {
            return;
        }

        if (project?.latestBuild?.state !== BUILD_STATES.running) {
            clearInterval(intervalsRef.current[buildId]);
            return;
        }

        intervalsRef.current[buildId] = setInterval(() => {
            setDuration(fDuration(build?.data?.start));
        }, 1000);

        // eslint-disable-next-line
        return () => {
            // eslint-disable-next-line
            clearInterval(intervalsRef.current[buildId]);
        };
        // eslint-disable-next-line
    }, [project?.latestBuild?.state]);

    return (
        <Alert
            severity="info"
            action={(
                <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={1}>
                    <MoreMenuButton
                        additionalItems={[{
                            label: translate('project.sync'),
                            callback: () => {
                                PubSub.publish('PROJECT_LOAD', {
                                    filterIdList: [projectId],
                                    includeDetails: true,
                                });
                            },
                        }]}
                    />
                </Stack>
            )}
            sx={{
                '.MuiAlert-message': {
                    flexGrow: 1,
                },
            }}>
            <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
            >
                <Typography variant="body">
                    {translate('project.build.indexedBuild', {
                        buildId,
                        state: translate(`project.build.states.${project?.latestBuild?.state}.label`),
                    })}
                </Typography>
                {
                    Boolean(duration) && (
                        <Typography variant="body">
                            {duration}
                        </Typography>
                    )
                }
            </Stack>
            <LinearProgress sx={{my: 1}} />
        </Alert>
    )
};

ProjectRunInProgress.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectRunInProgress);
