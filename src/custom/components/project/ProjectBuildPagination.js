import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Icon} from '@iconify/react';
import {Fragment} from 'react';
import {useTheme} from '@material-ui/core/styles';
import {IconButton, MenuItem, Stack, TextField, Tooltip, useMediaQuery} from '@material-ui/core';
import {Link as RouterLink, useNavigate} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useLocales from '../../../hooks/useLocales';

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const propTypes = {
    projects: PropTypes.object,
    projectId: PropTypes.string,
    buildId: PropTypes.number,
};

const ProjectBuildPagination = ({projects, projectId, buildId}) => {
    const {translate} = useLocales();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
    const project = projects?.[projectId];
    const build = project?.results?.[buildId];
    const navigate = useNavigate();
    const latestBuildIndex = project.latestBuild.index;

    if (latestBuildIndex < 2 || !build?.data) {
        return null;
    }

    const dropdownContent = (
        <TextField
            select
            label={translate('project.build.label')}
            value={build.data.buildId}
            onChange={(event) => {
                navigate(`${PATH_DASHBOARD.general.projects.root}/${project.id}/${event.target.value}`);
            }}
            sx={{
                minWidth: 160,
                textAlign: 'center',
            }}>
            {
                Array(latestBuildIndex)
                    .fill()
                    .map((_, index) => {
                        const value = index + 1;
                        return (
                            <MenuItem key={value} value={value}>
                                {value}
                            </MenuItem>
                        );
                    })
            }
        </TextField>
    );

    return (
        <Stack
            spacing={1}
            direction="row"
            alignItems="center"
            justifyContent="flex-end">
            {
                [
                    {
                        key: 'first',
                        icon: 'eva:arrowhead-left-outline',
                        index: 1,
                        disabled: build.data.buildId <= 2,
                        hide: isMobile,
                    },
                    {
                        key: 'previous',
                        icon: 'eva:arrow-ios-back-outline',
                        index: build.data.buildId - 1,
                        disabled: build.data.buildId < 2,
                    },
                    {
                        key: 'next',
                        icon: 'eva:arrow-ios-forward-outline',
                        index: build.data.buildId + 1,
                        disabled: build.data.buildId >= project?.latestBuild?.index,
                    },
                    {
                        key: 'last',
                        icon: 'eva:arrowhead-right-outline',
                        index: project?.latestBuild?.index,
                        disabled: build.data.buildId >= project?.latestBuild?.index,
                        hide: isMobile,
                    },
                ].map(({key, icon, index, disabled, hide}) => {
                    const label = translate(`project.build.navigation.${key}`);
                    return (
                        <Fragment key={key}>
                            {
                                !hide && (
                                    <Tooltip
                                        title={disabled ? '' : `${label} (#${index})`}>
                                        <IconButton
                                            to={`${PATH_DASHBOARD.general.projects.root}/${project.id}/${index}`}
                                            size="large"
                                            color="inherit"
                                            component={RouterLink}
                                            disabled={disabled}
                                        >
                                            <Icon icon={icon} />
                                        </IconButton>
                                    </Tooltip>
                                )
                            }
                            {
                                key === 'previous' && (
                                    dropdownContent
                                )
                            }
                        </Fragment>
                    );
                })
            }
        </Stack>
    );
};

ProjectBuildPagination.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildPagination);
