import PropTypes from 'prop-types';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, IconButton, Stack, Tooltip, Typography} from '@material-ui/core';
import {connect} from 'react-redux';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import {vendors} from '@w7-3/webeagle-resources/dist/config/project';
import {EVENTS} from '@w7-3/webeagle-resources/dist/config/infoCodes';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';

const propTypes = {
    projectId: PropTypes.string,
    projectBuildQueueItem: PropTypes.object,
};

const mapStateToProps = ({state}, ownProps) => {
    const {
        projects,
    } = state;
    const project = projects?.[ownProps?.projectId];
    const projectBuildQueueItem = project?.buildQueue?.[vendors.manual];

    return {
        projectBuildQueueItem,
    };
};

const ProjectRunInQueue = ({
    projectId,
    projectBuildQueueItem,
}) => {
    const {translate} = useLocales();

    if (!projectBuildQueueItem?.id) {
        return null;
    }

    return (
        <Alert
            severity="info"
            action={(
                <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={1}>
                    <Tooltip title={translate('project.sync')}>
                        <IconButton
                            onClick={() => {
                                PubSub.publish('PROJECT_LOAD', {
                                    filterIdList: [projectId],
                                });
                            }}
                            color="primary"
                        >
                            <Icon icon="eva:refresh-outline" width={40} height={40} />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={translate('project.build.cancellation.cta')}>
                        <IconButton
                            onClick={() => {
                                PubSub.publish('PROJECT.DEQUEUE', {
                                    projectId,
                                    id: projectBuildQueueItem.id,
                                });
                            }}
                            color="primary"
                        >
                            <Icon icon="eva:close-outline" width={40} height={40} />
                        </IconButton>
                    </Tooltip>
                </Stack>
            )}
            sx={{
                '.MuiAlert-message': {
                    flexGrow: 1,
                },
            }}>
            <AlertTitle sx={{
                display: 'flex',
            }}>
                {fDateTimeHumanReadable(projectBuildQueueItem.createdOn)}
                {': '}
                ****.{projectBuildQueueItem.id.slice(-4)}
                <CopyToClipboard text={projectBuildQueueItem.id}>
                    <Tooltip title={translate('copy')}>
                        <IconButton
                            sx={{ml: 1, mt: -1}}
                            color="primary"
                        >
                            <Icon icon="eva:copy-fill" width={40} height={40}/>
                        </IconButton>
                    </Tooltip>
                </CopyToClipboard>
            </AlertTitle>
            <Typography>
                {translate(`events.${EVENTS.DUPLICATE_PROJECT_QUEUED}`)}
            </Typography>
        </Alert>
    )
};

ProjectRunInQueue.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectRunInQueue);
