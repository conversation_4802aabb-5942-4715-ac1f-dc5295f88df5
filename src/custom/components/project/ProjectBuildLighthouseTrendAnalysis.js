import {useState} from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {<PERSON>ert, AlertTitle, Box, FormControlLabel, Paper, Radio, RadioGroup, Stack, Typography} from '@material-ui/core';
import ReactApex<PERSON>hart from 'react-apexcharts';
import {merge} from 'lodash';
import {BUILD_STATES, LIGHTHOUSE_DEVICES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {LIGHTHOUSE_FIELDS} from '@w7-3/webeagle-resources/dist/config/lighthouse';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {BaseOptionChart} from '../../../components/charts';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable, fPercentageValue} from '../../../utils/formatTime';

const mapStateToProps = ({state}, ownProps) => {
    const {
        projects,
    } = state;

    return {
        project: projects?.[ownProps?.projectId],
    };
};

const propTypes = {
    project: PropTypes.object,
    buildId: PropTypes.number ,
    url: PropTypes.string,
    resultIds: PropTypes.arrayOf(PropTypes.string),
};

export const getChartProps = ({project, buildId, resultIds}, translate) => {
    const buildKeyList = Object.keys(project?.results)
        .filter((buildKey) => {
            return Number.parseInt(buildKey, 10) <= buildId && [
                BUILD_STATES.success,
                BUILD_STATES.mixed,
            ].includes(project?.results?.[buildKey]?.data?.state);
        });
    const categories = [];
    const chartProps = {
        chartData: [],
    };
    const urls = {};
    const devices = [];

    buildKeyList.forEach((buildKey, buildIndex) => {
        const build = project.results[buildKey];
        categories.push(fDateTimeHumanReadable(build.data.start));
        Object.values(build.solutionResults).filter(({id}) => {
            if (!isNonEmptyArray(resultIds)) {
                return true;
            }

            return resultIds.includes(id);
        }).forEach(({url, ...data}) => {
            Object.values(LIGHTHOUSE_DEVICES).forEach(({key: device}) => {
                const lighthouseData = data?.solutionData?.lighthouse?.[device];

                if (!devices.includes(device)) {
                    devices.push(device);
                }

                urls[url] = urls[url] || {};
                urls[url][device] = urls[url][device] || Array(LIGHTHOUSE_FIELDS.categories).fill();

                let lhCategoryIndex = 0;

                Object.values(LIGHTHOUSE_FIELDS.categories).forEach((category) => {
                    const name = translate(`project.wizardSteps.solutionsConfigs.groups.lighthouse.categories.fields.${category}.label`);
                    const item = urls[url][device].find((item) => item?.name === name) || {
                        name,
                        data: Array(buildKeyList.length).fill(),
                    };

                    item.data[buildIndex] = lighthouseData?.scores?.[category] ?
                        fPercentageValue(lighthouseData?.scores?.[category]) : -1;
                    urls[url][device][lhCategoryIndex] = item;
                    lhCategoryIndex += 1;
                });
            });
        });
    });

    Object.keys(urls).forEach((url) => {
        chartProps.chartData.push({
            url,
            data: urls[url]
        });
    })

    chartProps.chartOptions = merge(BaseOptionChart(), {
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            offsetY: 10,
        },
        xaxis: {
            categories,
            labels: {
                rotate: -45,
                rotateAlways: true,
            },
        },
        yaxis: {
            min: 0,
            max: 100,
        },
        tooltip: {
            y: {
                formatter: (value) => Number.isNaN(value) ? '-' : `${value}%`,
            },
        },
    });

    return {
        chartProps,
        devices,
    };
};

const ProjectBuildLighthouseTrendAnalysis = ({project, buildId, url, resultIds}) => {
    const {translate} = useLocales();
    const {
        devices,
        chartProps,
    } = getChartProps({
        project,
        buildId,
        resultIds,
    }, translate);
    const [device, setActiveDevice] = useState(devices?.[0]);

    if (chartProps.chartData.length < 1) {
        return null;
    }

    return (
        <Stack spacing={3}>
            <Alert color="info">
                <AlertTitle sx={{mb: 3}}>{translate('notice')}</AlertTitle>
                <Typography>
                    {translate('project.wizardSteps.solutionsConfigs.groups.lighthouse.output.missingBuild')}
                </Typography>
                <Typography>
                    {translate('project.wizardSteps.solutionsConfigs.groups.lighthouse.output.negativeValueInfo')}
                </Typography>
            </Alert>
            <RadioGroup
                row
                value={device}
                onChange={(event) => {
                    setActiveDevice(event.target.value);
                }}>
                {
                    devices.map((key) => {
                        return (
                            <FormControlLabel
                                value={key}
                                key={key}
                                label={translate(`project.wizardSteps.target.device.lighthouse.targets.${key}`)}
                                control={<Radio />}/>
                        );
                    })
                }
            </RadioGroup>
            <Paper sx={{width: '100%'}}>
                {chartProps.chartData.map((item) => {
                    return (
                        <Box key={item.url} sx={{mt: 3, mx: 3}} dir="ltr">
                            {item.url === url && (
                                <ReactApexChart
                                    type="area"
                                    series={item.data[device]}
                                    options={chartProps.chartOptions}
                                    height={400}
                                />
                            )}
                        </Box>
                    );
                })}
            </Paper>
        </Stack>
    );
};

ProjectBuildLighthouseTrendAnalysis.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildLighthouseTrendAnalysis);
