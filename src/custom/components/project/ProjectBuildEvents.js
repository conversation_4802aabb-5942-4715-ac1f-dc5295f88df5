import {connect} from 'react-redux';
import {useEffect, useRef} from 'react';
import PropTypes from 'prop-types';
import EventsTimeline from '../utils/EventsTimeline';

const propTypes = {
    buildId: PropTypes.number,
    projectId: PropTypes.string,
    projects: PropTypes.object,
};

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const ProjectBuildEvents = ({
    buildId,
    projects,
    projectId,
}) => {
    const project = projects?.[projectId];
    const ref = useRef();
    const build = project?.results?.[buildId];

    useEffect(() => {
        if (!ref.current) {
            return;
        }

        ref.current.scrollTop = ref.current.scrollHeight;
    }, [build?.events]);

    if (!build) {
        return null;
    }

    return (
        <EventsTimeline
            eventList={Object.values(build.events)}
        />
    )
};

ProjectBuildEvents.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildEvents);
