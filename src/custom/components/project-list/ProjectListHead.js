import PropTypes from 'prop-types';
import {visuallyHidden} from '@material-ui/utils';
import {Box, TableCell, TableHead, TableRow, TableSortLabel} from '@material-ui/core';

const propTypes = {
    order: PropTypes.oneOf(['asc', 'desc']),
    orderBy: PropTypes.string,
    tableHeadList: PropTypes.array,
    onRequestSort: PropTypes.func,
};

const ProjectListHead = ({
    order,
    orderBy,
    tableHeadList,
    onRequestSort,
}) => {
    const createSortHandler = (property) => (event) => {
        onRequestSort(event, property);
    };

    return (
        <TableHead>
            <TableRow>
                {tableHeadList.map((headCell) => {
                    return (
                        <TableCell
                            key={headCell.id}
                            align="left"
                            sortDirection={orderBy === headCell.id ? order : false}
                            sx={headCell?.sx}>
                            <TableSortLabel
                                hideSortIcon
                                active={orderBy === headCell.id}
                                direction={orderBy === headCell.id ? order : 'asc'}
                                onClick={createSortHandler(headCell.id)}>
                                {headCell.label}
                                {orderBy === headCell.id ? (
                                    <Box sx={{...visuallyHidden}}>
                                        {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                                    </Box>
                                ) : null}
                            </TableSortLabel>
                        </TableCell>
                    );
                })}
                <TableCell sx={{width: 40, p: 1}} />
                <TableCell sx={{width: 40, p: 1}} />
            </TableRow>
        </TableHead>
    );
};

ProjectListHead.propTypes = propTypes;

export default ProjectListHead;
