import PropTypes from 'prop-types';
import {Paper, Typography} from '@material-ui/core';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';

const ProjectListEmpty = ({searchQuery = ''}) => {
    const {translate} = useLocales();

    return (
        <Paper sx={{p: 3}}>
            <Typography gutterBottom align="center" variant="subtitle1">
                {translate('project.list.search.emptyLabel')}
            </Typography>
            {
                isNonEmptyString(searchQuery) && (
                    <Typography variant="body2" align="center">
                        {translate('project.list.search.notice', {searchQuery})}
                    </Typography>
                )
            }
        </Paper>
    );
};

ProjectListEmpty.propTypes = {
    searchQuery: PropTypes.string,
};

export default ProjectListEmpty;

