import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {getSummaryData} from '@w7-3/webeagle-resources/dist/libs/project-solution/summaries';
import useLocales from '../../../hooks/useLocales';
import BasicSolutionSummaryDataItems from './BasicSolutionSummaryDataItems';
import LinkCheckerBuildSummary from './summaries/LinkCheckerBuildSummary';
import URLChallengeBuildSummary from './summaries/URLChallengeBuildSummary';

const propTypes = {
    solution: PropTypes.string,
    projectId: PropTypes.string,
    buildId: PropTypes.number,
    project: PropTypes.object,
};

const mapStateToProps = ({state}, {projectId}) => {
    const {
        projects,
    } = state;

    const project = projects?.[projectId];

    return {
        project,
    };
};

const ProjectSolutionBuildSummary = ({
    solution,
    buildId,
    project,
}) => {
    const {
        currentLang,
    } = useLocales();

    if (!project?.results?.[buildId]?.data?.solutionSummary?.[solution]) {
        return null;
    }

    if (solution === solutions.linkChecker.appName) {
        return (<LinkCheckerBuildSummary {...{solution, project, buildId}} />);
    }

    if (solution === solutions.urlChallenge.appName) {
        return (<URLChallengeBuildSummary {
            ...{solution, project, buildId}
        } />);
    }

    return (
        <BasicSolutionSummaryDataItems
            data={getSummaryData({
                solutionKey: solution,
                language: currentLang.value,
                timeZone: global.webAutomate.timeZone,
                projectBuildOverviewData: project?.results?.[buildId]?.data,
                previousProjectBuildOverviewData: project?.results?.[buildId - 1]?.data,
            })}
        />
    );
};

ProjectSolutionBuildSummary.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectSolutionBuildSummary);
