import PropTypes from 'prop-types';
import {<PERSON><PERSON>, <PERSON>rid, <PERSON>ack, Typography} from '@material-ui/core';
import {useSearchParams} from 'react-router-dom';
import {LIGHTHOUSE_FIELDS} from '@w7-3/webeagle-resources/dist/config/lighthouse';
import {isNonEmptyObject, isNonEmptyString, isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import useLocales from '../../../../hooks/useLocales';
import LighthouseItemCard from '../../project/LighthouseItemCard';
import LighthouseScoreScale from '../../project/LighthouseScoreScale';
import {thresholds} from '../../../../config/lighthouse';
import ProjectBuildResultInfo from '../../project/ProjectBuildResultInfo';
import ExternalLink from '../../utils/ExternalLink';
import {getCDNPath} from '../../../utils/getPath';
import DefinitionList from '../../utils/DefinitionList';
import InfoCode from '../../utils/InfoCode';

const propTypes = {
    data: PropTypes.object,
    isCompact: PropTypes.bool,
};

const availableLighthouseCategoryList = Object.values(LIGHTHOUSE_FIELDS.categories);

const LighthouseBuildDetails = ({data, isCompact}) => {
    const {translate} = useLocales();
    const [searchParams, updateSearchParams] = useSearchParams();
    const lighthouse = data?.solutionData?.lighthouse || {};
    const lighthouseDeviceList = Object.keys(lighthouse);

    if (lighthouseDeviceList.length === 0) {
        return (
            <Alert severity="warning">
                {translate('project.wizardSteps.solutionsConfigs.groups.lighthouse.captured', {
                    count: 0,
                })}
            </Alert>
        );
    }

    let activeTabKey = searchParams.get(urlParameters.project.lighthouse.tab);

    if (!activeTabKey) {
        ([activeTabKey] = lighthouseDeviceList);
    }

    return (
        <>
            <DefinitionList
                dataList={lighthouseDeviceList.map((device) => {
                    const requestData = lighthouse[device];
                    const reportList = isNonEmptyObject(requestData?.formats) ? Object.keys(requestData.formats)
                        .filter((format) => requestData.formats?.[format])
                        .map((format) => ({
                            format,
                            file: requestData.files[format],
                        })) : [];
                    const isSuccess = requestData?.scores && !requestData?.error;

                    return {
                        key: device,
                        label: translate(`project.wizardSteps.target.device.lighthouse.targets.${device}`),
                        node: (
                            <Grid container spacing={3}>
                                <Grid item
                                      xs={12}>
                                    <ProjectBuildResultInfo
                                        requestedUrl={data?.url}
                                        resolvedUrl={requestData?.resolvedUrl}
                                    />
                                </Grid>
                                {
                                    isSuccess ? (
                                        <>
                                            <Grid item
                                                  xs={12}>
                                                <Typography variant="subtitle1" sx={{mt: 3}}>
                                                    {translate('project.wizardSteps.solutionsConfigs.groups.lighthouse.scale')}
                                                </Typography>
                                            </Grid>
                                            {
                                                availableLighthouseCategoryList
                                                    .filter((category) => Object.keys(requestData?.scores).includes(category))
                                                    .map((category) => (
                                                        <Grid item
                                                              xs={6}
                                                              md={3}
                                                              key={category}>
                                                            <LighthouseItemCard score={requestData?.scores?.[category]} category={category}/>
                                                        </Grid>
                                                    ))
                                            }
                                            <Grid item
                                                  xs={12}>
                                                <Typography variant="subtitle1" sx={{my: 3}}>
                                                    {translate('project.wizardSteps.solutionsConfigs.groups.lighthouse.output.previewLabel')}
                                                </Typography>
                                                <Stack direction="row" alignItems="center" spacing={3}>
                                                    {
                                                        reportList.length > 0 ? (
                                                            reportList.map(({format, file}) => {
                                                                if (isNonEmptyString(file?.data?.destination)) {
                                                                    return (
                                                                        <ExternalLink
                                                                            key={format}
                                                                            url={getCDNPath(file?.data?.destination)}
                                                                            label={format.toUpperCase()}
                                                                        />
                                                                    );
                                                                }

                                                                if (isNonEmptyArray(file?.infoCodes)) {
                                                                    return file.infoCodes.map((infoCode, index) => (
                                                                        <InfoCode
                                                                            key={index}
                                                                            infoCode={infoCode}
                                                                            isDismissible={false}
                                                                        />
                                                                    ));
                                                                }

                                                                return (
                                                                    <Alert
                                                                        key={format}
                                                                        severity="error">
                                                                        {translate('events.EVENTS.GS_ERROR_UPLOAD', {format})}
                                                                    </Alert>
                                                                );
                                                            })
                                                        ) : (
                                                            translate('project.wizardSteps.solutionsConfigs.groups.lighthouse.output.fields.none.report')
                                                        )
                                                    }
                                                </Stack>
                                            </Grid>
                                        </>
                                    ) : (
                                        <Grid item xs={12}>
                                            <Alert severity="error">
                                                {translate('project.build.erroneous')}
                                            </Alert>
                                        </Grid>
                                    )
                                }
                            </Grid>
                        ),
                    };
                })}
                variant={DefinitionList.VARIANTS.tabs}
                tabConfig={{
                    activeTabKey,
                    onChange: (item) => {
                        searchParams.set(urlParameters.project.lighthouse.tab, item?.tab);
                        updateSearchParams(searchParams);
                    },
                }}
            />
            {
                !isCompact &&
                <Stack direction="row"
                       justifyContent="space-around"
                       alignItems="center"
                       spacing={1}
                       sx={{
                           width: '100%',
                           my: 5,
                           p: 1,
                           borderRadius: (theme) => theme.shape.borderRadiusSm,
                           border: (theme) => `solid 1px ${theme.palette.divider}`,
                       }}>
                    {
                        Object.values(thresholds)
                            .map(({range, color}) => (
                                <LighthouseScoreScale
                                    key={color}
                                    score={range[0]}
                                    label={`${range[0]} - ${range[1]}`} />
                            ))
                    }
                </Stack>
            }
        </>
    );
};

LighthouseBuildDetails.propTypes = propTypes;

export const Content = LighthouseBuildDetails;
