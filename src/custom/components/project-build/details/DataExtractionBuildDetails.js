import PubSub from 'pubsub-js';
import {Al<PERSON>, AlertT<PERSON>le, Box, Button, Stack, Typography} from '@material-ui/core';
import dataExtractions from '@w7-3/webeagle-resources/dist/config/dataExtractions';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import CopyClipboard from '../../../../components/CopyClipboard';
import useLocales from '../../../../hooks/useLocales';
import {ProjectSolutionRendererData} from '../prop-types/StepTypes';
import LabelStyle from '../../utils/LabelStyle';
import InfoPopoverAI from '../../utils/InfoPopoverAI';
import ConfidenceScoreAI from '../../utils/ConfidenceScoreAI';
import InfoCode from '../../utils/InfoCode';
import Label from '../../../../components/Label';
import {fData} from '../../../../utils/formatNumber';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.dataExtractions';

export const getValue = ({rawValue}) => {
    if (typeof rawValue === 'object') {
        return JSON.stringify(rawValue, null, 4);
    }

    return `${rawValue}`;
};

export const AIDialog = ({
    item,
    label,
}) => {
    const {translate} = useLocales();
    const {result, success} = item?.data || {};
    const value = getValue({rawValue: result?.value});
    const {
        byteCount,
        confidence,
        charCount,
    } = result?.extractionData || {};

    return (
        <Button
            size="large"
            variant="outlined"
            onClick={() => {
                PubSub.publish('SHOW.DIALOG', {
                    type: 'custom',
                    dialogProps: {
                        title: translate('project.wizardSteps.solutionsConfigs.groups.dataExtractions.extractedData', {
                            label,
                        }),
                    },
                    children: (
                        <Stack spacing={3}>
                            <Stack spacing={1}>
                                <LabelStyle>
                                    {translate('codeField.aiText.input')}
                                </LabelStyle>
                                <Box
                                    component="p"
                                    sx={{
                                        p: 2,
                                        border: (theme) => `solid 1px ${theme.palette.divider}`
                                    }}
                                >{item?.step?.value?.code?.value}</Box>
                            </Stack>
                            {
                                success && (
                                    <Stack spacing={1}>
                                        <LabelStyle>
                                            {translate('codeField.aiText.output')}
                                        </LabelStyle>
                                        <Box
                                            component="p"
                                            sx={{
                                                p: 2,
                                                border: (theme) => `solid 1px ${theme.palette.divider}`
                                            }}
                                        >{value}</Box>
                                        <Stack
                                            direction="row"
                                            justifyContent="flex-end"
                                            alignItems="center"
                                            spacing={1}
                                        >
                                            {
                                                charCount && (
                                                    <Label
                                                        variant="outlined"
                                                        color="primary"
                                                    >{translate('charCount')}: {charCount}</Label>
                                                )
                                            }
                                            {
                                                byteCount && (
                                                    <Label
                                                        variant="outlined"
                                                    >{translate('byteCount')}: {fData(byteCount)}</Label>
                                                )
                                            }
                                        </Stack>
                                    </Stack>
                                )
                            }
                            {
                                isNonEmptyArray(item?.infoCodes) && (
                                    item.infoCodes.map((infoCode, index) => (
                                        <InfoCode
                                            key={index}
                                            infoCode={infoCode}
                                        />
                                    ))
                                )
                            }
                            <ConfidenceScoreAI
                                value={confidence}
                                isDescription
                            />
                        </Stack>
                    )
                });
            }}
        >
            {translate('view.aiDialog')}
        </Button>
    );
};

AIDialog.propTypes = ProjectSolutionRendererData;

export const Content = ({
    label,
    item,
}) => {
    const {translate} = useLocales();
    const {result, success} = item?.data || {};
    const contentType = translate(`${i18nContext}.data`);
    const value = getValue({rawValue: result?.value});
    const isAIStep = item?.step?.type === dataExtractions.automated.ai.value;
    const confidence = result?.extractionData?.confidence;

    return (
        <Stack spacing={3}>
            {
                success ? (
                    <Stack
                        spacing={3}
                        direction="row"
                        alignItems="center"
                    >
                        <Button
                            size="large"
                            variant="outlined"
                            onClick={() => {
                                PubSub.publish('SHOW.DIALOG', {
                                    type: 'custom',
                                    dialogProps: {
                                        title: translate('project.wizardSteps.solutionsConfigs.groups.dataExtractions.extractedData', {
                                            label,
                                        }),
                                    },
                                    children: (
                                        <CopyClipboard
                                            value={value}
                                            title={translate('clipboard.copyTitle', {contentType})}
                                            InputProps={{
                                                readOnly: true,
                                            }}
                                        />
                                    )
                                });
                            }}
                        >
                            {translate('project.wizardSteps.solutionsConfigs.groups.dataExtractions.viewExtractedData')}
                        </Button>
                    </Stack>
                ) : (
                    <Alert severity="warning">
                        <AlertTitle>
                            {translate('project.wizardSteps.solutionsConfigs.groups.dataExtractions.captured_zero')}
                        </AlertTitle>
                    </Alert>
                )
            }
            {
                isAIStep && (
                    <Stack
                        spacing={3}
                        direction="row"
                        alignItems="center"
                    >
                        <ConfidenceScoreAI
                            value={confidence}
                        />
                        <AIDialog
                            item={item}
                            label={label}
                        />
                    </Stack>
                )
            }
        </Stack>
    );
};

Content.propTypes = ProjectSolutionRendererData;

export const getLabel = ({translate, item}) => {
    const isAIStep = item?.step?.type === dataExtractions.automated.ai.value;

    return (
        <Stack
            spacing={1}
            direction="row"
            alignItems="center"
        >
            <Typography>
                {translate(`${i18nContext}.label`)}
            </Typography>
            {
                isAIStep && (
                    <InfoPopoverAI />
                )
            }
        </Stack>
    );
};

export const getItemId = (props) => {
    return props?.item?.id;
};

export const getFieldDisplayManager = (props) => {
    return {
        summary: (
            <Content
                {...props}
            />
        ),
    };
};
