import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import path from 'ramda/src/path';
import {Box, Button, Stack, Typography} from '@material-ui/core';
import {useTheme} from '@material-ui/core/styles';
import {challenges} from '@w7-3/webeagle-resources/dist/config/urlChallenge';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../hooks/useLocales';
import Label from '../../../../components/Label';
import {urlChallengeBuildStates} from '../../../../config/base';
import InfoPopoverAI from '../../utils/InfoPopoverAI';
import ConfidenceScoreAI from '../../utils/ConfidenceScoreAI';
import LabelStyle from '../../utils/LabelStyle';
import InfoCode from '../../utils/InfoCode';
import {ProjectSolutionRendererData} from '../prop-types/StepTypes';

const propTypes = {
    item: PropTypes.object,
};

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.urlChallenge';

export const getItemSummaryFlag = (item) => {
    const {isAborted, isVoided, data} = item;

    if (isAborted) {
        return urlChallengeBuildStates.aborted;
    }

    if (isVoided) {
        return urlChallengeBuildStates.voided;
    }

    if (!data?.success) {
        return urlChallengeBuildStates.failed;
    }

    if (data?.result?.passed) {
        return urlChallengeBuildStates.passed;
    }

    return urlChallengeBuildStates.notPassed;
};

export const AIDialog = ({
    item,
}) => {
    const {translate} = useLocales();
    const {result, success} = item?.data || {};
    const confidence = result?.data?.confidence;
    const error = result?.data?.error;
    const conditionComments = result?.data?.urlChallengeComments;
    const passed = result?.passed === true;
    const theme = useTheme();
    const {
        label,
    } = getItemSummaryFlag(item);
    const {color, icon} = urlChallengeBuildStates[label];

    return (
        <Button
            size="large"
            variant="outlined"
            onClick={() => {
                PubSub.publish('SHOW.DIALOG', {
                    type: 'custom',
                    dialogProps: {
                        title: translate(`${i18nContext}.label`, {
                            label,
                        }),
                    },
                    children: (
                        <Stack spacing={3}>
                            <Stack spacing={1}>
                                <LabelStyle>
                                    {translate('codeField.aiText.input')}
                                </LabelStyle>
                                <Box
                                    component="p"
                                    sx={{
                                        p: 2,
                                        border: (theme) => `solid 1px ${theme.palette.divider}`
                                    }}
                                >{item?.step?.value?.code?.value}</Box>
                            </Stack>
                            {
                                success && passed && (
                                    <Stack spacing={1}>
                                        <LabelStyle>
                                            {translate('codeField.aiText.output')}
                                        </LabelStyle>
                                        <Box
                                            component="p"
                                            sx={{
                                                p: 2,
                                                border: (theme) => `solid 1px ${theme.palette.divider}`
                                            }}
                                        >{conditionComments}</Box>
                                    </Stack>
                                )
                            }
                            {
                                success && !passed && (
                                    <Stack spacing={1}>
                                        <LabelStyle>
                                            {translate('codeField.aiText.output')}
                                        </LabelStyle>
                                        <Box
                                            component="p"
                                            sx={{
                                                p: 2,
                                                border: (theme) => `solid 1px ${theme.palette.divider}`
                                            }}
                                        >{error}</Box>
                                    </Stack>
                                )
                            }
                            <Label
                                sx={{alignSelf: 'flex-start', p: 3}}
                            >
                                <Box
                                    sx={{mr: 1}}
                                    component={Icon}
                                    icon={icon}
                                    width={40}
                                    height={40}
                                    color={path(color.split('.'), theme.palette)}
                                />
                                {translate(`${i18nContext}.labels.${label}.latest`)}
                            </Label>
                            <ConfidenceScoreAI
                                value={confidence}
                                isDescription
                            />
                            {
                                isNonEmptyArray(item?.infoCodes) && (
                                    item.infoCodes.map((infoCode, index) => (
                                        <InfoCode
                                            key={index}
                                            infoCode={infoCode}
                                        />
                                    ))
                                )
                            }
                        </Stack>
                    )
                });
            }}
        >
            {translate('view.aiDialog')}
        </Button>
    );
};

AIDialog.propTypes = ProjectSolutionRendererData;

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const URLChallengeBuildDetails = ({
    item
}) => {
    const {translate} = useLocales();
    const theme = useTheme();
    const {label} = getItemSummaryFlag(item);
    const {color, icon} = urlChallengeBuildStates[label];
    const {result} = item?.data || {};
    const isAIStep = item?.step?.type === challenges.automated.ai.value;
    const confidence = result?.data?.confidence;

    return (
        <Stack spacing={3}>
            <Label
                sx={{alignSelf: 'flex-start', p: 3}}
            >
                <Box
                    sx={{mr: 1}}
                    component={Icon}
                    icon={icon}
                    width={40}
                    height={40}
                    color={path(color.split('.'), theme.palette)}
                />
                {translate(`${i18nContext}.labels.${label}.latest`)}
            </Label>
            {
                isAIStep && (
                    <Stack
                        spacing={3}
                        direction="row"
                        alignItems="center"
                    >
                        <ConfidenceScoreAI
                            value={confidence}
                        />
                        <AIDialog
                            item={item}
                        />
                    </Stack>
                )
            }
        </Stack>
    );
};

URLChallengeBuildDetails.propTypes = propTypes;

export const Content = connect(mapStateToProps)(URLChallengeBuildDetails);

export const getLabel = ({translate, item}) => {
    const isAIStep = item?.step?.type === challenges.automated.ai.value;

    return (
        <Stack
            spacing={1}
            direction="row"
            alignItems="center"
        >
            <Typography>
                {translate(`${i18nContext}.label`)}
            </Typography>
            {
                isAIStep && (
                    <InfoPopoverAI />
                )
            }
        </Stack>
    );
};

export const getItemId = (props) => {
    return props?.item?.id;
};

export const getFieldDisplayManager = ({item}) => {
    return {
        summary: (
            <Content
                item={item}
            />
        ),
    };
};
