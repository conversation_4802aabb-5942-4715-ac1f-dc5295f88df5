import {useState} from 'react';
import PropTypes from 'prop-types';
import {Alert, Box, Stack} from '@material-ui/core';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../hooks/useLocales';
import {getCDNPath} from '../../../utils/getPath';
import ExternalLink from '../../utils/ExternalLink';
import Drawer from '../../forms/Drawer';
import VideoTeaser from '../../VideoTeaser';

const i18nContext = 'project.wizardSteps.solutionsConfigs.groups.screenVideos';

const propTypes = {
    data: PropTypes.object,
    showTeaser: PropTypes.bool,
    sxConfig: PropTypes.object,
};

const ScreenVideosBuildDetails = ({
    data,
    showTeaser,
    sxConfig,
}) => {
    const video = data?.solutionData?.video;
    const {translate} = useLocales();
    const [expanded, setExpanded] = useState(false);

    if (!video?.success || !isNonEmptyString(video?.data?.destination)) {
        return (
            <Alert severity="warning">
                {translate(`${i18nContext}.captured`, {
                    count: 0,
                })}
            </Alert>
        );
    }

    const fullVideoPath = getCDNPath(video?.data?.destination);
    const content = (
        <Stack
            sx={{
                width: '100%',
                ...sxConfig?.content,
            }}
            alignItems="center"
            spacing={3}
        >
            <video
                width="100%"
                controls
                muted
                autoPlay
                {...sxConfig?.video}
                style={{
                    maxHeight: '100%',
                    ...sxConfig?.video?.style,
                }}
            >
                <source src={fullVideoPath} type="video/webm"/>
            </video>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                }}
            >
                <ExternalLink
                    label={translate(`${i18nContext}.openVideo`)}
                    url={fullVideoPath}
                />
            </Box>
        </Stack>
    );

    if (!showTeaser) {
        return content;
    }

    return (
        <>
            <Stack
                spacing={3}
                sx={{pb: 3}}
                alignItems="center"
            >
                <VideoTeaser
                    previewImage={getCDNPath(data?.requestData?.images?.screenshots?.[0]?.data?.destination)}
                    onPlay={() => setExpanded(true)}
                />
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                    }}
                >
                    <ExternalLink label={translate(`${i18nContext}.openVideo`)} url={fullVideoPath} />
                </Box>
            </Stack>
            {
                expanded && (
                    <Drawer
                        open
                        onClose={() => setExpanded(false)}
                        isFullScreen
                        title={data?.url}>
                        {content}
                    </Drawer>

                )
            }
        </>
    );
};

ScreenVideosBuildDetails.propTypes = propTypes;
ScreenVideosBuildDetails.defaultProps = {
    showTeaser: true,
};

export const Content = ScreenVideosBuildDetails;
