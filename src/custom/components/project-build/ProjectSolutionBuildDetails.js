import {useMemo, useState} from 'react';
import PropTypes from 'prop-types';
import {
    <PERSON><PERSON>,
    Box,
    Breadcrum<PERSON>,
    Button,
    Checkbox,
    FormControlLabel,
    FormGroup,
    Stack,
    Switch,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import {connect} from 'react-redux';
import PubSub from 'pubsub-js';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {getDateString, humanReadableDateFormats} from '@w7-3/webeagle-resources/dist/libs/date';
import useLocales from '../../../hooks/useLocales';
import * as StepTypeSolution from './step-types/StepTypeSolution';
import * as StepTypeAction from './step-types/StepTypeAction';
import * as StepTypeCondition from './step-types/StepTypeCondition';
import * as StepTypeLoop from './step-types/StepTypeLoop';
import * as StepTypeLoopIteration from './step-types/StepTypeLoopIteration';
import * as ScreenVideosBuildDetails from './details/ScreenVideosBuildDetails';
import * as LighthouseBuildDetails from './details/LighthouseBuildDetails';
import * as LinkCheckerBuildDetails from './details/LinkCheckerBuildDetails';
import {getLabel} from '../../utils/projectSolutionStepItemLabel';
import EventsTimeline from '../utils/EventsTimeline';
import {getTimeZone} from '../../utils/timezone';
import {toggleArrayStringItem} from '../../utils/toggle';
import Scrollbar from '../../../components/Scrollbar';
import {useDispatch} from '../../../redux/store';
import {ProjectSolutionRendererData} from './prop-types/StepTypes';
import ProjectStepState from '../forms/ProjectStepState';
import Label from '../../../components/Label';

const renderers = {
    [solutionStepTypes.solution]: StepTypeSolution,
    [solutionStepTypes.action]: StepTypeAction,
    [solutionStepTypes.condition]: StepTypeCondition,
    [solutionStepTypes.loop]: StepTypeLoop,
    [solutionStepTypes.loopIteration]: StepTypeLoopIteration,
};

const propTypes = {
    solution: PropTypes.string,
    data: PropTypes.object,
    buildId: PropTypes.number,
    projectId: PropTypes.string,
    resultIds: PropTypes.arrayOf(PropTypes.string),
    project: PropTypes.object,
};

export const mapStateToProps = ({state}, ownProps) => {
    const {
        projects,
    } = state;

    return {
        project: projects?.[ownProps?.projectId],
    };
};

const WorkflowStep = ({
    context,
    item,
    itemAncestry,
}) => {
    const dispatch = useDispatch();
    const {translate, currentLang} = useLocales();
    const Renderer = renderers[item?.stepType];
    const solutionConfig = context?.project?.configData?.solution?.config;
    const {
        solution,
        buildId,
        projectId,
        resultIds,
    } = context;
    const label = getLabel({
        item: solutionConfig,
        itemResult: item,
        translate,
    });
    const rendererData = {
        buildId,
        dispatch,
        item,
        itemAncestry,
        label,
        openWorkflowItemSubSteps: (subTableProps) => {
            PubSub.publish('SHOW.DIALOG', {
                type: 'custom',
                dialogProps: {
                    isFullScreen: true,
                    title: (
                        <Stack
                            direction="row"
                            flexWrap="wrap"
                            alignItems="center"
                            spacing={1}
                            sx={{
                                pb: 3,
                            }}
                        >
                            <Breadcrumbs>
                                {
                                    [
                                        ...itemAncestry.map((item) => {
                                            if (item?.stepType === solutionStepTypes.root) {
                                                return context?.data?.url;
                                            }

                                            return getLabel({
                                                item: solutionConfig,
                                                itemResult: item,
                                                translate,
                                            });
                                        }),
                                        label,
                                    ].map((item) => (
                                        <Box component="span" sx={{textDecoration: 'underline'}} key={item}>{item}</Box>
                                    ))
                                }
                            </Breadcrumbs>
                        </Stack>
                    ),
                },
                children: (
                    <ProjectSolutionBuildWorkflowTable
                        {...subTableProps}
                        context={context}
                    />
                ),
            });
        },
        projectId,
        resultIds,
        solution,
        translate,
        translateData: {
            count: 1,
        },
    };
    const displayManager = Renderer?.getFieldDisplayManager?.(rendererData);
    const summaryClickHandlerData = Renderer?.getSummaryClickHandlerData?.(rendererData);
    const id = Renderer?.getItemId?.(rendererData);
    const hasSummaryClickHandler = typeof summaryClickHandlerData?.callback === 'function';
    const {infoCodes} = useMemo(() => {
        const list = isNonEmptyArray(item?.infoCodes) ? item?.infoCodes : [];
        if (isNonEmptyArray(item.data?.result)) {
            item.data.result.forEach((result) => {
                if (isNonEmptyArray(result?.infoCodes)) {
                    list.push(...result.infoCodes);
                }
            });
        }

        return {infoCodes: list};
    }, [item?.infoCodes, item.data?.result]);

    return (
        <>
            {
                item?.stepType in renderers && (
                    <TableRow>
                        <TableCell>
                            {id}
                        </TableCell>
                        <TableCell>
                            <ProjectStepState
                                item={item}
                                isStrict={false}
                                showPopover={false}
                            />
                        </TableCell>
                        <TableCell
                            sx={{
                                maxWidth: '100%',
                                wordBreak: 'break-word',
                                wordWrap: 'break-word',
                            }}
                        >
                            {label}
                        </TableCell>
                        <TableCell>
                            {Renderer?.getLabel(rendererData, {count: 1})}
                            <Button
                                color="primary"
                                variant="text"
                                onClick={() => {
                                    PubSub.publish('SHOW.PROJECT-STEP', item?.step);
                                }}
                                sx={{
                                    display: 'block',
                                    my: 1,
                                    ml: 'auto',
                                }}
                            >
                                {translate('details')}
                            </Button>
                        </TableCell>
                        <TableCell>
                            {getDateString(item?.ts, {
                                timeZone: getTimeZone(),
                                template: humanReadableDateFormats.fullWithSeconds,
                                locale: currentLang.value,
                            })}
                        </TableCell>
                        <TableCell
                            sx={{
                                flexGrow: 1,
                            }}
                        >
                            <Stack
                                spacing={2}
                                flexWrap
                            >
                                {
                                    item?.isInactive ? (
                                        <Label
                                            sx={{alignSelf: 'flex-start', p: 2}}
                                        >
                                            {translate('inactive')}
                                        </Label>
                                    ) : displayManager.summary
                                }
                                {
                                    isNonEmptyArray(infoCodes) && (
                                        <Button
                                            size="large"
                                            variant="outlined"
                                            onClick={() => {
                                                PubSub.publish('SHOW.DIALOG', {
                                                    type: 'custom',
                                                    dialogProps: {
                                                        title: (
                                                            <Typography
                                                                component="p"
                                                                variant="body2"
                                                                sx={{
                                                                    wordBreak: 'break-word',
                                                                    wordWrap: 'break-word',
                                                                }}>
                                                                {`${label} - ${translate('project.build.timeline', {count: infoCodes.length})}`}
                                                            </Typography>
                                                        ),
                                                    },
                                                    children: (
                                                        <EventsTimeline
                                                            eventList={infoCodes}
                                                        />
                                                    ),
                                                });
                                            }}
                                            sx={{
                                                ml: 'auto !important',
                                            }}
                                        >
                                            {translate('project.build.timeline', {count: infoCodes.length})}
                                        </Button>
                                    )
                                }
                            </Stack>
                            {
                                hasSummaryClickHandler && (
                                    <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center">
                                        <Button
                                            size="large"
                                            variant="outlined"
                                            sx={{
                                                mt: 5,
                                                textTransform: 'none',
                                            }}
                                            onClick={summaryClickHandlerData?.callback}
                                        >
                                            {summaryClickHandlerData?.label}
                                        </Button>
                                    </Stack>
                                )
                            }
                        </TableCell>
                    </TableRow>
                )
            }
        </>
    );
};

WorkflowStep.propTypes = {
    context: PropTypes.shape(ProjectSolutionRendererData),
    item: PropTypes.object,
    itemAncestry: PropTypes.arrayOf(PropTypes.object),
};

export const WorkflowStepFilter = ({
    solution,
    stepTypeFilter,
    setStepTypeFilter,
    showInactiveItems,
    setShowInactiveItems,
}) => {
    const {translate} = useLocales();
    const isVideoSolution = solution === solutions.screenVideos.key;

    return (
        <Stack
            alignItems="center"
            justifyContent="flex-end"
            spacing={1}
        >
            <FormGroup
                row
                sx={{
                    display: {
                        xs: 'none',
                        md: 'flex',
                    },
                    alignSelf: 'flex-end',
                }}>
                {Object.values(solutionStepTypes).map((stepType) => {
                    const getLabel = renderers[stepType]?.getLabel;

                    if ([
                        solutionStepTypes.root,
                        solutionStepTypes.loopIteration,
                    ].includes(stepType)
                        || typeof getLabel !== 'function'
                        || isVideoSolution && stepType === solutionStepTypes.solution
                    ) {
                        return null;
                    }

                    return (
                        <FormControlLabel
                            key={stepType}
                            control={<Checkbox
                                checked={stepTypeFilter.includes(stepType)}
                                size="large"
                            />}
                            label={getLabel({solution, translate}) || ''}
                            onChange={(_, add) => {
                                setStepTypeFilter(toggleArrayStringItem({
                                    item: stepType,
                                    add,
                                    list: stepTypeFilter,
                                }));
                            }}
                            sx={{
                                '&:last-child': {
                                    mr: 0,
                                },
                            }}
                        />
                    );
                })}
            </FormGroup>
            {
                !isVideoSolution && (
                    <FormGroup
                        row
                        sx={{
                            display: {
                                xs: 'none',
                                md: 'flex',
                            },
                            alignSelf: 'flex-end',
                        }}>
                        <FormControlLabel
                            label={translate('workflowSteps.solutionsOnly', {
                                solution: translate(`project.wizardSteps.solutionsConfigs.groups.${solution}.label`),
                            })}
                            control={
                                <Switch
                                    onChange={(_, isChecked) => {
                                        if (isChecked) {
                                            setStepTypeFilter([solutionStepTypes.solution]);
                                            return;
                                        }

                                        setStepTypeFilter(Object.values(solutionStepTypes));
                                    }}
                                    checked={stepTypeFilter.length === 1 && stepTypeFilter[0] === solutionStepTypes.solution}
                                    size="large"
                                />
                            }
                            sx={{alignSelf: 'flex-end'}}
                        />
                        <FormControlLabel
                            label={translate('workflowSteps.showInactiveItems')}
                            control={
                                <Switch
                                    onChange={(_, isChecked) => {
                                        setShowInactiveItems(isChecked);
                                    }}
                                    checked={showInactiveItems}
                                    size="large"
                                />
                            }
                            sx={{alignSelf: 'flex-end',}}
                        />
                    </FormGroup>
                )
            }
        </Stack>
    );
};

WorkflowStepFilter.propTypes = {
    solution: ProjectSolutionRendererData.solution,
    stepTypeFilter: PropTypes.arrayOf(PropTypes.string),
    setStepTypeFilter: PropTypes.func,
    showInactiveItems: PropTypes.bool,
    setShowInactiveItems: PropTypes.func,
};

const ProjectSolutionBuildWorkflowTable = ({
    context,
    item,
    itemAncestry,
}) => {
    const {translate} = useLocales();
    const [stepTypeFilter, setStepTypeFilter] = useState(Object.values(solutionStepTypes));
    const [showInactiveItems, setShowInactiveItems] = useState(false);

    if (!isNonEmptyArray(item?.data?.stepListResults)) {
        return null;
    }

    const {solution} = context;

    return (
        <>
            {
                item?.stepType !== solutionStepTypes.loop && (
                    <WorkflowStepFilter
                        {...{
                            solution,
                            stepTypeFilter,
                            setStepTypeFilter,
                            showInactiveItems,
                            setShowInactiveItems,
                        }}
                    />
                )
            }
            <Scrollbar>
                <TableContainer sx={{minWidth: 1024}}>
                    <Table
                        stickyHeader
                        sx={{
                            borderCollapse: 'collapse',
                            'tr:nth-child(even)': {
                                backgroundColor: 'background.neutral',
                            },
                            'tr:nth-child(odd)': {
                                backgroundColor: 'background.paper',
                            },
                            td: {
                                borderRadius: 0,
                                border: (theme) => `solid 1px ${theme.palette.divider}`,
                            },
                            'tr:first-child td': {
                                borderTop: 'none',
                            },
                            'tr:last-child td': {
                                borderBottom: 'none',
                            },
                            'td:first-child': {
                                borderLeft: 'none',
                            },
                            'td:last-child': {
                                borderRight: 'none',
                            },
                        }}
                    >
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{width: '5%'}}>
                                    {translate('id')}
                                </TableCell>
                                <TableCell sx={{width: '10%'}}>
                                    {translate('workflowSteps.state.label')}
                                </TableCell>
                                <TableCell sx={{width: '15%'}}>
                                    {translate('itemLabelling.label')}
                                </TableCell>
                                <TableCell sx={{width: '20%'}}>
                                    {translate('workflowSteps.step')}
                                </TableCell>
                                <TableCell sx={{width: '10%'}}>
                                    {translate('timestamp')}
                                </TableCell>
                                <TableCell>
                                    {translate('summary')}
                                </TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {
                                item.data.stepListResults.map((stepItem) => {
                                    if ((!showInactiveItems && stepItem?.isInactive) || !stepTypeFilter.includes(stepItem?.stepType)) {
                                        return null;
                                    }

                                    return (
                                        <WorkflowStep
                                            key={stepItem.id}
                                            context={context}
                                            item={stepItem}
                                            itemAncestry={[
                                                ...itemAncestry,
                                                item,
                                            ]}
                                        />
                                    );
                                })
                            }
                        </TableBody>
                    </Table>
                </TableContainer>
            </Scrollbar>
        </>
    );
};

ProjectSolutionBuildWorkflowTable.propTypes = {
    context: PropTypes.object,
    item: PropTypes.object,
    itemAncestry: PropTypes.arrayOf(PropTypes.object),
};

const ProjectSolutionBuildDetails = ({
    solution,
    data,
    projectId,
    buildId,
    project,
    resultIds,
}) => {
    const {translate} = useLocales();

    if (solutions.linkChecker.appName === solution) {
        return (
            <LinkCheckerBuildDetails.Content
                data={data}
                projectId={projectId}
                buildId={buildId}
            />
        );
    }

    if (solutions.lighthouse.appName === solution) {
        return (
            <LighthouseBuildDetails.Content
                data={data}
                projectId={projectId}
                buildId={buildId}
            />
        );
    }

    if (!isNonEmptyArray(data.solutionData?.data?.stepListResults)) {
        return (
            <Alert severity="warning" sx={{mb: 3}}>
                {translate(`project.wizardSteps.solutionsConfigs.groups.${solution}.captured`, {count: 0})}
            </Alert>
        );
    }

    const context = {
        buildId,
        data,
        project,
        projectId,
        resultIds,
        solution,
    };

    return (
        <Stack spacing={3}>
            {
                solutions.screenVideos.key === solution && (
                    <ScreenVideosBuildDetails.Content
                        data={data}
                        projectId={projectId}
                        buildId={buildId}
                    />
                )
            }
            <ProjectSolutionBuildWorkflowTable
                item={data.solutionData}
                itemAncestry={[]}
                context={context}
            />
        </Stack>
    );
};

ProjectSolutionBuildDetails.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectSolutionBuildDetails);
