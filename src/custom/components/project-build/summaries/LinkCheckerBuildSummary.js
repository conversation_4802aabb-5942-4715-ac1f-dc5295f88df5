import {Grid, Typography} from '@material-ui/core';
import PropTypes from 'prop-types';
import getLinkCheckerSolutionSummaryData
    from '@w7-3/webeagle-resources/dist/libs/project-solution/summaries/getLinkCheckerSolutionSummaryData';
import {isNonEmptyObject} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../hooks/useLocales';
import SolutionDataItem from '../SolutionDataItem';

const propTypes = {
    project: PropTypes.object,
    buildId: PropTypes.number,
};

const LinkCheckerBuildSummary = ({project, buildId}) => {
    const {
        currentLang,
    } = useLocales();
    const summaryData = getLinkCheckerSolutionSummaryData({
        language: currentLang.value,
        projectBuildOverviewData: project?.results?.[buildId]?.data,
        previousProjectBuildOverviewData: project?.results?.[buildId - 1]?.data,
    });

    if (!isNonEmptyObject(summaryData)) {
        return null;
    }

    return (
        <Grid container sx={{mb: 3}}>
            <Grid item xs={12}>
                <Typography
                    variant="subtitle2"
                    sx={{
                        mb: 5,
                        textDecoration: 'underline',
                    }}
                >
                    {summaryData.label}
                </Typography>
            </Grid>
            <Grid item
                  xs={12}
                  sm={4}>
                <SolutionDataItem
                    item={summaryData.items?.total}
                    sx={{
                        alignItems: 'center',
                    }}
                />
            </Grid>
            <Grid
                item
                xs={12}
                sm={4}>
                <SolutionDataItem
                    item={summaryData.items?.success}
                    sx={{
                        alignItems: 'center',
                    }}
                />
            </Grid>
            <Grid
                item
                xs={12}
                sm={4}>
                <SolutionDataItem
                    item={summaryData.items?.failure}
                    sx={{
                        alignItems: 'center',
                    }}
                />
            </Grid>
        </Grid>
    );
};

LinkCheckerBuildSummary.propTypes = propTypes;

export default LinkCheckerBuildSummary;
