import PropTypes from 'prop-types';
import {Grid, Typography} from '@material-ui/core';
import solutions from '@w7-3/webeagle-resources/dist/config/solutions';
import {getSummaryData} from '@w7-3/webeagle-resources/dist/libs/project-solution/summaries';
import useLocales from '../../../../hooks/useLocales';
import SolutionDataItem from '../SolutionDataItem';

const propTypes = {
    project: PropTypes.object,
    buildId: PropTypes.number,
};

const WebTestsBuildSummary = ({project, buildId}) => {
    const {
        currentLang,
    } = useLocales();
    const data = getSummaryData({
        solutionKey: solutions.urlChallenge.appName,
        language: currentLang.value,
        timeZone: global.webAutomate.timeZone,
        projectBuildOverviewData: project?.results?.[buildId]?.data,
        previousProjectBuildOverviewData: project?.results?.[buildId - 1]?.data,
    });

    return (
        <Grid container rowSpacing={3}>
            <Grid item xs={12} sx={{mb: -3}}>
                <Typography
                    variant="subtitle2"
                    sx={{
                        mb: 5,
                        textDecoration: 'underline',
                    }}
                >
                    {data.label}
                </Typography>
            </Grid>
            {
                [
                    data.items?.total,
                    data.items?.success,
                    data.items?.failure,
                    data.additionalItems?.participants,
                    data.additionalItems?.passedChallenges,
                    data.additionalItems?.failedOrVoidChallenges,
                ].map((item, index) => {
                    return (
                        <Grid
                            item
                            xs={12}
                            sm={4}
                            key={index}
                        >
                            <SolutionDataItem
                                item={item}
                                sx={{
                                    alignItems: 'center',
                                    mb: 3,
                                }}
                            />
                        </Grid>
                    );
                })
            }
        </Grid>
    );
};

WebTestsBuildSummary.propTypes = propTypes;

export default WebTestsBuildSummary;
