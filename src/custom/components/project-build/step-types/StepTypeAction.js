import PropTypes from 'prop-types';
import {Box, Stack, Typography} from '@material-ui/core';
import {Icon} from '@iconify/react';
import {useTheme} from '@material-ui/core/styles';
import path from 'ramda/src/path';
import useLocales from '../../../../hooks/useLocales';
import Label from '../../../../components/Label';

export const Flags = {
    executed: {
        label: 'executed',
        color: 'success.main',
        icon: 'eva:checkmark-outline',
    },
    failed: {
        label: 'failed',
        color: 'error.main',
        icon: 'eva:alert-triangle-outline',
    },
    voided: {
        label: 'voided',
        color: 'text.secondary',
        icon: 'eva:minus-outline',
    },
    aborted: {
        label: 'aborted',
        color: 'warning.main',
        icon: 'eva:close-outline',
    },
};

export const getItemId = (props) => {
    return props?.item?.id;
};

export const getItemSummaryFlag = (item) => {
    const {isAborted, isVoided, data} = item;

    if (isAborted) {
        return Flags.aborted;
    }

    if (isVoided) {
        return Flags.voided;
    }

    if (!data?.success) {
        return Flags.failed;
    }

    return Flags.executed;
};

export const Content = ({item}) => {
    const {
        label,
        color,
        icon,
    } = getItemSummaryFlag(item);
    const {translate} = useLocales();
    const theme = useTheme();

    return (
        <Stack spacing={3}>
            <Label
                sx={{alignSelf: 'flex-start', p: 3}}
            >
                <Box
                    sx={{mr: 1}}
                    component={Icon}
                    icon={icon}
                    width={40}
                    height={40}
                    color={path(color.split('.'), theme.palette)}
                />
                {translate(`actions.labels.${label}.description`)}
            </Label>
        </Stack>
    );
};

Content.propTypes = {
    item: PropTypes.object,
};

export const getLabel = ({translate, translateData}) => {
    return (
        <Stack
            spacing={1}
            direction="row"
            alignItems="center"
        >
            <Typography>
                {translate('actions.label', translateData)}
            </Typography>
        </Stack>
    );
};

export const getFieldDisplayManager = ({
    item,
}) => {
    return {
        summary: (
            <Content
                item={item}
            />
        ),
    };
};


