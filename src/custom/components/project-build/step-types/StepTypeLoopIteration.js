import PropTypes from 'prop-types';
import {Box} from '@material-ui/core';
import {Icon} from '@iconify/react';
import path from 'ramda/src/path';
import {useTheme} from '@material-ui/core/styles';
import {isPositiveInt} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../../hooks/useLocales';
import Label from '../../../../components/Label';

export const Flags = {
    completed: {
        label: 'success',
        color: 'success.main',
        icon: 'eva:checkmark-outline',
    },
    broken: {
        label: 'failed',
        color: 'warning.main',
        icon: 'eva:alert-triangle-outline',
    },
    failed: {
        label: 'failed',
        color: 'error.main',
        icon: 'eva:alert-triangle-outline',
    },
    voided: {
        label: 'voided',
        color: 'text.secondary',
        icon: 'eva:minus-outline',
    },
    aborted: {
        label: 'aborted',
        color: 'warning.main',
        icon: 'eva:close-outline',
    },
};

export const getItemId = () => {
    return '-';
};

export const getItemSummaryFlag = (item) => {
    const {isAborted, isVoided, data} = item;

    if (isAborted) {
        return Flags.aborted;
    }

    if (isVoided) {
        return Flags.voided;
    }

    if (!data?.success) {
        return Flags.failed;
    }

    return Flags.completed;
};

const propTypes = {
    item: PropTypes.object,
};

export const Content = ({item}) => {
    const {
        icon,
        label,
        color,
    } = getItemSummaryFlag(item);
    const {translate} = useLocales();
    const theme = useTheme();

    console.log({item});

    return (
        <Label
            sx={{alignSelf: 'flex-start', p: 2}}
        >
            <Box
                sx={{mr: 1}}
                component={Icon}
                icon={icon}
                width={40}
                height={40}
                color={path(color.split('.'), theme.palette)}
            />
            {translate(`loops.labels.${label}.description`)}
        </Label>
    );
};

Content.propTypes = propTypes;

export const getLabel = ({translate, translateData}) => {
    return translate('loops.repetitions.label', translateData);
};

export const getSummaryClickHandlerData = (props) => {
    const {
        item,
        openWorkflowItemSubSteps,
        translate,
    } = props;

    const count = item?.data?.stepListResults?.length;
    if (!item?.data?.success || !isPositiveInt(count)) {
        return {};
    }

    return {
        label: `${count} ${translate('workflowSteps.subItems.view', {count})}`,
        callback: () => {
            openWorkflowItemSubSteps(props);
        },
    };
};

export const getFieldDisplayManager = ({
    item,
}) => {
    return {
        summary: (
            <Content
                item={item}
            />
        ),
    };
};
