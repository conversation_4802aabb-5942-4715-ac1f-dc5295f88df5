import PropTypes from 'prop-types';

export const ProjectSolutionRendererData = {
    buildId: PropTypes.number,
    item: PropTypes.object,
    itemAncestry: PropTypes.arrayOf(PropTypes.object),
    label: PropTypes.string,
    projectId: PropTypes.string,
    resultIds: PropTypes.arrayOf(PropTypes.string),
    solution: PropTypes.string,
    translate: PropTypes.func,
    translateData: PropTypes.shape({
        count: PropTypes.number,
    }),
};
