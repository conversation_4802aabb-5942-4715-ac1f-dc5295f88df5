import PropTypes from 'prop-types';
import {Stack, Typography} from '@material-ui/core';

const deltaShape = PropTypes.shape({
    label: PropTypes.string,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    isInvalid: PropTypes.bool,
    color: PropTypes.string,
    link: PropTypes.string,
});

const propTypes = {
    item: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        isInvalid: PropTypes.bool,
        color: PropTypes.string,
        link: PropTypes.string,
        delta: deltaShape,
        deltaShare: deltaShape,
    }),
    sx: PropTypes.object,
};

const SolutionDataItem = ({item, sx}) => {
    if (!item?.label) {
        return null;
    }

    return (
        <Stack sx={{...sx}}>
            <Typography
                variant="heading"
                align="center"
                style={{color: item.color}}>
                {item.label}
            </Typography>
            {
                item.value && (
                    <Typography
                        variant="heading"
                        style={{color: item.color}}>
                        {item.value}
                    </Typography>
                )
            }
            {
                item.delta && (
                    <Typography
                        variant="subtitle2"
                        style={{color: item.delta.color}}>
                        {item.delta.label}: {item.delta.value}
                    </Typography>
                )
            }
            {
                item.deltaShare && (
                    <Typography
                        style={{color: item.deltaShare.color}}>
                        {item.deltaShare.label}: {item.deltaShare.value}
                    </Typography>
                )
            }
        </Stack>
    );
};

SolutionDataItem.propTypes = propTypes;

export default SolutionDataItem;

