import {useState} from 'react';
import {Icon} from '@iconify/react';
import {LoadingButton} from '@material-ui/lab';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {isNonEmptyString} from '@w7-3/webeagle-resources/dist/libs/validators';
import roundArrowRightAlt from '@iconify/icons-ic/round-arrow-right-alt';
import PubSub from 'pubsub-js';
import {Link, Stack, Typography} from '@material-ui/core';
import {getCheckoutApiPath} from '../utils/getPath';
import useApiCaller from '../hooks/useApiCaller';
import PrivilegedContent from '../../guards/PrivilegedContent';
import useLocales from '../../hooks/useLocales';

const CustomerPortalButton = () => {
    const {translate} = useLocales();
    const apiCaller = useApiCaller();
    const [isLoading, setIsLoading] = useState(false);
    const openCustomerPortal = () => {
        setIsLoading(true);
        apiCaller({
            uri: getCheckoutApiPath(api.customerPortal),
            data: {
                url: window.location.href,
            },
            successCallback: (result) => {
                const customerGateway = result.data?.url;
                if (!isNonEmptyString(customerGateway)) {
                    return;
                }

                PubSub.publish('SHOW.MODAL', {
                    type: 'custom',
                    isBlocking: true,
                    children: (
                        <Stack spacing={3} sx={{p: 3}}>
                            <Typography>
                                {translate('account.customerPortal.ready')}
                            </Typography>
                            <Link href={customerGateway} target="_blank">
                                {translate('account.customerPortal.view')}
                            </Link>
                        </Stack>
                    ),
                });
            },
            alwaysCallback: () => {
                setIsLoading(false);
            },
        });
    };

    return (
        <PrivilegedContent
            isCovert>
            <LoadingButton
                loading={isLoading}
                color="inherit"
                variant="outlined"
                endIcon={<Icon icon={roundArrowRightAlt} width={40} height={40}/>}
                onClick={openCustomerPortal}>
                {translate('account.customerPortal.label')}
            </LoadingButton>
        </PrivilegedContent>
    );
};

export default CustomerPortalButton;
