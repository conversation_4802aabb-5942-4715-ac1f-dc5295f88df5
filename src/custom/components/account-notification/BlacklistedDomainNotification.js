import {But<PERSON>, <PERSON>ack} from '@material-ui/core';
import {Link as RouterLink} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import InfoCode from '../utils/InfoCode';
import ExternalLink from '../utils/ExternalLink';
import {domainsTabs} from '../../pages/DomainsPage';
import {NotificationTypes} from '../../../config/prop-types/NotificationTypes';
import useLocales from '../../../hooks/useLocales';
import {globalConfig} from '../../../config/setup';

const propTypes = {
    item: NotificationTypes,
};

const BlacklistedDomainNotification = ({item}) => {
    const {translate} = useLocales();
    return (
        <>
            {
                item.infoCodes?.map((infoCode) => (
                    <InfoCode
                        key={infoCode?.code}
                        infoCode={infoCode}
                        title={translate(`accountNotifications.types.${item.type}`, {
                            app: globalConfig.domain,
                        })}
                        isDismissible={false}
                    >
                        <Stack spacing={3}>
                            {
                                infoCode?.data?.blacklistedDomainListInTarget?.map((domain) => (
                                    <ExternalLink
                                        key={domain}
                                        label={domain}
                                        url={domain}
                                    />
                                ))
                            }
                            <Stack
                                direction="row"
                                justifyContent="flex-end"
                                alignItems="center"
                                spacing={3}
                                sx={{mt: 3, width: '100%'}}>
                                <Button
                                    color="inherit"
                                    variant="contained"
                                    sx={{textTransform: 'none'}}
                                    component={RouterLink}
                                    to={`${PATH_DASHBOARD.general.projects.root}/${infoCode.data?.projectId}`}
                                >
                                    {translate('account.domains.projectBuild.gotoProject')}
                                </Button>
                                <Button
                                    color="inherit"
                                    variant="contained"
                                    sx={{textTransform: 'none'}}
                                    component={RouterLink}
                                    to={`${PATH_DASHBOARD.management.domains}?${urlParameters.miscellaneous.tab}=${domainsTabs.blacklist}`}>
                                    {translate('account.domains.goto')}
                                </Button>
                            </Stack>
                        </Stack>
                    </InfoCode>
                ))
            }
        </>
    );
};

BlacklistedDomainNotification.propTypes = propTypes;

export default BlacklistedDomainNotification;
