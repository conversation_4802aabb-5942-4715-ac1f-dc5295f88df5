import {Alert, AlertTitle} from '@material-ui/core';
import InfoCode from '../utils/InfoCode';
import useLocales from '../../../hooks/useLocales';
import {NotificationTypes} from '../../../config/prop-types/NotificationTypes';
import {globalConfig} from '../../../config/setup';

const propTypes = {
    item: NotificationTypes,
};

const CustomNotification = ({item}) => {
    const {translate, currentLang} = useLocales();

    return (
        <>
            {item?.i18nInfos?.map((infoCode) => {
                return (
                    <Alert
                        key={infoCode.ts}
                    >
                        <AlertTitle>{infoCode.titles?.[currentLang?.value]}</AlertTitle>
                        {infoCode.messages?.[currentLang?.value]}
                    </Alert>
                );
            })}
            {item?.infoCodes?.map((infoCode) => {
                return (
                    <InfoCode
                        key={infoCode.ts}
                        infoCode={infoCode}
                        title={translate(`accountNotifications.types.${item.type}`, {
                            app: globalConfig.domain,
                        })}
                        isDismissible={false}
                    />
                );
            })}
        </>
    );
};

CustomNotification.propTypes = propTypes;

export default CustomNotification;
