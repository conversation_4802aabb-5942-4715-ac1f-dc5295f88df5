import {useLocation, useParams, useSearchParams} from 'react-router-dom';
import {<PERSON>ert, AlertTitle, Box, Button, Grid, IconButton, Stack, Typography} from '@material-ui/core';
import {connect} from 'react-redux';
import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {useEffect, useRef, useState} from 'react';
import {Icon} from '@iconify/react';
import closeOutline from '@iconify/icons-eva/close-outline';
import {projectStates,ACTIONS} from '@w7-3/webeagle-resources/dist/config/project';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import {accountNotificationTypes} from '@w7-3/webeagle-resources/dist/config/account';
import PageWrapper from '../components/PageWrapper';
import useLocales from '../../hooks/useLocales';
import HeaderBreadcrumbs from '../../components/HeaderBreadcrumbs';
import ProjectOverview from '../components/project/ProjectOverview';
import ProjectSettings from '../components/project/ProjectSettings';
import ProjectHistory from '../components/project/ProjectHistory';
import ProjectBuildState from '../components/project/ProjectBuildState';
import useAuth from '../../hooks/useAuth';
import DefinitionList from '../components/utils/DefinitionList';
import {fDateTimeHumanReadable} from '../../utils/formatTime';
import ExternalLink from '../components/utils/ExternalLink';
import {NotificationTypes} from '../../config/prop-types/NotificationTypes';
import BlacklistedDomainNotification from '../components/account-notification/BlacklistedDomainNotification';
import ProjectRunTeaser from './project/ProjectRunTeaser';
import ProjectHeaderBreadcrumbActions from './project/ProjectHeaderBreadcrumbActions';
import useProjectLoader from '../hooks/useProjectLoader';
import {useDispatch} from '../../redux/store';
import {updateProjectAssistant} from '../../redux/projectAssistant/store';
import getProjectFieldDisplayManager from '../utils/getProjectFieldDisplayManager';

const mapStateToProps = ({state}) => {
    const {
        projects,
        accountNotificationList,
        timeZone,
    } = state;

    return {
        projects,
        accountNotificationList,
        timeZone,
    };
};

const propTypes = {
    projects: PropTypes.object,
    timeZone: PropTypes.string,
    accountNotificationList: PropTypes.arrayOf(NotificationTypes),
};

const tabs = {
    overview: 'overview',
    settings: 'settings',
    history: 'history',
};

const TABS = [
    {
        value: tabs.overview,
        Component: ProjectOverview,
    },
    {
        value: tabs.settings,
        Component: ProjectSettings,
        blackListedUserRoles: [userRoles.VIEWER]
    },
    {
        value: tabs.history,
        Component: ProjectHistory,
        blackListedUserRoles: [userRoles.VIEWER]
    },
];

const ProjectPage = ({projects, accountNotificationList, timeZone}) => {
    const [showSchedulerNotification, setShowSchedulerNotification] = useState(true);
    const {user} = useAuth();
    const {translate} = useLocales();
    const [searchParams, updateSearchParams] = useSearchParams();
    const {projectId} = useParams();
    const location = useLocation();
    const {project} = useProjectLoader({projects, projectId});
    const editSessionKey = useRef(null);
    const dispatch = useDispatch();
    useEffect(() => {
        const action = searchParams.get('action');
        const step = searchParams.get('step');
        if (action !== ACTIONS.edit || !project?.configData || editSessionKey.current === location.key) {
            return;
        }

        editSessionKey.current = location.key;
        dispatch(updateProjectAssistant({
            configData: project.configData,
            action: ACTIONS.edit,
            step,
        }));
        PubSub.publish('SHOW.PROJECT-ASSISTANT', {
            project,
        });
        // eslint-disable-next-line
    }, [location.key, project?.configData]);

    if (!project?.configData) {
        return null;
    }

    const filteredTabs = TABS.filter(({blackListedUserRoles}) => {
        return !blackListedUserRoles || !blackListedUserRoles?.includes(user.role);
    });
    const projectNotifications = [];
    const blacklistedDomainNotification = [];

    accountNotificationList?.forEach?.((item) => {
        if (item?.data?.projectId !== projectId || !item.isNew) {
            return;
        }

        if (item.type === accountNotificationTypes.blacklistedDomainsInProject) {
            blacklistedDomainNotification.push(item);
            return;
        }

        projectNotifications.push(item);
    });
    const name = project.configData?.projectName;
    const accessManager = getProjectFieldDisplayManager({translate});

    return (
        <PageWrapper
            title={name}
            maxWidth="xl"
        >
            <HeaderBreadcrumbs
                heading={name}
                links={[
                    {
                        name: translate('dashboard.label'),
                        href: PATH_DASHBOARD.root,
                    },
                    {
                        name: translate('project.list.label'),
                        href: PATH_DASHBOARD.general.projects.root,
                    },
                    {
                        name: (
                            <Box
                                component="span"
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                }}>
                                {name}
                                <ProjectBuildState
                                    state={project?.latestBuild?.state}
                                    sx={{ml: 1}}
                                />
                                {
                                    project.state !== projectStates?.active &&
                                    <Box
                                        component="span"
                                        sx={{
                                            mx: 1,
                                        }}>{accessManager.state.q(project)}</Box>
                                }
                            </Box>
                        )
                    },
                ]}
                sx={{
                    alignItems: 'baseline',
                }}
            />
            <ProjectHeaderBreadcrumbActions
                project={project}
            />
            {
                project.state !== projectStates.archive ? (
                    <Grid container spacing={3}>
                        {
                            blacklistedDomainNotification?.map?.((item, index) => (
                                <Grid key={index} item xs={12}>
                                    <BlacklistedDomainNotification
                                        item={item}
                                    />
                                </Grid>
                            ))
                        }
                        {
                            projectNotifications?.map?.((item) => {
                                return (
                                    <Grid key={item.tz} item xs={12}>
                                        <Alert
                                            severity="warning"
                                            variant="outlined"
                                            sx={{width: '100%'}}
                                        >
                                            <AlertTitle>{translate('project.build.domainApprovalError.label', {
                                                count: item.blacklistedDomainListInTarget?.length,
                                            })}</AlertTitle>
                                            {translate(`events.${item.type}`, {
                                                dateTime: fDateTimeHumanReadable(item.ts),
                                                tz: timeZone,
                                            })}
                                            <Stack spacing={3}>
                                                {
                                                    item.blacklistedDomainListInTarget?.map((domain) => (
                                                        <ExternalLink
                                                            key={domain}
                                                            label={domain}
                                                            url={domain}
                                                        />
                                                    ))
                                                }
                                            </Stack>
                                        </Alert>
                                    </Grid>
                                );
                            })
                        }
                        <Grid item xs={12}>
                            <ProjectRunTeaser
                                projectId={projectId}
                            />
                        </Grid>
                        {
                            showSchedulerNotification && project.state !== projectStates?.active &&
                            <Grid item xs={12}>
                                <Alert
                                    severity="info"
                                    action={
                                        <IconButton
                                            onClick={() => {
                                                setShowSchedulerNotification(false);
                                            }}
                                            color="primary"

                                        >
                                            <Icon icon={closeOutline} width={40} height={40} />
                                        </IconButton>
                                    }
                                    sx={{
                                        '.MuiAlert-message': {
                                            flexGrow: 1,
                                        },
                                    }}
                                >
                                    <Typography>
                                        {translate('project.stateNotification')}
                                    </Typography>
                                    <Stack
                                        direction="row"
                                        justifyContent="flex-end"
                                        alignItems="center"
                                        sx={{mt: 3}}>
                                        <Button
                                            size="large"
                                            variant="contained"
                                            onClick={() => {
                                                PubSub.publish('PROJECT_CHANGE_STATE', {
                                                    project,
                                                    state: projectStates?.active,
                                                });
                                            }}
                                        >
                                            {translate('project.stateTypes.active.cta')}
                                        </Button>
                                    </Stack>
                                </Alert>
                            </Grid>
                        }
                        <Grid item xs={12}>
                            <DefinitionList
                                dataList={filteredTabs.map(({value, Component}) => (
                                    {
                                        key: value,
                                        label: translate(`project.result.${value}.label`),
                                        node: <Component projectId={project.id} />
                                    }
                                ))}
                                variant={DefinitionList.VARIANTS.tabs}
                                tabConfig={{
                                    onChange: (item) => {
                                        searchParams.set(urlParameters.project.tab, item?.tab);
                                        updateSearchParams(searchParams);
                                    },
                                    activeTabKey: searchParams.get(urlParameters.project.tab),
                                }}
                            />
                        </Grid>
                    </Grid>
                ) : (
                    <Alert
                        severity="warning"
                        sx={{
                            my: 3,
                            '.MuiAlert-message': {
                                flexGrow: 1,
                            },
                        }}>
                        {translate('project.stateTypes.archive.info')}
                        <Box sx={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center'}}>
                            <Button
                                color="inherit"
                                variant="contained"
                                onClick={() => {
                                    PubSub.publish('PROJECT_CHANGE_STATE', {
                                        project,
                                        state: projectStates.pause,
                                    });
                                }}
                            >
                                {translate('project.stateTypes.archive.unarchive')}
                            </Button>
                        </Box>
                    </Alert>
                )
            }
        </PageWrapper>
    );
};

ProjectPage.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectPage);
