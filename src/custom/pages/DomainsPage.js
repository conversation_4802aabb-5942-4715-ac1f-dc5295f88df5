import {useState} from 'react';
import {Box, Tab, Tabs} from '@material-ui/core';
import {useSearchParams} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import PageWrapper from '../components/PageWrapper';
import useLocales from '../../hooks/useLocales';
import HeaderBreadcrumbs from '../../components/HeaderBreadcrumbs';
import BlacklistedDomains from '../components/domains/BlacklistedDomains';
import DomainVerificationList from '../components/domains/DomainVerificationList';
import ProjectBuildDomainList from '../components/domains/ProjectBuildDomainList';

export const domainsTabs = {
    build: 'build',
    verification: 'verification',
    blacklist: 'blacklist',
};

const ACCOUNT_TABS = [
    {
        value: domainsTabs.build,
        label: 'account.domains.projectBuild.label',
        Component: ProjectBuildDomainList,
    },
    {
        value: domainsTabs.verification,
        label: 'account.domains.verification.label',
        Component: DomainVerificationList,
    },
    {
        value: domainsTabs.blacklist,
        label: 'account.domains.blacklisted.label',
        Component: BlacklistedDomains,
    },
];

const AccountPage = () => {
    const {translate} = useLocales();
    const [searchParams, updateSearchParams] = useSearchParams();
    const [currentTab, setCurrentTab] = useState(searchParams.get(urlParameters.miscellaneous.tab) || domainsTabs.build);

    const handleChangeTab = (event, tab) => {
        setCurrentTab(tab);
        searchParams.set(urlParameters.miscellaneous.tab, tab);
        updateSearchParams(searchParams);
    };

    return (
        <>
            <PageWrapper
                title={translate('account.domains.label')}
                maxWidth="xl"
            >
                <HeaderBreadcrumbs
                    heading={translate('account.domains.label')}
                    links={[
                        {
                            name: translate('dashboard.label'),
                            href: PATH_DASHBOARD.root,
                        },
                        {name: translate('account.domains.label')},
                    ]}
                />
                <Tabs
                    value={currentTab}
                    scrollButtons="auto"
                    variant="scrollable"
                    allowScrollButtonsMobile
                    onChange={handleChangeTab}
                >
                    {ACCOUNT_TABS.map((tab) => {
                        return (
                            <Tab disableRipple
                                 key={tab.value}
                                 label={translate(tab.label)}
                                 value={tab.value} />
                        );
                    })}
                </Tabs>

                {ACCOUNT_TABS.map((tab) => {
                    const isMatched = tab.value === currentTab;
                    const {Component} = tab;

                    return isMatched && <Box key={tab.value} sx={{
                        mt: 3,
                        height: 'auto',
                        width: '100%',
                    }}>
                        <Component />
                    </Box>;
                })}
            </PageWrapper>
        </>
    );
};

export default AccountPage;
