import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {Link as RouterLink} from 'react-router-dom';
import {But<PERSON>, Divider, Stack} from '@material-ui/core';
import api from '@w7-3/webeagle-resources/dist/config/api';
import {PATH_AUTH} from '@w7-3/webeagle-resources/dist/config/paths';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {useEffect} from 'react';
import useAuth from '../../hooks/useAuth';
import {useDispatch} from '../../redux/store';
import useLocales from '../../hooks/useLocales';
import PageWrapper from '../components/PageWrapper';
import PrivilegedContent from '../../guards/PrivilegedContent';
import ProjectWizardHero from '../components/project-wizard/ProjectWizardHero';
import ProjectWizardForm from '../components/project-wizard/ProjectWizardForm';
import {getAPIPath} from '../utils/getPath';
import useApiCaller from '../hooks/useApiCaller';
import {useProject} from '../hooks/useProject';
import {updateProjects} from '../../redux/slices/state';
import {useProjectWizardFormState} from '../components/project-wizard/ProjectWizardForm/ProjectWizardForm.hooks';
import getProjectConfig from '../utils/getProjectConfig';
import {resetProjectWizard} from '../../redux/projectAssistant/store';

const ProjectWizardPage = () => {
    const {isAuthenticated} = useAuth();
    const {translate} = useLocales();
    const dispatch = useDispatch();
    const apiCaller = useApiCaller();
    const {goToProjectEdit} = useProject();
    const {
        step,
        configData,
        validation,
    } = useProjectWizardFormState({});

    const handleCreateProject = () => {
        apiCaller({
            isAnimated: true,
            uri: getAPIPath(api.projectCreate),
            data: {
                configData: getProjectConfig(configData),
            },
            successCallback: ({data}) => {
                dispatch(updateProjects({
                    [data.projectData.id]: data.projectData,
                }));
                goToProjectEdit({
                    projectId: data.projectData.id,
                });
            },
        });
    };

    useEffect(() => {
        return () => {
            dispatch(resetProjectWizard());
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <PageWrapper
            title={translate('project.pageTitle')}
            hero={<ProjectWizardHero/>}
            sx={{my: 10, mx: 0, p: 0, minWidth: 360}}
            maxWidth="xl">
            <Stack spacing={3} direction="row" justifyContent="flex-end" alignItems="center" sx={{my: 3}}>
                <Button
                    variant="outlined"
                    startIcon={<Icon icon="eva:message-square-outline" width={40} height={40}/>}
                    onClick={() => {
                        PubSub.publish('SHOW.DIALOG', {
                            type: 'aiAssistant',
                        });
                    }}
                    sx={{textTransform: 'none'}}
                >
                    {translate('assistant.prompts.exploreFeatures.label')}
                </Button>
            </Stack>
            <Divider sx={{my: 5}} />
            {
                isAuthenticated ? (
                    <PrivilegedContent
                        accessibleRoles={[
                            userRoles.EDITOR.key,
                        ]}>
                        <ProjectWizardForm
                            step={step}
                        />
                        <Stack
                            direction="row"
                            justifyContent="flex-end"
                            spacing={3}
                            sx={{width: '100%', pt: 3}}
                        >
                            <Button
                                size="large"
                                variant="outlined"
                                onClick={handleCreateProject}
                                sx={{
                                    textTransform: 'none',
                                    justifyContent: 'flex-start',
                                    maxWidth: 'fit-content',
                                }}
                                disabled={!validation.success}
                            >
                                {translate('project.create')}
                            </Button>
                        </Stack>
                    </PrivilegedContent>
                ) : (
                    <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{my: 5}}>
                        <Button
                            size="large"
                            variant="contained"
                            component={RouterLink}
                            to={PATH_AUTH.login}
                            sx={{
                                m: 'auto',
                                px: 3,
                                py: 4,
                                textTransform: 'none',
                            }}>
                            {translate('project.wizardLoginRequired')}
                        </Button>
                    </Stack>
                )
            }
        </PageWrapper>
    );
};

export default ProjectWizardPage;
