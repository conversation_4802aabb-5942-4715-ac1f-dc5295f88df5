import {Link as RouterLink, useSearchParams} from 'react-router-dom';
import {<PERSON><PERSON>, Card, Stack} from '@material-ui/core';
import {useState} from 'react';
import {PATH_DASHBOARD, PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import PageWrapper from '../components/PageWrapper';
import useLocales from '../../hooks/useLocales';
import HeaderBreadcrumbs from '../../components/HeaderBreadcrumbs';
import OrderHistory from './transaction-list/OrderHistory';
import DefinitionList from '../components/utils/DefinitionList';
import useOpenPaymentItemDetails from './transaction-list/hooks/useOpenPaymentItemDetails';
import useOpenPaymentDetails from './transaction-list/hooks/useOpenPaymentDetails';
import SubscriptionHistory from './transaction-list/SubscriptionHistory';
import {transactionPageTabs} from '../../config/account';
import BillingCycle from '../components/account/BillingCycle';
import AutomationCreditsTransaction from './transaction-list/AutomationCreditsTransaction';
import CustomerPortalButton from '../components/CustomerPortalButton';

const TABS = [
    {
        value: transactionPageTabs.orderHistory,
        Component: OrderHistory,
    },
    {
        value: transactionPageTabs.subscriptionHistory,
        Component: SubscriptionHistory,
    },
    {
        value: transactionPageTabs.automationCredits,
        Component: AutomationCreditsTransaction,
    },
];

const TransactionListPage = () => {
    const {translate} = useLocales();
    const [searchParams, updateSearchParams] = useSearchParams();
    const [currentTab, setCurrentTab] = useState(searchParams.get('tab') || transactionPageTabs.orderHistory);
    useOpenPaymentItemDetails();
    useOpenPaymentDetails();

    const handleChangeTab = (selection) => {
        setCurrentTab(selection.tab);
        searchParams.set('tab', selection.tab);
        updateSearchParams(searchParams);
    };

    return (
        <PageWrapper
            title={translate('transactions.pageTitle')}>
            <HeaderBreadcrumbs
                heading={translate('transactions.label')}
                links={[
                    {
                        name: translate('dashboard.label'),
                        href: PATH_DASHBOARD.root,
                    },
                    {name: translate('transactions.label')},
                ]}
            />
            <Stack
                direction={{
                    xs: 'column',
                    md: 'row',
                }}
                alignItems="center"
                justifyContent="flex-end"
                sx={{my: 3}}
            >
                <Button
                    variant="contained"
                    component={RouterLink}
                    to={PATH_PAGE.pricing}
                    sx={{textTransform: 'none'}}>
                    {translate('pricing.cta')}
                </Button>
            </Stack>
            <Stack
                direction={{
                    xs: 'column',
                    md: 'row',
                }}
                alignItems="center"
                justifyContent="space-between"
                spacing={3}
                sx={{mb: 3}}
            >
                <BillingCycle />
                <CustomerPortalButton/>
            </Stack>
            <DefinitionList
                dataList={TABS.map((tab) => {
                    const {Component, value} = tab;

                    return {
                        key: value,
                        label: translate(`transactions.${value}.label`),
                        node: (
                            <Card sx={{p: 3}}>
                                <Component value={value} />
                            </Card>
                        ),
                    };
                })}
                variant={DefinitionList.VARIANTS.tabs}
                tabConfig={{
                    activeTabKey: currentTab,
                    onChange: handleChangeTab,
                }}
            />
        </PageWrapper>
    );
};

export default TransactionListPage;
