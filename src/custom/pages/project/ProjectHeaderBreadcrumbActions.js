import {I<PERSON><PERSON><PERSON>on, Stack, Tooltip} from '@material-ui/core';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {Link as RouterLink} from 'react-router-dom';
import PropTypes from 'prop-types';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import ProjectBuildActions from '../../components/project/ProjectBuildActions';
import PrivilegedContent from '../../../guards/PrivilegedContent';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    project: PropTypes.object,
};

const ProjectHeaderBreadcrumbActions = ({project}) => {
    const {translate} = useLocales();
    const projectId = project.id;

    return (
        <Stack
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
            spacing={1}>
            {
                project.state !== projectStates.archive && (
                    <>
                        <PrivilegedContent
                            accessibleRoles={[
                                userRoles.EDITOR.key,
                            ]}
                            isCovert>
                            <Tooltip title={translate('project.run')}>
                                <IconButton
                                    color="primary"
                                    size="large"
                                    onClick={() => {
                                        PubSub.publish('PROJECT.BUILD', {
                                            projectId,
                                        });
                                    }}
                                >
                                    <Icon icon="eva:play-circle-outline" width={40} height={40} />
                                </IconButton>
                            </Tooltip>
                        </PrivilegedContent>
                        <ProjectBuildActions
                            projectId={projectId}
                        />
                    </>
                )
            }
            <PrivilegedContent
                accessibleRoles={[
                    userRoles.EDITOR.key,
                ]}
                isCovert>
                <Tooltip title={translate('project.new.label')}>
                    <IconButton
                        color="primary"
                        size="large"
                        component={RouterLink}
                        to={PATH_PAGE.projectWizard}
                    >
                        <Icon icon="eva:plus-fill" width={40} height={40} />
                    </IconButton>
                </Tooltip>
            </PrivilegedContent>
        </Stack>
    );
};

ProjectHeaderBreadcrumbActions.propTypes = propTypes;

export default ProjectHeaderBreadcrumbActions;
