import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {useParams, useSearchParams} from 'react-router-dom';
import {Box, Stack} from '@material-ui/core';
import {BUILD_STATES} from '@w7-3/webeagle-resources/dist/config/scrapper';
import {ROLES as userRoles} from '@w7-3/webeagle-resources/dist/config/users';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {projectStates} from '@w7-3/webeagle-resources/dist/config/project';
import {browserApiKeys} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import PageWrapper from '../components/PageWrapper';
import useLocales from '../../hooks/useLocales';
import HeaderBreadcrumbs from '../../components/HeaderBreadcrumbs';
import LoadingScreen from '../../components/LoadingScreen';
import ProjectBuildTeaser from '../components/project/ProjectBuildTeaser';
import {fDateTimeHumanReadable} from '../../utils/formatTime';
import ProjectBuildDelete from '../components/project/ProjectBuildDelete';
import PrivilegedContent from '../../guards/PrivilegedContent';
import ProjectBuildState from '../components/project/ProjectBuildState';
import {useDispatch} from '../../redux/store';
import {showSolutionReviewer} from '../../redux/slices/state';
import {getGlobalValue, setGlobalValue} from '../utils/globals';
import ProjectRunTeaser from './project/ProjectRunTeaser';
import ProjectHeaderBreadcrumbActions from './project/ProjectHeaderBreadcrumbActions';
import useProjectBuildLoader from '../hooks/useProjectBuildLoader';
import getProjectFieldDisplayManager from '../utils/getProjectFieldDisplayManager';

const mapStateToProps = ({state}) => {
    const {
        projects,
    } = state;

    return {
        projects,
    };
};

const propTypes = {
    projects: PropTypes.object,
};

const ProjectBuildPage = ({projects}) => {
    const {translate} = useLocales();
    const {projectId, buildId: buildIdString} = useParams();
    const dispatch = useDispatch();
    const [searchParams] = useSearchParams();
    const buildId = Number.parseInt(buildIdString, 10);
    useProjectBuildLoader({projects, projectId, buildId});
    const project = projects?.[projectId];
    const isLatestBuild = project?.latestBuild?.index === buildId;

    useEffect(() => {
        if (!project || !searchParams.has(browserApiKeys.reviewer) || getGlobalValue(['autoOpenedReviewer'])) {
            return;
        }

        setGlobalValue(['autoOpenedReviewer'], true);
        const appName = project?.configData?.solution?.appName;
        PubSub.publish('SOLUTION-MANAGER', {
            appName,
            config: {
                buildId,
                projectId,
            },
        });
        dispatch(showSolutionReviewer());
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [project]);

    if (!project?.results?.[buildId]?.data?.state) {
        return (
            <Box sx={{py: 5}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const build = project?.results?.[buildId];

    if (!build || build?.data?.state === BUILD_STATES.running) {
        return (
            <Box sx={{py: 5}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const name = project?.configData?.projectName;
    const buildName = `#${build.data.buildId}`;
    const title = `${name} - ${translate('project.build.label')} - ${buildName}`;
    const accessManager = getProjectFieldDisplayManager({translate});

    return (
        <PageWrapper
            title={title}
            maxWidth="xl">
            <HeaderBreadcrumbs
                heading={title}
                links={[
                    {
                        name: translate('dashboard.label'),
                        href: PATH_DASHBOARD.root,
                    },
                    {
                        name: translate('project.list.label'),
                        href: PATH_DASHBOARD.general.projects.root,
                    },
                    {
                        name: (
                            <Box component="span" sx={{
                                display: 'flex',
                                alignItems: 'center',
                            }}>
                                {name}
                                <ProjectBuildState
                                    state={project?.latestBuild?.state}
                                    sx={{ml: 1}}
                                />
                                {
                                    project.state !== projectStates?.active &&
                                    <Box component="span" sx={{mx: 1}}>{accessManager.state.q(project)}</Box>
                                }
                            </Box>
                        ),
                        href: `${PATH_DASHBOARD.general.projects.root}/${projectId}`,
                    },
                    {name: `#${buildId} (${fDateTimeHumanReadable(build?.data?.start)})`},
                ]}
            />
            <Stack spacing={3}>
                <ProjectHeaderBreadcrumbActions
                    project={project}
                />
                <ProjectRunTeaser
                    projectId={projectId}
                />
                <ProjectBuildTeaser
                    projectId={projectId}
                    buildId={buildId}
                    showConfigInfo
                    showActions
                />
                {
                    isLatestBuild && (
                        <PrivilegedContent
                            accessibleRoles={[
                                userRoles.EDITOR.key,
                            ]}
                            isCovert>
                            <ProjectBuildDelete
                                projectId={projectId}
                                buildId={buildId}
                            />
                        </PrivilegedContent>
                    )
                }
            </Stack>
        </PageWrapper>
    );
};

ProjectBuildPage.propTypes = propTypes;

export default connect(mapStateToProps)(ProjectBuildPage);
