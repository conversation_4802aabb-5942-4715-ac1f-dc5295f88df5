import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {IconButton, TableCell, TableRow} from '@material-ui/core';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import credits from '@w7-3/webeagle-resources/dist/config/catalog/credits';
import {getI18nDuration} from '@w7-3/webeagle-resources/dist/libs/date';
import {Icon} from '@iconify/react';
import useLocales from '../../../hooks/useLocales';
import Label from '../../../components/Label';
import {PurchaseType} from '../../../config/prop-types/SubscriptionTypes';
import UserDate from '../../components/UserDate';

const AutomationCreditsRow = (({
    item,
    isEven,
    pricing,
}) => {
    const {translate, currentLang} = useLocales();
    const {
        checkout: {
            created,
        },
        product,
    } = item;
    const labelColor = 'success';
    const sx = getOptionalMap(!isEven, {
        backgroundColor: (theme) => theme.palette.action.hover,
    });
    const creditItem = Object.values(credits.items).find((credit) => {
        return credit.priceId === item.priceId && credit.productId === item.product;
    });
    const handleRowClick = () => {
        PubSub.publish('OPEN.PAYMENT.ITEM-DETAILS', {item, creditItem});
    }

    return (
        <TableRow
            sx={{
                ...sx,
                cursor: 'pointer',
            }}
        >
            <TableCell onClick={handleRowClick}>
                {item.quantity}x {pricing?.products?.[product]?.name}
            </TableCell>
            <TableCell
                onClick={handleRowClick}
            >
                <UserDate date={created} />
            </TableCell>
            <TableCell onClick={handleRowClick}>
                {getI18nDuration(creditItem.validity, {
                    locale: currentLang.value,
                    humanize: true,
                })}
            </TableCell>
            <TableCell
                onClick={handleRowClick}
                align="center">
                <Label color={labelColor}>
                    {translate('transactions.subscriptions.states.active')}
                </Label>
            </TableCell>
            <TableCell align="center">
                <IconButton
                    size="large"
                    onClick={handleRowClick}
                    color="primary"

                >
                    <Icon icon="eva:eye-outline"/>
                </IconButton>
            </TableCell>
        </TableRow>
    );
});

AutomationCreditsRow.propTypes = {
    item: PurchaseType,
    isEven: PropTypes.bool,
    pricing: PropTypes.object,
};

export default AutomationCreditsRow;
