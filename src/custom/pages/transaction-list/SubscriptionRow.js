import PropTypes from 'prop-types';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {useSearchParams} from 'react-router-dom';
import {IconButton, TableCell, TableRow, Typography} from '@material-ui/core';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import {urlParameters} from '@w7-3/webeagle-resources/dist/config/keysAndUrlParameters';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {useEffect} from 'react';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import Label from '../../../components/Label';
import {CheckoutTransactionType} from '../../../config/prop-types/SubscriptionTypes';
import SubscriptionInfo from './SubscriptionInfo';
import UserDate from '../../components/UserDate';

const SubscriptionRow = (({
    item,
    isEven,
    pricing,
}) => {
    const {translate} = useLocales();
    const [searchParams] = useSearchParams();
    const {
        active,
        product,
        data: {
            created,
            periodStart,
            periodEnd,
            subscriptionSummary,
        },
        monthlyReset,
    } = item;
    const activeSubscriptionId = searchParams.get(urlParameters.subscription.id);
    const labelColor = active ? 'success' : 'secondary';
    const sx = getOptionalMap(!isEven, {
        backgroundColor: (theme) => theme.palette.action.hover,
    });
    const handleRowClick = (event) => {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        PubSub.publish('OPEN.PAYMENT.ITEM-DETAILS', {item});
    }

    useEffect(() => {
        if (activeSubscriptionId !== item.id) {
            return;
        }

        queueCallback(handleRowClick);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeSubscriptionId]);

    return (
        <>
            <TableRow
                sx={{
                    ...sx,
                    cursor: 'pointer',
                }}
            >
                <TableCell
                    onClick={handleRowClick}
                >
                    {pricing?.products?.[product]?.name}
                    <Typography component="p" variant="caption" sx={{color: 'text.secondary'}}>
                        ({translate(`pricing.subscriptions.intervals.${subscriptionSummary?.interval}.label`)})
                    </Typography>
                </TableCell>
                <TableCell
                    onClick={handleRowClick}
                >
                    <UserDate date={created} />
                </TableCell>
                <TableCell
                    onClick={handleRowClick}
                >
                    {translate('dateFromTo', {
                        startDate: fDateTimeHumanReadable(periodStart),
                        endDate: fDateTimeHumanReadable(periodEnd),
                    })}

                    <Typography component="p" variant="caption" sx={{color: 'text.secondary'}}>
                        {translate('transactions.subscriptions.usageReset.label')}
                        {': '}
                        {translate('transactions.subscriptions.usageReset.remainingDays', {
                            days: Math.ceil((monthlyReset.nextResetTimestamp - Date.now()) / 86400000),
                        })}
                    </Typography>
                </TableCell>
                <TableCell
                    onClick={handleRowClick}
                    align="center">
                    <Label color={labelColor}>
                        {translate(active ? 'transactions.subscriptions.states.active' : 'transactions.subscriptions.states.inActive')}
                    </Label>
                </TableCell>
                <TableCell align="center">
                    <IconButton
                        size="large"
                        onClick={handleRowClick}
                        color="primary"

                    >
                        <Icon icon="eva:eye-outline"/>
                    </IconButton>
                </TableCell>
            </TableRow>
            <TableRow
                sx={{
                    cursor: 'pointer',
                }}
            >
                <TableCell
                    colSpan={4}
                    onClick={handleRowClick}
                >
                    <SubscriptionInfo
                        item={item}
                    />
                </TableCell>
            </TableRow>
        </>
    );
});

SubscriptionRow.propTypes = {
    item: CheckoutTransactionType,
    isEven: PropTypes.bool,
    pricing: PropTypes.object,
};

export default SubscriptionRow;
