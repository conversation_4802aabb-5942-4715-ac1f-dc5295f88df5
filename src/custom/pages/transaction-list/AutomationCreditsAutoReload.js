import {Fragment, useEffect, useState} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    But<PERSON>,
    Container,
    Di<PERSON>r,
    FormControlLabel,
    <PERSON>rid,
    <PERSON>ack,
    Switch,
    Typography,
} from '@material-ui/core';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import equals from 'ramda/src/equals';
import credits from '@w7-3/webeagle-resources/dist/config/catalog/credits';
import {getAutoRenewCreditsSummary} from '@w7-3/webeagle-resources/dist/libs/accounts';
import {UIStatePropType} from '../../../config/prop-types/ts/ui.d';
import AutomationCredits from '../../components/account/AutomationCredits';
import useLocales from '../../../hooks/useLocales';
import LabelStyle from '../../components/utils/LabelStyle';
import MenuContainer from '../../components/utils/MenuContainer';
import QuantityStepper from '../../components/QuantityStepper';
import {<PERSON><PERSON><PERSON><PERSON>} from '../../../components/@material-extend';

const mapStateToProps = ({state, pricing}) => {
    const {
        automationCredits,
        customerData,
    } = state;

    return {
        automationCredits,
        customerData,
        pricing,
    };
};

const propTypes = {
    callbacks: PropTypes.shape({
        cancel: PropTypes.func,
        save: PropTypes.func,
    }),
    automationCredits: UIStatePropType.automationCredits,
    customerData: UIStatePropType.customerData,
    pricing: PropTypes.object,
};

const AutomationCreditsAutoReload = ({callbacks, automationCredits, customerData, pricing}) => {
    const [
        items,
        setItems,
    ] = useState(customerData?.automationCredits?.items || []);
    const [
        triggerAt,
        setTriggerAt,
    ] = useState(customerData?.automationCredits?.triggerAt);
    const [
        autoRenew,
        setAutoRenew,
    ] = useState(true);
    const [isValid, setIsValid] = useState(false);
    const {translate} = useLocales();
    const autoRenewCreditsSummary = getAutoRenewCreditsSummary({
        accountData: {
            customerData: {
                automationCredits: {
                    ...customerData?.automationCredits,
                    autoRenew,
                },
            },
            lifeTimeAutomationCredits: {
                value: automationCredits,
            },
        },
    });
    const cannotBeDisabled = autoRenewCreditsSummary?.isActive && !autoRenewCreditsSummary?.isDeactivationAllowed;

    useEffect(() => {
        setIsValid((
                (!equals(items, customerData?.automationCredits?.items) ||
                    autoRenew !== customerData?.automationCredits?.autoRenew) ||
                triggerAt !== customerData?.automationCredits?.triggerAt) &&
            (!autoRenew || items.length > 0),
        );
    }, [customerData, autoRenew, items, triggerAt]);

    return (
        <Container maxWidth="lg">
            <Stack
                spacing={3}
            >
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="flex-end"
                >
                    <AutomationCredits/>
                </Stack>
                {
                    cannotBeDisabled && (
                        <Alert severity="warning">
                            {translate('events.AUTOMATION_CREDITS_AUTO_RENEW_BLOCKED_DUE_TO_BALANCE')}
                        </Alert>
                    )
                }
                <Stack
                    direction={{
                        xs: 'column',
                        sm: 'row',
                    }}
                >
                    <Typography>
                        {translate('status')}
                    </Typography>
                    <FormControlLabel
                        label={autoRenew ?
                            translate('transactions.automationCredits.autoReloadActive') :
                            translate('transactions.automationCredits.autoReloadInActive')}
                        control={
                            <Switch
                                checked={autoRenew}
                                onChange={(event) => {
                                    setAutoRenew(event.target.checked);
                                }}
                            />
                        }
                        disabled={cannotBeDisabled}
                    />
                </Stack>
                <Box sx={{
                    position: 'relative',
                    '&:after': autoRenew ? {} : {
                        content: '""',
                        position: 'absolute',
                        top: '-1em',
                        left: '-1em',
                        right: '-1em',
                        bottom: '-1em',
                        pointerEvents: 'none',
                        background: 'rgba(255, 255, 255, 0.0125)',
                    },
                }}>
                    <LabelStyle>
                        {translate('transactions.automationCredits.productItems')}
                    </LabelStyle>
                    <Grid
                        container
                        spacing={1}
                        alignItems="center"
                        sx={{mt: 3}}
                    >
                        <MHidden width="mdDown">
                            <Grid item xs={8}>
                                {translate('pricing.cart.items.product')}
                            </Grid>
                            <Grid item xs={4}>
                                {translate('pricing.cart.items.quantity')}
                            </Grid>
                        </MHidden>
                        <Grid item xs={12}>
                            <Divider sx={{
                                borderStyle: 'solid',
                                my: 1,
                            }}/>
                        </Grid>
                        {
                            items.map((item) => {
                                const priceData = pricing?.prices?.[item?.priceId];
                                const productData = pricing?.products?.[priceData?.product];
                                const creditItem = Object.values(credits.items).find((i) => i.priceId === item.priceId);

                                return (
                                    <Fragment key={item?.priceId}>
                                        <Grid item xs={12} md={8}>
                                            <MHidden width="mdUp">
                                                {translate('pricing.cart.items.product')}
                                            </MHidden>
                                            <Typography>
                                                {productData?.name}
                                            </Typography>
                                            <Typography
                                                variant="caption"
                                                sx={{color: 'text.secondary'}}
                                            >
                                                {translate('pricing.otp.automationCredits.label')}: {creditItem?.quantity}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={12} md={4}>
                                            <MHidden width="mdUp">
                                                {translate('pricing.cart.items.quantity')}
                                            </MHidden>
                                            <QuantityStepper
                                                showDeleteButton
                                                quantity={item.quantity}
                                                setQuantity={({quantity}) => {
                                                    if (quantity < 1) {
                                                        const newItems = items.filter((i) => i.priceId !== item.priceId);
                                                        setItems(newItems);

                                                        return;
                                                    }

                                                    const newItems = JSON.parse(JSON.stringify(items));
                                                    const index = items.findIndex((i) => i.priceId === item.priceId);

                                                    newItems[index].quantity = quantity;
                                                    setItems(newItems);
                                                    setAutoRenew(true);
                                                }}
                                            />
                                        </Grid>
                                        <Grid item xs={12}>
                                            <Divider sx={{
                                                borderStyle: 'dashed',
                                                my: 1,
                                            }}/>
                                        </Grid>
                                    </Fragment>
                                );
                            })
                        }
                        <Grid
                            item
                            xs={12}
                            sx={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                            }}>
                            <MenuContainer
                                label={translate('transactions.automationCredits.selectItem')}
                                optionList={Object.values(credits.items).map((item) => {
                                    const productData = pricing?.products?.[item?.productId];
                                    return {
                                        content: productData?.name,
                                        value: item?.priceId,
                                    };
                                })}
                                onChange={(priceId) => {
                                    const newItems = JSON.parse(JSON.stringify(items));
                                    const index = items.findIndex((i) => i.priceId === priceId);
                                    if (index > -1) {
                                        newItems[index].quantity += 1;
                                    } else {
                                        newItems.push({
                                            priceId,
                                            quantity: 1,
                                        });
                                    }

                                    setItems(newItems);
                                    setAutoRenew(true);
                                }}
                                isSelectMode={false}
                                disableSelection={false}
                            />
                        </Grid>
                    </Grid>
                </Box>
                <LabelStyle>
                    {translate('transactions.automationCredits.threshold')}
                </LabelStyle>
                <QuantityStepper
                    quantity={triggerAt}
                    setQuantity={(data) => {
                        setTriggerAt(data.quantity);
                    }}
                    sxSettings={{
                        root: {
                            width: '100%',
                        },
                        input: {
                            width: 'auto',
                        },
                    }}
                    step={1000}
                />
                {
                    autoRenew && (
                        <Alert severity="info">
                            {translate('transactions.automationCredits.autoReloadActiveDescription', autoRenewCreditsSummary)}
                        </Alert>
                    )
                }
                <Stack
                    spacing={3}
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="center"
                >
                    <Button color="inherit" onClick={callbacks.cancel}>
                        {translate('cancel')}
                    </Button>
                    <Button
                        size="large"
                        variant="contained"
                        onClick={() => {
                            callbacks.save({
                                items,
                                autoRenew,
                                triggerAt,
                            });
                        }}
                        disabled={!isValid}
                    >
                        {translate('save')}
                    </Button>
                </Stack>
            </Stack>
        </Container>
    );
};

AutomationCreditsAutoReload.propTypes = propTypes;

export default connect(mapStateToProps)(AutomationCreditsAutoReload);
