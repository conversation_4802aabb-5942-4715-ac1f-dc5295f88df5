import {Fragment, useEffect, useRef} from 'react';
import {
    Box,
    Button,
    IconButton,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tooltip,
    Typography,
} from '@material-ui/core';
import {Link as RouterLink, useSearchParams} from 'react-router-dom';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {Icon} from '@iconify/react';
import PubSub from 'pubsub-js';
import {PATH_PAGE} from '@w7-3/webeagle-resources/dist/config/paths';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import queueCallback from '@w7-3/webeagle-resources/dist/libs/queueCallback';
import {invoiceStatuses} from '@w7-3/webeagle-resources/dist/config/account';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import Scrollbar from '../../../components/Scrollbar';
import useLocales from '../../../hooks/useLocales';
import LoadingScreen from '../../../components/LoadingScreen';
import {CheckoutType, CheckoutTransactionType} from '../../../config/prop-types/SubscriptionTypes';
import UserDate from '../../components/UserDate';
import Price from '../../components/payment/Price';
import Label from '../../../components/Label';
import {paymentStatusOptions} from '../../../config/base';
import ExternalLink from '../../components/utils/ExternalLink';

const mapStateToProps = ({state}) => {
    const {
        checkoutsList,
        transactionList,
    } = state;

    return {
        checkoutsList,
        transactionList,
    };
};

const propTypes = {
    checkoutsList: PropTypes.arrayOf(CheckoutType),
    transactionList: PropTypes.arrayOf(CheckoutTransactionType),
};

const OrderHistory = ({
    checkoutsList,
    transactionList,
}) => {
    const {translate} = useLocales();
    const [searchParams] = useSearchParams();
    const hasBeenRedirected = useRef(false);
    const checkoutId = searchParams.get('item');

    useEffect(() => {
        if (!checkoutsList?.some((checkout) => checkout.id === checkoutId) || hasBeenRedirected.current) {
            return;
        }

        queueCallback(() => {
            hasBeenRedirected.current = true;
            PubSub.publish('OPEN.PAYMENT.DETAILS', {checkoutId});
        });
    }, [checkoutId, checkoutsList]);

    if (!checkoutsList || !transactionList) {
        return (
            <Box sx={{py: 5}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    return (
        <Stack spacing={3} sx={{py: 5}}>
            {
                isNonEmptyArray(checkoutsList) ? (
                    <Box sx={{my: 1}}>
                        <Scrollbar>
                            <TableContainer sx={{minWidth: 600}}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.orderHistory.fields.created')}
                                            </TableCell>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.orderHistory.fields.id')}
                                            </TableCell>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.orderHistory.fields.context')}
                                            </TableCell>
                                            <TableCell>
                                                {translate('transactions.orderHistory.fields.amount')}
                                            </TableCell>
                                            <TableCell sx={{width: '10%'}}>
                                                {translate('transactions.status')}
                                            </TableCell>
                                            <TableCell sx={{width: 60}}/>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {checkoutsList.map((checkout) => {
                                            const checkoutId = checkout.id;
                                            const handleRowClick = () => {
                                                PubSub.publish('OPEN.PAYMENT.DETAILS', {checkoutId});
                                            };
                                            const {color} = paymentStatusOptions[checkout.paymentStatus];

                                            return (
                                                <Fragment key={checkoutId}>
                                                    <TableRow sx={{
                                                        cursor: 'pointer',
                                                        borderBottom: (theme) => `solid 1px ${theme.palette.divider}`,
                                                    }}>
                                                        <TableCell
                                                            onClick={handleRowClick}
                                                        >
                                                            <UserDate
                                                                date={checkout.created}
                                                            />
                                                        </TableCell>
                                                        <TableCell
                                                            onClick={handleRowClick}
                                                        >
                                                            <CopyToClipboard text={checkout.id}>
                                                                <Tooltip title={translate('copy')}>
                                                                    <Button
                                                                        variant="text"
                                                                        endIcon={<Icon
                                                                            icon="eva:copy-fill"
                                                                            width={40}
                                                                            height={40}
                                                                        />}
                                                                        sx={{m: 0}}
                                                                    >
                                                                        ****{checkout.id.slice(-4)}
                                                                    </Button>
                                                                </Tooltip>
                                                            </CopyToClipboard>
                                                        </TableCell>
                                                        <TableCell
                                                            onClick={handleRowClick}
                                                            sx={{
                                                                maxWidth: 200,
                                                                wordBreak: 'break-word',
                                                                wordWrap: 'break-word',
                                                            }}
                                                        >
                                                            {translate(`transactions.orderHistory.fields.billingAction.${checkout.context}`)}
                                                        </TableCell>
                                                        <TableCell
                                                            onClick={handleRowClick}
                                                        >
                                                            <Stack>
                                                                <Stack direction="row" spacing={1}>
                                                                    <Price
                                                                        variant="b"
                                                                        price={checkout.price}
                                                                        currency={checkout.currency}
                                                                    />
                                                                </Stack>
                                                                {
                                                                    [
                                                                        invoiceStatuses.open.appName,
                                                                        invoiceStatuses.uncollectible.appName,
                                                                    ].includes(checkout.paymentStatus) && (
                                                                        <ExternalLink
                                                                            label={translate('transactions.payment.trigger.label')}
                                                                            url={checkout.invoiceUrl}
                                                                        />
                                                                    )
                                                                }
                                                            </Stack>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Label variant="filled" color={color}>
                                                                {translate(`transactions.payment.status.options.${checkout.paymentStatus}.label`)}
                                                            </Label>
                                                        </TableCell>
                                                        <TableCell align="center">
                                                            <IconButton
                                                                size="large"
                                                                onClick={handleRowClick}
                                                                color="primary"

                                                            >
                                                                <Icon icon="eva:eye-outline"/>
                                                            </IconButton>
                                                        </TableCell>
                                                    </TableRow>
                                                </Fragment>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Scrollbar>
                    </Box>
                ) : (
                    <Stack
                        direction="row"
                        alignItems="center"
                        justifyContent="center"
                        spacing={.5}>
                        <Typography
                            variant="body2"
                            sx={{color: 'text.secondary'}}>
                            {translate('transactions.missingItem')}
                        </Typography>
                        <Button
                            to={PATH_PAGE.contact}
                            component={RouterLink}
                            sx={{textTransform: 'none'}}>
                            {translate('contactUs.label')}
                        </Button>
                    </Stack>
                )
            }
        </Stack>
    );
};

OrderHistory.propTypes = propTypes;

export default connect(mapStateToProps)(OrderHistory);
