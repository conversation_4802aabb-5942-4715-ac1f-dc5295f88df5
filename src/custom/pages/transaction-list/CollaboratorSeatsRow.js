import PropTypes from 'prop-types';
import {Icon<PERSON>utton, TableCell, TableRow} from '@material-ui/core';
import PubSub from 'pubsub-js';
import getOptionalMap from '@w7-3/webeagle-resources/dist/libs/getOptionalMap';
import collaborators from '@w7-3/webeagle-resources/dist/config/catalog/collaborators';
import {Icon} from '@iconify/react';
import useLocales from '../../../hooks/useLocales';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import Label from '../../../components/Label';
import {PurchaseType} from '../../../config/prop-types/SubscriptionTypes';
import UserDate from '../../components/UserDate';

const CollaboratorSeatsRow = (({
    item,
    isEven,
    pricing,
}) => {
    const {translate} = useLocales();
    const {
        checkout: {
            created,
        },
        product,
    } = item;
    const labelColor = 'success';
    const sx = getOptionalMap(!isEven, {
        backgroundColor: (theme) => theme.palette.action.hover,
    });
    const collaboratorItem = Object.values(collaborators.items).find((credit) => {
        return credit.priceId === item.priceId && credit.productId === item.product;
    });
    const handleRowClick = () => {
        PubSub.publish('OPEN.PAYMENT.ITEM-DETAILS', {item, collaboratorItem});
    }

    return (
        <TableRow
            sx={{
                ...sx,
                cursor: 'pointer',
            }}
        >
            <TableCell onClick={handleRowClick}>
                {pricing?.products?.[product]?.name}
            </TableCell>
            <TableCell
                onClick={handleRowClick}
            >
                <UserDate date={created} />
            </TableCell>
            <TableCell onClick={handleRowClick}>
                {translate('dateFrom', {
                    startDate: fDateTimeHumanReadable(created),
                })}
            </TableCell>
            <TableCell align="center" onClick={handleRowClick}>
                <Label color={labelColor}>
                    {translate('transactions.subscriptions.states.active')}
                </Label>
            </TableCell>
            <TableCell align="center">
                <IconButton
                    size="large"
                    onClick={handleRowClick}
                    color="primary"

                >
                    <Icon icon="eva:eye-outline"/>
                </IconButton>
            </TableCell>
        </TableRow>
    );
});

CollaboratorSeatsRow.propTypes = {
    item: PurchaseType,
    isEven: PropTypes.bool,
    pricing: PropTypes.object,
};

export default CollaboratorSeatsRow;
