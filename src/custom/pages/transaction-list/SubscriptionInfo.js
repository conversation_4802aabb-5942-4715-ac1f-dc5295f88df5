import {Alert, Al<PERSON>Title, But<PERSON>} from '@material-ui/core';
import {useNavigate} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import CustomerPortalButton from '../../components/CustomerPortalButton';
import {fDateTimeHumanReadable} from '../../../utils/formatTime';
import useLocales from '../../../hooks/useLocales';
import {CheckoutTransactionType} from '../../../config/prop-types/SubscriptionTypes';

const SubscriptionInfo = ({item}) => {
    const {translate} = useLocales();
    const navigate = useNavigate();
    const {
        data: {
            periodEnd,
            isCancelled,
            subscriptionSummary,
        },
    } = item;

    if (isCancelled) {
        return (
            <Alert
                variant="standard"
                severity="warning"
                sx={{
                    px: 2,
                    py: 1,
                }}
                icon={false}
                action={<CustomerPortalButton/>}
            >
                <AlertTitle>
                    {translate('transactions.subscriptions.isCancelled', {
                        endDate: fDateTimeHumanReadable(periodEnd),
                    })}
                </AlertTitle>
            </Alert>
        );
    }

    const {
        data: {
            renewals,
        },
    } = item;

    if (renewals?.isPastDue) {
        return (
            <Alert
                variant="standard"
                severity="warning"
                sx={{
                    px: 2,
                    py: 1,
                }}
                icon={false}
                action={
                    <Button
                        color="inherit"
                        variant="outlined"
                        onClick={() => {
                            navigate(`${PATH_DASHBOARD.management.transactionList}?item=${renewals.billingReference}`);
                        }}
                    >
                        {translate('transactions.orderHistory.viewDetails')}
                    </Button>
                }
            >
                <AlertTitle>
                    {translate('transactions.subscriptions.isPastDue')}
                </AlertTitle>
            </Alert>
        );
    }

    if (subscriptionSummary?.hasEnteredOverage) {
        return (
            <Alert
                variant="standard"
                severity="warning"
                sx={{
                    px: 2,
                    py: 1,
                }}
            >
                {translate('transactions.subscriptions.hasEnteredOverage.label')}
            </Alert>
        );
    }

    return null;
};

SubscriptionInfo.propTypes = {
    item: CheckoutTransactionType,
};

export default SubscriptionInfo;
