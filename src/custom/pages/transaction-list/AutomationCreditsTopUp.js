import {
    Box,
    Button,
    IconButton,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import {connect} from 'react-redux';
import {Fragment} from 'react';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {CREDIT_EVENT_CONTEXTS, CREDIT_EVENTS} from '@w7-3/webeagle-resources/dist/config/administration';
import Scrollbar from '../../../components/Scrollbar';
import MissingTransactionItem from '../../components/subscriptions/MissingTransactionItem';
import useLocales from '../../../hooks/useLocales';
import UserDate from '../../components/UserDate';
import DefinitionList from '../../components/utils/DefinitionList';
import {
    AutomationCreditDataPropType,
    AutomationCreditEventPropType,
} from '../../../config/prop-types/ts/global.d';

const mapStateToProps = ({state}) => {
    const {
        billingCycle,
        billingHistory,
        billingCyclesData,
    } = state;

    return {
        currentBillingCycle: billingCycle,
        billingHistory,
        billingCyclesData,
    };
};

const propTypes = {
    billingCycleData: AutomationCreditDataPropType,
};

export const AutomationCreditsTopUpDetails = ({item, eventData}) => {
    const {translate} = useLocales();

    return (
        <>
            <DefinitionList
                dataList={[
                    {
                        key: translate('transactions.billingCycle.label'),
                        node: (
                            <Typography>
                                {item.billingCycle}
                            </Typography>
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsTopUp.fields.date'),
                        node: (
                            <UserDate
                                date={item.ts}
                            />
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsTopUp.fields.expirationDate'),
                        node: (
                            <UserDate
                                date={eventData.expiry}
                            />
                        ),
                    },
                    {
                        key: `${translate('transactions.automationCreditsTopUp.fields.credits')} (${translate('pricing.otp.automationCredits.label')})`,
                        node: (
                            `${eventData.quantity}`
                        ),
                    },
                    {
                        key: translate('transactions.creditEvents.balanceBefore'),
                        node: (
                            `${eventData.quantityBefore}`
                        ),
                    },
                    {
                        key: translate('transactions.creditEvents.balanceAfter'),
                        node: (
                            `${eventData.quantityAfter}`
                        ),
                    }
                ]}
            />
            <Stack direction="row" spacing={1} alignItems="center">
                <Typography>
                    {translate('transactions.automationCreditsTopUp.fields.reference')}:
                </Typography>
                {
                    item.event.context === CREDIT_EVENT_CONTEXTS.DEMO ?
                        <Typography>
                            {translate('demo.application.label')}
                        </Typography> :
                        <Button
                            variant="text"
                            onClick={() => {
                                PubSub.publish('OPEN.PAYMENT.DETAILS', {
                                    checkoutId: eventData.checkoutId,
                                });
                            }}
                        >
                            {translate('transactions.orderHistory.fields.invoice')}
                            {' '}
                            ****{eventData.checkoutId.slice(-4)}
                        </Button>
                }
            </Stack>
        </>
    );
};

AutomationCreditsTopUpDetails.propTypes = {
    item: AutomationCreditDataPropType,
    eventData: AutomationCreditEventPropType,
};

const AutomationCreditsTopUp = ({billingCycleData}) => {
    const {translate} = useLocales();
    const transactionList = Object.values(billingCycleData || {}).filter((item) => {
        return item.event.type === CREDIT_EVENTS.TOP_UP;
    });


    return (
        <Stack spacing={3} sx={{py: 5}}>
            {
                isNonEmptyArray(transactionList) ? (
                    <Box sx={{my: 1}}>
                        <Scrollbar>
                            <TableContainer sx={{minWidth: 600}}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.automationCreditsTopUp.fields.date')}
                                            </TableCell>
                                            <TableCell sx={{width: '10%'}}>
                                                {translate('transactions.automationCreditsTopUp.fields.credits')}
                                            </TableCell>
                                            <TableCell sx={{width: '15%'}}>
                                                {translate('transactions.automationCreditsTopUp.fields.billingCycle')}
                                            </TableCell>
                                            <TableCell sx={{width: '40%'}}>
                                                {translate('transactions.automationCreditsTopUp.fields.reference')}
                                            </TableCell>
                                            <TableCell sx={{maxWidth: 40}} align="right" />
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {transactionList.map((item) => {
                                            return item.event.data.map((eventData) => {
                                                const handleClick = () => {
                                                    PubSub.publish('SHOW.DIALOG', {
                                                        type: 'custom',
                                                        dialogProps: {
                                                            isFullScreen: true,
                                                            title: (
                                                                <Stack direction="row" spacing={1} alignItems="center">
                                                                    <div>{ translate('transactions.automationCreditsTopUp.label')}</div>
                                                                    <div><UserDate date={item.ts} /></div>
                                                                </Stack>
                                                            ),
                                                        },
                                                        children: (
                                                            <AutomationCreditsTopUpDetails
                                                                item={item}
                                                                eventData={eventData}
                                                            />
                                                        ),
                                                    });
                                                };
                                                return (
                                                    <Fragment key={item.ts}>
                                                        <TableRow sx={{
                                                            cursor: 'pointer',
                                                            borderBottom: (theme) => `solid 1px ${theme.palette.divider}`,
                                                        }}>
                                                            <TableCell
                                                                onClick={handleClick}
                                                            >
                                                                <UserDate date={item.ts} />
                                                            </TableCell>
                                                            <TableCell
                                                                onClick={handleClick}
                                                            >
                                                                + {eventData.quantity}
                                                            </TableCell>
                                                            <TableCell onClick={handleClick}>
                                                                {item.billingCycle}
                                                            </TableCell>
                                                            {
                                                                item.event.context === CREDIT_EVENT_CONTEXTS.DEMO ?
                                                                    <TableCell onClick={handleClick}>
                                                                        <Typography sx={{pl: 1}}>
                                                                            {translate(`transactions.creditEvents.contexts.options.${item.event.context}`)}
                                                                        </Typography>
                                                                    </TableCell> :
                                                                    <TableCell>
                                                                        <Button
                                                                            variant="text"
                                                                            onClick={() => {
                                                                                PubSub.publish('OPEN.PAYMENT.DETAILS', {
                                                                                    checkoutId: eventData.checkoutId,
                                                                                });
                                                                            }}
                                                                        >
                                                                            {translate(`transactions.creditEvents.contexts.options.${item.event.context}`)}
                                                                            {' '}
                                                                            ({translate('transactions.orderHistory.fields.invoice')}{' '}****{eventData.checkoutId.slice(-4)})
                                                                        </Button>
                                                                    </TableCell>
                                                            }
                                                            <TableCell>
                                                                <IconButton
                                                                    size="large"
                                                                    onClick={handleClick}
                                                                    color="primary"

                                                                >
                                                                    <Icon icon="eva:eye-outline"/>
                                                                </IconButton>
                                                            </TableCell>
                                                        </TableRow>
                                                    </Fragment>
                                                );
                                            });
                                        })}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Scrollbar>
                    </Box>
                ) : (
                    <>
                        <Typography
                            variant="body2"
                            textAlign="center"
                            sx={{color: 'text.secondary'}}>
                            {translate('transactions.automationCreditsTopUp.empty.label')}
                        </Typography>
                        <MissingTransactionItem />
                    </>
                )
            }
        </Stack>
    );
};

AutomationCreditsTopUp.propTypes = propTypes;

export default connect(mapStateToProps)(AutomationCreditsTopUp);
