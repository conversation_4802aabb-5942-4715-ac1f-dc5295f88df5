import {
    Box,
    Button,
    IconButton,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@material-ui/core';
import {Fragment} from 'react';
import PubSub from 'pubsub-js';
import {Icon} from '@iconify/react';
import {Link as RouterLink} from 'react-router-dom';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {CREDIT_EVENT_CONTEXTS, CREDIT_EVENTS} from '@w7-3/webeagle-resources/dist/config/administration';
import getParsedJSON from '@w7-3/webeagle-resources/dist/libs/getParsedJSON';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import Scrollbar from '../../../components/Scrollbar';
import MissingTransactionItem from '../../components/subscriptions/MissingTransactionItem';
import useLocales from '../../../hooks/useLocales';
import {AutomationCreditDataPropType, AutomationCreditEventPropType} from '../../../config/prop-types/ts/global.d';
import UserDate from '../../components/UserDate';
import DefinitionList from '../../components/utils/DefinitionList';

const propTypes = {
    billingCycleData: AutomationCreditDataPropType,
};

export const AutomationCreditsConsumptionEvent = ({item, eventData}) => {
    const {translate} = useLocales();
    const sessionData = item.event.context === CREDIT_EVENT_CONTEXTS.PROJECT_BUILD ?
        getParsedJSON(item.event.loginSessionId) : null;

    return (
        <>
            <DefinitionList
                dataList={[
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.timestamp'),
                        node: (
                            <UserDate
                                date={item.ts}
                            />
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.context'),
                        node: (
                            <Stack direction="row" spacing={1} alignItems="center">
                                <Typography>
                                    {translate(`transactions.creditEvents.contexts.options.${item.event.context}`)}
                                </Typography>
                                {
                                    sessionData?.projectId && sessionData?.buildId && (
                                        <Button
                                            variant="text"
                                            to={`${PATH_DASHBOARD.general.projects.root}/${sessionData?.projectId}/${sessionData?.buildId}`}
                                            component={RouterLink}
                                        >
                                            {translate('project.build.ctaLabel')}
                                        </Button>
                                    )
                                }
                            </Stack>
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.billableAction'),
                        node: (
                            translate(`checkout.metrics.items.${eventData.billableAction}.label`)
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.quantity'),
                        node: (
                            eventData.quantity
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.consumption'),
                        node: (
                            eventData.totalCost
                        ),
                    },
                    {
                        key: translate('transactions.creditEvents.balanceBefore'),
                        node: (
                            `${eventData.quantityBefore}`
                        ),
                    },
                    {
                        key: translate('transactions.creditEvents.balanceAfter'),
                        node: (
                            `${eventData.quantityAfter}`
                        ),
                    }
                ]}
            />
        </>
    );
};

AutomationCreditsConsumptionEvent.propTypes = {
    item: AutomationCreditDataPropType,
    eventData: AutomationCreditEventPropType,
};

export const AutomationCreditsConsumptionEntry = ({item}) => {
    const {translate} = useLocales();
    const totalConsumption = item.event.data.reduce((sum, eventData) => {
        return sum + eventData.quantity;
    }, 0);

    return (
        <>
            <DefinitionList
                dataList={[
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.timestamp'),
                        node: (
                            <UserDate date={item.ts} />
                        ),
                    },
                    {
                        key: translate('transactions.automationCreditsConsumption.fields.context'),
                        node: (
                            translate(`transactions.creditEvents.contexts.options.${item.event.context}`)
                        ),
                    },
                    {
                        key: `${translate('transactions.automationCreditsConsumption.fields.consumption')} (${translate('pricing.otp.automationCredits.label')})`,
                        node: (
                            totalConsumption
                        ),
                    },
                ]}
            />
            <Typography variant="h6" sx={{mt: 3}}>
                {translate('transactions.automationCreditsConsumption.fields.billableActions')}
                {': '}
                {item.event.data.length}
            </Typography>
            <Scrollbar>
                <TableContainer sx={{minWidth: 750}}>
                    <Table size="medium">
                        <TableHead>
                            <TableRow>
                                <TableCell>
                                    {translate('transactions.automationCreditsConsumption.fields.billableAction')}
                                </TableCell>
                                <TableCell>
                                    {translate('transactions.automationCreditsConsumption.fields.quantity')}
                                </TableCell>
                                <TableCell align="center">
                                    {translate('transactions.automationCreditsConsumption.fields.consumption')}
                                    {' '}
                                    ({translate('pricing.otp.automationCredits.label')})
                                </TableCell>
                                <TableCell />
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {item.event.data.map((eventData) => {
                                const handleClick = () => {
                                    PubSub.publish('SHOW.DIALOG', {
                                        type: 'custom',
                                        dialogProps: {
                                            isFullScreen: true,
                                            title: (
                                                <>
                                                    <UserDate date={item.ts} />
                                                    {': '}
                                                    {translate(`transactions.creditEvents.contexts.options.${item.event.context}`)}
                                                    {' / '}
                                                    {translate(`checkout.metrics.items.${eventData.billableAction}.label`)}
                                                </>
                                            ),
                                        },
                                        children: (
                                            <AutomationCreditsConsumptionEvent
                                                item={item}
                                                eventData={eventData}
                                            />
                                        ),
                                    });
                                };
                                return (
                                    <TableRow
                                        key={item.ts}
                                        sx={{
                                            cursor: 'pointer',
                                            borderBottom: (theme) => `solid 1px ${theme.palette.grey[500_8]}`,
                                        }}
                                    >
                                        <TableCell
                                            onClick={handleClick}
                                        >
                                            {translate(`checkout.metrics.items.${eventData.billableAction}.label`)}
                                        </TableCell>
                                        <TableCell
                                            onClick={handleClick}
                                        >
                                            {eventData.quantity}
                                        </TableCell>
                                        <TableCell
                                            align="center"
                                            onClick={handleClick}
                                        >
                                            {eventData.totalCost}
                                        </TableCell>
                                        <TableCell>
                                            <IconButton
                                                size="large"
                                                onClick={handleClick}
                                                color="primary"

                                            >
                                                <Icon icon="eva:eye-outline"/>
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Scrollbar>
        </>
    );
};

AutomationCreditsConsumptionEntry.propTypes = {
    item: AutomationCreditDataPropType,
};

const AutomationCreditsConsumption = ({billingCycleData}) => {
    const {translate} = useLocales();
    const transactionList = Object.values(billingCycleData || {}).filter((item) => {
        return item.event.type === CREDIT_EVENTS.CONSUMPTION;
    });

    return (
        <Stack spacing={3} sx={{py: 5}}>
            {
                isNonEmptyArray(transactionList) ? (
                    <Box sx={{my: 1}}>
                        <Scrollbar>
                            <TableContainer sx={{minWidth: 750}}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.automationCreditsConsumption.fields.timestamp')}
                                            </TableCell>
                                            <TableCell sx={{width: '10%'}}>
                                                {translate('transactions.automationCreditsConsumption.fields.credits')}
                                            </TableCell>
                                            <TableCell sx={{width: '15%'}}>
                                                {translate('transactions.automationCreditsConsumption.fields.billingCycle')}
                                            </TableCell>
                                            <TableCell sx={{width: '10%'}}>
                                                {translate('transactions.automationCreditsConsumption.fields.context')}
                                            </TableCell>
                                            <TableCell>
                                                # {translate('transactions.automationCreditsConsumption.fields.billableActions')}
                                            </TableCell>
                                            <TableCell sx={{width: 80}} align="right" />
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {transactionList.map((item) => {
                                            const totalConsumption = item.event.data.reduce((sum, eventData) => {
                                                return sum + eventData.quantity;
                                            }, 0);
                                            const handleClick = () => {
                                                PubSub.publish('SHOW.DIALOG', {
                                                    type: 'custom',
                                                    dialogProps: {
                                                        isFullScreen: true,
                                                        title: (
                                                            <>
                                                                <UserDate date={item.ts} />
                                                                {': '}
                                                                {translate(`transactions.creditEvents.contexts.options.${item.event.context}`)}
                                                            </>
                                                        ),
                                                    },
                                                    children: (
                                                        <AutomationCreditsConsumptionEntry
                                                            item={item}
                                                        />
                                                    ),
                                                });
                                            };

                                            return (
                                                <TableRow
                                                    key={item.ts}
                                                    sx={{
                                                        cursor: 'pointer',
                                                        borderBottom: (theme) => `solid 1px ${theme.palette.grey[500_8]}`,
                                                    }}
                                                >
                                                    <TableCell
                                                        onClick={handleClick}
                                                    >
                                                        <UserDate date={item.ts} />
                                                    </TableCell>
                                                    <TableCell
                                                        onClick={handleClick}
                                                    >
                                                        - {totalConsumption}
                                                    </TableCell>
                                                    <TableCell onClick={handleClick}>
                                                        {item.billingCycle}
                                                    </TableCell>
                                                    <TableCell
                                                        onClick={handleClick}
                                                    >
                                                        {translate(`transactions.creditEvents.contexts.options.${item.event.context}`)}
                                                    </TableCell>
                                                    <TableCell
                                                        onClick={handleClick}
                                                    >
                                                        {item.event.data.length}
                                                    </TableCell>
                                                    <TableCell>
                                                        <IconButton
                                                            size="large"
                                                            onClick={handleClick}
                                                            color="primary"

                                                        >
                                                            <Icon icon="eva:eye-outline"/>
                                                        </IconButton>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Scrollbar>
                    </Box>
                ) : (
                    <>
                        <Typography
                            variant="body2"
                            textAlign="center"
                            sx={{color: 'text.secondary'}}>
                            {translate('transactions.automationCreditsConsumption.empty.label')}
                        </Typography>
                        <MissingTransactionItem />
                    </>
                )
            }
        </Stack>
    );
};

AutomationCreditsConsumption.propTypes = propTypes;

export default AutomationCreditsConsumption;
