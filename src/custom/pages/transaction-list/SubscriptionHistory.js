import {useState} from 'react';
import {
    Box,
    FormControlLabel, Stack,
    Switch,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow, Typography,
} from '@material-ui/core';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import useLocales from '../../../hooks/useLocales';
import {CheckoutTransactionType} from '../../../config/prop-types/SubscriptionTypes';
import PaymentDetailsRow from './PaymentDetailsRow';
import LoadingScreen from '../../../components/LoadingScreen';
import MissingTransactionItem from '../../components/subscriptions/MissingTransactionItem';
import Scrollbar from '../../../components/Scrollbar';

const mapStateToProps = ({state, pricing}) => {
    const {
        transactionList,
    } = state;

    return {
        transactionList,
        pricing,
    };
};

const propTypes = {
    transactionList: PropTypes.arrayOf(CheckoutTransactionType),
    pricing: PropTypes.object,
};

const SubscriptionHistory = (props) => {
    const {translate} = useLocales();
    const [showOnlyActive, setShowOnlyActive] = useState(true);

    if (!props.transactionList) {
        return (
            <Box sx={{py: 5}}>
                <LoadingScreen isInline/>
            </Box>
        );
    }

    const transactionList = props.transactionList?.filter((item) => {
        return item.isSubscriptionItem && (!showOnlyActive || item.active);
    });

    if (!isNonEmptyArray(transactionList)) {
        return (
            <Stack
                alignItems="center"
                justifyContent="center"
                spacing={1}>
                <Typography
                    variant="body2"
                    sx={{color: 'text.secondary'}}>
                    {translate('transactions.subscriptions.noActiveLabel')}
                </Typography>
                <MissingTransactionItem />
            </Stack>
        );
    }

    return (
        <Stack spacing={3}>
            <Stack direction="row" alignItems="center" justifyContent="flex-end">
                <FormControlLabel
                    label={translate('transactions.subscriptions.showOnlyActiveItems')}
                    control={
                        <Switch
                            onChange={() => {
                                setShowOnlyActive(!showOnlyActive);
                            }}
                            checked={showOnlyActive}
                        />
                    }
                />
            </Stack>
            <Scrollbar>
                <TableContainer sx={{minWidth: 600}}>
                    <Table size="medium" stickyHeader>
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{width: '20%'}}>
                                    {translate('transactions.license')}
                                </TableCell>
                                <TableCell sx={{width: '20%'}}>
                                    {translate('transactions.createdOn')}
                                </TableCell>
                                <TableCell>
                                    {translate('transactions.validity')}
                                </TableCell>
                                <TableCell align="center">
                                    {translate('transactions.status')}
                                </TableCell>
                                <TableCell sx={{maxWidth: 40}} />
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {transactionList
                                .filter((item) => item.isSubscriptionItem)
                                .map((item, index) => (
                                    <PaymentDetailsRow
                                        key={item.id}
                                        item={item}
                                        isEven={index % 2 === 0}
                                        pricing={props.pricing}
                                    />
                                ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Scrollbar>
        </Stack>
    )
};

SubscriptionHistory.propTypes = propTypes;

export default connect(mapStateToProps)(SubscriptionHistory);
