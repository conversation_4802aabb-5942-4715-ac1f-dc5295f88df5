import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import SubscriptionDetails from '../../../components/subscriptions/SubscriptionDetails';
import CollaboratorSeatsDetails from '../../../components/subscriptions/CollaboratorSeatsDetails';
import AutomationCreditsDetails from '../../../components/subscriptions/AutomationCreditsDetails';
import PrivilegedContent from '../../../../guards/PrivilegedContent';
import useLocales from '../../../../hooks/useLocales';
import ExternalLink from '../../../components/utils/ExternalLink';
import {useSelector} from '../../../../redux/store';

export default () => {
    const {translate} = useLocales();
    const {
        pricing,
    } = useSelector(({pricing}) => {
        return {
            pricing,
        };
    });

    useEffect(() => {
        const token = PubSub.subscribe('OPEN.PAYMENT.ITEM-DETAILS', (_, payload) => {
            const {
                isLifeTimeAutomationCredits,
                isLifeTimeCollaboratorSeats,
                isSubscriptionItem,
                product,
                checkout: {
                    invoiceUrl,
                },
            } = payload.item;

            let title = `${translate('pricing.subscriptions.label')}: ${pricing?.products?.[product]?.name}`;
            let Component = SubscriptionDetails;

            if (isLifeTimeCollaboratorSeats) {
                title = `${translate('pricing.otp.collaboratorSeats.label')}: ${pricing?.products?.[product]?.name} x ${payload.collaboratorItem.quantity}`;
                Component = CollaboratorSeatsDetails;
            }

            if (isLifeTimeAutomationCredits) {
                title = `${translate('pricing.otp.automationCredits.label')}: ${pricing?.products?.[product]?.name} x ${payload.creditItem.quantity}`;
                Component = AutomationCreditsDetails;
            }

            PubSub.publish('SHOW.DIALOG', {
                type: 'custom',
                dialogProps: {
                    isFullScreen: isSubscriptionItem,
                    title,
                },
                commit: (
                    <PrivilegedContent
                        isCovert>
                        {
                            invoiceUrl &&
                            <ExternalLink
                                label={translate('payment.confirmation.invoiceDetails')}
                                url={invoiceUrl}
                            />
                        }
                    </PrivilegedContent>
                ),
                children: (
                    <>
                        <Component
                            {...payload}
                        />
                    </>
                ),
            });
        });

        return () => {
            PubSub.unsubscribe(token);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pricing]);
};
