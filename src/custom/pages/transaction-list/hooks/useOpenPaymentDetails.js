import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import {
    Button,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tooltip,
} from '@material-ui/core';
import {Icon} from '@iconify/react';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import {invoiceStatuses} from '@w7-3/webeagle-resources/dist/config/account';
import PrivilegedContent from '../../../../guards/PrivilegedContent';
import useLocales from '../../../../hooks/useLocales';
import UserDate from '../../../components/UserDate';
import DefinitionList from '../../../components/utils/DefinitionList';
import Label from '../../../../components/Label';
import Price from '../../../components/payment/Price';
import ExternalLink from '../../../components/utils/ExternalLink';
import PaymentDetailsRow from '../PaymentDetailsRow';
import {useSelector} from '../../../../redux/store';
import {paymentStatusOptions} from '../../../../config/base';
import Scrollbar from '../../../../components/Scrollbar';

export default () => {
    const {translate} = useLocales();
    const {
        checkoutsList,
        pricing,
        transactionList,
    } = useSelector(({state, pricing}) => {
        const {
            checkoutsList,
            transactionList,
        } = state;

        return {
            checkoutsList,
            transactionList,
            pricing,
        };
    });

    useEffect(() => {
        const token = PubSub.subscribe('OPEN.PAYMENT.DETAILS', (_, payload) => {
            const {
                checkoutId,
            } = payload;
            const checkout = checkoutsList.find((item) => item.id === checkoutId);
            const {color} = paymentStatusOptions[checkout.paymentStatus];

            PubSub.publish('SHOW.DIALOG', {
                type: 'custom',
                dialogProps: {
                    isFullScreen: true,
                    title: (
                        `${translate('transactions.orderHistory.fields.invoice')}: ****${checkoutId.slice(-4)}`
                    ),
                },
                commit: (
                    <PrivilegedContent
                        isCovert>
                        {
                            checkout?.invoiceUrl &&
                            <ExternalLink
                                label={translate('payment.confirmation.invoiceDetails')}
                                url={checkout.invoiceUrl}
                            />
                        }
                    </PrivilegedContent>
                ),
                children: (
                    <>
                        <DefinitionList
                            dataList={[
                                {
                                    key: translate('transactions.orderHistory.fields.id'),
                                    node: (
                                        <CopyToClipboard text={checkout.id}>
                                            <Tooltip title={translate('copy')}>
                                                <Button
                                                    variant="text"
                                                    endIcon={<Icon
                                                        icon="eva:copy-fill"
                                                        width={40}
                                                        height={40}
                                                    />}
                                                    sx={{m: 0}}
                                                >
                                                    ****{checkout.id.slice(-4)}
                                                </Button>
                                            </Tooltip>
                                        </CopyToClipboard>
                                    ),
                                },
                                {
                                    key: translate('details'),
                                    node: (
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <Stack>
                                                <Stack direction="row" spacing={1}>
                                                    <div>
                                                        <UserDate
                                                            date={checkout.created}
                                                        />
                                                    </div>
                                                    <Label variant="filled" color={color}>
                                                        {translate(`transactions.payment.status.options.${checkout.paymentStatus}.label`)}
                                                    </Label>
                                                </Stack>
                                                {
                                                    [
                                                        invoiceStatuses.open.appName,
                                                        invoiceStatuses.uncollectible.appName,
                                                    ].includes(checkout.paymentStatus) && (
                                                        <ExternalLink
                                                            label={translate('transactions.payment.trigger.label')}
                                                            url={checkout.invoiceUrl}
                                                        />
                                                    )
                                                }
                                            </Stack>
                                        </Stack>
                                    ),
                                },
                                {
                                    key: translate('transactions.orderHistory.fields.amount'),
                                    node: (
                                        <Price
                                            variant="b"
                                            price={checkout.price}
                                            currency={checkout.currency}
                                        />
                                    ),
                                },
                            ]}
                        />
                        <TableContainer sx={{minWidth: 600, pt: 3}}>
                            <Scrollbar>
                                <Table size="medium" stickyHeader>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.createdOn')}
                                            </TableCell>
                                            <TableCell sx={{width: '20%'}}>
                                                {translate('transactions.license')}
                                            </TableCell>
                                            <TableCell>
                                                {translate('transactions.validity')}
                                            </TableCell>
                                            <TableCell align="center">
                                                {translate('transactions.status')}
                                            </TableCell>
                                            <TableCell sx={{maxWidth: 40}} />
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {transactionList
                                            .filter((item) => item.checkoutId === checkout.id)
                                            .map((item, index) => (
                                                <PaymentDetailsRow
                                                    key={item.id}
                                                    item={item}
                                                    isEven={index % 2 === 0}
                                                    pricing={pricing}
                                                />
                                            ))}
                                    </TableBody>
                                </Table>
                            </Scrollbar>
                        </TableContainer>
                    </>
                ),
            });
        });

        return () => {
            PubSub.unsubscribe(token);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pricing, checkoutsList, transactionList]);
};
