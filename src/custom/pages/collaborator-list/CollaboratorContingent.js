import {LinearProgress, Stack, Typography} from '@material-ui/core';
import PropTypes from 'prop-types';
import {connect} from 'react-redux';
import {fPercentageValue} from '../../../utils/formatTime';
import {getColor} from '../../utils/restContingent';
import useLocales from '../../../hooks/useLocales';

const mapStateToProps = ({state}) => {
    const {
        collaboratorList,
        collaboratorSeats,
    } = state;

    return {
        collaboratorList,
        collaboratorSeats,
    };
};

const CollaboratorContingent = ({
    collaboratorList,
    collaboratorSeats,
}) => {
    const {translate} = useLocales();
    const initialContingent = collaboratorSeats;
    const usedContingent = collaboratorList?.length || 0;
    const restContingent = initialContingent - usedContingent;
    const restPercentage = fPercentageValue(restContingent / initialContingent);
    const color = getColor(restPercentage);

    return (
        <Stack
            spacing={3}>
            <LinearProgress
                variant="determinate"
                value={restPercentage}
                color={color}/>
            <Typography sx={{ml: 'auto !important'}}>
                {translate('pricing.otp.collaboratorSeats.occupied', {
                    used: usedContingent,
                    total: initialContingent,
                })}
            </Typography>
        </Stack>
    );
};

CollaboratorContingent.propTypes = {
    collaboratorList: PropTypes.array,
    collaboratorSeats: PropTypes.number,
};

export default connect(mapStateToProps)(CollaboratorContingent);
