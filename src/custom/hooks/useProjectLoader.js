import {useEffect} from 'react';
import PubSub from 'pubsub-js';
import {useNavigate} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import useProjectsLoader from './useProjectsLoader';

export default ({projects, projectId}) => {
    const navigate = useNavigate();
    useProjectsLoader({projects});

    useEffect(() => {
        if (!projects) {
            return;
        }

        const project = projects?.[projectId];

        if (!project) {
            navigate(PATH_DASHBOARD.general.projects.root);
            return;
        }

        if (project?.latestBuild?.index > 0 && !project?.results?.[project?.latestBuild?.index]) {
            PubSub.publish('PROJECT_LOAD', {
                filterIdList: [projectId],
                includeDetails: true,
            });
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [projects]);

    return {
        project: projects?.[projectId],
    };
};
