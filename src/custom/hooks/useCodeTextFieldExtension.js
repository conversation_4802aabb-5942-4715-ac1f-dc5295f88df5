import {EditorState} from '@codemirror/state';
import {EditorView} from '@codemirror/view';

export const charLimitExtension = (limit) => {
    return EditorState.transactionFilter.of((tr) => {
        const newLen = tr.newDoc.length;

        // Allow transaction only if new doc is within limit
        if (newLen <= limit) {
            return tr;
        }

        // Block the transaction
        return [];
    });
};

export default ({characterLimit, setValue}) => {
    return {
        extensions: [
            charLimitExtension(characterLimit),
            EditorView.updateListener.of((viewUpdate) => {
                if (!viewUpdate.docChanged) {
                    return;
                }

                setValue(viewUpdate.state.doc.toString());
            }),
        ],
    };
};
