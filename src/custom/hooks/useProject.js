import {useNavigate} from 'react-router-dom';
import {PATH_DASHBOARD} from '@w7-3/webeagle-resources/dist/config/paths';
import {ACTIONS} from '@w7-3/webeagle-resources/dist/config/project';

export const useProject = () => {
    const navigate = useNavigate();

    return {
        goToProject: ({projectId}) => {
            navigate(`${PATH_DASHBOARD.general.projects.root}/${projectId}`);
        },
        goToProjectBuilds: ({projectId, buildId}) => {
            navigate(`${PATH_DASHBOARD.general.projects.root}/${projectId}/${buildId}`);
        },
        goToProjectEdit: ({projectId, step}) => {
            navigate(`${PATH_DASHBOARD.general.projects.root}/${projectId}?action=${ACTIONS.edit}${
                step ? `&step=${step}` : ''
            }`);
        },
    };
};
