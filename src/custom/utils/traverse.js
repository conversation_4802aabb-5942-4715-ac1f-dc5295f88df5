import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';

export const INFO_CODES_KEY_NAME = 'infoCodes';
export const infoCodeSeparator = '_';
export const getCodeName = (infoCode) => `${infoCode.level}${infoCodeSeparator}${infoCode.code}`;

export const traverse = (obj, func) => {
    Object.keys(obj).forEach((property) => {
        if (property !== INFO_CODES_KEY_NAME) {
            return;
        }

        func.apply(this, [property, obj[property]]);
        if (obj[property] !== null && typeof obj[property] === 'object') {
            traverse(obj[property], func, {});
        }
    });
};

export const isValidInfoCode = (infoCode) => {
    return typeof infoCode === 'object' && typeof infoCode.level === 'string' && typeof infoCode.code !== 'undefined';
};

export const extractInfoCodes = (
    data = {},
    errorCodesListingConfig = {},
) => {
    const infoCodes = [];
    const distinctList = [];

    const processInfoCode = (infoCode) => {
        if (!isValidInfoCode(infoCode)) {
            return;
        }

        const codeName = getCodeName(infoCode);

        if (distinctList.includes(codeName)) {
            return;
        }

        infoCodes.push(infoCode);
        distinctList.push(codeName);
    };

    traverse(
        data,
        (property, value) => {
            if (Array.isArray(value) && value?.length > 0) {
                if (property === INFO_CODES_KEY_NAME) {
                    value.forEach(processInfoCode);
                    return;
                }

                value.forEach((item) => {
                    if (!isNonEmptyArray(item[INFO_CODES_KEY_NAME])) {
                        return;
                    }

                    item[INFO_CODES_KEY_NAME].forEach(processInfoCode);
                });
            }
        },
        errorCodesListingConfig,
    );

    return infoCodes;
};
