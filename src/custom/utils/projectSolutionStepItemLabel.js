import {Stack, Typography} from '@material-ui/core';
import {ITEM_LABELLING_STRATEGY} from '@w7-3/webeagle-resources/dist/config/misc';
import {solutionStepTypes} from '@w7-3/webeagle-resources/dist/config/project';
import * as subscriptions from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';
import {Icon} from '@iconify/react';
import Label from '../../components/Label';
import InfoPopover from '../components/utils/InfoPopover';

export const getLabel = ({item, itemResult, translate}) => {
    if (itemResult?.stepType === solutionStepTypes.root) {
        return translate('project.wizardSteps.solutionsConfigs.steps.label');
    }

    if (itemResult?.stepType === solutionStepTypes.loopIteration) {
        return translate('loops.iteration', {iteration: Number.parseInt(itemResult.id.split('_').pop(), 10) + 1});
    }

    if (itemResult?.labellingData?.label) {
        return itemResult?.labellingData?.label;
    }

    return item?.labelStrategy === ITEM_LABELLING_STRATEGY.JS ?
        translate('workflowSteps.dynamicLabel') :
        (item?.label || item?.fallbackLabel);
};

export const getFailStopLabel = ({item, translate}) => {
    if (item?.stepType === solutionStepTypes.root) {
        return '';
    }

    if (item?.stepType === solutionStepTypes.loopIteration) {
        return '';
    }

    const i18nContext = {
        [solutionStepTypes.condition]: 'conditions',
        [solutionStepTypes.action]: 'actions',
        [solutionStepTypes.loop]: 'loops',
        [solutionStepTypes.solution]: `project.wizardSteps.solutionsConfigs.groups.${item.solution}`,
    }[item?.stepType];

    if (i18nContext) {
        return translate(`${i18nContext}.failStopLabel`)
    }

    return '';
};

export const getProjectSolutionStepItemLabel = (config) => {
    const {item, translate, showFailStop} = config;

    if (!item) {
        return '';
    }

    const {
        stepType,
        solution,
        abortOnError,
        fallbackLabel,
    } = item;
    const count = item?.stepList?.length || 0;
    const countLabel = count > 0 ?
        ` (${count} ${translate('workflowSteps.subItems.label', {count})})` :
        '';
    const title = item?.stepType === solutionStepTypes.solution ?
        subscriptions.packages.solutions.items[solution].name :
        translate(`workflowSteps.options.${stepType}.label`);

    return (
        <Stack spacing={1}>
            <Typography
                variant="body2">
                {`${getLabel(config) || fallbackLabel}${countLabel}`}
            </Typography>
            {
                ![
                    solutionStepTypes.root,
                    solutionStepTypes.loopIteration,
                ].includes(stepType) && (
                    <Stack
                        spacing={1}
                        direction="row"
                    >
                        <Label
                            color="primary"
                            sx={{alignSelf: 'flex-start', p: 3}}
                        >
                            {title}
                        </Label>
                        {
                            abortOnError && showFailStop && (
                                <InfoPopover
                                    label={(
                                        <Label
                                            color="warning"
                                            sx={{
                                                p: 3,
                                            }}
                                        >
                                            <Icon
                                                width={40}
                                                icon="eva:alert-triangle-outline"
                                                height={40}/>
                                            {translate('project.build.failStop.trigger')}
                                        </Label>
                                    )}
                                    hideIcon
                                >
                                    {getFailStopLabel({item, translate})}
                                </InfoPopover>
                            )
                        }
                    </Stack>
                )
            }
        </Stack>
    );
};
