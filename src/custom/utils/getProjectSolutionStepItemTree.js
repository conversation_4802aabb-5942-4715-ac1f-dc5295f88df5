import PubSub from 'pubsub-js';
import {Button, Stack} from '@material-ui/core';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import {getProjectSolutionStepItemLabel} from './projectSolutionStepItemLabel';

const getProjectSolutionStepItemTree = ({
    item,
    translate,
}) => {
    if (!item?.id) {
        return {};
    }

    const {id, stepList = []} = item;

    return {
        id,
        label: getProjectSolutionStepItemLabel({item, translate, showFailStop: true}),
        content: (
            <Stack
                direction={{
                    xs: 'column',
                    md: 'row',
                }}
                spacing={3}
                sx={{pl: 3, pb: 3}}
            >
                <Button
                    color="primary"
                    variant="outlined"
                    onClick={() => {
                        PubSub.publish('SHOW.PROJECT-STEP', item);
                    }}
                >
                    {translate('workflowSteps.view')}
                </Button>
            </Stack>
        ),
        nodeList: isNonEmptyArray(stepList) ? stepList.map((item) => (getProjectSolutionStepItemTree({
            item,
            translate,
        }))) : [],
    };
};

export default getProjectSolutionStepItemTree;
