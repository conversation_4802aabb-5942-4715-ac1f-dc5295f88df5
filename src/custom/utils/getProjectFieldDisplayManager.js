import {Box, Tooltip, Typography} from '@material-ui/core';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';
import Label from '../../components/Label';
import {fDateTimeHumanReadable} from '../../utils/formatTime';
import {getProjectStatusFlag} from './flags';
import ProjectBuildInfo from '../components/project/ProjectBuildInfo';

export default ({translate, clientKeyList = []}) => ({
    projectName: {
        active: clientKeyList?.includes('projectName') || !isNonEmptyArray(clientKeyList),
        q: (data) => {
            return data?.configData?.projectName;
        },
        getValue: (data) => data?.configData?.projectName,
        config: {
            label: translate('project.list.fields.name'),
            show: true,
            fixed: true,
            sx: {
                minWidth: '300px',
            },
        },
    },
    latestBuild: {
        active: clientKeyList?.includes('latestBuild') || !isNonEmptyArray(clientKeyList),
        q: (data) => {
            return (
                <ProjectBuildInfo
                    start={data?.latestBuild?.start}
                    state={data?.latestBuild?.state}
                    end={data?.latestBuild?.end}
                    latestBuildIndex={data?.latestBuild?.index}
                    projectId={data?.id}
                    showLatestBuildIndex
                    showDate
                    showState
                />
            );
        },
        getValue: (data) => data?.latestBuild?.start,
        config: {
            label: translate('project.build.latestBuild'),
            show: true,
            sx: {
                minWidth: '350px',
            },
        },
    },
    buildDuration: {
        active: clientKeyList?.includes('buildDuration') || !isNonEmptyArray(clientKeyList),
        q: (data) => {
            return (
                <ProjectBuildInfo
                    start={data?.latestBuild?.start}
                    end={data?.latestBuild?.end}
                    showDuration
                />
            );
        },
        getValue: (data) => data?.latestBuild?.end - data?.latestBuild?.start,
        config: {
            label: translate('project.list.fields.duration'),
            show: true,
            sx: {
                minWidth: '60px',
            },
        },
    },
    buildVendor: {
        active: clientKeyList?.includes('buildVendor') || !isNonEmptyArray(clientKeyList),
        q: (data, showFullDescription) => {
            const vendor = data?.latestBuild?.vendor;

            if (!vendor) {
                return (
                    <Typography sx={{color: 'text.secondary'}}>
                        {translate('notAvailable')}
                    </Typography>
                );
            }

            const fullDescription = translate(`project.build.trigger.vendors.${vendor}.description`);

            return (
                <Tooltip title={
                    showFullDescription ? '' :
                        fullDescription
                }>
                    <Box component="span" sx={{m: '0 !important', display: 'flex', alignItems: 'center'}}>
                        <Label color="default">
                            {
                                showFullDescription ?
                                    fullDescription :
                                    translate(`project.build.trigger.vendors.${vendor}.label`)
                            }
                        </Label>
                    </Box>
                </Tooltip>
            );
        },
        getValue: (data) => data?.latestBuild?.vendor,
        config: {
            label: translate('project.build.trigger.label'),
            show: false,
        },
    },
    solution: {
        active: clientKeyList?.includes('solution') || !isNonEmptyArray(clientKeyList),
        q: (data) => {
            return translate(`project.wizardSteps.solutionsConfigs.groups.${data?.configData?.solution?.key}.label`);
        },
        getValue: (data) => translate(`project.wizardSteps.solutionsConfigs.groups.${data?.configData?.solution?.key}.label`),
        config: {
            label: translate('project.list.fields.solution'),
            show: true,
        },
    },
    device: {
        active: clientKeyList?.includes('device') || false,
        q: (data) => {
            return data?.configData?.target?.device?.name;
        },
        getValue: (data) => data?.configData?.target?.device?.name,
        config: {
            label: translate('project.wizardSteps.target.device.label'),
            show: true,
        },
    },
    creator: {
        active: clientKeyList?.includes('creator') || false,
        q: (data) => data?.author?.email,
        getValue: (data) => data?.author?.email,
        config: {
            label: translate('project.list.fields.creator'),
            show: true,
        },
    },
    created: {
        active: clientKeyList?.includes('created') || false,
        q: (data) => fDateTimeHumanReadable(data.created),
        getValue: (data) => data.created,
        config: {
            label: translate('project.list.fields.created'),
            show: true,
            sx: {
                minWidth: '300px',
            },
        },
    },
    checkType: {
        active: clientKeyList?.includes('checkType') || false,
        getValue: (data) => data?.configData?.target?.checkType,
        q: (data) => (
            translate(`project.wizardSteps.target.checkType.groups.${data?.configData?.target?.checkType}.label`)
        ),
        config: {
            label: translate('project.wizardSteps.target.checkType.mode'),
            show: true,
        },
    },
    urls: {
        active: clientKeyList?.includes('urls') || false,
        q: (data) => (
            data?.configData?.target?.urls.map(({url}) => url).join(', ')
        ),
        getValue: (data) => data?.configData?.target?.urls.map(({url}) => url).length,
        config: {
            label: translate('urls'),
            show: true,
        },
    },
    state: {
        active: clientKeyList?.includes('state') || !isNonEmptyArray(clientKeyList),
        getValue: (data) => data?.state,
        q: (data) => (
            <Label
                color={getProjectStatusFlag(data?.state)}
                sx={{p: 3}}
            >
                {translate(`project.stateTypes.${data?.state}.label`)}
            </Label>
        ),
        config: {
            label: translate('project.list.fields.state'),
            show: true,
        },
    },
});
