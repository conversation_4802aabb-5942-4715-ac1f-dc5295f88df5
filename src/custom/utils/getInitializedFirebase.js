import firebase from 'firebase/compat/app';
import 'firebase/compat/auth';
import 'firebase/compat/firestore';
import 'firebase/compat/functions';
import 'firebase/compat/storage';
import {firebaseConfig, globalConfig} from '../../config/setup';
import firebaseJSON from '../../../firebase.json';

if (!firebase.apps.length) {
    firebase.initializeApp(firebaseConfig);

    if (globalConfig.isDev) {
        firebase.firestore()
            .useEmulator('localhost', firebaseJSON.emulators.firestore.port);
        firebase.auth()
            .useEmulator(`http://localhost:${firebaseJSON.emulators.auth.port}/auth`);
        firebase.functions()
            .useEmulator('localhost', firebaseJSON.emulators.functions.port);
        firebase.storage()
            .useEmulator('localhost', firebaseJSON.emulators.storage.port);
    }

    if (!globalConfig.isDev) {
        firebase.firestore();
        firebase.auth();
        firebase.functions();
        firebase.storage();
    }
}

firebase.auth().languageCode = localStorage.getItem('i18nextLng');

export default firebase;
