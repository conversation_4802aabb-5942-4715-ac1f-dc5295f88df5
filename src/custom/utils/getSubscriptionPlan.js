import * as starterPackage from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/starterPackage';
import * as advancedPackage from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/advancedPackage';
import * as premiumPackage from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions/premiumPackage';
import {categories} from '@w7-3/webeagle-resources/dist/config/catalog/subscriptions';

export const plans = {
    // [categories.prePaidPackage.appName]: prePaidPackage,
    [categories.starterPackage.appName]: starterPackage,
    [categories.advancedPackage.appName]: advancedPackage,
    [categories.premiumPackage.appName]: premiumPackage,
};

export const getSubscriptionPlan = (appName) => {
    return plans[appName];
};
