const {serverlessAPI} = require('@w7-3/webeagle-resources/dist/config/api');
const {createProxyMiddleware} = require('http-proxy-middleware');

module.exports = (app) => {
    app.use(
        [
            serverlessAPI.api.root,
            serverlessAPI.cdnApi.root,
            serverlessAPI.checkoutApi.root,
            serverlessAPI.adminApi.root,
            serverlessAPI.managementApi.root,
            serverlessAPI.assistantApi.root,
        ],
        createProxyMiddleware({
            target: 'http://localhost:3101',
            changeOrigin: false,
        })
    );
};
