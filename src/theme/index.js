import PropTypes from 'prop-types';

import {useMemo} from 'react';
import {CssBaseline} from '@material-ui/core';
import {createTheme, StyledEngineProvider, ThemeProvider} from '@material-ui/core/styles';
import useSettings from '../hooks/useSettings';
import shape from './shape';
import palette from './palette';
import typography from './typography';
import breakpoints from './breakpoints';
import zIndex from './zIndex';
import animations, {AnimationStyles} from './animations';
import GlobalStyles from './globalStyles';
import componentsOverride from './overrides';
import shadows, {customShadows} from './shadows';

// ----------------------------------------------------------------------

ThemeConfig.propTypes = {
    children: PropTypes.node,
};

export default function ThemeConfig({children}) {
    const {themeMode, themeDirection} = useSettings();
    const isLight = themeMode === 'light';
    const themeOptions = useMemo(
        () => ({
            palette: isLight ? {...palette.light, mode: 'light'} : {...palette.dark, mode: 'dark'},
            shape,
            typography,
            breakpoints,
            zIndex,
            animations,
            direction: themeDirection,
            shadows: isLight ? shadows.light : shadows.dark,
            customShadows: isLight ? customShadows.light : customShadows.dark,
        }),
        [isLight, themeDirection],
    );
    const theme = createTheme(themeOptions);

    theme.components = componentsOverride(theme);

    return (
        <StyledEngineProvider injectFirst>
            <ThemeProvider theme={theme}>
                <CssBaseline/>
                <GlobalStyles/>
                <AnimationStyles/>
                {children}
            </ThemeProvider>
        </StyledEngineProvider>
    );
}
