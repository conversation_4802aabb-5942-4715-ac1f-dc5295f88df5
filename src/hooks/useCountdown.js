import {useEffect, useState} from 'react';
import {getI18nDuration} from '@w7-3/webeagle-resources/dist/libs/date';

export default function useCountdown(targetTimestamp) {
    const [countdown, setCountdown] = useState();

    useEffect(() => {
        const interval = setInterval(() => {
            setCountdown(getI18nDuration(targetTimestamp - Date.now()));
        }, 1000);

        // Cleanup the interval on component unmount
        return () => clearInterval(interval);
    }, [targetTimestamp]);

    return {countdown};
}
