import {useMemo} from 'react';
import {isNonEmptyArray} from '@w7-3/webeagle-resources/dist/libs/validators';

export const useAccountNotifications = (accountNotificationList) => {
    return useMemo(
        () => {
            const newNotifications = [];
            const oldNotifications = [];

            if (isNonEmptyArray(accountNotificationList)) {
                accountNotificationList.forEach((item) => {
                    if (item.isNew) {
                        newNotifications.push(item);
                        return;
                    }

                    oldNotifications.push(item);
                });
            }

            newNotifications.sort((a, b) => new Date(b.ts) - new Date(a.ts));
            oldNotifications.sort((a, b) => new Date(b.ts) - new Date(a.ts));

            return {
                newNotifications,
                oldNotifications,
                totalCount: accountNotificationList?.length,
                newCount: newNotifications.length,
                oldCount: oldNotifications.length,
            };
        },
        [accountNotificationList]);
};
