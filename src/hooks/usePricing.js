import Price from '../custom/components/payment/Price';
import OneTimePlanPrice from '../custom/components/payment/OneTimePlanPrice';
import {useSelector} from '../redux/store';

export const pricingItemGroupConfig = [{
    listType: 'subscriptionsCartItemList',
    i18nContext: 'pricing.subscriptions',
    suffixKey: 'pricing.subscriptions.intervals.monthlyX1.perLabel',
    Component: Price,
}, {
    listType: 'collaboratorSeats',
    i18nContext: 'pricing.otp',
    Component: OneTimePlanPrice,
    isOneTime: true,
}, {
    listType: 'automationCredits',
    i18nContext: 'pricing.otp',
    Component: OneTimePlanPrice,
    isOneTime: true,
}];

export default () => {
    return useSelector(({pricing}) => {
        const itemGroups = {};
        const cartItemList = [];
        let totalPrice = 0;

        pricingItemGroupConfig.forEach((group) => {
            const list = pricing[group.listType] || [];
            list.forEach(({
                priceData,
                quantity,
                autoRenew,
            }) => {
                cartItemList.push({
                    priceId: priceData.id,
                    quantity,
                    autoRenew,
                });

                totalPrice += quantity * priceData?.unit_amount;
            });

            itemGroups[group.listType] = {
                group,
                list,
                totalPrice,
            };
        });

        return {
            pricing,
            itemGroups,
            totalPrice,
            cartItemList,
        };
    });
};
