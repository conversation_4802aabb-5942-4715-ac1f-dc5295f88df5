import {useTranslation} from 'react-i18next';
import {deDE, enUS, frFR} from '@material-ui/core/locale';

const LANGS = [
    {
        label: 'English',
        value: 'en',
        systemValue: enUS,
        icon: '/static/icons/ic_flag_en.svg',
    },
    {
        label: 'Deutsch',
        value: 'de',
        systemValue: deDE,

        icon: '/static/icons/ic_flag_de.svg',
    },
    {
        label: 'Français',
        value: 'fr',
        systemValue: frFR,
        icon: '/static/icons/ic_flag_fr.svg',
    },
];

export const DEFAULT_LANG = LANGS[1];

export default function useLocales() {
    const {
        i18n,
        t: translate,
    } = useTranslation();
    const langStorage = localStorage.getItem('i18nextLng');
    const currentLang = LANGS.find((_lang) => _lang.value === langStorage) || DEFAULT_LANG;
    const onChangeLang = (newlang) => {
        i18n.changeLanguage(newlang);
    };

    return {
        onChangeLang,
        translate,
        currentLang,
        allLang: LANGS,
    };
}
