import i18next from 'i18next';
import {initReactI18next} from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import enLocales from '@w7-3/webeagle-resources/dist/i18n/locales/en';
import deLocales from '@w7-3/webeagle-resources/dist/i18n/locales/de';
import frLocales from '@w7-3/webeagle-resources/dist/i18n/locales/fr';
import {LANGUAGES, URL_PARAM_LANGUAGE, DEFAULT_LANGUAGE} from '@w7-3/webeagle-resources/dist/config/languages';

const lngKV = window.location.search.substr(1)
    .split('&')
    .filter((keyValue) => {
        return keyValue.split('=')?.[0] === URL_PARAM_LANGUAGE;
    });

const lng = lngKV?.[0]?.split('=')?.[1];

i18next
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
        resources: {
            [LANGUAGES.en]: {translations: enLocales},
            [LANGUAGES.de]: {translations: deLocales},
            [LANGUAGES.fr]: {translations: frLocales},
        },
        lng: lng || localStorage.getItem('i18nextLng') || DEFAULT_LANGUAGE,
        fallbackLng: DEFAULT_LANGUAGE,
        debug: false,
        ns: ['translations'],
        defaultNS: 'translations',
        interpolation: {
            escapeValue: false,
        },
        addMissing: true,
    });

i18next.on('missingKey', (lng, namespace, key, fallbackValue) => {
    console.warn(lng, namespace, key, fallbackValue);
})

export default i18next;
