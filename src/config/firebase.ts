export const operators = {
    // LESS_THAN: '<', @todo: activate after auto indexing is enabled
    // LESS_THAN_OR_EQUAL: '<=', @todo: activate after auto indexing is enabled
    EQUAL: '==',
    // NOT_EQUAL: '!=', @todo: activate after auto indexing is enabled
    // GREATER_THAN: '>', @todo: activate after auto indexing is enabled
    // GREATER_THAN_OR_EQUAL: '>=', @todo: activate after auto indexing is enabled
    ARRAY_CONTAINS: 'array-contains',
    IN: 'in',
    // NOT_IN: 'not-in', @todo: activate after auto indexing is enabled
    ARRAY_CONTAINS_ANY: 'array-contains-any',
} as const;

export const conditionOps = Object.values(operators);
