export const keyActions = {
    down: 'down',
    press: 'press',
    up: 'up',
    type: 'type',
} as const;

export default {
    '{Escape}': {
        key: '{Escape}',
        code: 'Escape',
        display: 'esc ⎋',
        allowDown: true,
    },
    '{f1}': {
        key: '{f1}',
        code: 'F1',
        display: 'F1',
        allowDown: true,
    },
    '{f2}': {
        key: '{f2}',
        code: 'F2',
        display: 'F2',
        allowDown: true,
    },
    '{f3}': {
        key: '{f3}',
        code: 'F3',
        display: 'F3',
        allowDown: true,
    },
    '{f4}': {
        key: '{f4}',
        code: 'F4',
        display: 'F4',
        allowDown: true,
    },
    '{f5}': {
        key: '{f5}',
        code: 'F5',
        display: 'F5',
        allowDown: true,
    },
    '{f6}': {
        key: '{f6}',
        code: 'F6',
        display: 'F6',
        allowDown: true,
    },
    '{f7}': {
        key: '{f7}',
        code: 'F7',
        display: 'F7',
        allowDown: true,
    },
    '{f8}': {
        key: '{f8}',
        code: 'F8',
        display: 'F8',
        allowDown: true,
    },
    '{f9}': {
        key: '{f9}',
        code: 'F9',
        display: 'F9',
        allowDown: true,
    },
    '{f10}': {
        key: '{f10}',
        code: 'F10',
        display: 'F10',
        allowDown: true,
    },
    '{f11}': {
        key: '{f11}',
        code: 'F11',
        display: 'F11',
        allowDown: true,
    },
    '{f12}': {
        key: '{f12}',
        code: 'F12',
        display: 'F12',
        allowDown: true,
    },
    '`': {
        key: '`',
        code: '`',
        display: '`',
    },
    1: {
        key: '1',
        code: '1',
        display: '1',
    },
    2: {
        key: '2',
        code: '2',
        display: '2',
    },
    3: {
        key: '3',
        code: '3',
        display: '3',
    },
    4: {
        key: '4',
        code: '4',
        display: '4',
    },
    5: {
        key: '5',
        code: '5',
        display: '5',
    },
    6: {
        key: '6',
        code: '6',
        display: '6',
    },
    7: {
        key: '7',
        code: '7',
        display: '7',
    },
    8: {
        key: '8',
        code: '8',
        display: '8',
    },
    9: {
        key: '9',
        code: '9',
        display: '9',
    },
    0: {
        key: '0',
        code: '0',
        display: '0',
    },
    '-': {
        key: '-',
        code: '-',
        display: '-',
    },
    '=': {
        key: '=',
        code: '=',
        display: '=',
    },
    '{backspace}': {
        key: '{backspace}',
        code: 'Backspace',
        display: 'backspace ⌫',
    },
    '{tab}': {
        key: '{tab}',
        code: 'Tab',
        display: 'tab ⇥',
    },
    q: {
        key: 'q',
        code: 'q',
        display: 'q',
    },
    w: {
        key: 'w',
        code: 'w',
        display: 'w',
    },
    e: {
        key: 'e',
        code: 'e',
        display: 'e',
    },
    r: {
        key: 'r',
        code: 'r',
        display: 'r',
    },
    t: {
        key: 't',
        code: 't',
        display: 't',
    },
    y: {
        key: 'y',
        code: 'y',
        display: 'y',
    },
    u: {
        key: 'u',
        code: 'u',
        display: 'u',
    },
    i: {
        key: 'i',
        code: 'i',
        display: 'i',
    },
    o: {
        key: 'o',
        code: 'o',
        display: 'o',
    },
    p: {
        key: 'p',
        code: 'p',
        display: 'p',
    },
    '[': {
        key: '[',
        code: '[',
        display: '[',
    },
    ']': {
        key: ']',
        code: ']',
        display: ']',
    },
    '\\': {
        key: '\\',
        code: '\\',
        display: '\\',
    },
    '{capslock}': {
        key: '{capslock}',
        code: 'CapsLock',
        display: 'caps lock ⇪',
    },
    a: {
        key: 'a',
        code: 'a',
        display: 'a',
    },
    s: {
        key: 's',
        code: 's',
        display: 's',
    },
    d: {
        key: 'd',
        code: 'd',
        display: 'd',
    },
    f: {
        key: 'f',
        code: 'f',
        display: 'f',
    },
    g: {
        key: 'g',
        code: 'g',
        display: 'g',
    },
    h: {
        key: 'h',
        code: 'h',
        display: 'h',
    },
    j: {
        key: 'j',
        code: 'j',
        display: 'j',
    },
    k: {
        key: 'k',
        code: 'k',
        display: 'k',
    },
    l: {
        key: 'l',
        code: 'l',
        display: 'l',
    },
    ';': {
        key: ';',
        code: ';',
        display: ';',
    },
    '{enter}': {
        key: '{enter}',
        code: 'Enter',
        display: 'enter ↵',
        displayMobile: 'return',
    },
    '{shiftleft}': {
        key: '{shiftleft}',
        code: 'Shift',
        display: 'shift ⇧',
        allowDown: true,
    },
    z: {
        key: 'z',
        code: 'z',
        display: 'z',
    },
    x: {
        key: 'x',
        code: 'x',
        display: 'x',
    },
    c: {
        key: 'c',
        code: 'c',
        display: 'c',
    },
    v: {
        key: 'v',
        code: 'v',
        display: 'v',
    },
    b: {
        key: 'b',
        code: 'b',
        display: 'b',
    },
    n: {
        key: 'n',
        code: 'n',
        display: 'n',
    },
    m: {
        key: 'm',
        code: 'm',
        display: 'm',
    },
    ',': {
        key: ',',
        code: ',',
        display: ',',
    },
    '.': {
        key: '.',
        code: '.',
        display: '.',
    },
    '/': {
        key: '/',
        code: '/',
        display: '/',
    },
    '{shiftright}': {
        key: '{shiftright}',
        code: 'Shift',
        display: 'shift ⇧',
        allowDown: true,
    },
    '{controlleft}': {
        key: '{controlleft}',
        code: 'Control',
        display: 'ctrl ⌃',
        allowDown: true,
    },
    '{altleft}': {
        key: '{altleft}',
        code: 'Alt',
        display: 'alt ⌥',
        allowDown: true,
    },
    '{metaleft}': {
        key: '{metaleft}',
        code: 'Meta',
        display: 'cmd ⌘',
        allowDown: true,
    },
    '{space}': {
        key: '{space}',
        code: '{space}',
        display: ' ',
        allowDown: true,
    },
    '{metaright}': {
        key: '{metaright}',
        code: 'Meta',
        display: 'cmd ⌘',
        allowDown: true,
    },
    '{altright}': {
        key: '{altright}',
        code: 'Alt',
        display: 'alt ⌥',
        displayMobile: '.?123',
        allowDown: true,
    },
    '': {
        key: '',
        code: '',
        display: '',
    },
    '~': {
        key: '~',
        code: '~',
        display: '~',
    },
    '!': {
        key: '!',
        code: '!',
        display: '!',
    },
    '@': {
        key: '@',
        code: '@',
        display: '@',
    },
    '#': {
        key: '#',
        code: '#',
        display: '#',
    },
    $: {
        key: '$',
        code: '$',
        display: '$',
    },
    '%': {
        key: '%',
        code: '%',
        display: '%',
    },
    '^': {
        key: '^',
        code: '^',
        display: '^',
    },
    '&': {
        key: '&',
        code: '&',
        display: '&',
    },
    '*': {
        key: '*',
        code: '*',
        display: '*',
    },
    '(': {
        key: '(',
        code: '(',
        display: '(',
    },
    ')': {
        key: ')',
        code: ')',
        display: ')',
    },
    _: {
        key: '_',
        code: '_',
        display: '_',
    },
    '+': {
        key: '+',
        code: '+',
        display: '+',
    },
    /*
    '{backspace}': {
        key: '{backspace}',
        code: '{backspace}',
        display: '{backspace}',
    },
    '{tab}': {
        key: '{tab}',
        code: '{tab}',
        display: '{tab}',
    },
     */
    Q: {
        key: 'Q',
        code: 'Q',
        display: 'Q',
    },
    W: {
        key: 'W',
        code: 'W',
        display: 'W',
    },
    E: {
        key: 'E',
        code: 'E',
        display: 'E',
    },
    R: {
        key: 'R',
        code: 'R',
        display: 'R',
    },
    T: {
        key: 'T',
        code: 'T',
        display: 'T',
    },
    Y: {
        key: 'Y',
        code: 'Y',
        display: 'Y',
    },
    U: {
        key: 'U',
        code: 'U',
        display: 'U',
    },
    I: {
        key: 'I',
        code: 'I',
        display: 'I',
    },
    O: {
        key: 'O',
        code: 'O',
        display: 'O',
    },
    P: {
        key: 'P',
        code: 'P',
        display: 'P',
    },
    '{': {
        key: '{',
        code: '{',
        display: '{',
    },
    '}': {
        key: '}',
        code: '}',
        display: '}',
    },
    '|': {
        key: '|',
        code: '|',
        display: '|',
    },
    /*
    '{capslock}': {
        key: '{capslock}',
        code: '{capslock}',
        display: '{capslock}',
    },
     */
    A: {
        key: 'A',
        code: 'A',
        display: 'A',
    },
    S: {
        key: 'S',
        code: 'S',
        display: 'S',
    },
    D: {
        key: 'D',
        code: 'D',
        display: 'D',
    },
    F: {
        key: 'F',
        code: 'F',
        display: 'F',
    },
    G: {
        key: 'G',
        code: 'G',
        display: 'G',
    },
    H: {
        key: 'H',
        code: 'H',
        display: 'H',
    },
    J: {
        key: 'J',
        code: 'J',
        display: 'J',
    },
    K: {
        key: 'K',
        code: 'K',
        display: 'K',
    },
    L: {
        key: 'L',
        code: 'L',
        display: 'L',
    },
    ':': {
        key: ':',
        code: ':',
        display: ':',
    },
    '"': {
        key: '"',
        code: '"',
        display: '"',
    },
    Z: {
        key: 'Z',
        code: 'Z',
        display: 'Z',
    },
    X: {
        key: 'X',
        code: 'X',
        display: 'X',
    },
    C: {
        key: 'C',
        code: 'C',
        display: 'C',
    },
    V: {
        key: 'V',
        code: 'V',
        display: 'V',
    },
    B: {
        key: 'B',
        code: 'B',
        display: 'B',
    },
    N: {
        key: 'N',
        code: 'N',
        display: 'N',
    },
    M: {
        key: 'M',
        code: 'M',
        display: 'M',
    },
    '<': {
        key: '<',
        code: '<',
        display: '<',
    },
    '>': {
        key: '>',
        code: '>',
        display: '>',
    },
    '?': {
        key: '?',
        code: '?',
        display: '?',
    },
    // Control
    '{prtscr}': {
        key: '{prtscr}',
        code: 'Print',
        display: 'print',
    },
    '{scrolllock}': {
        key: '{scrolllock}',
        code: 'ScrollLock',
        display: 'scroll',
    },
    '{pause}': {
        key: '{pause}',
        code: 'Pause',
        display: 'pause',
    },
    '{insert}': {
        key: '{insert}',
        code: 'Insert',
        display: 'ins',
    },
    '{home}': {
        key: '{home}',
        code: 'Home',
        display: 'home ⌂',
    },
    '{pageup}': {
        key: '{pageup}',
        code: 'PageUp',
        display: '⇞',
    },
    '{delete}': {
        key: '{delete}',
        code: 'Delete',
        display: 'delete ⌦',
    },
    '{end}': {
        key: '{end}',
        code: 'End',
        display: 'end',
    },
    '{pagedown}': {
        key: '{pagedown}',
        code: 'PageDown',
        display: '⇟',
    },
    // Arrows
    '{arrowup}': {
        key: '{arrowup}',
        code: 'ArrowUp',
        display: '↑',
    },
    '{arrowleft}': {
        key: '{arrowleft}',
        code: 'ArrowLeft',
        display: '←',
    },
    '{arrowdown}': {
        key: '{arrowdown}',
        code: 'ArrowDown',
        display: '↓',
    },
    '{arrowright}': {
        key: '{arrowright}',
        code: 'ArrowRight',
        display: '→',
    },
    '{numlock}': {
        key: '{numlock}',
        code: 'Num Lock',
        display: 'Num Lock',
    },
    '{numpaddivide}': {
        key: '{numpaddivide}',
        code: '/',
        display: 'Num Lock: /',
    },
    '{numpadmultiply}': {
        key: '{numpadmultiply}',
        code: 'y',
        display: 'Num Lock: *',
    },
    '{numpad7}': {
        key: '{numpad7}',
        code: '7',
        display: 'Num Lock: 7',
    },
    '{numpad8}': {
        key: '{numpad8}',
        code: '8',
        display: 'Num Lock: 8',
    },
    '{numpad9}': {
        key: '{numpad9}',
        code: '9',
        display: 'Num Lock: 9',
    },
    '{numpad4}': {
        key: '{numpad4}',
        code: '4',
        display: 'Num Lock: 4',
    },
    '{numpad5}': {
        key: '{numpad5}',
        code: '5',
        display: 'Num Lock: 5',
    },
    '{numpad6}': {
        key: '{numpad6}',
        code: '6',
        display: 'Num Lock: 6',
    },
    '{numpad1}': {
        key: '{numpad1}',
        code: '1',
        display: 'Num Lock: 1',
    },
    '{numpad2}': {
        key: '{numpad2}',
        code: '2',
        display: 'Num Lock: 2',
    },
    '{numpad3}': {
        key: '{numpad3}',
        code: '3',
        display: 'Num Lock: 3',
    },
    '{numpad0}': {
        key: '{numpad0}',
        code: '0',
        display: 'Num Lock: 0',
    },
    '{numpaddecimal}': {
        key: '{numpaddecimal}',
        code: '.',
        display: 'Num Lock: .',
    },
    '{numpadsubtract}': {
        key: '{numpadsubtract}',
        code: '-',
        display: 'Num Lock: -',
    },
    '{numpadadd}': {
        key: '{numpadadd}',
        code: '+',
        display: 'Num Lock: +',
    },
    '{numpadenter}': {
        key: '{numpadenter}',
        code: 'Enter',
        display: 'Num Lock: Enter',
    },
    '{bksp}': {
        key: '{bksp}',
        code: 'Backspace',
        display: '⌫',
    },
    '{shift}': {
        key: '{shift}',
        code: 'Shift',
        display: '⇧',
    },
    '{alt}': {
        key: '{alt}',
        code: 'Alt',
        display: '.?123',
        isVoid: true,
    },
    '{smileys}': {
        key: '{smileys}',
        code: 'Smileys',
        display: '\uD83D\uDE03',
        isVoid: true,
    },
    '{back}': {
        key: '{back}',
        code: 'Back',
        display: '⇦',
        isVoid: true,
    },
    '{shiftactivated}': {
        key: '{shiftactivated}',
        code: 'shiftactivated',
        display: '⇧',
        isVoid: true,
    },
    '{default}': {
        key: '{default}',
        code: 'Default',
        display: 'ABC',
        isVoid: true,
    },
} as const;
