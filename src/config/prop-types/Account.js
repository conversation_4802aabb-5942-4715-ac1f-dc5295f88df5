import PropTypes from 'prop-types';
import {requestOptions} from '@w7-3/webeagle-resources/dist/config/contactFormRequests';

export const DemoRequestType = PropTypes.shape({
    applicant: PropTypes.string,
    id: PropTypes.string,
    ts: PropTypes.number,
    approvedTs: PropTypes.number,
    activatedTs: PropTypes.number,
    completedTs: PropTypes.number,
    state: PropTypes.oneOf(Object.keys(requestOptions.demo.states)),
    declinationReason: PropTypes.string,
    message: PropTypes.string,
});
