export const NOTIFICATION_TYPES = {
    ACCOUNT_UPDATES: {
        type: 'ACCOUNT_UPDATES',
        subTypes: {
            AUTOMATION_CREDITS_RESET: 'AUTOMATION_CREDITS_RESET',
            AUTOMATION_CREDITS_RESET_PENDING: 'AUTOMATION_CREDITS_RESET_PENDING',
            DOMAIN_BLACKLISTED: 'DOMAIN_BLACKLISTED',
            DOMAIN_WHITELISTED: 'DOMAIN_WHITELISTED',
            REGISTRATION_COMPLETED: 'REGISTRATION_COMPLETED',
            STATE_CHANGED: 'STATE_CHANGED',
        },
    },
    PAYMENT_UPDATES: {
        type: 'PAYMENT_UPDATES',
        subTypes: {
            PAYMENT_FAILED: 'PAYMENT_FAILED',
        },
    },
    PROJECT_UPDATES: {
        type: 'PROJECT_UPDATES',
        subTypes: {
            PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST: 'PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST',
        },
    },
    PROJECT_BUILD_UPDATES: {
        type: 'PROJECT_BUILD_UPDATES',
        subTypes: {
            BUILD_SKIPPED_DUE_TO_OVERAGE: 'BUILD_SKIPPED_DUE_TO_OVERAGE',
            BUILD_COMPLETED: 'BUILD_COMPLETED',
        },
    },
    REQUEST_UPDATES: {
        type: 'REQUEST_UPDATES',
        subTypes: {
            DEMO_REQUEST_APPROVED: 'DEMO_REQUEST_APPROVED',
            DEMO_REQUEST_DECLINED: 'DEMO_REQUEST_DECLINED',
            DEMO_REQUEST_RECEIVED: 'DEMO_REQUEST_RECEIVED',
            GENERIC_REQUEST_RECEIVED: 'GENERIC_REQUEST_RECEIVED',
        },
    },
    USER_UPDATES: {
        subTypes: {
            DELETED_SELF: 'DELETED_SELF',
            IS_INVITED_AS_COLLABORATOR: 'IS_INVITED_AS_COLLABORATOR',
            IS_REMOVED_AS_COLLABORATOR: 'IS_REMOVED_AS_COLLABORATOR',
        },
        type: 'USER_UPDATES',
    },
} as const;

export const NOTIFICATION_METHODS = {
    HYBRID: 'HYBRID',
    EMAIL: 'EMAIL',
    WEBHOOK: 'WEBHOOK',
} as const;
