import {index} from './specs';

export const paramNames = {
    breakCondition: {
        fe: '{index}',
        be: {
            index: 'index',
        },
        spec: {
            index,
        },
    },
} as const;

export default {
    selector: {
        value: 'selector',
    },
    range: {
        value: 'range',
    },
    js: {
        value: 'js',
    },
    jsList: {
        value: 'jsList',
    },
    customList: {
        value: 'customList',
    },
} as const;
