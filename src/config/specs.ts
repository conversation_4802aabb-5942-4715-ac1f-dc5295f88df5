export enum resourceTypes {
    document = 'document',
    stylesheet = 'stylesheet',
    image = 'image',
    media = 'media',
    font = 'font',
    script = 'script',
    texttrack = 'texttrack',
    xhr = 'xhr',
    fetch = 'fetch',
    eventsource = 'eventsource',
    websocket = 'websocket',
    manifest = 'manifest',
    other = 'other',
}

export const typeSpecs = {
    Object: {
        description: 'Object',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object',
    },
    String: {
        description: 'String',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String',
    },
    Number: {
        description: 'Number',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number',
    },
    Boolean: {
        description: 'Boolean',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean',
    },
    Array: {
        description: 'Array',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array',
    },
    Null: {
        description: 'Null',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/null',
    },
    Undefined: {
        description: 'Undefined',
        link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/undefined',
    },
    NodeList: {
        description: 'NodeList',
        link: 'https://developer.mozilla.org/en-US/docs/Web/API/NodeList',
    },
    Cookie: {
        description: 'Cookie',
        link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies',
        properties: {
            name: {
                type: 'String',
                description: 'Name of the cookie',
            },
            value: {
                type: 'String',
                description: 'Value of the cookie',
            },
            domain: {
                type: 'String',
                description: 'Domain of the cookie',
            },
            expires: {
                type: 'Number',
                description: 'Expiration time of the cookie',
            },
            httpOnly: {
                type: 'boolean',
                description: 'Is the cookie HTTP only',
            },
            secure: {
                type: 'boolean',
                description: 'Is the cookie secure',
            },
            sameSite: {
                type: ['None', 'Lax', 'Strict'],
                description: 'Same site of the cookie',
            },
        },
    },
    SecurityDetails: {
        description: 'Security details of the connection',
        link: 'https://developer.mozilla.org/en-US/docs/Web/Security/Public_Key_Pinning',
        properties: {
            subject: {
                type: 'String',
                description: 'Subject of the certificate',
            },
            issuer: {
                type: 'String',
                description: 'Issuer of the certificate',
            },
            validFrom: {
                type: 'Number',
                description: 'Valid from time of the certificate',
            },
            validTo: {
                type: 'Number',
                description: 'Valid to time of the certificate',
            },
        },
    },
    ServerAddr: {
        description: 'Server details',
        link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Servers',
        properties: {
            ipAddress: {
                type: 'String',
                description: 'IP address of the server',
            },
            port: {
                type: 'Number',
                description: 'Port of the server',
            },
        },
    },
    RequestTiming: {
        description: 'Timing details of the request',
        link: 'https://developer.mozilla.org/en-US/docs/Web/Performance/Navigation_and_resource_timings',
        properties: {
            startTime: {
                type: 'Number',
                description: 'Start time of the request',
            },
            domainLookupStart: {
                type: 'Number',
                description: 'Domain lookup start time',
            },
            domainLookupEnd: {
                type: 'Number',
                description: 'Domain lookup end time',
            },
            connectStart: {
                type: 'Number',
                description: 'Connect start time',
            },
            secureConnectionStart: {
                type: 'Number',
                description: 'Secure connection start time',
            },
            connectEnd: {
                type: 'Number',
                description: 'Connect end time',
            },
            requestStart: {
                type: 'Number',
                description: 'Request start time',
            },
            responseStart: {
                type: 'Number',
                description: 'Response start time',
            },
            responseEnd: {
                type: 'Number',
                description: 'Response end time',
            },
        },
    },
    RequestSize: {
        description: 'Size details of the request',
        link: 'https://developer.mozilla.org/en-US/docs/Web/API/PerformanceResourceTiming',
        properties: {
            requestBodySize: {
                type: 'Number',
                description: 'Request body size',
            },
            requestHeadersSize: {
                type: 'Number',
                description: 'Request headers size',
            },
            responseBodySize: {
                type: 'Number',
                description: 'Response body size',
            },
            responseHeadersSize: {
                type: 'Number',
                description: 'Response headers size',
            },
        },
    },
    NetworkResponse: {
        description: 'Response of the network request',
        link: 'https://developer.mozilla.org/en-US/docs/Web/API/Response',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the response',
            },
            status: {
                type: 'Number',
                description: 'Status of the response',
            },
            statusText: {
                type: 'String',
                description: 'Status text of the response',
            },
            contentType: {
                type: 'String',
                description: 'Content type of the response',
            },
            headers: {
                type: 'Object',
                description: 'Headers of the response',
            },
            body: {
                type: 'String',
                description: 'Body of the response',
            },
            text: {
                type: 'String',
                description: 'Text of the response',
            },
            json: {
                type: 'Object',
                description: 'JSON of the response',
            },
            ok: {
                type: 'boolean',
                description: 'Is the response ok',
            },
            securityDetails: {
                type: 'SecurityDetails',
                description: 'Security details of the response',
            },
            serverAddr: {
                type: 'ServerAddr',
                description: 'Server details of the response',
            },
            request: {
                type: 'RequestData',
                description: 'Request details of the response',
            },
        },
    },
    NetworkErrorResponse: {
        description: 'Error response of the network request',
        link: 'https://developer.mozilla.org/en-US/docs/Web/API/Response',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the response',
            },
            status: {
                type: 'Number',
                description: 'Status of the response',
            },
            errorText: {
                type: 'String',
                description: 'Error text of the response',
            },
        },
    },
    NetworkRequest: {
        description: 'Network requests that were made after loading the page and their responses',
        link: 'https://developer.mozilla.org/en-US/docs/Web/API/Request',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the request',
            },
            method: {
                type: 'String',
                description: 'Method of the request',
            },
            headers: {
                type: 'Object',
                description: 'Headers of the request',
            },
            postData: {
                type: 'String',
                description: 'Post data of the request',
            },
            redirectedFrom: {
                type: 'String',
                description: 'Redirected from URL',
            },
            redirectedTo: {
                type: 'String',
                description: 'Redirected to URL',
            },
            resourceType: {
                type: ['document', 'stylesheet', 'image', 'media', 'font', 'script', 'texttrack', 'xhr', 'fetch', 'eventsource', 'websocket', 'manifest', 'other'],
                description: 'Resource type of the request',
            },
            sizes: {
                type: 'RequestSize',
                description: 'Size details of the request',
            },
            timing: {
                type: 'RequestTiming',
                description: 'Timing of the request',
            },
            responseData: {
                type: 'NetworkResponse | NetworkErrorResponse',
                description: 'Response data of the request',
            },
        },
    },
    ConsoleLog: {
        description: 'Console log that was made after loading the page',
        link: 'https://developer.mozilla.org/en-US/docs/Web/API/Console',
        properties: {
            type: {
                type: ['log', 'debug', 'info', 'error', 'warning', 'dir', 'dirxml', 'table', 'trace', 'clear', 'startGroup', 'startGroupCollapsed', 'endGroup', 'assert', 'profile', 'profileEnd', 'count', 'timeEnd'],
                description: 'Type of the log',
            },
            args: {
                type: 'Array<*>',
                description: 'Arguments of the log',
            },
            url: {
                type: 'String',
                description: 'URL of the log',
            },
            lineNumber: {
                type: 'Number',
                description: 'Line number of the log',
            },
            columnNumber: {
                type: 'Number',
                description: 'Column number of the log',
            },
        },
    },
    PageCrash: {
        description: 'Crash data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the crash',
            },
            ts: {
                type: 'Number',
                description: 'Timestamp of the crash',
            },
        },
    },
    PageOpen: {
        description: 'Open data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the open',
            },
            actor: {
                type: 'String',
                description: 'Actor of the open',
            },
            ts: {
                type: 'Number',
                description: 'Timestamp of the open',
            },
        },
    },
    PageClose: {
        description: 'Close data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the close',
            },
            actor: {
                type: 'String',
                description: 'Actor of the close',
            },
            ts: {
                type: 'Number',
                description: 'Timestamp of the close',
            },
        },
    },
    PageDialog: {
        description: 'Dialog data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the dialog',
            },
            ts: {
                type: 'Number',
                description: 'Timestamp of the dialog',
            },
            message: {
                type: 'String',
                description: 'Message of the dialog',
            },
            decision: {
                type: ['accept', 'dismiss'],
                description: 'Decision of the dialog',
            },
        },
    },
    PageWebsocket: {
        description: 'Websocket data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the websocket',
            },
            open: {
                type: 'Number',
                description: 'Open time of the websocket',
            },
            close: {
                type: 'Number',
                description: 'Close time of the websocket',
            },
        },
    },
    PageWorker: {
        description: 'Worker data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the worker',
            },
            open: {
                type: 'Number',
                description: 'Open time of the worker',
            },
            close: {
                type: 'Number',
                description: 'Close time of the worker',
            },
        },
    },
    PageRecord: {
        description: 'Record data of the page',
        properties: {
            url: {
                type: 'String',
                description: 'URL of the page',
            },
            open: {
                type: 'PageOpen',
                description: 'Open data of the page',
            },
            request: {
                type: 'NetworkRequest',
                description: 'Request data of the page',
            },
            networkRequests: {
                type: 'Array<NetworkRequest>',
                description: 'Network requests of the page',
            },
            close: {
                type: 'PageClose',
                description: 'Close data of the page',
            },
            crash: {
                type: 'PageCrash',
                description: 'Crash data of the page',
            },
            consoleLogs: {
                type: 'Array<ConsoleLog>',
                description: 'Console logs of the page',
            },
            dialogs: {
                type: 'Array<PageDialog>',
                description: 'Dialogs of the page',
            },
            webSockets: {
                type: 'Array<PageWebsocket>',
                description: 'Websockets of the page',
            },
            workers: {
                type: 'Array<PageWorker>',
                description: 'Workers of the page',
            },
            cookies: {
                type: 'Array<Cookie>',
                description: 'Cookies of the page',
            },
            localStorage: {
                type: 'Object',
                description: 'Local storage of the page',
            },
            sessionStorage: {
                type: 'Object',
                description: 'Session storage of the page',
            },
        },
    },
    PageErrorRecord: {

    },
    AutomationState: {
        description: 'State of the Web Automate Build',
        link: '@todo: add link to the developer docs',
        properties: {
            sessionId: {
                type: 'String',
                description: 'Session ID of the build',
            },
            projectId: {
                type: 'String',
                description: 'Project ID of the build',
            },
            buildId: {
                type: 'String',
                description: 'Build ID of the build',
            },
            buildIndex: {
                type: 'Number',
                description: 'Build index of the build',
            },
            sessionStart: {
                type: 'Number',
                description: 'Start time of the session',
            },
            sessionEnd: {
                type: 'Number',
                description: 'End time of the session',
            },
            navigationHistory: {
                type: 'Array<PageRecord | PageErrorRecord>',
                description: 'Navigation history of the build',
            },
        },
    },
} as const;

export const requestHeaders = {
    type: 'Object',
    description: 'Request headers, with which the request was sent to the server',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers',
} as const;

export const requestData = {
    type: 'String | Object',
    description: 'Request body, can be a string or an object',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods#request_body',
} as const;

export const requestUrl = {
    type: 'String',
    description: 'Request URL',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/Request/url',
} as const;

export const requestMethod = {
    type: 'String',
    description: 'Request method',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods',
} as const;

export const requestPostData = {
    type: 'String | Object',
    description: 'Request body, can be a string or an object',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods#request_body',
} as const;

export const requestParameters = {
    type: 'Object',
    description: 'Request parameters',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods#request_parameters',
} as const;

export const responseHeaders = {
    type: 'Object',
    description: 'Response headers, that sent to the client from the server',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers',
} as const;

export const responseData = {
    type: 'String | Object',
    description: 'Response body, can be a string or an object',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods#response_body',
} as const;

export const responseBody = {
    type: 'String',
    description: 'Body of the request',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/Request/body',
} as const;

export const responseJson = {
    type: 'Object',
    description: 'Response body as JSON',
    link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON',
} as const;

export const responseContentType = {
    type: 'String',
    description: 'Content type of the response',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Type',
} as const;

export const responseUrl = {
    type: 'String',
    description: 'Response URL',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/Response/url',
} as const;

export const nodeList = {
    type: 'NodeList',
    description: 'List of elements that match the selector',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/NodeList',
} as const;

export const url = {
    type: 'String',
    description: 'URL of the page',
} as const;

export const cookies = {
    type: 'Array<Cookie>',
    description: 'All Cookies at time of extraction, including session cookies or cookies that were set via JavaScript in the browser',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies',
} as const;

export const certificate = {
    type: 'SecurityDetails',
    description: 'Certificate, that was used for the connection',
    link: 'https://developer.mozilla.org/en-US/docs/Web/Security/Public_Key_Pinning',
} as const;

export const server = {
    type: 'ServerAddr',
    description: 'Server details where the page is hosted',
    link: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Servers',
} as const;

export const timing = {
    type: 'Timing',
    description: 'Timing details of the page',
    link: 'https://developer.mozilla.org/en-US/docs/Web/Performance/Navigation_and_resource_timings',
} as const;

export const sizes = {
    type: 'RequestSize',
    description: 'Size details of the request',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/PerformanceResourceTiming',
} as const;

export const index = {
    type: 'Number',
    description: 'Current zero-based iteration number',
    link: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Loops_and_iteration',
} as const;

export const networkRequests = {
    type: 'Array<NetworkRequests>',
    description: 'Network requests that were made after loading the page and their responses',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/Request',
} as const;

export const consoleLogs = {
    type: 'Array<ConsoleLog>',
    description: 'Console logs that were made after loading the page',
    link: 'https://developer.mozilla.org/en-US/docs/Web/API/Console',
} as const;

export const automationState = {
    type: 'AutomationState',
    description: 'State of the Web Automate Build',
    link: '@todo: add link to the developer docs',
} as const;

export const allParamNames = {
    state: {
        fe: 'automationState',
        be: 'automationState',
        type: 'AutomationState',
    },
} as const;
