import {getTimeZone} from '../custom/utils/timezone';
import {isDev, isProd, isStage} from './env-detection';

const appOrigin = process.env.REACT_APP_APP_ORIGIN;
const webAutomate = {
    appOrigin,
    timeZone: getTimeZone(),
    i18n: {
        changeLanguage: () => {},
        t: () => {},
    },
}

export const globalConfig = {
    version: '1.0.0',
    domain: process.env.REACT_APP_DOMAIN_NAME,
    appOrigin,
    apiRoot: process.env.REACT_APP_API_ROOT,
    w73Static: process.env.REACT_APP_STATIC,
    isDev: isDev(),
    isStage: isStage(),
    isProd: isProd(),
    domainName: process.env.REACT_APP_DOMAIN_NAME,
    exampleURL: process.env.REACT_APP_EXAMPLE_URL,
    appName: process.env.REACT_APP_APP_NAME,
    appNameShort: process.env.REACT_APP_APP_NAME_SHORT,
    emailCompany: process.env.REACT_APP_EMAILS_COMPANY,
    emailContact: process.env.REACT_APP_EMAILS_CONTACT,
    emailSupport: process.env.REACT_APP_EMAILS_SUPPORT,
    emailAbuse: process.env.REACT_APP_EMAILS_ABUSE,
    companyContact: process.env.REACT_APP_COMPANY_CONTACT,
    companyUid: process.env.REACT_APP_COMPANY_UID,
    companyName: process.env.REACT_APP_COMPANY_NAME,
    companyStreet: process.env.REACT_APP_COMPANY_STREET,
    companyCity: process.env.REACT_APP_COMPANY_CITY,
    companyState: process.env.REACT_APP_COMPANY_STATE,
    companyCountry: process.env.REACT_APP_COMPANY_COUNTRY,
    companyYoutube: process.env.REACT_APP_COMPANY_SOCIALS_YOUTUBE,
    companyX: process.env.REACT_APP_COMPANY_SOCIALS_X,
    companyWhatsapp: process.env.REACT_APP_COMPANY_SOCIALS_WHATSAPP,
    paymentProviderName: process.env.REACT_APP_PAYMENT_PROVIDER,
    paymentProviderLink: process.env.REACT_APP_PAYMENT_PROVIDER_LINK,
    FIREBASE_EMULATOR_HOST: process.env.REACT_APP_FIREBASE_EMULATOR_HOST,
    FIREBASE_AUTH_EMULATOR_HOST: process.env.REACT_APP_FIREBASE_AUTH_EMULATOR_HOST,
    STORAGE_EMULATOR_HOST: process.env.REACT_APP_STORAGE_EMULATOR_HOST,
    FIREBASE_DATABASE_URL: process.env.REACT_APP_FIREBASE_DATABASE_URL,
    FIREBASE_STORAGE_BUCKET: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    STORAGE_AUTH_DOMAIN: process.env.REACT_APP_STORAGE_AUTH_DOMAIN,
};

export const firebaseConfig = {
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.REACT_APP_FIREBASE_APPID,
    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

export const mapConfig = process.env.REACT_APP_MAP_MAPBOX;

export const googleAnalyticsConfig = process.env.REACT_APP_GA_MEASUREMENT_ID;

window.webAutomate = webAutomate;
window.globalConfig = globalConfig;

