import type {OverageItem} from 'webautomate/global';

/**
 * Adjusted Cost Estimate for 1 AI Request (gpt-4o model):
 * Breakdown:
 *   - Base operations: 0.20 CHF (includes creating assistant, thread start, uploading files)
 *   - Completion cost: 0.15 CHF (0.05 CHF for prompt, 0.10 CHF for completion)
 *   - Overhead: 0.30 CHF (service and operational overhead)
 * Total: 0.50 CHF per AI request.
 */
export const aiRequests: OverageItem = {
    appName: 'aiRequests',
    name: 'AI-Requests',
    data: {
        model: 'gpt-4o',
    },
    pricing: {
        billing_scheme: 'tiered',
        tiers_mode: 'graduated',
        tiers: [
            {
                up_to: 100,
                unit_amount_decimal: 50,
            },
            {
                up_to: 500,
                unit_amount_decimal: 40,
            },
            {
                up_to: 'inf',
                unit_amount_decimal: 40,
            },
        ],
    },
} as const;
