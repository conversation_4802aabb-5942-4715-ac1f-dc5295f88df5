import type {OverageItem} from 'webautomate/global';

/**
 * Adjusted Cost Estimate for 1 Build Step:
 * Breakdown:
 *   - Cost per step: 0.01 CHF
 *   - No additional overhead
 * Total: 0.01 CHF per step.
 */
export const buildSteps: OverageItem = {
    appName: 'buildSteps',
    name: 'Build Steps',
    pricing: {
        billing_scheme: 'tiered',
        tiers_mode: 'graduated',
        tiers: [
            {
                up_to: 1000,
                unit_amount_decimal: 1,
            },
            {
                up_to: 20000,
                unit_amount_decimal: 0.75,
            },
            {
                up_to: 100000,
                unit_amount_decimal: 0.5,
            },
            {
                up_to: 'inf',
                unit_amount_decimal: 0.45,
            },
        ],
    },
} as const;
