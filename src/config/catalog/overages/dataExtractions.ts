import type {OverageItem} from 'webautomate/global';

/**
 * Adjusted Cost Estimate for 1 Data Extraction:
 * Breakdown:
 *   - Base extraction cost: 0.01 CHF
 *   - No additional overhead
 * Total: 0.01 CHF per extraction.
 */
export const dataExtractions: OverageItem = {
    appName: 'dataExtractions',
    name: 'Data Extractions',
    pricing: {
        billing_scheme: 'tiered',
        tiers_mode: 'graduated',
        tiers: [
            {
                up_to: 100,
                unit_amount_decimal: 1,
            },
            {
                up_to: 500,
                unit_amount_decimal: 0.75,
            },
            {
                up_to: 1000,
                unit_amount_decimal: 0.5,
            },
            {
                up_to: 'inf',
                unit_amount_decimal: 0.45,
            },
        ],
    },
} as const;
