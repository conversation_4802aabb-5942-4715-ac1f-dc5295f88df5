import type {OverageItem} from 'webautomate/global';

/**
 * Adjusted Cost Estimate for 1 E2E Visual Test:
 * Breakdown:
 *   - Screenshot: 0.01 CHF
 *   - Visual diff: 0.01 CHF
 *   - Storage and cleanup: 0.011 CHF
 *   - Overhead: 0.078 CHF
 * Total: 0.10 CHF per visual test.
 */
export const e2eVisualTests: OverageItem = {
    appName: 'e2eVisualTests',
    name: 'E2E Visual Tests',
    pricing: {
        billing_scheme: 'tiered',
        tiers_mode: 'graduated',
        tiers: [
            {
                up_to: 100,
                unit_amount_decimal: 10,
            },
            {
                up_to: 1000,
                unit_amount_decimal: 7.5,
            },
            {
                up_to: 10000,
                unit_amount_decimal: 5,
            },
            {
                up_to: 'inf',
                unit_amount_decimal: 4.5,
            },
        ],
    },
} as const;
