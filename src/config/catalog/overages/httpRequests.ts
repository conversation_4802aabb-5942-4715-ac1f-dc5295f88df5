import type {OverageItem} from 'webautomate/global';

/**
 * Adjusted Cost Estimate for 1 HTTP Request:
 * Breakdown:
 *   - Base data extraction: 0.001 CHF
 *   - No overhead
 * Total: 0.001 CHF per HTTP request.
 */
export const httpRequests: OverageItem = {
    appName: 'httpRequests',
    name: 'HTTP Requests',
    pricing: {
        billing_scheme: 'tiered',
        tiers_mode: 'graduated',
        tiers: [
            {
                up_to: 5000,
                unit_amount_decimal: 0.1,
            },
            {
                up_to: 50000,
                unit_amount_decimal: 0.08,
            },
            {
                up_to: 100000,
                unit_amount_decimal: 0.05,
            },
            {
                up_to: 'inf',
                unit_amount_decimal: 0.045,
            },
        ],
    },
} as const;
