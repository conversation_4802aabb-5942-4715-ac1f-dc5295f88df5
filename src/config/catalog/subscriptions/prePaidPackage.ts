import type {SubscriptionPackage} from 'administration';
import {priorityLevels} from './index';

export const appName = 'prePaidPackage';

export const packages: SubscriptionPackage = {
    solutions: {
        items: {
            dataExtractions: {
                active: true,
                count: Infinity,
            },
            e2eVisualTests: {
                active: true,
                count: Infinity,
            },
            screenshots: {
                active: true,
                count: Infinity,
            },
            screenVideos: {
                active: true,
                count: Infinity,
            },
            lighthouse: {
                active: true,
                count: Infinity,
            },
            urlChallenge: {
                active: true,
                count: Infinity,
            },
        },
    },
    administration: {
        items: {
            httpRequests: {
                active: true,
                count: Infinity,
            },
            buildSteps: {
                active: true,
                count: Infinity,
            },
            aiRequests: {
                active: true,
                count: Infinity,
            },
            builds: {
                active: true,
                count: Infinity,
            },
            buildRuntimeMinutes: {
                active: true,
                count: Infinity,
            },
        },
    },
    notifications: {
        email: {
            active: true,
        },
        webhooks: {
            active: false,
        },
    },
    limits: {
        parallelBuilds: 1,
        storageInGB: Infinity,
    },
    additional: {
        automationCredits: {
            active: false,
            quantity: 0,
        },
    },
    priorityLevel: priorityLevels.PREMIUM,
} as const;
