import type {SubscriptionPackage} from 'administration';
import credits from '../credits';
import {priorityLevels} from './index';

export const buildModeLicenses = {
    name: 'Premium',
    linkMode: {
        name: 'Link mode',
        plans: {
            monthlyX1: {
                priceId: 'price_1QwokAQmpu7UZVlaGoufUMzz',
                price: 69000,
            },
            monthlyX3: {
                priceId: 'price_1QwokAQmpu7UZVla9d7HV2iq',
                price: 179000,
            },
            monthlyX6: {
                priceId: 'price_1QwokAQmpu7UZVlasRrUS0Y1',
                price: 319000,
            },
            monthlyX12: {
                priceId: 'price_1QwokBQmpu7UZVla04dBOnju',
                price: 599000,
            },
        },
    },
    pageMode: {
        name: 'Page-License',
        plans: {
            monthlyX1: {
                priceId: 'price_1QwokBQmpu7UZVlaWQoDZr9E',
                price: 79900,
            },
            monthlyX3: {
                priceId: 'price_1QwokBQmpu7UZVla43wJWs5a',
                price: 199000,
            },
            monthlyX6: {
                priceId: 'price_1QwokCQmpu7UZVlamDMg53Kc',
                price: 359000,
            },
            monthlyX12: {
                priceId: 'price_1QwokCQmpu7UZVla9cogTAYw',
                price: 689000,
            },
        },
    },
} as const;

export const name = 'Premium';

export const appName = 'premiumPackage';

export const defaultLicence = 'pageMode';

export const packages: SubscriptionPackage = {
    solutions: {
        items: {
            dataExtractions: {
                active: true,
                count: 50_000,
            },
            e2eVisualTests: {
                active: true,
                count: 10_000,
            },
            screenshots: {
                active: true,
                count: 50_000,
            },
            screenVideos: {
                active: true,
                count: 5_000,
            },
            lighthouse: {
                active: true,
                count: 2_000,
            },
            urlChallenge: {
                active: true,
                count: 2_000,
            },
        },
    },
    administration: {
        items: {
            aiRequests: {
                active: true,
                count: 500,
            },
            buildSteps: {
                active: true,
                count: 20_000,
            },
            builds: {
                active: true,
                count: 5_000,
            },
            httpRequests: {
                active: true,
                count: 500_000,
            },
            buildRuntimeMinutes: {
                active: true,
                count: 10_080,
            },
        },
    },
    notifications: {
        email: {
            active: true,
        },
        webhooks: {
            active: true,
        },
    },
    limits: {
        parallelBuilds: 20,
        storageInGB: 2_000,
    },
    additional: {
        automationCredits: {
            active: true,
            quantity: credits.items.webAutomationCreditsX75k.quantity,
        },
    },
    priorityLevel: priorityLevels.PREMIUM,
} as const;
