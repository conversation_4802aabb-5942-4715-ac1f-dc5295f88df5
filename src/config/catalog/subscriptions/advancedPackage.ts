import type {SubscriptionPackage} from 'administration';
import packagePrices from '../credits/index';
import {priorityLevels} from './index';

export const buildModeLicenses = {
    name: 'Advanced',
    linkMode: {
        name: 'Link mode',
        plans: {
            monthlyX1: {
                priceId: 'price_1Qwok7Qmpu7UZVlaKSmdLBPV',
                price: 16900,
            },
            monthlyX3: {
                priceId: 'price_1Qwok7Qmpu7UZVlawM7yL4XW',
                price: 45900,
            },
            monthlyX6: {
                priceId: 'price_1Qwok8Qmpu7UZVlalLVPIv1E',
                price: 84900,
            },
            monthlyX12: {
                priceId: 'price_1Qwok8Qmpu7UZVlaqiklnh7X',
                price: 159000,
            },
        },
    },
    pageMode: {
        name: 'Page-License',
        plans: {
            monthlyX1: {
                priceId: 'price_1Qwok8Qmpu7UZVlaQW0Rs2pd',
                price: 19900,
            },
            monthlyX3: {
                priceId: 'price_1Qwok9Qmpu7UZVlaU6rcLxhw',
                price: 52900,
            },
            monthlyX6: {
                priceId: 'price_1Qwok9Qmpu7UZVla4t6znPLz',
                price: 97900,
            },
            monthlyX12: {
                priceId: 'price_1Qwok9Qmpu7UZVla8oiR4klN',
                price: 189000,
            },
        },
    },
} as const;

export const name = 'Advanced';

export const appName = 'advancedPackage';

export const defaultLicence = 'pageMode';

export const packages: SubscriptionPackage = {
    solutions: {
        items: {
            dataExtractions: {
                active: true,
                count: 5000,
            },
            e2eVisualTests: {
                active: true,
                count: 1000,
            },
            screenshots: {
                active: true,
                count: 5000,
            },
            screenVideos: {
                active: true,
                count: 500,
            },
            lighthouse: {
                active: false,
                count: 0,
            },
            urlChallenge: {
                active: false,
                count: 0,
            },
        },
    },
    administration: {
        items: {
            aiRequests: {
                active: true,
                count: 150,
            },
            buildSteps: {
                active: true,
                count: 10000,
            },
            builds: {
                active: true,
                count: 1000,
            },
            httpRequests: {
                active: true,
                count: 10000,
            },
            buildRuntimeMinutes: {
                active: true,
                count: 1440,
            },
        },
    },
    notifications: {
        email: {
            active: true,
        },
        webhooks: {
            active: true,
        },
    },
    limits: {
        parallelBuilds: 5,
        storageInGB: 200,
    },
    additional: {
        automationCredits: {
            active: true,
            quantity: packagePrices.items.webAutomationCreditsX12k.quantity,
        },
    },
    priorityLevel: priorityLevels.ADVANCED,
} as const;
