import {RUN_MODES} from '../../scrapper';
import solutions from '../../solutions';

export const PREPAID_SUBSCRIPTION_KEY = 'PREPAID';

export const plans = {
    monthlyX1: {
        appName: 'monthlyX1',
        numberOfMonths: 1,
    },
    monthlyX3: {
        appName: 'monthlyX3',
        numberOfMonths: 3,
    },
    monthlyX6: {
        appName: 'monthlyX6',
        numberOfMonths: 6,
    },
    monthlyX12: {
        appName: 'monthlyX12',
        numberOfMonths: 12,
    },
} as const;

export const packages = {
    solutions: {
        items: {
            dataExtractions: {
                appName: solutions.dataExtractions.appName,
                name: 'Data-Extraction',
            },
            e2eVisualTests: {
                appName: solutions.e2eVisualTests.appName,
                name: 'Visual-Testing',
            },
            screenshots: {
                appName: solutions.screenshots.appName,
                name: 'Screenshot-Capturing',
            },
            screenVideos: {
                appName: solutions.screenVideos.appName,
                name: 'Screen-Video-Capturing',
            },
            lighthouse: {
                appName: solutions.lighthouse.appName,
                name: 'Lighthouse-Auditing',
            },
            urlChallenge: {
                appName: solutions.urlChallenge.appName,
                name: 'URL-Challenge',
            },
        },
    },
    administration: {
        items: {
            httpRequests: {
                appName: 'httpRequests',
                name: 'HTTP-Requests',
            },
            buildSteps: {
                appName: 'buildSteps',
                name: 'Build-Steps',
            },
            aiRequests: {
                appName: 'aiRequests',
                name: 'AI-Requests',
            },
            builds: {
                appName: 'builds',
                name: 'Builds',
            },
            buildRuntimeMinutes: {
                appName: 'buildRuntimeMinutes',
                name: 'Build Runtime (mins)',
            },
        },
    },
    notifications: {
        email: {
            appName: 'email',
            name: 'E-Mail notification',
        },
        webhooks: {
            appName: 'webhooks',
            name: 'Webhook notification',
        },
    },
    limits: {
        parallelBuilds: {
            appName: 'parallelBuilds',
            name: 'Parallel Builds',
        },
        storageInGB: {
            appName: 'storageInGB',
            name: 'Storage (GB)',
        },
    },
} as const;

export const buildModeLicenses = {
    linkMode: {
        appName: 'linkMode',
        availableRunModes: [
            RUN_MODES.link,
        ],
    },
    pageMode: {
        appName: 'pageMode',
        availableRunModes: [
            RUN_MODES.link,
            RUN_MODES.page,
        ],
    },
} as const;

export const positions = {
    dataExtractions: 1,
    screenshots: 2,
    screenVideos: 3,
    e2eVisualTests: 4,
    lighthouse: 5,
    urlChallenge: 6,
} as const;

export const categories = {
    prePaidPackage: {
        appName: 'prePaidPackage',
    },
    starterPackage: {
        appName: 'starterPackage',
    },
    advancedPackage: {
        appName: 'advancedPackage',
    },
    premiumPackage: {
        appName: 'premiumPackage',
    },
} as const;

export const priorityLevels = {
    STARTER: {
        appName: 'starter',
        value: 1,
    },
    ADVANCED: {
        appName: 'advanced',
        value: 2,
    },
    PREMIUM: {
        appName: 'premium',
        value: 3,
    },
};
