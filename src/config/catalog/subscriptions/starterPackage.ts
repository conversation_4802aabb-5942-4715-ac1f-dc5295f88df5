import type {SubscriptionPackage} from 'administration';
import credits from '../credits';
import {priorityLevels} from './index';

export const buildModeLicenses = {
    name: 'Starter',
    linkMode: {
        name: 'Link mode',
        plans: {
            monthlyX1: {
                priceId: 'price_1Qwok4Qmpu7UZVlaQi8yVnmG',
                price: 2900,
            },
            monthlyX3: {
                priceId: 'price_1Qwok5Qmpu7UZVlaaGTBvurM',
                price: 7900,
            },
            monthlyX6: {
                priceId: 'price_1Qwok5Qmpu7UZVlaBkoGTeH8',
                price: 15900,
            },
            monthlyX12: {
                priceId: 'price_1Qwok5Qmpu7UZVlazhctfyj8',
                price: 27900,
            },
        },
    },
    pageMode: {
        name: 'Page-License',
        plans: {
            monthlyX1: {
                priceId: 'price_1Qwok6Qmpu7UZVlaBgPyFRBP',
                price: 2900,
            },
            monthlyX3: {
                priceId: 'price_1Qwok6Qmpu7UZVlaSmgjtOG9',
                price: 8900,
            },
            monthlyX6: {
                priceId: 'price_1Qwok6Qmpu7UZVlalg3vv7Jk',
                price: 17900,
            },
            monthlyX12: {
                priceId: 'price_1Qwok6Qmpu7UZVla7s19ldFz',
                price: 31900,
            },
        },
    },
} as const;

export const name = 'Starter';

export const appName = 'starterPackage';

export const defaultLicence = 'linkMode';

export const packages: SubscriptionPackage = {
    solutions: {
        items: {
            dataExtractions: {
                active: true,
                count: 100,
            },
            e2eVisualTests: {
                active: true,
                count: 75,
            },
            screenshots: {
                active: true,
                count: 150,
            },
            screenVideos: {
                active: false,
                count: 0,
            },
            lighthouse: {
                active: false,
                count: 0,
            },
            urlChallenge: {
                active: false,
                count: 0,
            },
        },
    },
    administration: {
        items: {
            aiRequests: {
                active: true,
                count: 30,
            },
            buildSteps: {
                active: true,
                count: 100,
            },
            builds: {
                active: true,
                count: 75,
            },
            httpRequests: {
                active: true,
                count: 5000,
            },
            buildRuntimeMinutes: {
                active: true,
                count: 90,
            },
        },
    },
    notifications: {
        email: {
            active: true,
        },
        webhooks: {
            active: false,
        },
    },
    limits: {
        parallelBuilds: 2,
        storageInGB: 25,
    },
    additional: {
        automationCredits: {
            active: true,
            quantity: credits.items.webAutomationCreditsX1k.quantity,
        },
    },
    priorityLevel: priorityLevels.STARTER,
} as const;
