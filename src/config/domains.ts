import {WEB_AUTOMATE_CRAWLER_NAME} from './w73';

export const VERIFICATION_TYPE = {
    COLLABORATOR: 'COLLABORATOR',
    META_TAG: 'META_TAG',
    ROBOTS_TXT: 'ROBOTS_TXT',
} as const;

export const VERIFICATION_ACTION = {
    VERIFY: 'VERIFY',
    REVOKE: 'REVOKE',
} as const;

export const VERIFICATION_STATE = {
    VERIFIED: 'VERIFIED',
    REVOKED: 'REVOKED',
    PENDING: 'PENDING',
} as const;

export const PLACEHOLDER = '{{code}}';

export const VERIFICATION_SNIPPETS = {
    [VERIFICATION_TYPE.META_TAG]: {
        snippet: `<meta name="webautomate.app-verification" content="${PLACEHOLDER}">`,
    },
    [VERIFICATION_TYPE.ROBOTS_TXT]: {
        snippet: `User-agent: ${WEB_AUTOMATE_CRAWLER_NAME}\nDisallow:\n# Crawl-delay: 5\n`,
    },
} as const;
