export const serverlessAPI = {
    api: {
        root: '/_',
    },
    cdnApi: {
        root: '/cdn',
    },
    checkoutApi: {
        root: '/checkout',
    },
    adminApi: {
        root: '/admin',
        uris: {
            broadcastAccountNotification: '/broadcast-account-notification',
            blacklistDomain: '/blacklist-domain',
            blacklistDomainRemove: '/blacklist-domain-remove',
            blacklistDomainStatus: '/black-domain-status',
            buildProject: '/build-project',
            fetchAccount: '/fetch-account',
            fetchAccountList: '/fetch-account-list',
            fetchAdminLogs: '/fetch-admin-logs',
            fetchBuildNotifications: '/fetch-notifications',
            fetchProjectList: '/fetch-project-list',
            fetchRequestList: '/fetch-request-list',
            processAdminLogs: '/process-admin-logs',
            processProjectBuildDeleteQueue: '/process-project-build-delete-queue',
            processProjectDeleteQueue: '/process-project-delete-queue',
            processRequest: '/process-request',
            replyDemo: '/reply-demo',
            updateAccount: '/update-account',
            updateSubscriptions: '/update-subscriptions',
        },
    },
    managementApi: {
        root: '/management',
        uris: {
            projectList: '/project-list',
            projectConfig: '/project-config',
            projectBuildQueue: '/project-build-queue',
            projectBuildDequeue: '/project-build-dequeue',
            projectStateChange: '/project-state-change',
            // projectSchedulerUpdate: '/project-scheduler-update',
            projectLogs: '/project-logs',
            projectResult: '/project-result',
            // accountOverview: '/account-overview',
            // accountLogs: '/account-logs',
            // transactionList: '/transaction-list',
            // transactionDetails: '/transaction-details',
            // userCreate: '/user-create',
            // userUpdate: '/user-update',
            // userDelete: '/user-delete',
            // userList: '/user-list',
        },
    },
    assistantApi: {
        root: '/assistant',
        uris: {
            faq: '/faq',
            generateProjectSchedulerCron: '/validate-project-scheduler-cron',
            validateSolutionCode: '/validate-solution-code',
            validateJSCode: '/validate-js-code',
            validateProjectScheduler: '/validate-project-scheduler',
        },
    },
} as const;

export default {
    automatedCreditPurchase: '/automated-credit-purchase',
    accountEvents: '/account-events',
    accountNotifications: '/account-notifications',
    accountNotificationsList: '/account-notifications-list',
    billing: '/billing',
    billingCycle: '/billing-cycle',
    checkApiAccess: '/check-access',
    checkoutCancel: '/checkout-cancel',
    checkoutInit: '/checkout-init',
    collaborator: '/collaborator',
    collaboratorList: '/collaborator-list',
    completeRegistration: '/verify-user',
    createUser: '/create-user',
    deleteUser: '/delete-user',
    clearScreenshotCache: '/clear-screenshot-cache',
    customerPortal: '/customer-portal',
    demo: '/demo',
    endSession: '/end-session',
    filesFetch: '/files-fetch',
    filesUpload: '/files-upload',
    filesDelete: '/files-delete',
    filesUpdate: '/files-update',
    logUIEvent: '/ui-event',
    nextProjectBuildList: '/next-project-build-list',
    paymentConfirmation: '/payment-confirmation',
    prices: '/prices',
    projectBuild: '/project-build',
    projectBuildCancel: '/project-build-cancel',
    projectBuildDelete: '/project-build-delete',
    projectBuildDeleteAll: '/project-build-delete-all',
    projectBuildItemHistory: '/project-build-item-history',
    projectBuildResult: '/project-build-result',
    projectBuildSubPageResultAll: '/project-build-sub-page-result-all',
    projectCreate: '/project-create',
    projectDelete: '/project-delete',
    projectDequeue: '/project-dequeue',
    projectDownload: '/project-download',
    projectEvents: '/project-events',
    projectTransactionList: '/project-transaction-list',
    projectTransactionUpdate: '/project-transaction-update',
    projectUpdate: '/project-update',
    projectSubscriptionUpdate: '/project-subscription-update',
    projectStateChange: '/project-state-change',
    projects: '/projects',
    refreshSession: '/refresh-session',
    resetVisualTestState: '/reset-visual-test',
    sendRequest: '/send-request',
    state: '/state',
    transactionList: '/transaction-list',
    templates: '/templates',
    updateAccountData: '/update-account-data',
    updateUserData: '/update-user-data',
    updateVisualTestState: '/update-visual-test',
    user: '/user',
    verifyDomain: '/verify-domain',
    version: '/v1',
    versionOpen: '/v10',
    webhook: '/webhook',
} as const;
