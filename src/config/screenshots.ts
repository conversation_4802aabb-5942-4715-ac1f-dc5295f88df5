export const SCREENSHOT_EXTENSIONS = {
    PNG: 'png',
} as const;

export const SCREENSHOT_IMAGE_TYPE = {
    viewport: 'viewport',
    element: 'element',
    fullPage: 'fullPage',
} as const;

export const SELECTOR_TARGETS = {
    all: 'all',
    first: 'first',
    last: 'last',
    custom: 'custom',
} as const;

export const DEFAULT_SCREENSHOT_ID = 'default-screenshot-id';

export const defaultScreenshot = {
    id: DEFAULT_SCREENSHOT_ID,
    name: 'Screenshot',
    extension: SCREENSHOT_EXTENSIONS.PNG,
    type: SCREENSHOT_IMAGE_TYPE.fullPage,
    selector: '',
    selectorTargets: SELECTOR_TARGETS.all,
} as const;

export const minDimensions = {
    nameLength: 2,
} as const;

export const maxDimensions = {
    nameLength: 64,
    scriptLength: 250,
} as const;

export default {
    custom: {
        manual: {
            value: 'manual',
        },
    },
    automated: {
        ai: {
            value: 'ai',
        },
    },
} as const;
