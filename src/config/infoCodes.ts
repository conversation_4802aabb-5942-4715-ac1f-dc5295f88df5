export const EVENTS = {
    ACCOUNT_COLLABORATOR_EDITED: 'ACCOUNT_COLLABORATOR_EDITED',
    ACCOUNT_COLLABORATOR_REMOVED: 'ACCOUNT_COLLABORATOR_REMOVED',
    ACCOUNT_CREATED: 'ACCOUNT_CREATED',
    ACCOUNT_NEW_COLLABORATOR_CONFIRMS_ACCOUNT: 'ACCOUNT_NEW_COLLABORATOR_CONFIRMS_ACCOUNT',
    ACCOUNT_NEW_COLLABORATOR_INVITED: 'ACCOUNT_NEW_COLLABORATOR_INVITED',
    ACCOUNT_NEW_COLLABORATOR_RE_INVITED: 'ACCOUNT_NEW_COLLABORATOR_RE_INVITED',
    ACCOUNT_NEW_PROJECT: 'ACCOUNT_NEW_PROJECT',
    ACCOUNT_NEW_SUBSCRIPTION: 'ACCOUNT_NEW_SUBSCRIPTION',
    ACCOUNT_PRE_PAID_SUBSCRIPTION_CREATED: 'ACCOUNT_PRE_PAID_SUBSCRIPTION_CREATED',
    ACTIONS_TIMEOUT: 'ACTIONS_TIMEOUT',
    ACTION_FILE_UPLOAD_ONLY_SINGLE_FILES: 'ACTION_FILE_UPLOAD_ONLY_SINGLE_FILES',
    ACTION_NOT_ALLOWED_ON_DESKTOP: 'ACTION_NOT_ALLOWED_ON_DESKTOP',
    ACTION_NOT_ALLOWED_ON_FOREIGN_HOST: 'ACTION_NOT_ALLOWED_ON_FOREIGN_HOST',
    ACTION_NOT_ALLOWED_ON_MOBILE: 'ACTION_NOT_ALLOWED_ON_MOBILE',
    AI_SERVICE_FAILED: 'AI_SERVICE_FAILED',
    AI_THRESHOLD_NOT_MET: 'AI_THRESHOLD_NOT_MET',
    AUTOMATION_CREDITS_AUTO_RENEW_BLOCKED_DUE_TO_BALANCE: 'AUTOMATION_CREDITS_AUTO_RENEW_BLOCKED_DUE_TO_BALANCE',
    AUTOMATION_CREDITS_AUTO_RENEW_FAILED: 'AUTOMATION_CREDITS_AUTO_RENEW_FAILED',
    AUTOMATION_CREDITS_AUTO_RENEW_SUCCESS: 'AUTOMATION_CREDITS_AUTO_RENEW_SUCCESS',
    AUTOMATION_CREDITS_REQUIRED: 'AUTOMATION_CREDITS_REQUIRED',
    BLOCKED_DOMAINS: 'BLOCKED_DOMAINS',
    BROWSER_NOT_AVAILABLE: 'BROWSER_NOT_AVAILABLE',
    BROWSER_STARTED: 'BROWSER_STARTED',
    CANNOT_EXECUTE_INACTIVE_PROJECT: 'CANNOT_EXECUTE_INACTIVE_PROJECT',
    CANNOT_FIND_PROJECT_CONFIG: 'CANNOT_FIND_PROJECT_CONFIG',
    CLIENT_ERROR: 'CLIENT_ERROR',
    DOWNLOAD_PROJECT_RUNNING: 'DOWNLOAD_PROJECT_RUNNING',
    DUPLICATE_PROJECT_QUEUED: 'DUPLICATE_PROJECT_QUEUED',
    DUPLICATE_PROJECT_RUNNING: 'DUPLICATE_PROJECT_RUNNING',
    ELEMENT_NOT_FOUND: 'ELEMENT_NOT_FOUND',
    ELEMENT_NOT_VISIBLE: 'ELEMENT_NOT_VISIBLE',
    ERROR_DELETING_FILE_AFTER_UPLOAD: 'ERROR_DELETING_FILE_AFTER_UPLOAD',
    FILE_NOT_FOUND: 'FILE_NOT_FOUND',
    GS_ERROR_DOWNLOAD: 'GS_ERROR_DOWNLOAD',
    GS_ERROR_NOT_FOUND: 'GS_ERROR_NOT_FOUND',
    GS_ERROR_UPLOAD: 'GS_ERROR_UPLOAD',
    INVALID_ACTION_CONFIGURATION: 'INVALID_ACTION_CONFIGURATION',
    INVALID_CONDITION_CONFIGURATION: 'INVALID_CONDITION_CONFIGURATION',
    INVALID_DATA_EXTRACTION_CONFIGURATION: 'INVALID_DATA_EXTRACTION_CONFIGURATION',
    INVALID_E2E_VISUAL_TEST_CONFIGURATION: 'INVALID_E2E_VISUAL_TEST_CONFIGURATION',
    INVALID_JS_CODE: 'INVALID_JS_CODE',
    INVALID_LOOP_CONFIGURATION: 'INVALID_LOOP_CONFIGURATION',
    INVALID_SCREENSHOT_CONFIGURATION: 'INVALID_SCREENSHOT_CONFIGURATION',
    INVALID_SOLUTION_CONFIGURATION: 'INVALID_SOLUTION_CONFIGURATION',
    INVALID_URL_CHALLENGE_CONFIGURATION: 'INVALID_URL_CHALLENGE_CONFIGURATION',
    LIGHTHOUSE_BUILD_FAILED: 'LIGHTHOUSE_BUILD_FAILED',
    LIGHTHOUSE_BUILD_STARTED: 'LIGHTHOUSE_BUILD_STARTED',
    LINKS_FOUND_ON_PAGE: 'LINKS_FOUND_ON_PAGE',
    LINK_CHECKER_BUILD_COMPLETED: 'LINK_CHECKER_BUILD_COMPLETED',
    LINK_CHECKER_BUILD_FAILED: 'LINK_CHECKER_BUILD_FAILED',
    LINK_CHECKER_SOLUTION_STARTED: 'LINK_CHECKER_SOLUTION_STARTED',
    MANAGER_ERROR_MISSING_JOB_REFERENCE: 'MANAGER_ERROR_MISSING_JOB_REFERENCE',
    PAGE_CRASH: 'PAGE_CRASH',
    PAGE_CUSTOM_TIMEOUT: 'PAGE_CUSTOM_TIMEOUT',
    PAGE_CUSTOM_TIMEOUT_JS_ERROR: 'PAGE_CUSTOM_TIMEOUT_JS_ERROR',
    PAGE_TIMEOUT: 'PAGE_TIMEOUT',
    PAGE_UNAVAILABLE: 'PAGE_UNAVAILABLE',
    PAGE_UNKNOWN_HOST: 'PAGE_UNKNOWN_HOST',
    PAYMENT_DETAILS_NEED_UPDATE: 'PAYMENT_DETAILS_NEED_UPDATE',
    PROJECT_BUILD_ABORTED_BY_STEP: 'PROJECT_PROJECT_BUILD_ABORTED_BY_STEP',
    PROJECT_BUILD_BY_AUTO: 'PROJECT_BUILD_AUTO',
    PROJECT_BUILD_BY_MANAGEMENT_API: 'PROJECT_PROJECT_BUILD_BY_MANAGEMENT_API',
    PROJECT_BUILD_BY_MANUAL: 'PROJECT_BUILD_MANUAL',
    PROJECT_BUILD_CANCEL: 'PROJECT_BUILD_CANCEL',
    PROJECT_BUILD_DATA_EXTRACTION_NO_RESULT: 'PROJECT_BUILD_DATA_EXTRACTION_NO_RESULT',
    PROJECT_BUILD_DELETED: 'PROJECT_BUILD_DELETED',
    PROJECT_BUILD_DELETED_ALL: 'PROJECT_BUILD_DELETED_ALL',
    PROJECT_BUILD_STEP_CONDITION_AI_ERROR: 'PROJECT_BUILD_STEP_CONDITION_AI_ERROR',
    PROJECT_BUILD_STEP_DATA_EXTRACTION_AI_ERROR: 'PROJECT_BUILD_STEP_DATA_EXTRACTION_AI_ERROR',
    PROJECT_BUILD_STEP_DATA_EXTRACTION_NO_RESULT: 'PROJECT_BUILD_STEP_DATA_EXTRACTION_NO_RESULT',
    PROJECT_BUILD_STEP_URL_CHALLENGE_AI_ERROR: 'PROJECT_BUILD_STEP_URL_CHALLENGE_AI_ERROR',
    PROJECT_BUILD_STEP_URL_CHALLENGE_NO_RESULT: 'PROJECT_BUILD_STEP_URL_CHALLENGE_NO_RESULT',
    PROJECT_BUILD_STEP_E2E_VISUAL_TEST_AI_ERROR: 'PROJECT_BUILD_STEP_E2E_VISUAL_TEST_AI_ERROR',
    PROJECT_BUILD_STEP_E2E_VISUAL_TEST_NO_REVIEW: 'PROJECT_BUILD_STEP_E2E_VISUAL_TEST_NO_REVIEW',
    PROJECT_BUILD_FINISHED: 'PROJECT_BUILD_FINISHED',
    PROJECT_BUILD_RETRY: 'PROJECT_BUILD_RETRY',
    PROJECT_BUILD_RETRY_EXHAUSTED: 'PROJECT_BUILD_RETRY_EXHAUSTED',
    PROJECT_BUILD_SCREENSHOT_NO_DOM_SELECTOR_FOUND_BY_AI: 'PROJECT_BUILD_SCREENSHOT_NO_DOM_SELECTOR_FOUND_BY_AI',
    PROJECT_BUILD_SKIPPED_DUE_TO_OVERAGE: 'PROJECT_BUILD_SKIPPED_DUE_TO_OVERAGE',
    PROJECT_CREATED: 'PROJECT_CREATED',
    PROJECT_DELETED: 'PROJECT_DELETED',
    PROJECT_EDITED: 'PROJECT_EDITED',
    PROJECT_NAME_TOO_LONG: 'PROJECT_NAME_TOO_LONG',
    PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST: 'PROJECT_PAUSED_DUE_TO_DOMAIN_BLACKLIST',
    PROJECT_STATUS_CHANGED: 'PROJECT_STATUS_CHANGED',
    PROJECT_SUBSCRIPTION_CHANGED: 'PROJECT_SUBSCRIPTION_CHANGED',
    REPORT_EXPORT_ERROR: 'REPORT_EXPORT_ERROR',
    REQUEST_BLOCKED: 'REQUEST_BLOCKED',
    REQUEST_MOCK_COMPLETED: 'REQUEST_MOCK_COMPLETED',
    REQUEST_MODIFICATION_COMPLETED: 'REQUEST_MOCK_COMPLETED',
    REQUEST_MODIFICATION_FAILED: 'REQUEST_MODIFICATION_FAILED',
    REQUEST_MODIFICATION_JS_CODE_INVALID: 'REQUEST_MODIFICATION_JS_CODE_INVALID',
    RESOURCE_RELEASE_ERROR: 'RESOURCE_RELEASE_ERROR',
    RESPONSE_MODIFICATION_COMPLETED: 'RESPONSE_MODIFICATION_COMPLETED',
    RESPONSE_MODIFICATION_FAILED: 'RESPONSE_MODIFICATION_FAILED',
    RESPONSE_MODIFICATION_JS_CODE_INVALID: 'RESPONSE_MODIFICATION_JS_CODE_INVALID',
    RUNTIME_ERROR: 'RUNTIME_ERROR',
    SERVER_ERROR: 'SERVER_ERROR',
    SESSION_CANCELLED: 'SESSION_CANCELLED',
    SESSION_CANCELLED_BY_MANAGER: 'SESSION_CANCELLED_BY_MANAGER',
    SESSION_CANCELLED_MANUALLY: 'SESSION_CANCELLED_MANUALLY',
    SESSION_DATABASE_ERROR: 'SESSION_DATABASE_ERROR',
    SESSION_ENDED: 'SESSION_ENDED',
    SESSION_ERROR: 'SESSION_ERROR',
    SESSION_STARTED: 'SESSION_STARTED',
    SOLUTION_URL_CHALLENGE_ELIMINATION: 'SOLUTION_URL_CHALLENGE_ELIMINATION',
    STEP_ABORTED: 'STEP_ABORTED',
    STEP_COMPLETED: 'STEP_COMPLETED',
    STEP_FAILED: 'STEP_FAILED',
    STEP_INACTIVE: 'STEP_INACTIVE',
    STEP_LABELLING_FUNCTION_FAILED: 'STEP_LABELLING_FUNCTION_FAILED',
    STEP_LOOP_BROKEN: 'STEP_LOOP_BROKEN',
    STEP_LOOP_EMPTY: 'STEP_LOOP_EMPTY',
    STEP_REFERENCE_LOST: 'STEP_REFERENCE_LOST',
    STEP_SOLUTION_URL_CHALLENGE_FAILED: 'STEP_SOLUTION_URL_CHALLENGE_FAILED',
    STEP_STARTED: 'STEP_STARTED',
    STRIPE_UNKNOWN_CUSTOMER: 'STRIPE_UNKNOWN_CUSTOMER',
    STRIPE_UNKNOWN_SUBSCRIPTION: 'STRIPE_UNKNOWN_SUBSCRIPTION',
    SUBSCRIPTION_CONTINGENT_UPDATE_ERROR: 'SUBSCRIPTION_CONTINGENT_UPDATE_ERROR',
    SUBSCRIPTION_DOMAIN_LIMIT_REACHED: 'SUBSCRIPTION_DOMAIN_LIMIT_REACHED',
    SUBSCRIPTION_MISSING: 'SUBSCRIPTION_MISSING',
    TIMEOUT: 'TIMEOUT',
    TOOL_ERROR: 'TOOL_ERROR',
    UNKNOWN: 'UNKNOWN',
    UNKNOWN_ACCOUNT: 'UNKNOWN_ACCOUNT',
    UNKNOWN_CHECK_TYPE: 'UNKNOWN_CHECK_TYPE',
    URL_AUTHORIZATION: 'URL_AUTHORIZATION',
    URL_BAD_SSL: 'URL_BAD_SSL',
    URL_CLIENT_ERROR: 'URL_CLIENT_ERROR',
    URL_DISALLOWED_BY_ETHICAL_ACCESS_CHECK: 'URL_DISALLOWED_BY_ETHICAL_ACCESS_CHECK',
    URL_GONE: 'URL_GONE',
    URL_NOT_RESPONDING: 'URL_NOT_RESPONDING',
    URL_PROCESSED: 'URL_PROCESSED',
    URL_PROCESSED_DEVICE: 'URL_PROCESSED_DEVICE',
    URL_PROCESSING: 'URL_PROCESSING',
    URL_REDIRECTED: 'URL_REDIRECTED',
    WEB_RESOURCE_INJECTION_COMPLETED: 'WEB_RESOURCE_INJECTION_COMPLETED',
    WEB_RESOURCE_INJECTION_FAILED: 'WEB_RESOURCE_INJECTION_FAILED',
} as const;

export const INFO_CODE_LEVELS = {
    DEBUG: 'DEBUG',
    ERROR: 'ERROR',
    INFO: 'INFO',
    SUCCESS: 'SUCCESS',
    WARNING: 'WARNING',
} as const;

export const PAGE_ERROR_EVENT_MAP = {
    ' connect ': EVENTS.URL_NOT_RESPONDING,
    ERR_CERT: EVENTS.URL_BAD_SSL,
    ERR_CONNECTION_REFUSED: EVENTS.URL_NOT_RESPONDING,
    'frame was detached': EVENTS.TOOL_ERROR,
    SSL_ERROR: EVENTS.URL_BAD_SSL,
    TimeoutError: EVENTS.PAGE_TIMEOUT,
    cert: EVENTS.URL_BAD_SSL,
    err_name_not_resolved: EVENTS.URL_NOT_RESPONDING,
    'page.evaluate:': EVENTS.INVALID_JS_CODE,
    'page.goto: NS_ERROR_UNKNOWN_HOST': EVENTS.PAGE_UNKNOWN_HOST,
    'page.goto: Timeout': EVENTS.PAGE_CUSTOM_TIMEOUT,
    'page.goto: net::ERR_FAILED': EVENTS.PAGE_UNAVAILABLE,
    'page.goto: net::ERR_INVALID_AUTH': EVENTS.URL_AUTHORIZATION,
    'page.waitForEvent': EVENTS.TIMEOUT,
    'page.goto': EVENTS.UNKNOWN,
    runTime: EVENTS.RUNTIME_ERROR,
    unknown: EVENTS.UNKNOWN,
} as const;

export const OUTPUT_BLACKLIST = [
    'page.$$:',
];

export const NOTIFICATIONS = {
    accountCardsExpiredOrMissing: 'accountCardsExpiredOrMissing',
    accountExists: 'accountExists',
    aiServiceNotAvailable: 'aiServiceNotAvailable',
    automationCreditsDepleted: 'automationCreditsDepleted',
    automationCreditsToppedUpForDemo: 'automationCreditsToppedUpForDemo',
    cannotUpdateProjectWhenBuilding: 'cannotUpdateProjectWhenBuilding',
    cannotUpdateProjectWhenAuditing: 'cannotUpdateProjectWhenAuditing',
    cannotUpdateEmailAfterAcceptance: 'cannotUpdateEmailAfterAcceptance',
    authCannotBeDeleted: 'authCannotBeDeleted',
    collaboratorPackageRequired: 'collaboratorPackageRequired',
    customerPortalError: 'customerPortalError',
    faqPromptMissing: 'faqPromptMissing',
    fileNotFound: 'fileNotFound',
    fileTooBig: 'fileTooBig',
    genericError: 'genericError',
    genericErrorWithRetry: 'genericErrorWithRetry',
    invalidDemoCartItem: 'invalidDemoCartItem',
    loginRequired: 'loginRequired',
    noRights: 'noRights',
    noInternet: 'noInternet',
    oneDemoRequestPolicy: 'oneDemoRequestPolicy',
    oneGenericRequestPolicy: 'oneGenericRequestPolicy',
    projectBuildCanceled: 'projectBuildCanceled',
    projectBuildCannotBeDeletedByAge: 'projectBuildCannotBeDeletedByAge',
    projectBuildCannotBeDequeued: 'projectBuildCannotBeDequeued',
    projectBuildDequeued: 'projectBuildDequeued',
    projectBuildDomainError: 'projectBuildDomainError',
    projectBuildNotFound: 'projectBuildNotFound',
    projectBuildQueued: 'projectBuildQueued',
    projectNotFound: 'projectNotFound',
    redirectTo: 'redirectTo',
    showError: 'showError',
    stripeError: 'stripeError',
    subscription: 'subscription',
    subscriptionChange: 'subscriptionChange',
    subscriptionSelection: 'subscriptionSelection',
    unauthenticated: 'unauthenticated',
    unverified: 'unverified',
    userIsArchived: 'userIsArchived',
} as const;

export const PROJECT_VALIDATION = {
    PROJECT_CONFIG_OK: 'PROJECT_CONFIG_OK',
    PROJECT_NAME_MISSING: 'PROJECT_NAME_MISSING',
    PROJECT_NAME_ALREADY_EXISTS: 'PROJECT_NAME_ALREADY_EXISTS',
    PROJECT_NAME_TOO_LONG: 'PROJECT_NAME_TOO_LONG',
    PROJECT_SOLUTION_MISSING_MISSING: 'PROJECT_SOLUTION_MISSING_MISSING',
    URLS_MISSING: 'URLS_MISSING',
    LIGHT_HOUSE_CATEGORY_LIST_MISSING: 'LIGHT_HOUSE_CATEGORY_LIST_MISSING',
    DEVICE_CONFIG_INVALID: 'DEVICE_CONFIG_INVALID',
    STEPS_MISSING: 'STEPS_MISSING',
    INVALID_CONFIG: 'INVALID_CONFIG',
} as const;
