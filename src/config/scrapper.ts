import {DEFAULT_LANGUAGE} from './languages';
import {LINKED_TAGS} from './linkTags';
import {defaultDevice} from './devices';
import {LIGHTHOUSE_FIELDS} from './lighthouse';
import {INFO_CODE_LEVELS} from './infoCodes';
import {REQUEST_WAIT_UNTIL} from './request';

export const EXECUTION_STRATEGY = {
    ISOLATED_BROWSER_INSTANCES: {
        key: 'isolatedBrowserInstances',
    },
    SHARED_BROWSER_INSTANCE: {
        key: 'sharedBrowserInstance',
    },
} as const;

export const KEYS = {
    actionList: 'actionList',
    activateScheduler: 'activateScheduler',
    availableCategoryList: 'availableCategoryList',
    availableOutputFormats: 'availableOutputFormats',
    browser: 'browser',
    categoryList: 'categoryList',
    checkType: 'checkType',
    clustersSize: 'clustersSize',
    conditions: 'conditions',
    content: 'content',
    contentType: 'contentType',
    customSelectorTargets: 'customSelectorTargets',
    dataExtractions: 'dataExtractions',
    deactivateJS: 'deactivateJS',
    device: 'device',
    deviceList: 'deviceList',
    executionStrategy: 'executionStrategy',
    extension: 'extension',
    fallbackLabel: 'fallbackLabel',
    groups: 'groups',
    highlightColor: 'highlightColor',
    id: 'id',
    includeExternalLinks: 'includeExternalLinks',
    includeInternalLinks: 'includeInternalLinks',
    includeSubdomains: 'includeSubdomains',
    injectionLocation: 'injectionLocation',
    injectionPosition: 'injectionPosition',
    injectionType: 'injectionType',
    item: 'item',
    jsCode: 'jsCode',
    label: 'label',
    labelScript: 'labelScript',
    labelStrategy: 'labelStrategy',
    language: 'language',
    lighthouse: 'lighthouse',
    lighthouseDevices: 'lighthouseDevices',
    lighthouseOutputFormats: 'lighthouseOutputFormats',
    linkRequestTimeout: 'linkRequestTimeout',
    list: 'list',
    localStorage: 'localStorage',
    location: 'location',
    logLevels: 'logLevels',
    name: 'name',
    notifications: 'notifications',
    notificationsData: 'notificationsData',
    operatingSystem: 'operatingSystem',
    outputFormat: 'outputFormat',
    overrideType: 'overrideType',
    projectName: 'projectName',
    removeQueryParameters: 'removeQueryParameters',
    removeTrailingSlash: 'removeTrailingSlash',
    requestBlockers: 'requestBlockers',
    requestCookies: 'requestCookies',
    requestHeaders: 'requestHeaders',
    requestMocks: 'requestMocks',
    requestOverrides: 'requestOverrides',
    requestParameters: 'requestParameters',
    resourceTimeout: 'resourceTimeout',
    resourcesInjection: 'resourcesInjection',
    responseHeaders: 'responseHeaders',
    responseOverrides: 'responseOverrides',
    schedulerData: 'schedulerData',
    schedulerEndDate: 'schedulerEndDate',
    schedulerStartDate: 'schedulerStartDate',
    screenVideos: 'screenVideos',
    screenshots: 'screenshots',
    selector: 'selector',
    selectorTargets: 'selectorTargets',
    sessionStorage: 'sessionStorage',
    solution: 'solution',
    solutionsConfigs: 'solutionsConfigs',
    source: 'source',
    stepDelay: 'stepDelay',
    stepList: 'stepList',
    stripAuthentication: 'stripAuthentication',
    stripHash: 'stripHash',
    stripWWW: 'stripWWW',
    tags: 'tags',
    target: 'target',
    type: 'type',
    urlFilter: 'urlFilter',
    urlMatch: 'urlMatch',
    urlMatchAny: 'urlMatchAny',
    urls: 'urls',
    useResolvedURLBase: 'useResolvedURLBase',
    waitUntil: 'waitUntil',
} as const;

export const BROWSERS = {
    chromium: {
        key: 'chromium',
    },
    firefox: {
        key: 'firefox',
    },
    safari: {
        key: 'safari',
    },
} as const;

export const LIGHTHOUSE_DEVICES = {
    mobile: {
        key: 'mobile',
    },
    desktop: {
        key: 'desktop',
    },
} as const;

export const OPTIONS = {
    // Management + Resources
    resourceTimeout: 300000,
    // Devices
    browser: BROWSERS.chromium,
    device: defaultDevice,
    deviceList: [],
    executionStrategy: EXECUTION_STRATEGY.SHARED_BROWSER_INSTANCE.key,
    // Lighthouse
    lighthouseDevices: [
        LIGHTHOUSE_DEVICES.mobile.key,
        LIGHTHOUSE_DEVICES.desktop.key,
    ],
    lighthouseOutputFormats: [
        LIGHTHOUSE_FIELDS.outputs.html,
    ],
    // URL Search
    tags: LINKED_TAGS,
    selector: '',
    // URL Normalization
    stripAuthentication: false,
    stripHash: false,
    stripWWW: false,
    removeQueryParameters: false,
    removeTrailingSlash: false,
    // URL Filtering (not applicable for checktype === link)
    useResolvedURLBase: true, // URLs are not checked more than once
    urlMatch: [], // if present only matching urls will be checked
    urlMatchAny: true,
    includeSubdomains: true, // remove anything, that is not www.
    includeExternalLinks: false, // URL of external domains are excluded
    includeInternalLinks: true, // All links matching domain or subdomains are excluded
    // Timing
    linkRequestTimeout: 20000,
    waitUntil: {
        option: REQUEST_WAIT_UNTIL.domContentLoaded, // puppeteer flag, for when to consider a page loaded
    },
    stepDelay: 1000, // Delay between steps, while capturing a video
    // Data + Results
    responseHeaders: {
        items: [],
    },
    deactivateJS: false,
    // AI + Browser
    language: DEFAULT_LANGUAGE,
    requestParameters: [], // url parameters => Can override URL params
    requestHeaders: [],
    requestCookies: [],
    sessionStorage: [],
    localStorage: [],
    resourcesInjection: [],
    responseOverrides: [],
    requestOverrides: [],
    requestMocks: [],
    requestBlockers: [],
    // Scheduler
    activateScheduler: false,
    schedulerData: null,
    notifications: {
        email: false,
        webhooks: false,
    },
    notificationsData: {
        email: null,
        webhooks: null,
    },
    logLevels: {
        [INFO_CODE_LEVELS.INFO]: false,
        [INFO_CODE_LEVELS.WARNING]: false,
        [INFO_CODE_LEVELS.SUCCESS]: false,
        [INFO_CODE_LEVELS.ERROR]: true,
    },
    version: 'v.1.4.2025', // 1 = major release, 4 = api version, 2015 = year
} as const;

export const URL_MATCHER = {
    exact: 'exact',
    substring: 'substring',
    regex: 'regex',
} as const;

export const LINK_CHECK_QUEUE_ITEM_KEYS = {
    url: 'url',
    selector: 'selector',
    tag: 'tag',
    html: 'html',
    text: 'text',
} as const;

export const RUN_MODES = {
    link: 'link',
    page: 'page',
} as const;

export const BUILD_STATES = {
    cancel: 'cancel',
    failure: 'failure',
    mixed: 'mixed',
    queue: 'queue',
    running: 'running',
    success: 'success',
    timeout: 'timeout',
    terminated: 'terminated',
} as const;

export const EXCLUSION_REASONS = {
    format: 'format',
    visited: 'visited',
    normalization: 'normalization',
    regex: 'regex',
    subdomain: 'subdomain',
    external: 'external',
    internal: 'internal',
} as const;

export const BUILD_SCHEDULER_TYPES = {
    CRON: 'cron',
    RANDOM: 'random',
} as const;

export const BUILD_SCHEDULER_TYPE_RANDOM_FREQUENCY = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: 'monthly',
} as const;
