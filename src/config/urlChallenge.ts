export const URL_CHALLENGE_MODES = {
    eliminationMode: 'eliminationMode',
    comprehensiveMode: 'comprehensiveMode',
} as const;

export const challenges = {
    custom: {
        js: {
            value: 'js',
        },
    },
    automated: {
        ai: {
            value: 'ai',
        },
    },
} as const;

export const urlChallengeData = {
    participants: 'participants',
    totalChallenges: 'totalChallenges',
    passedChallenges: 'passedChallenges',
    failedOrVoidChallenges: 'failedOrVoidChallenges',
} as const;

export const indecisiveCase = {
    options: {
        isPassed: 'isPassed',
        isFailed: 'isFailed',
        isVoided: 'isVoided',
    },
} as const;

export const indecisiveCaseDefault = indecisiveCase.options.isVoided;
