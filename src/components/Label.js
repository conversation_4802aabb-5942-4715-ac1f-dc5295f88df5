import PropTypes from 'prop-types';
import {alpha, styled} from '@material-ui/core/styles';

const RootStyle = styled('span')(({theme, styleProps}) => {
    const isLight = theme.palette.mode === 'light';
    const {color, colorVariant, variant} = styleProps;

    const styleFilled = (color) => ({
        color: theme.palette[color].contrastText,
        backgroundColor: theme.palette[color][colorVariant],
    });

    const styleOutlined = (color) => ({
        color: theme.palette[color][colorVariant],
        backgroundColor: 'transparent',
        border: `1px solid ${theme.palette[color][colorVariant]}`,
    });

    const styleGhost = (color) => ({
        color: theme.palette[color][isLight ? 'dark' : 'light'],
        backgroundColor: alpha(theme.palette[color][colorVariant], 0.16),
    });

    return {
        height: 22,
        minWidth: 22,
        lineHeight: 0,
        borderRadius: 8,
        cursor: 'default',
        alignItems: 'center',
        whiteSpace: 'nowrap',
        display: 'inline-flex',
        justifyContent: 'center',
        padding: theme.spacing(0, 1),
        color: theme.palette.grey[800],
        fontSize: theme.typography.pxToRem(12),
        fontFamily: theme.typography.fontFamily,
        backgroundColor: theme.palette.grey[300],
        fontWeight: theme.typography.fontWeightBold,

        ...(color !== 'default'
            ? {
                ...(variant === 'filled' && {...styleFilled(color)}),
                ...(variant === 'outlined' && {...styleOutlined(color)}),
                ...(variant === 'ghost' && {...styleGhost(color)}),
            }
            : {
                ...(variant === 'outlined' && {
                    backgroundColor: 'transparent',
                    color: theme.palette.text.primary,
                    border: `1px solid ${theme.palette.grey[500_32]}`,
                }),
                ...(variant === 'ghost' && {
                    color: isLight ? theme.palette.text.secondary : theme.palette.common.white,
                    backgroundColor: theme.palette.grey[500_16],
                }),
            }),
    };
});

const defaultProps = {
    colorVariant: 'main',
    color: 'default',
    variant: 'ghost',
};

const Label = ({color, variant, colorVariant, children, ...other}) => {
    return (
        <RootStyle styleProps={{color, variant, colorVariant}} {...other}>
            {children}
        </RootStyle>
    );
}

Label.propTypes = {
    children: PropTypes.node,
    colorVariant: PropTypes.string,
    color: PropTypes.oneOf(['default', 'primary', 'secondary', 'info', 'success', 'warning', 'error']),
    variant: PropTypes.oneOf(['filled', 'outlined', 'ghost']),
};

Label.defaultProps = defaultProps;

export default Label;
