import * as Yup from 'yup';
import PropTypes from 'prop-types';
import {Form, FormikProvider, useFormik} from 'formik';
import {<PERSON><PERSON>, Stack, TextField, Typography} from '@material-ui/core';
import {LoadingButton} from '@material-ui/lab';
import useAuth from '../../../hooks/useAuth';
import useIsMountedRef from '../../../hooks/useIsMountedRef';
import useLocales from '../../../hooks/useLocales';

const propTypes = {
    onSent: PropTypes.func,
    onGetEmail: PropTypes.func,
};

const ResetPasswordForm = ({onSent, onGetEmail}) => {
    const {translate} = useLocales();
    const {resetPassword} = useAuth();
    const isMountedRef = useIsMountedRef();

    const ResetPasswordSchema = Yup.object().shape({
        email: Yup.string()
            .email(translate('form.validation.emailInvalid'))
            .required(translate('form.validation.emailRequired')),
    });

    const formik = useFormik({
        initialValues: {email: ''},
        validationSchema: ResetPasswordSchema,
        onSubmit: async (values, {setErrors, setSubmitting}) => {
            try {
                await resetPassword(values.email);
                if (isMountedRef.current) {
                    onSent();
                    onGetEmail(formik.values.email);
                    setSubmitting(false);
                }
            } catch (error) {
                if (isMountedRef.current) {
                    setErrors({afterSubmit: translate('passwordReset.validation.registrationError')});
                    setSubmitting(false);
                }
            }
        },
    });

    const {errors, touched, isSubmitting, handleSubmit, getFieldProps} = formik;

    return (
        <FormikProvider value={formik}>
            <Form autoComplete="off" noValidate onSubmit={handleSubmit}>
                <Stack spacing={3}>
                    {errors.afterSubmit && <Alert severity="error">{errors.afterSubmit}</Alert>}
                    <Stack spacing={1}>
                        <TextField
                            fullWidth
                            {...getFieldProps('email')}
                            type="email"
                            label={translate('form.email')}
                        />
                        {Boolean(touched.email && errors.email) && (
                            <Typography
                                sx={{color: 'error.main'}}>
                                {errors.email}
                            </Typography>
                        )}
                    </Stack>

                    <LoadingButton fullWidth size="large" type="submit" variant="contained" loading={isSubmitting}>
                        {translate('form.passwordReset')}
                    </LoadingButton>
                </Stack>
            </Form>
        </FormikProvider>
    );
};

ResetPasswordForm.propTypes = propTypes;

export default ResetPasswordForm;
