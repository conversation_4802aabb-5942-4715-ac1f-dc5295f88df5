import {useState} from 'react';
import {Icon} from '@iconify/react';
import googleFill from '@iconify/icons-eva/google-fill';
import twitterFill from '@iconify/icons-eva/twitter-fill';
import facebookFill from '@iconify/icons-eva/facebook-fill';
import {Alert, Button, Divider, Stack, Typography} from '@material-ui/core';
import useAuth from '../../hooks/useAuth';
import useLocales from '../../hooks/useLocales';

const activateLoginWithFaceBook = false;
const activateLoginWithTwitter = false;

const AuthFirebaseSocials = () => {
    const {
        loginWithGoogle,
        loginWithFaceBook,
        loginWithTwitter,
    } = useAuth();
    const {translate} = useLocales();
    const [state, setState] = useState({});
    const handleLogin = async (method, loginCallback) => {
        setState({
            method,
            hasError: false,
        });
        try {
            await loginCallback();
        } catch (error) {
            setState({
                method,
                hasError: true,
            });
        }
    };

    return (
        <>
            {
                state.hasError &&
                <Alert
                    severity="error"
                    onClose={() => {setState({})}}
                    sx={{mb: 3}}>
                    {translate('register.validation.methodError', state)}
                </Alert>
            }
            <Stack direction="row" spacing={2}>
                <Button fullWidth size="large" color="inherit" variant="outlined"
                        onClick={() => handleLogin('Google', loginWithGoogle)}>
                    <Icon icon={googleFill} color="#DF3E30" height={40}/>
                </Button>
                {
                    activateLoginWithFaceBook &&
                    <Button fullWidth size="large" color="inherit" variant="outlined"
                            onClick={() => handleLogin('Facebook', loginWithFaceBook)}>
                        <Icon icon={facebookFill} color="#1877F2" height={40}/>
                    </Button>
                }
                {
                    activateLoginWithTwitter &&
                    <Button fullWidth size="large" color="inherit" variant="outlined"
                            onClick={() => handleLogin('Twitter', loginWithTwitter)}>
                        <Icon icon={twitterFill} color="#1DA1F2" height={40}/>
                    </Button>
                }
            </Stack>

            <Divider sx={{my: 3}}>
                <Typography variant="body2" sx={{color: 'text.secondary'}}>
                    {translate('or')}
                </Typography>
            </Divider>
        </>
    );
};

export default AuthFirebaseSocials;
