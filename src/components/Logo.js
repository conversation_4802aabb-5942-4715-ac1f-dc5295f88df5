import {PATH_HOME} from '@w7-3/webeagle-resources/dist/config/paths';
import PubSub from 'pubsub-js';
import {Box, Button} from '@material-ui/core';
import {globalConfig} from '../config/setup';

const propTypes = {};

const Logo = () => {
    return (
        <Button
            size="large"
            onClick={(event) => {
                PubSub.publish('NAVIGATE.TO.URI', {to: PATH_HOME});
                event.preventDefault();
                event.stopPropagation();
            }}
            sx={{
                height: 64,
                pl: '0 !important',
                backgroundColor: 'transparent !important',
                '> span': {
                    m: '0 !important',
                },
                fontSize: {
                    xs: 21,
                    lg: 35,
                },
                textTransform: 'none',
            }}
        >
            <Box
                component="span"
                sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: 'column',
                    textTransform: 'none',
                }}
            >
                <Box
                    component="span"
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        flexDirection: 'column',
                        textTransform: 'none',
                    }}
                >
                    {globalConfig.domain}
                </Box>
            </Box>
        </Button>
    );
};

Logo.propTypes = propTypes;

export default Logo;
