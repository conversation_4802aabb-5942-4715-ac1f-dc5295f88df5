import PropTypes from 'prop-types';
import { ScaleControl } from 'react-map-gl';
// material
import { styled } from '@material-ui/core/styles';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  zIndex: 99,
  position: 'absolute',
  left: theme.spacing(1.5),
  bottom: theme.spacing(3.5),
  boxShadow: theme.customShadows.z8,
  '& .mapboxgl-ctrl': {
    border: 'none',
    borderRadius: 4,
    lineHeight: '14px',
    color: theme.palette.common.white,
    backgroundImage: 'linear-gradient(to right, #8a2387, #e94057, #f27121)'
  }
}));

// ----------------------------------------------------------------------

MapControlScale.propTypes = {
  sx: PropTypes.object
};

export default function MapControlScale({ sx, ...other }) {
  return (
    <RootStyle sx={sx}>
      <ScaleControl maxWidth={100} unit="imperial" {...other} />
    </RootStyle>
  );
}
