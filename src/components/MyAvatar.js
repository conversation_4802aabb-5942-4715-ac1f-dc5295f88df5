import personOutline from '@iconify/icons-eva/person-outline';
import {Icon} from '@iconify/react';
import {useTheme} from '@material-ui/core/styles';
import useAuth from '../hooks/useAuth';
import {MAvatar} from './@material-extend';
import {getFirstCharacter} from '../utils/createAvatar';

const MyAvatar = () => {
    const {user} = useAuth();
    const theme = useTheme();

    if (!user?.email) {
        return (
            <Icon
                icon={personOutline}
                width={40}
                height={40}
                color={theme.palette.primary.main}
            />
        );
    }

    return (
        <MAvatar
            src={user.photoURL}
            alt={user.email}
            width={40}
            height={40}
            color="primary">
            {getFirstCharacter(user.email) || ' * '}
        </MAvatar>
    );
};

export default MyAvatar;
