import {Box, FormControlLabel, Radio, RadioGroup, Stack, Typography} from '@material-ui/core';
import useSettings from '../../hooks/useSettings';

const SettingColor = () => {
    const {themeColor, onChangeColor, colorOption} = useSettings();

    return (
        <RadioGroup
            row
            name="themeColor"
            value={themeColor}
            onChange={onChangeColor}>
            {colorOption.map((color) => {
                return (
                    <FormControlLabel
                        key={color.name}
                        value={color.name}
                        label={(
                            <Stack direction="row" alignItems="center" spacing={1}>
                                <Box
                                    sx={{
                                        width: 24,
                                        height: 14,
                                        borderRadius: '25%',
                                        bgcolor: color.value,
                                        transform: 'rotate(-45deg)',
                                        transition: (theme) =>
                                            theme.transitions.create('all', {
                                                easing: theme.transitions.easing.easeInOut,
                                                duration: theme.transitions.duration.shorter,
                                            }),
                                    }}
                                />
                                <Typography variant="body2" sx={{mr: 1}}>
                                    {`${color?.mainName}`.toUpperCase()}
                                </Typography>
                            </Stack>
                        )}
                        control={<Radio/>}/>
                );
            })}
        </RadioGroup>
    );
};

export default SettingColor;
