import {useState} from 'react';
import {Slider, Stack, useMediaQuery} from '@material-ui/core';
import {useTheme} from '@material-ui/core/styles';
import {getLocalStorageItem, setLocalStorageItem} from '../../custom/utils/storage';

const fontSizes = [
    {
        value: 1,
        label: 'S',
    },
    {
        value: 1.125,
        label: 'M',
    },
    {
        value: 1.25,
        label: 'L',
    },
    {
        value: 1.375,
        label: 'XL',
    },
];

const SettingFontSize = () => {
    const theme = useTheme();
    const initialFont = useMediaQuery(theme.breakpoints.down('lg')) ? 2 : 1;
    const [fontSize, setFontSize] = useState(getLocalStorageItem('settings', 'fontSize') || fontSizes[initialFont].value);
    const handleFontSizeChange = ({target}) => {
        setFontSize(target.value);
        window.document.documentElement.style.fontSize = `${target.value}rem`;
        setLocalStorageItem('settings', target.value, 'fontSize');
    };

    return (
        <Stack direction="row" alignItems="center" justifyContent="space-around">
            <Slider
                min={fontSizes[0].value}
                step={.125}
                max={fontSizes[fontSizes.length - 1].value}
                value={fontSize}
                onChange={handleFontSizeChange}
                marks={fontSizes}
            />
        </Stack>
    );
};

export default SettingFontSize;
