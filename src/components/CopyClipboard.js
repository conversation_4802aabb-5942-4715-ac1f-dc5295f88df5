import {useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {Icon} from '@iconify/react';
import {useSnackbar} from 'notistack5';
import copyFill from '@iconify/icons-eva/copy-fill';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import {IconButton, InputAdornment, TextField, Tooltip} from '@material-ui/core';
import useLocales from '../hooks/useLocales';

CopyClipboard.propTypes = {
    value: PropTypes.string,
    title: PropTypes.string,
    successMessage: PropTypes.string,
    iconOnly: PropTypes.bool,
    IconProps: PropTypes.object,
    InputProps: PropTypes.object,
};

export default function CopyClipboard({value, title, successMessage, iconOnly, IconProps, InputProps, ...other}) {
    const {enqueueSnackbar} = useSnackbar();
    const {translate} = useLocales();
    const [state, setState] = useState({
        value,
        copied: false,
    });

    useEffect(() => {
        setState({value, copied: false});
    }, [value]);

    const handleChange = (event) => {
        setState({value: event.target.value, copied: false});
    };

    const onCopy = () => {
        setState({...state, copied: true});
        if (state.value) {
            enqueueSnackbar(successMessage || translate('copied'), {variant: 'success'});
        }
    };

    const icon = (
        <CopyToClipboard text={state.value} onCopy={onCopy}>
            <Tooltip title={title || translate('copy')}>
                <IconButton>
                    <Icon
                        icon={copyFill}
                        width={40}
                        height={40}
                        {...IconProps}
                    />
                </IconButton>
            </Tooltip>
        </CopyToClipboard>
    );

    if (iconOnly) {
        return icon;
    }

    return (
        <TextField
            fullWidth
            multiline
            value={state.value}
            onChange={handleChange}
            InputProps={{
                ...InputProps,
                endAdornment: (
                    <InputAdornment position="end">
                        {icon}
                    </InputAdornment>
                ),
            }}
            {...other}
        />
    );
}
