{
  "summary": {
    "language": "de",
    "activateScheduler": false,
    "schedulerStartDate": 1655404305636,
    "notifications": {
      "webhooks": true,
      "email": true
    },
    "schedulerEndDate": null
  },
  "requestOptions": {
    "activeMenuItem": {
      "key": "browser"
    },
    "storage": {
      "localStorage": [],
      "requestParameters": [],
      "requestHeaders": [],
      "sessionStorage": [],
      "requestCookies": []
    },
    "browser": {
      "linkRequestTimeout": 20000,
      "deactivateJS": false
    },
    "resources": {
      "responseOverrides": [],
    }
  },
  "solutionsConfigs": {
    "lighthouse": {
      "lighthouseOutputFormats": [
        "html"
      ],
      "availableOutputFormats": [
        "html",
        "json",
        "csv"
      ],
      "availableCategoryList": [
        "performance",
        "accessibility",
        "best-practices",
        "seo",
        "pwa"
      ],
      "categoryList": [
        "performance",
        "accessibility",
        "best-practices",
        "seo"
      ]
    },
    "screenVideos": {
      "actionList": [],
      "list": [],
      "conditions": {}
    },
    "screenshots": {
      "actionList": [],
      "groups": {},
      "urlFilter": {
        "urlMatch": [],
        "urlMatchAny": true
      },
      "conditions": {}
    },
    "dataExtractions": {
      "actionList": [],
      "groups": {},
      "urlFilter": {
        "urlMatch": [],
        "urlMatchAny": true
      },
      "conditions": {}
    }
  },
  "target": {
    "checkType": "link",
    "customDevice": {
      "userAgent": "Webeagle - w10.3 V1.0 2021",
      "viewport": {
        "hasTouch": true,
        "deviceScaleFactor": 1,
        "isLandscape": true,
        "width": 768,
        "isMobile": true,
        "height": 480
      },
      "name": ""
    },
    "urls": [
      {
        "url": "https://roboyo.global/de/"
      }
    ],
    "browser": {
      "key": "firefox"
    },
    "deviceList": [],
    "lighthouseDevices": [
      "mobile",
      "desktop"
    ],
    "device": {
      "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1",
      "viewport": {
        "hasTouch": true,
        "deviceScaleFactor": 2,
        "isLandscape": false,
        "width": 320,
        "isMobile": true,
        "height": 568
      },
      "name": "iPhone 5"
    }
  },
  "solution": {
    "configurable": false,
    "key": "linkChecker"
  },
  "linkOptions": {
    "activeMenuItem": {
      "key": "urlSelection"
    },
    "normalizations": {
      "stripWWW": false,
      "stripHash": true,
      "removeTrailingSlash": false,
      "stripAuthentication": false,
      "removeQueryParameters": true
    },
    "selection": {
      "tags": {
        "a": {
          "attributeList": [
            "href",
            "ping"
          ],
          "selector": "a"
        },
        "blockquote": {
          "attributeList": [
            "cite"
          ],
          "selector": "blockquote"
        },
        "img": {
          "attributeList": [
            "src",
            "srcset",
            "longdesc"
          ],
          "selector": "img"
        },
        "link": {
          "attributeList": [
            "href"
          ],
          "selector": "link"
        },
        "source": {
          "attributeList": [
            "src",
            "srcset"
          ],
          "selector": "source"
        },
        "script": {
          "attributeList": [
            "src"
          ],
          "selector": "script"
        },
        "form": {
          "attributeList": [
            "action"
          ],
          "additionalSelector": ":not([method=\"post\"])",
          "selector": "form"
        },
        "iframe": {
          "attributeList": [
            "src"
          ],
          "selector": "iframe"
        },
        "base": {
          "attributeList": [
            "href"
          ],
          "selector": "base"
        },
        "frame": {
          "attributeList": [
            "src"
          ],
          "selector": "frame"
        },
        "object": {
          "attributeList": [
            "data"
          ],
          "selector": "object"
        }
      },
      "selector": ""
    },
    "filtering": {
      "includeSubdomains": true,
      "useResolvedURLBase": true,
      "includeInternalLinks": true,
      "includeExternalLinks": false,
      "urlFilter": {
        "urlMatch": [],
        "urlMatchAny": true
      }
    }
  },
  "projectName": "Roboyo Test - 3",
  "projectId": "29021fe0-eda3-11ec-8894-dd1b3a0da0b2"
}
