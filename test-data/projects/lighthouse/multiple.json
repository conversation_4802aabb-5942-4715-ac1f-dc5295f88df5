{"projectName": "Multiple, only score", "linkOptions": {"selection": {"selector": "", "tags": {"a": {"selector": "a", "attributeList": ["href", "ping"]}, "base": {"selector": "base", "attributeList": ["href"]}, "blockquote": {"selector": "blockquote", "attributeList": ["cite"]}, "form": {"selector": "form", "additionalSelector": ":not([method=\"post\"])", "attributeList": ["action"]}, "frame": {"selector": "frame", "attributeList": ["src"]}, "iframe": {"selector": "iframe", "attributeList": ["src"]}, "img": {"selector": "img", "attributeList": ["src", "srcset", "longdesc"]}, "link": {"selector": "link", "attributeList": ["href"]}, "object": {"selector": "object", "attributeList": ["data"]}, "script": {"selector": "script", "attributeList": ["src"]}, "source": {"selector": "source", "attributeList": ["src", "srcset"]}}}, "normalizations": {"stripHash": true, "stripWWW": true, "removeQueryParameters": true, "removeTrailingSlash": true, "stripAuthentication": false}, "filtering": {"urlFilter": {"urlMatchAny": true, "urlMatch": []}, "includeSubdomains": true, "includeInternalLinks": true, "includeExternalLinks": true}, "results": {"language": "de", "notifications": {"webhooks": true, "email": true}}}, "requestOptions": {"browser": {"deactivateJS": false, "linkRequestTimeout": 20000}, "storage": {"requestParameters": [], "requestHeaders": [], "requestCookies": [], "responseOverrides": []}, "resources": {}}, "solution": {"key": "lighthouse", "configurable": true}, "solutionsConfigs": {"lighthouse": {"availableCategoryList": ["performance", "accessibility", "best-practices", "seo", "pwa"], "categoryList": ["performance", "accessibility", "best-practices", "seo", "pwa"], "availableOutputFormats": ["html", "json", "csv"], "lighthouseOutputFormats": [], "urlFilter": {"urlMatch": []}}, "screenshots": {"urlFilter": {"urlMatchAny": true, "urlMatch": []}, "actionList": [], "conditions": {}, "groups": {}}, "screenVideos": {"urlFilter": {"urlMatchAny": true, "urlMatch": []}, "actionList": [], "conditions": {}, "list": []}, "dataExtractions": {"urlFilter": {"urlMatchAny": true, "urlMatch": []}, "actionList": [], "conditions": {}, "groups": {}}}, "summary": {"language": "de", "notifications": {"webhooks": true, "email": true}, "activateScheduler": true}, "target": {"checkType": "link", "urls": [{"url": "https://yahoo.de"}, {"url": "https://google.ch"}], "device": {"name": "Desktop XXL", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36", "viewport": {"width": 1920, "height": 1080, "deviceScaleFactor": 1, "isMobile": false, "hasTouch": false, "isLandscape": false}}, "browser": {"key": "chromium"}, "deviceList": [], "lighthouseDevices": ["mobile", "desktop"]}}