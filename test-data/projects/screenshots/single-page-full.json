{
  "summary": {
    "language": "de",
    "activateScheduler": false,
    "schedulerStartDate": 1653848317640,
    "notifications": {
      "webhooks": true,
      "email": true
    },
    "schedulerEndDate": null
  },
  "requestOptions": {
    "activeMenuItem": {
      "key": "browser"
    },
    "storage": {
      "localStorage": [],
      "requestParameters": [],
      "requestHeaders": [],
      "sessionStorage": [],
      "requestCookies": []
    },
    "browser": {
      "linkRequestTimeout": 20000,
      "deactivateJS": false
    },
    "resources": {
      "responseOverrides": [],
    }
  },
  "solutionsConfigs": {
    "lighthouse": {
      "lighthouseOutputFormats": [
        "html"
      ],
      "availableOutputFormats": [
        "html",
        "json",
        "csv"
      ],
      "availableCategoryList": [
        "performance",
        "accessibility",
        "best-practices",
        "seo",
        "pwa"
      ],
      "categoryList": [
        "performance",
        "accessibility",
        "best-practices",
        "seo"
      ]
    },
    "screenVideos": {
      "actionList": [],
      "list": [],
      "urlFilter": {
        "urlMatch": [],
        "urlMatchAny": true
      },
      "conditions": {}
    },
    "screenshots": {
      "actionList": [
        {
          "abortOnError": false,
          "label": "Mouse Interaktionen - 1653848510142",
          "type": "interaction",
          "selector": "",
          "id": "9af04a0b-9f53-4fff-b191-149bd72ffc9e",
          "value": {
            "clickCount": 1,
            "target": "move",
            "buttonType": "left",
            "delay": 0,
            "dx": 0,
            "dy": 0,
            "x": 0,
            "y": 0,
            "buttonAction": "click"
          },
          "categoryId": "mouse"
        }
      ],
      "groups": {
        "screenshots1": {
          "list": [
            {
              "content": {
                "extension": "png",
                "selectorTargets": "all",
                "actionList": [],
                "customSelectorTargets": "",
                "type": "fullPage",
                "name": "Screenshot - 1",
                "selector": "",
                "id": "482f8350-fd34-455e-b8cd-faff74382721",
                "conditions": {}
              },
              "id": "482f8350-fd34-455e-b8cd-faff74382721"
            }
          ],
          "name": "Screenshot Gruppe - 1",
          "id": "screenshots1"
        }
      },
      "urlFilter": {
        "urlMatchAny": true
      },
      "conditions": {
        "condition-group-2": {
          "list": [
            {
              "label": "Response-Header - 1653848471207",
              "type": "responseHeader",
              "selector": "",
              "id": "58e0e8dc-bf80-44cc-8579-8e00cfde3600",
              "value": {
                "name": "Test",
                "value": "test"
              }
            }
          ],
          "name": "Bedingungsgruppe 2",
          "id": "condition-group-2"
        },
        "condition-group-1": {
          "list": [
            {
              "label": "Cookie - 1653848425339",
              "type": "cookie",
              "selector": "",
              "id": "be11e8a8-b2f8-4da5-afa6-01d71b988645",
              "value": {
                "expiresType": "=",
                "data": {
                  "expires": 0,
                  "secure": false,
                  "path": "",
                  "domain": "",
                  "sameSite": "None",
                  "name": "sdf",
                  "httpOnly": true,
                  "value": "kzgk"
                }
              }
            },
            {
              "label": "HTML Element - 1653848443406",
              "type": "elementPresent",
              "selector": "sdfsdf",
              "id": "faf11ce5-3bf3-4c26-8416-a6d766dcbcd9",
              "value": null
            },
            {
              "label": "JavaScript - 1653848450605",
              "type": "js",
              "selector": "",
              "id": "a7225390-d688-4a49-8f6d-370c8b605413",
              "value": {
                "jsCode": {
                  "isValid": true,
                  "isValidated": true,
                  "value": "return '';"
                }
              }
            }
          ],
          "name": "Bedingungsgruppe 1",
          "id": "condition-group-1"
        }
      }
    },
    "dataExtractions": {
      "actionList": [],
      "groups": {},
      "urlFilter": {
        "urlMatch": [],
        "urlMatchAny": true
      },
      "conditions": {}
    }
  },
  "target": {
    "checkType": "link",
    "customDevice": {
      "userAgent": "Webeagle - w10.3 V1.0 2021",
      "viewport": {
        "hasTouch": true,
        "deviceScaleFactor": 1,
        "isLandscape": true,
        "width": 768,
        "isMobile": true,
        "height": 480
      },
      "name": ""
    },
    "urls": [
      {
        "url": "https://yahoo.de"
      }
    ],
    "browser": {
      "key": "firefox"
    },
    "deviceList": [],
    "lighthouseDevices": [
      "mobile",
      "desktop"
    ],
    "device": {
      "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
      "viewport": {
        "hasTouch": false,
        "deviceScaleFactor": 1,
        "isLandscape": false,
        "width": 1920,
        "isMobile": false,
        "height": 1080
      },
      "name": "Desktop XXL"
    }
  },
  "solution": {
    "key": "screenshots",
    "configurable": true
  },
  "linkOptions": {
    "normalizations": {
      "stripWWW": false,
      "stripHash": true,
      "removeTrailingSlash": false,
      "stripAuthentication": false,
      "removeQueryParameters": true
    },
    "selection": {
      "tags": {
        "a": {
          "attributeList": [
            "href",
            "ping"
          ],
          "selector": "a"
        },
        "blockquote": {
          "attributeList": [
            "cite"
          ],
          "selector": "blockquote"
        },
        "img": {
          "attributeList": [
            "src",
            "srcset",
            "longdesc"
          ],
          "selector": "img"
        },
        "link": {
          "attributeList": [
            "href"
          ],
          "selector": "link"
        },
        "source": {
          "attributeList": [
            "src",
            "srcset"
          ],
          "selector": "source"
        },
        "script": {
          "attributeList": [
            "src"
          ],
          "selector": "script"
        },
        "form": {
          "attributeList": [
            "action"
          ],
          "additionalSelector": ":not([method=\"post\"])",
          "selector": "form"
        },
        "iframe": {
          "attributeList": [
            "src"
          ],
          "selector": "iframe"
        },
        "base": {
          "attributeList": [
            "href"
          ],
          "selector": "base"
        },
        "frame": {
          "attributeList": [
            "src"
          ],
          "selector": "frame"
        },
        "object": {
          "attributeList": [
            "data"
          ],
          "selector": "object"
        }
      },
      "selector": ""
    },
    "filtering": {
      "includeSubdomains": true,
      "useResolvedURLBase": true,
      "includeInternalLinks": true,
      "includeExternalLinks": false,
      "urlFilter": {
        "urlMatch": [],
        "urlMatchAny": true
      }
    }
  },
  "projectName": "Tester",
  "projectId": "4cf4e7a0-df7c-11ec-8894-dd1b3a0da0b2"
}
