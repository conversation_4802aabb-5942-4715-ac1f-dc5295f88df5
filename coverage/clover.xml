<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1725635111551" clover="3.2.0">
  <project timestamp="1725635111551" name="All files">
    <metrics statements="19" coveredstatements="16" conditionals="7" coveredconditionals="2" methods="3" coveredmethods="3" elements="29" coveredelements="21" complexity="0" loc="19" ncloc="19" packages="2" files="2" classes="2"/>
    <package name="utils">
      <metrics statements="9" coveredstatements="7" conditionals="4" coveredconditionals="1" methods="2" coveredmethods="2"/>
      <file name="evaluateFunction.ts" path="/Users/<USER>/work/webeagle-api/src/utils/evaluateFunction.ts">
        <metrics statements="9" coveredstatements="7" conditionals="4" coveredconditionals="1" methods="2" coveredmethods="2"/>
        <line num="3" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="25" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
    </package>
    <package name="utils.playwright">
      <metrics statements="10" coveredstatements="9" conditionals="3" coveredconditionals="1" methods="1" coveredmethods="1"/>
      <file name="getRequestData.ts" path="/Users/<USER>/work/webeagle-api/src/utils/playwright/getRequestData.ts">
        <metrics statements="10" coveredstatements="9" conditionals="3" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="8" count="1" type="stmt"/>
        <line num="11" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
