{"name": "@w7-3/webeagle-api", "description": "Webeagle API", "license": "UNLICENSED", "version": "0.0.1", "private": true, "author": "c.francis", "engines": {"node": "=18"}, "exports": {".": "./src/index.js"}, "scripts": {"reset": "yarn --frozen-lockfile", "reset:yarn": "rm -rf ./node_modules && rm ./yarn.lock && yarn", "preinstall": "node -e \"if(process.env.npm_execpath.indexOf('yarn') === -1) throw new Error('Please use yarn, not NPM. Thank you :-)')\"", "eslint": "eslint --ext .js,.jsx,.ts,.tsx --max-warnings 0 src/", "tslint": "tsc --noEmit --resolveJsonModule", "lint": "yarn eslint && yarn tslint", "eslint:fix": "yarn eslint --fix", "test:unit": "TZ=Europe/Zurich jest src --silent", "test:unit:coverage:regenerate": "yarn test:unit --coverage --updateSnapshot", "test": "yarn tslint && yarn test:unit", "dev:resources:upgrade": "yarn upgrade @w7-3/webeagle-resources --latest", "dev:start:stripe": "stripe listen --forward-to http://localhost:3101/checkout/v1/webhook", "build:clean": "node scripts/clean.js", "start:dev": "WEB_AUTOMATE_ENV=dev WEB_AUTOMATE_APP=api yarn build:dev && sh scripts/jobs/envs/dev/local-deploy-api.sh", "build:dev": "rollup -c scripts/rollup/configs/rollup.config.dev.js --bundleConfigAsCjs", "build:stage": "rollup -c scripts/rollup/configs/rollup.config.stage.js --bundleConfigAsCjs", "build:prod": "rollup -c scripts/rollup/configs/rollup.config.prod.js --bundleConfigAsCjs", "dev:resources:copy:raw:libs:types": "rm -R ./node_modules/@w7-3/webeagle-resources; mkdir -p ./node_modules/@w7-3/webeagle-resources/ && cp -R ../webeagle-resources/dist ./node_modules/@w7-3/webeagle-resources && cp -R ../webeagle-resources/types ./node_modules/@w7-3/webeagle-resources"}, "devDependencies": {"@babel/cli": "7.8.3", "@babel/core": "7.8.3", "@babel/node": "7.16.0", "@babel/plugin-proposal-class-properties": "7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-transform-runtime": "7.23.4", "@babel/plugin-transform-unicode-sets-regex": "^7.24.1", "@babel/preset-env": "7.8.3", "@babel/preset-typescript": "7.8.3", "@babel/runtime": "7.23.4", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-html": "^2.0.0", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-typescript": "11.1.6", "@types/jest": "27.4.0", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "concurrently": "^8.2.2", "eslint": "8.54.0", "eslint-config-google": "0.14.0", "eslint-loader": "4.0.2", "eslint-plugin-import": "2.29.0", "jest": "27.4.7", "nodemon": "^3.1.4", "rollup": "4.17.2", "rollup-plugin-string": "^3.0.0", "should": "13.2.3", "ts-jest": "27.1.3", "ts-node": "^10.9.2", "typescript": "^5.5.2"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "1.1.0", "@google-cloud/datastore": "8.7.0", "@google-cloud/logging": "^11.1.0", "@google-cloud/pubsub": "^4.5.0", "@google-cloud/secret-manager": "5.5.0", "@sendgrid/mail": "8.1.3", "@w7-3/webeagle-resources": "^2.2.8", "axios": "1.6.8", "change-case": "4.1.2", "cookie-parser": "1.4.6", "cors": "^2.8.5", "cron-parser": "4.8.1", "dayjs": "1.10.7", "deep-diff": "^1.0.2", "deepmerge": "^4.3.1", "dotenv-extended": "2.9.0", "express": "4.17.2", "fetch-to-curl": "^0.6.0", "firebase": "9.19.1", "firebase-admin": "11.8.0", "fs-extra": "11.2.0", "get-port": "^7.1.0", "lighthouse": "9.3.1", "looks-same": "^9.0.1", "mime-types": "2.1.35", "multer": "1.4.5-lts.1", "normalize-url": "7.0.3", "openai": "^4.98.0", "pdfjs": "2.5.2", "playwright": "^1.52.0", "playwright-lighthouse": "2.2.1", "playwright-video": "2.4.0", "query-string": "7.1.1", "ramda": "0.30.0", "robots-parser": "^3.0.1", "stripe": "17.7.0", "tiktoken-node": "^0.0.7", "tldts": "5.7.75", "url-parse": "1.5.10"}}