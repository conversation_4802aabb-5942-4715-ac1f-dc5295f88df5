{"name": "@w7-3/webeagle-resources", "description": "Webeagle resources", "license": "UNLICENSED", "version": "0.0.1", "author": "c. francis", "engines": {"node": "=18"}, "files": ["dist", "types"], "repository": {"type": "git", "url": "**************:w7-3/webeagle-resources.git"}, "scripts": {"reset": "yarn --frozen-lockfile", "reset:yarn": "rm -rf ./node_modules && rm ./yarn.lock && yarn", "eslint": "eslint --ext .js,.jsx,.ts,.tsx --max-warnings 0 src/", "tslint": "tsc --noEmit --resolveJsonModule", "lint": "yarn eslint && yarn tslint", "eslint:fix": "yarn eslint --fix", "test:unit": "TZ=Europe/Zurich jest src --silent", "test:unit:coverage:regenerate": "yarn test:unit --coverage --updateSnapshot", "test": "yarn lint && yarn test:unit", "build:copy-md": "cpx \"src/**/*.md\" dist", "build:clean": "rm -rf dist", "build:clean:all": "yarn build:clean && tsc && yarn build:copy-md", "run:manage:translations": "ts-node src/webautomate/dev-assistant/manageTranslations.ts"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/ramda": "^0.30.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "cpx": "^1.5.0", "eslint": "^8.54.0", "eslint-config-google": "^0.14.0", "eslint-loader": "^3.0.2", "eslint-plugin-import": "^2.18.2", "jest": "^27.5.1", "playwright": "^1.44.0", "ts-jest": "^27.1.4", "ts-node": "^10.9.2", "typescript": "^5.5.4"}, "dependencies": {"@google-cloud/pubsub": "^4.5.0", "axios": "^1.6.2", "dayjs": "^1.10.7", "firebase-admin": "^12.0.0", "fs-extra": "^11.2.0", "i18next": "^23.10.1", "mime-types": "^2.1.35", "nanoid": "^5.0.7", "normalize-url": "^7.0.2", "query-string": "^7.1.1", "ramda": "^0.30.1", "stripe": "14.9.0", "tldts": "^5.7.75", "uuid": "^9.0.1"}}