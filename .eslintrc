{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "google", "plugin:import/typescript", "plugin:@typescript-eslint/recommended"], "env": {"browser": true, "es6": true, "node": true}, "globals": {"test": true, "describe": true, "expect": true, "it": true, "Phaser": true, "__webpack_hash__": true, "globalConfig": true}, "rules": {"max-len": ["error", {"code": 180, "tabWidth": 4, "ignoreUrls": true}], "require-jsdoc": "off", "operator-linebreak": ["error", "before"], "new-cap": ["error", {"capIsNew": false}], "one-var": "off", "guard-for-in": "off", "indent": ["error", 4]}, "overrides": [{"files": ["*.ts", "*.tsx"], "extends": ["plugin:@typescript-eslint/recommended"], "parserOptions": {"project": "./tsconfig.json", "ecmaFeatures": {"jsx": true}, "useJSXTextNode": true}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}}, "rules": {"max-len": "off", "semi": "off", "no-inner-declarations": "off", "no-async-promise-executor": "off", "no-throw-literal": "off", "no-empty": "off", "camelcase": "off", "quote-props": ["error", "as-needed"], "@typescript-eslint/semi": "error", "@typescript-eslint/no-this-alias": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/ban-ts-ignore": "off", "@typescript-eslint/camelcase": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports", "disallowTypeAnnotations": true, "fixStyle": "separate-type-imports"}]}}]}